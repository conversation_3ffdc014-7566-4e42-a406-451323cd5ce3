{"resourceType": "Bundle", "id": "resources", "meta": {"lastUpdated": "2019-11-01T09:29:23.356+11:00"}, "type": "collection", "entry": [{"fullUrl": "https://medplum.com/fhir/StructureDefinition/Project", "resource": {"resourceType": "StructureDefinition", "id": "Project", "name": "Project", "url": "https://medplum.com/fhir/StructureDefinition/Project", "status": "active", "description": "Encapsulation of resources for a specific project or organization.", "kind": "resource", "abstract": false, "type": "Project", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/DomainResource", "snapshot": {"element": [{"id": "Project", "path": "Project", "short": "Encapsulation of resources for a specific project or organization.", "definition": "Encapsulation of resources for a specific project or organization.", "min": 0, "max": "*", "base": {"min": 0, "max": "*", "path": "Project"}}, {"id": "Project.id", "path": "Project.id", "short": "Logical id of this artifact", "definition": "The logical id of the resource, as used in the URL for the resource. Once assigned, this value never changes.", "min": 0, "max": "1", "base": {"path": "Resource.id", "min": 0, "max": "1"}, "type": [{"code": "string"}]}, {"id": "Project.meta", "path": "Project.meta", "definition": "The metadata about the resource. This is content that is maintained by the infrastructure. Changes to the content might not always be associated with version changes to the resource.", "min": 0, "max": "1", "base": {"path": "Resource.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}]}, {"id": "Project.implicitRules", "path": "Project.implicitRules", "definition": "A reference to a set of rules that were followed when the resource was constructed, and which must be understood when processing the content. Often, this is a reference to an implementation guide that defines the special rules along with other profiles etc.", "min": 0, "max": "1", "base": {"path": "Resource.implicitRules", "min": 0, "max": "1"}, "type": [{"code": "uri"}]}, {"id": "Project.language", "path": "Project.language", "definition": "The base language in which the resource is written.", "min": 0, "max": "1", "base": {"path": "Resource.language", "min": 0, "max": "1"}, "type": [{"code": "code"}]}, {"id": "Project.text", "path": "Project.text", "short": "Text summary of the resource, for human interpretation", "definition": "A human-readable narrative that contains a summary of the resource and can be used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative. Resource definitions may define what content should be represented in the narrative to ensure clinical safety.", "min": 0, "max": "1", "base": {"path": "DomainResource.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}]}, {"id": "Project.contained", "path": "Project.contained", "short": "Contained, inline Resources", "definition": "These resources do not have an independent existence apart from the resource that contains them - they cannot be identified independently, and nor can they have their own independent transaction scope.", "min": 0, "max": "*", "base": {"path": "DomainResource.contained", "min": 0, "max": "*"}, "type": [{"code": "Resource"}]}, {"id": "Project.extension", "path": "Project.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the resource. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "min": 0, "max": "*", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "Project.modifierExtension", "path": "Project.modifierExtension", "short": "Extensions that cannot be ignored", "definition": "May be used to represent additional information that is not part of the basic definition of the resource and that modifies the understanding of the element that contains it and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer is allowed to define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "min": 0, "max": "*", "base": {"path": "DomainResource.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "Project.identifier", "path": "Project.identifier", "short": "An identifier for this project", "definition": "An identifier for this project.", "min": 0, "max": "*", "type": [{"code": "Identifier"}], "base": {"path": "Project.identifier", "min": 0, "max": "*"}}, {"id": "Project.name", "path": "Project.name", "definition": "A name associated with the Project.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "Project.name", "min": 0, "max": "1"}}, {"id": "Project.description", "path": "Project.description", "definition": "A summary, characterization or explanation of the Project.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "Project.description", "min": 0, "max": "1"}}, {"id": "Project.superAdmin", "path": "Project.superAdmin", "short": "Whether this project is the super administrator project.", "definition": "Whether this project is the super administrator project. A super administrator is a user who has complete access to all resources in all projects.", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "Project.superAdmin", "min": 0, "max": "1"}}, {"id": "Project.strictMode", "path": "Project.strictMode", "short": "Whether this project uses strict FHIR validation.", "definition": "Whether this project uses strict FHIR validation.  This setting has been deprecated, and can only be set by a super admin.", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "Project.strictMode", "min": 0, "max": "1"}}, {"id": "Project.checkReferencesOnWrite", "path": "Project.checkReferencesOnWrite", "short": "Whether this project uses referential integrity on write operations such as 'create' and 'update'.", "definition": "Whether this project uses referential integrity on write operations such as 'create' and 'update'.", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "Project.checkReferencesOnWrite", "min": 0, "max": "1"}}, {"id": "Project.owner", "path": "Project.owner", "definition": "The user who owns the project.", "min": 0, "max": "1", "type": [{"code": "Reference", "targetProfile": ["https://medplum.com/fhir/StructureDefinition/User"]}], "base": {"path": "Project.owner", "min": 0, "max": "1"}}, {"id": "Project.features", "path": "Project.features", "definition": "A list of optional features that are enabled for the project.", "min": 0, "max": "*", "type": [{"code": "code"}], "base": {"path": "Project.features", "min": 0, "max": "*"}, "binding": {"strength": "required", "valueSet": "https://medplum.com/fhir/ValueSet/project-feature"}}, {"id": "Project.defaultPatientAccessPolicy", "path": "Project.defaultPatientAccessPolicy", "definition": "The default access policy for patients using open registration.", "min": 0, "max": "1", "type": [{"code": "Reference", "targetProfile": ["https://medplum.com/fhir/StructureDefinition/AccessPolicy"]}], "base": {"path": "Project.defaultPatientAccessPolicy", "min": 0, "max": "1"}}, {"id": "Project.setting", "path": "Project.setting", "definition": "Option or parameter that can be adjusted within the Medplum Project to customize its behavior.", "min": 0, "max": "*", "type": [{"code": "BackboneElement"}], "base": {"path": "Project.setting", "min": 0, "max": "*"}}, {"id": "Project.setting.name", "path": "Project.setting.name", "definition": "The secret name.", "min": 1, "max": "1", "type": [{"code": "string"}], "base": {"path": "Project.setting.name", "min": 1, "max": "1"}}, {"id": "Project.setting.value[x]", "path": "Project.setting.value[x]", "definition": "The secret value.", "min": 1, "max": "1", "type": [{"code": "string"}, {"code": "boolean"}, {"code": "decimal"}, {"code": "integer"}], "base": {"path": "Project.setting.value[x]", "min": 1, "max": "1"}}, {"id": "Project.secret", "path": "Project.secret", "definition": "Option or parameter that can be adjusted within the Medplum Project to customize its behavior, only visible to project administrators.", "min": 0, "max": "*", "base": {"path": "Project.secret", "min": 0, "max": "*"}, "contentReference": "#Project.setting"}, {"id": "Project.systemSetting", "path": "Project.systemSetting", "definition": "Option or parameter that can be adjusted within the Medplum Project to customize its behavior, only modifiable by system administrators.", "min": 0, "max": "*", "base": {"path": "Project.systemSetting", "min": 0, "max": "*"}, "contentReference": "#Project.setting"}, {"id": "Project.systemSecret", "path": "Project.systemSecret", "definition": "Option or parameter that can be adjusted within the Medplum Project to customize its behavior, only visible to system administrators.", "min": 0, "max": "*", "base": {"path": "Project.systemSecret", "min": 0, "max": "*"}, "contentReference": "#Project.setting"}, {"id": "Project.site", "path": "Project.site", "definition": "Web application or web site that is associated with the project.", "min": 0, "max": "*", "type": [{"code": "BackboneElement"}], "base": {"path": "Project.site", "min": 0, "max": "*"}}, {"id": "Project.site.name", "path": "Project.site.name", "definition": "Friendly name that will make it easy for you to identify the site in the future.", "min": 1, "max": "1", "type": [{"code": "string"}], "base": {"path": "Project.site.name", "min": 1, "max": "1"}}, {"id": "Project.site.domain", "path": "Project.site.domain", "definition": "The list of domain names associated with the site. User authentication will be restricted to the domains you enter here, plus any subdomains. In other words, a registration for example.com also registers subdomain.example.com. A valid domain requires a host and must not include any path, port, query or fragment.", "min": 1, "max": "*", "type": [{"code": "string"}], "base": {"path": "Project.site.domain", "min": 1, "max": "*"}}, {"id": "Project.site.googleClientId", "path": "Project.site.googleClientId", "definition": "The publicly visible Google Client ID for the site. This is used to authenticate users with Google. This value is available in the Google Developer Console.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "Project.site.googleClientId", "min": 0, "max": "1"}}, {"id": "Project.site.googleClientSecret", "path": "Project.site.googleClientSecret", "definition": "The private Google Client Secret for the site. This value is available in the Google Developer Console.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "Project.site.googleClientSecret", "min": 0, "max": "1"}}, {"id": "Project.site.recaptchaSiteKey", "path": "Project.site.recaptchaSiteKey", "definition": "The publicly visible reCAPTCHA site key. This value is generated when you create a new reCAPTCHA site in the reCAPTCHA admin console. Use this site key in the HTML code your site serves to users.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "Project.site.recaptchaSiteKey", "min": 0, "max": "1"}}, {"id": "Project.site.recaptchaSecretKey", "path": "Project.site.recaptchaSecretKey", "definition": "The private reCAPTCHA secret key. This value is generated when you create a new reCAPTCHA site in the reCAPTCHA admin console. Use this secret key for communication between your site and reCAPTCHA.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "Project.site.recaptchaSecretKey", "min": 0, "max": "1"}}, {"id": "Project.link", "path": "Project.link", "definition": "Linked Projects whose contents are made available to this one", "min": 0, "max": "*", "type": [{"code": "BackboneElement"}], "base": {"path": "Project.link", "min": 0, "max": "*"}}, {"id": "Project.link.project", "path": "Project.link.project", "definition": "A reference to the Project to be linked into this one", "min": 1, "max": "1", "type": [{"code": "Reference", "targetProfile": ["Project"]}], "base": {"path": "Project.link.project", "min": 1, "max": "1"}}, {"id": "Project.defaultProfile", "path": "Project.defaultProfile", "definition": "Default profiles to apply to resources in this project that do not individually specify profiles", "min": 0, "max": "*", "type": [{"code": "BackboneElement"}], "base": {"path": "Project.defaultProfile", "min": 0, "max": "*"}}, {"id": "Project.defaultProfile.resourceType", "path": "Project.defaultProfile.resourceType", "definition": "The resource type onto which to apply the default profiles", "min": 1, "max": "1", "type": [{"code": "code"}], "base": {"path": "Project.defaultProfile.resourceType", "min": 1, "max": "1"}, "binding": {"strength": "required", "valueSet": "http://hl7.org/fhir/ValueSet/resource-types"}}, {"id": "Project.defaultProfile.profile", "path": "Project.defaultProfile.profile", "definition": "The profiles to add by default", "min": 1, "max": "*", "type": [{"code": "canonical", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/StructureDefinition"]}], "base": {"path": "Project.defaultProfile.profile", "min": 1, "max": "*"}}, {"id": "Project.exportedResourceType", "path": "Project.exportedResourceType", "definition": "The resource types exported by the project when linked", "min": 0, "max": "*", "type": [{"code": "code"}], "base": {"path": "Project.exportedResourceType", "min": 0, "max": "*"}, "binding": {"strength": "required", "valueSet": "http://hl7.org/fhir/ValueSet/resource-types"}}]}}}, {"fullUrl": "https://medplum.com/fhir/StructureDefinition/ClientApplication", "resource": {"resourceType": "StructureDefinition", "id": "ClientApplication", "name": "ClientApplication", "url": "https://medplum.com/fhir/StructureDefinition/ClientApplication", "status": "active", "description": "Medplum client application for automated access.", "kind": "resource", "abstract": false, "type": "ClientApplication", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/DomainResource", "snapshot": {"element": [{"id": "ClientApplication", "path": "ClientApplication", "short": "Medplum client application for automated access.", "definition": "Medplum client application for automated access.", "min": 0, "max": "*", "base": {"path": "ClientApplication", "min": 0, "max": "*"}}, {"id": "ClientApplication.id", "path": "ClientApplication.id", "short": "Logical id of this artifact", "definition": "The logical id of the resource, as used in the URL for the resource. Once assigned, this value never changes.", "min": 0, "max": "1", "base": {"path": "Resource.id", "min": 0, "max": "1"}, "type": [{"code": "string"}]}, {"id": "ClientApplication.meta", "path": "ClientApplication.meta", "definition": "The metadata about the resource. This is content that is maintained by the infrastructure. Changes to the content might not always be associated with version changes to the resource.", "min": 0, "max": "1", "base": {"path": "Resource.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}]}, {"id": "ClientApplication.implicitRules", "path": "ClientApplication.implicitRules", "definition": "A reference to a set of rules that were followed when the resource was constructed, and which must be understood when processing the content. Often, this is a reference to an implementation guide that defines the special rules along with other profiles etc.", "min": 0, "max": "1", "base": {"path": "Resource.implicitRules", "min": 0, "max": "1"}, "type": [{"code": "uri"}]}, {"id": "ClientApplication.language", "path": "ClientApplication.language", "definition": "The base language in which the resource is written.", "min": 0, "max": "1", "base": {"path": "Resource.language", "min": 0, "max": "1"}, "type": [{"code": "code"}]}, {"id": "ClientApplication.text", "path": "ClientApplication.text", "short": "Text summary of the resource, for human interpretation", "definition": "A human-readable narrative that contains a summary of the resource and can be used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative. Resource definitions may define what content should be represented in the narrative to ensure clinical safety.", "min": 0, "max": "1", "base": {"path": "DomainResource.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}]}, {"id": "ClientApplication.contained", "path": "ClientApplication.contained", "short": "Contained, inline Resources", "definition": "These resources do not have an independent existence apart from the resource that contains them - they cannot be identified independently, and nor can they have their own independent transaction scope.", "min": 0, "max": "*", "base": {"path": "DomainResource.contained", "min": 0, "max": "*"}, "type": [{"code": "Resource"}]}, {"id": "ClientApplication.extension", "path": "ClientApplication.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the resource. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "min": 0, "max": "*", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "ClientApplication.modifierExtension", "path": "ClientApplication.modifierExtension", "short": "Extensions that cannot be ignored", "definition": "May be used to represent additional information that is not part of the basic definition of the resource and that modifies the understanding of the element that contains it and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer is allowed to define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "min": 0, "max": "*", "base": {"path": "DomainResource.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "ClientApplication.status", "path": "ClientApplication.status", "short": "active | error | off", "definition": "The client application status. The status is active by default. The status can be set to error to indicate that the client application is not working properly. The status can be set to off to indicate that the client application is no longer in use.", "min": 0, "max": "1", "base": {"path": "ClientApplication.status", "min": 1, "max": "1"}, "type": [{"code": "code"}], "binding": {"strength": "required", "valueSet": "https://medplum.com/fhir/CodeSystem/client-application-status"}}, {"id": "ClientApplication.name", "path": "ClientApplication.name", "definition": "A name associated with the ClientApplication.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "ClientApplication.name", "min": 0, "max": "1"}}, {"id": "ClientApplication.description", "path": "ClientApplication.description", "definition": "A summary, characterization or explanation of the ClientApplication.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "ClientApplication.description", "min": 0, "max": "1"}}, {"id": "ClientApplication.signInForm", "path": "ClientApplication.signInForm", "definition": "Custom values for the Log In form.", "min": 0, "max": "1", "type": [{"code": "BackboneElement"}], "base": {"path": "ClientApplication.signInForm", "min": 0, "max": "1"}}, {"id": "ClientApplication.signInForm.welcomeString", "path": "ClientApplication.signInForm.welcomeString", "definition": "Welcome string for the Log In Form.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "ClientApplication.signInForm.welcomeString", "min": 0, "max": "1"}}, {"id": "ClientApplication.signInForm.logo", "path": "ClientApplication.signInForm.logo", "definition": "Logo for the Log In Form.", "min": 0, "max": "1", "type": [{"code": "Attachment"}], "base": {"path": "ClientApplication.signInForm.logo", "min": 0, "max": "1"}}, {"id": "ClientApplication.secret", "path": "ClientApplication.secret", "definition": "Client secret string used to verify the identity of a client.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "ClientApplication.secret", "min": 0, "max": "1"}}, {"id": "ClientApplication.retiringSecret", "path": "ClientApplication.retiringSecret", "definition": "Old version of the client secret that is being rotated out.  Instances of the client using this value should update to use the value in ClientApplication.secret", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "ClientApplication.retiringSecret", "min": 0, "max": "1"}}, {"id": "ClientApplication.jwksUri", "path": "ClientApplication.jwksUri", "definition": "Optional JWKS URI for public key verification of JWTs issued by the authorization server (client_secret_jwt).", "min": 0, "max": "1", "type": [{"code": "uri"}], "base": {"path": "ClientApplication.jwksUri", "min": 0, "max": "1"}}, {"id": "ClientApplication.redirectUri", "path": "ClientApplication.redirectUri", "definition": "Optional redirect URI used when redirecting a client back to the client application.", "min": 0, "max": "1", "type": [{"code": "uri"}], "base": {"path": "ClientApplication.redirectUri", "min": 0, "max": "1"}}, {"id": "ClientApplication.launchUri", "path": "ClientApplication.launchUri", "definition": "Optional launch URI for SMART EHR launch sequence.", "min": 0, "max": "1", "type": [{"code": "uri"}], "base": {"path": "ClientApplication.launchUri", "min": 0, "max": "1"}}, {"id": "ClientApplication.pkceOptional", "path": "ClientApplication.pkceOptional", "definition": "Flag to make PKCE optional for this client application. PKCE is required by default for compliance with Smart App Launch. It can be disabled for compatibility with legacy client applications.", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "ClientApplication.pkceOptional", "min": 0, "max": "1"}}, {"id": "ClientApplication.identityProvider", "path": "ClientApplication.identityProvider", "definition": "Optional external Identity Provider (IdP) for the client application.", "min": 0, "max": "1", "type": [{"code": "IdentityProvider"}], "base": {"path": "ClientApplication.identityProvider", "min": 0, "max": "1"}}, {"id": "ClientApplication.accessTokenLifetime", "path": "ClientApplication.accessTokenLifetime", "definition": "Optional configuration to set the access token duration", "min": 0, "max": "1", "type": [{"code": "string"}], "constraint": [{"key": "clapp-1", "severity": "error", "human": "Token lifetime must be a valid string representing time duration (eg. 2w, 1h)", "expression": "$this.matches('^[0-9]+[smhdwy]$')"}], "base": {"path": "ClientApplication.accessTokenLifetime", "min": 0, "max": "1"}}, {"id": "ClientApplication.refreshTokenLifetime", "path": "ClientApplication.refreshTokenLifetime", "definition": "Optional configuration to set the refresh token duration", "min": 0, "max": "1", "type": [{"code": "string"}], "constraint": [{"key": "clapp-1", "severity": "error", "human": "Token lifetime must be a valid string representing time duration (eg. 2w, 1h)", "expression": "$this.matches('^[0-9]+[smhdwy]$')"}], "base": {"path": "ClientApplication.refreshTokenLifetime", "min": 0, "max": "1"}}, {"id": "ClientApplication.allowed<PERSON><PERSON>in", "path": "ClientApplication.allowed<PERSON><PERSON>in", "definition": "Optional CORS allowed origin for the client application.  By default, all origins are allowed.", "min": 0, "max": "*", "type": [{"code": "string"}], "base": {"path": "ClientApplication.allowed<PERSON><PERSON>in", "min": 0, "max": "*"}}, {"id": "ClientApplication.defaultScope", "path": "ClientApplication.defaultScope", "definition": "Optional default OAuth scope for the client application. This scope is used when the client application does not specify a scope in the authorization request.", "min": 0, "max": "*", "type": [{"code": "string"}], "base": {"path": "ClientApplication.defaultScope", "min": 0, "max": "*"}}]}}}, {"fullUrl": "https://medplum.com/fhir/StructureDefinition/User", "resource": {"resourceType": "StructureDefinition", "id": "User", "name": "User", "url": "https://medplum.com/fhir/StructureDefinition/User", "status": "active", "kind": "resource", "abstract": false, "type": "User", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/DomainResource", "description": "Representation of a human user of the system.", "snapshot": {"element": [{"id": "User", "path": "User", "short": "Representation of a human user of the system", "definition": "Representation of a human user of the system.", "min": 0, "max": "*", "base": {"path": "User", "min": 0, "max": "*"}}, {"id": "User.id", "path": "User.id", "short": "Logical id of this artifact", "definition": "The logical id of the resource, as used in the URL for the resource. Once assigned, this value never changes.", "min": 0, "max": "1", "base": {"path": "Resource.id", "min": 0, "max": "1"}, "type": [{"code": "string"}]}, {"id": "User.meta", "path": "User.meta", "definition": "The metadata about the resource. This is content that is maintained by the infrastructure. Changes to the content might not always be associated with version changes to the resource.", "min": 0, "max": "1", "base": {"path": "Resource.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}]}, {"id": "User.implicitRules", "path": "User.implicitRules", "definition": "A reference to a set of rules that were followed when the resource was constructed, and which must be understood when processing the content. Often, this is a reference to an implementation guide that defines the special rules along with other profiles etc.", "min": 0, "max": "1", "base": {"path": "Resource.implicitRules", "min": 0, "max": "1"}, "type": [{"code": "uri"}]}, {"id": "User.language", "path": "User.language", "definition": "The base language in which the resource is written.", "min": 0, "max": "1", "base": {"path": "Resource.language", "min": 0, "max": "1"}, "type": [{"code": "code"}]}, {"id": "User.text", "path": "User.text", "short": "Text summary of the resource, for human interpretation", "definition": "A human-readable narrative that contains a summary of the resource and can be used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative. Resource definitions may define what content should be represented in the narrative to ensure clinical safety.", "min": 0, "max": "1", "base": {"path": "DomainResource.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}]}, {"id": "User.contained", "path": "User.contained", "short": "Contained, inline Resources", "definition": "These resources do not have an independent existence apart from the resource that contains them - they cannot be identified independently, and nor can they have their own independent transaction scope.", "min": 0, "max": "*", "base": {"path": "DomainResource.contained", "min": 0, "max": "*"}, "type": [{"code": "Resource"}]}, {"id": "User.extension", "path": "User.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the resource. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "min": 0, "max": "*", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "User.modifierExtension", "path": "User.modifierExtension", "short": "Extensions that cannot be ignored", "definition": "May be used to represent additional information that is not part of the basic definition of the resource and that modifies the understanding of the element that contains it and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer is allowed to define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "min": 0, "max": "*", "base": {"path": "DomainResource.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "User.identifier", "path": "User.identifier", "short": "An identifier for this user", "definition": "An identifier for this user.", "min": 0, "max": "*", "type": [{"code": "Identifier"}], "base": {"path": "User.identifier", "min": 0, "max": "*"}}, {"id": "User.first<PERSON><PERSON>", "path": "User.first<PERSON><PERSON>", "definition": "The first name or given name of the user. This is the value as entered when the user is created. It is used to populate the profile resource.", "min": 1, "max": "1", "type": [{"code": "string"}], "base": {"path": "User.first<PERSON><PERSON>", "min": 1, "max": "1"}}, {"id": "User.last<PERSON><PERSON>", "path": "User.last<PERSON><PERSON>", "definition": "The last name or family name of the user. This is the value as entered when the user is created. It is used to populate the profile resource.", "min": 1, "max": "1", "type": [{"code": "string"}], "base": {"path": "User.last<PERSON><PERSON>", "min": 1, "max": "1"}}, {"id": "User.externalId", "path": "User.externalId", "definition": "@deprecated Replaced by ProjectMembership.externalId.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "User.externalId", "min": 0, "max": "1"}}, {"id": "User.email", "path": "User.email", "definition": "The email address that uniquely identifies the user.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "User.email", "min": 0, "max": "1"}}, {"id": "User.emailVerified", "path": "User.emailVerified", "definition": "Whether the system has verified that the user has access to the email address.", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "User.emailVerified", "min": 0, "max": "1"}}, {"id": "User.admin", "path": "User.admin", "short": "@deprecated", "definition": "@deprecated", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "User.admin", "min": 0, "max": "1"}}, {"id": "User.passwordHash", "path": "User.passwordHash", "definition": "Encrypted hash of the user's password.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "User.passwordHash", "min": 0, "max": "1"}}, {"id": "User.mfaSecret", "path": "User.mfaSecret", "definition": "Shared secret for MFA authenticator applications.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "User.mfaSecret", "min": 0, "max": "1"}}, {"id": "User.mfaEnrolled", "path": "User.mfaEnrolled", "definition": "Whether the user has completed MFA enrollment.", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "User.mfaEnrolled", "min": 0, "max": "1"}}, {"id": "User.project", "path": "User.project", "definition": "Optional project if the user only exists for the project. This is used for the project-specific user database.", "min": 0, "max": "1", "type": [{"code": "Reference", "targetProfile": ["https://medplum.com/fhir/StructureDefinition/Project"]}], "base": {"path": "User.project", "min": 0, "max": "1"}}]}}}, {"fullUrl": "https://medplum.com/fhir/StructureDefinition/ProjectMembership", "resource": {"resourceType": "StructureDefinition", "id": "ProjectMembership", "name": "ProjectMembership", "url": "https://medplum.com/fhir/StructureDefinition/ProjectMembership", "status": "active", "kind": "resource", "abstract": false, "type": "ProjectMembership", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/DomainResource", "description": "Medplum project membership. A project membership grants a user access to a project.", "snapshot": {"element": [{"id": "ProjectMembership", "path": "ProjectMembership", "short": "Medplum project membership. A project membership grants a user access to a project.", "definition": "Medplum project membership. A project membership grants a user access to a project.", "min": 0, "max": "*", "base": {"path": "ProjectMembership", "min": 0, "max": "*"}}, {"id": "ProjectMembership.id", "path": "ProjectMembership.id", "short": "Logical id of this artifact", "definition": "The logical id of the resource, as used in the URL for the resource. Once assigned, this value never changes.", "min": 0, "max": "1", "base": {"path": "Resource.id", "min": 0, "max": "1"}, "type": [{"code": "string"}]}, {"id": "ProjectMembership.meta", "path": "ProjectMembership.meta", "definition": "The metadata about the resource. This is content that is maintained by the infrastructure. Changes to the content might not always be associated with version changes to the resource.", "min": 0, "max": "1", "base": {"path": "Resource.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}]}, {"id": "ProjectMembership.implicitRules", "path": "ProjectMembership.implicitRules", "definition": "A reference to a set of rules that were followed when the resource was constructed, and which must be understood when processing the content. Often, this is a reference to an implementation guide that defines the special rules along with other profiles etc.", "min": 0, "max": "1", "base": {"path": "Resource.implicitRules", "min": 0, "max": "1"}, "type": [{"code": "uri"}]}, {"id": "ProjectMembership.language", "path": "ProjectMembership.language", "definition": "The base language in which the resource is written.", "min": 0, "max": "1", "base": {"path": "Resource.language", "min": 0, "max": "1"}, "type": [{"code": "code"}]}, {"id": "ProjectMembership.text", "path": "ProjectMembership.text", "short": "Text summary of the resource, for human interpretation", "definition": "A human-readable narrative that contains a summary of the resource and can be used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative. Resource definitions may define what content should be represented in the narrative to ensure clinical safety.", "min": 0, "max": "1", "base": {"path": "DomainResource.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}]}, {"id": "ProjectMembership.contained", "path": "ProjectMembership.contained", "short": "Contained, inline Resources", "definition": "These resources do not have an independent existence apart from the resource that contains them - they cannot be identified independently, and nor can they have their own independent transaction scope.", "min": 0, "max": "*", "base": {"path": "DomainResource.contained", "min": 0, "max": "*"}, "type": [{"code": "Resource"}]}, {"id": "ProjectMembership.extension", "path": "ProjectMembership.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the resource. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "min": 0, "max": "*", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "ProjectMembership.modifierExtension", "path": "ProjectMembership.modifierExtension", "short": "Extensions that cannot be ignored", "definition": "May be used to represent additional information that is not part of the basic definition of the resource and that modifies the understanding of the element that contains it and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer is allowed to define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "min": 0, "max": "*", "base": {"path": "DomainResource.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "ProjectMembership.identifier", "path": "ProjectMembership.identifier", "short": "An identifier for this ProjectMembership", "definition": "An identifier for this ProjectMembership.", "min": 0, "max": "*", "type": [{"code": "Identifier"}], "base": {"path": "ProjectMembership.identifier", "min": 0, "max": "*"}}, {"id": "ProjectMembership.active", "path": "ProjectMembership.active", "short": "Whether this patient's record is in active use", "definition": "Whether this project membership record is in active use.", "min": 0, "max": "1", "base": {"path": "ProjectMembership.active", "min": 0, "max": "1"}, "type": [{"code": "boolean"}], "meaningWhenMissing": "This resource is generally assumed to be active if no value is provided for the active element"}, {"id": "ProjectMembership.project", "path": "ProjectMembership.project", "definition": "Project where the memberships are available.", "min": 1, "max": "1", "type": [{"code": "Reference", "targetProfile": ["https://medplum.com/fhir/StructureDefinition/Project"]}], "base": {"path": "ProjectMembership.project", "min": 1, "max": "1"}}, {"id": "ProjectMembership.invitedBy", "path": "ProjectMembership.invitedBy", "definition": "The project administrator who invited the user to the project.", "min": 0, "max": "1", "type": [{"code": "Reference", "targetProfile": ["https://medplum.com/fhir/StructureDefinition/User"]}], "base": {"path": "ProjectMembership.invitedBy", "min": 0, "max": "1"}}, {"id": "ProjectMembership.user", "path": "ProjectMembership.user", "definition": "User that is granted access to the project.", "min": 1, "max": "1", "type": [{"code": "Reference", "targetProfile": ["https://medplum.com/fhir/StructureDefinition/Bot", "https://medplum.com/fhir/StructureDefinition/ClientApplication", "https://medplum.com/fhir/StructureDefinition/User"]}], "base": {"path": "ProjectMembership.user", "min": 1, "max": "1"}}, {"id": "ProjectMembership.profile", "path": "ProjectMembership.profile", "definition": "Reference to the resource that represents the user profile within the project.", "min": 1, "max": "1", "type": [{"code": "Reference", "targetProfile": ["https://medplum.com/fhir/StructureDefinition/Bot", "https://medplum.com/fhir/StructureDefinition/ClientApplication", "http://hl7.org/fhir/StructureDefinition/Patient", "http://hl7.org/fhir/StructureDefinition/Practitioner", "http://hl7.org/fhir/StructureDefinition/RelatedPerson"]}], "base": {"path": "ProjectMembership.profile", "min": 1, "max": "1"}}, {"id": "ProjectMembership.userName", "path": "ProjectMembership.userName", "definition": "SCIM userName. A service provider's unique identifier for the user, typically used by the user to directly authenticate to the service provider. Often displayed to the user as their unique identifier within the system (as opposed to \"id\" or \"externalId\", which are generally opaque and not user-friendly identifiers).  Each User MUST include a non-empty userName value.  This identifier MUST be unique across the service provider's entire set of Users.  This attribute is REQUIRED and is case insensitive.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "ProjectMembership.userName", "min": 0, "max": "1"}}, {"id": "ProjectMembership.externalId", "path": "ProjectMembership.externalId", "definition": "SCIM externalId. A String that is an identifier for the resource as defined by the provisioning client.  The \"externalId\" may simplify identification of a resource between the provisioning client and the service provider by allowing the client to use a filter to locate the resource with an identifier from the provisioning domain, obviating the need to store a local mapping between the provisioning domain's identifier of the resource and the identifier used by the service provider.  Each resource MAY include a non-empty \"externalId\" value.  The value of the \"externalId\" attribute is always issued by the provisioning client and MUST NOT be specified by the service provider.  The service provider MUST always interpret the externalId as scoped to the provisioning domain.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "ProjectMembership.externalId", "min": 0, "max": "1"}}, {"id": "ProjectMembership.accessPolicy", "path": "ProjectMembership.accessPolicy", "definition": "The access policy for the user within the project memebership.", "min": 0, "max": "1", "type": [{"code": "Reference", "targetProfile": ["https://medplum.com/fhir/StructureDefinition/AccessPolicy"]}], "base": {"path": "ProjectMembership.accessPolicy", "min": 0, "max": "1"}}, {"id": "ProjectMembership.access", "path": "ProjectMembership.access", "definition": "Extended access configuration using parameterized access policies.", "min": 0, "max": "*", "type": [{"code": "BackboneElement"}], "base": {"path": "ProjectMembership.access", "min": 0, "max": "*"}}, {"id": "ProjectMembership.access.policy", "path": "ProjectMembership.access.policy", "definition": "The base access policy used as a template.  Variables in the template access policy are replaced by the values in the parameter.", "min": 1, "max": "1", "type": [{"code": "Reference", "targetProfile": ["https://medplum.com/fhir/StructureDefinition/AccessPolicy"]}], "base": {"path": "ProjectMembership.access.policy", "min": 1, "max": "1"}}, {"id": "ProjectMembership.access.parameter", "path": "ProjectMembership.access.parameter", "definition": "User options that control the display of the application.", "min": 0, "max": "*", "type": [{"code": "BackboneElement"}], "base": {"path": "ProjectMembership.access.parameter", "min": 0, "max": "*"}}, {"id": "ProjectMembership.access.parameter.name", "path": "ProjectMembership.access.parameter.name", "definition": "The unique name of the parameter.", "min": 1, "max": "1", "type": [{"code": "code"}], "base": {"path": "ProjectMembership.access.parameter.name", "min": 1, "max": "1"}}, {"id": "ProjectMembership.access.parameter.value[x]", "path": "ProjectMembership.access.parameter.value[x]", "short": "Value of the parameter.", "definition": "Value of the parameter - must be one of a constrained set of the data types (see [Extensibility](extensibility.html) for a list).", "min": 1, "max": "1", "type": [{"code": "string"}, {"code": "Reference"}], "base": {"path": "ProjectMembership.access.parameter.value[x]", "min": 1, "max": "1"}}, {"id": "ProjectMembership.userConfiguration", "path": "ProjectMembership.userConfiguration", "definition": "The user configuration for the user within the project memebership such as menu links, saved searches, and features.", "min": 0, "max": "1", "type": [{"code": "Reference", "targetProfile": ["https://medplum.com/fhir/StructureDefinition/UserConfiguration"]}], "base": {"path": "ProjectMembership.userConfiguration", "min": 0, "max": "1"}}, {"id": "ProjectMembership.admin", "path": "ProjectMembership.admin", "short": "Whether this user is a project administrator.", "definition": "Whether this user is a project administrator.", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "ProjectMembership.admin", "min": 0, "max": "1"}}]}}}, {"fullUrl": "https://medplum.com/fhir/StructureDefinition/Bot", "resource": {"resourceType": "StructureDefinition", "id": "Bot", "name": "Bot", "url": "https://medplum.com/fhir/StructureDefinition/Bot", "status": "active", "kind": "resource", "abstract": false, "type": "Bot", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/DomainResource", "description": "Bot account for automated actions.", "snapshot": {"element": [{"id": "Bot", "path": "Bot", "short": "Bot account for automated actions.", "definition": "Bot account for automated actions.", "min": 0, "max": "*", "base": {"path": "Bot", "min": 0, "max": "*"}}, {"id": "Bot.id", "path": "Bot.id", "short": "Logical id of this artifact", "definition": "The logical id of the resource, as used in the URL for the resource. Once assigned, this value never changes.", "min": 0, "max": "1", "base": {"path": "Resource.id", "min": 0, "max": "1"}, "type": [{"code": "string"}]}, {"id": "Bot.meta", "path": "Bot.meta", "definition": "The metadata about the resource. This is content that is maintained by the infrastructure. Changes to the content might not always be associated with version changes to the resource.", "min": 0, "max": "1", "base": {"path": "Resource.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}]}, {"id": "Bot.implicitRules", "path": "Bot.implicitRules", "definition": "A reference to a set of rules that were followed when the resource was constructed, and which must be understood when processing the content. Often, this is a reference to an implementation guide that defines the special rules along with other profiles etc.", "min": 0, "max": "1", "base": {"path": "Resource.implicitRules", "min": 0, "max": "1"}, "type": [{"code": "uri"}]}, {"id": "Bot.language", "path": "Bot.language", "definition": "The base language in which the resource is written.", "min": 0, "max": "1", "base": {"path": "Resource.language", "min": 0, "max": "1"}, "type": [{"code": "code"}]}, {"id": "Bot.text", "path": "Bot.text", "short": "Text summary of the resource, for human interpretation", "definition": "A human-readable narrative that contains a summary of the resource and can be used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative. Resource definitions may define what content should be represented in the narrative to ensure clinical safety.", "min": 0, "max": "1", "base": {"path": "DomainResource.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}]}, {"id": "Bot.contained", "path": "Bot.contained", "short": "Contained, inline Resources", "definition": "These resources do not have an independent existence apart from the resource that contains them - they cannot be identified independently, and nor can they have their own independent transaction scope.", "min": 0, "max": "*", "base": {"path": "DomainResource.contained", "min": 0, "max": "*"}, "type": [{"code": "Resource"}]}, {"id": "Bot.extension", "path": "Bot.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the resource. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "min": 0, "max": "*", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "Bot.modifierExtension", "path": "Bot.modifierExtension", "short": "Extensions that cannot be ignored", "definition": "May be used to represent additional information that is not part of the basic definition of the resource and that modifies the understanding of the element that contains it and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer is allowed to define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "min": 0, "max": "*", "base": {"path": "DomainResource.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "Bot.identifier", "path": "Bot.identifier", "short": "An identifier for this bot", "definition": "An identifier for this bot.", "min": 0, "max": "*", "type": [{"code": "Identifier"}], "base": {"path": "Bot.identifier", "min": 0, "max": "*"}}, {"id": "Bot.name", "path": "Bot.name", "definition": "A name associated with the Bot.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "Bot.name", "min": 0, "max": "1"}}, {"id": "Bot.description", "path": "Bot.description", "definition": "A summary, characterization or explanation of the Bot.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "Bot.description", "min": 0, "max": "1"}}, {"id": "Bot.runtimeVersion", "path": "Bot.runtimeVersion", "definition": "The identifier of the bot runtime environment (i.e., vmcontext, awslambda, etc).", "min": 0, "max": "1", "type": [{"code": "code"}], "base": {"path": "Bot.runtimeVersion", "min": 0, "max": "1"}, "binding": {"strength": "required", "valueSet": "https://medplum.com/fhir/ValueSet/bot-runtime-version|4.0.1"}}, {"id": "Bot.timeout", "path": "Bot.timeout", "definition": "The maximum allowed execution time of the bot in seconds.", "min": 0, "max": "1", "type": [{"code": "integer"}], "base": {"path": "Bot.timeout", "min": 0, "max": "1"}}, {"id": "Bot.photo", "path": "Bot.photo", "definition": "Image of the bot.", "min": 0, "max": "1", "type": [{"code": "Attachment"}], "base": {"path": "Bot.photo", "min": 0, "max": "1"}}, {"id": "Bot.cron[x]", "path": "Bot.cron[x]", "definition": "A schedule for the bot to be executed.", "min": 0, "max": "1", "type": [{"code": "Timing"}, {"code": "string"}], "base": {"path": "Bot.cron[x]", "min": 0, "max": "1"}}, {"id": "Bot.category", "path": "Bot.category", "short": "Classification of service", "definition": "A code that classifies the service for searching, sorting and display purposes (e.g. \"Surgical Procedure\").", "comment": "There may be multiple axis of categorization depending on the context or use case for retrieving or displaying the resource.  The level of granularity is defined by the category concepts in the value set.", "min": 0, "max": "*", "type": [{"code": "CodeableConcept"}], "base": {"path": "Bot.category", "min": 0, "max": "*"}}, {"id": "Bot.system", "path": "Bot.system", "definition": "Optional flag to indicate that the bot is a system bot and therefore has access to system secrets.", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "Bot.system", "min": 0, "max": "1"}}, {"id": "<PERSON><PERSON>.runAs<PERSON>ser", "path": "<PERSON><PERSON>.runAs<PERSON>ser", "definition": "Optional flag to indicate that the bot should be run as the user.", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "<PERSON><PERSON>.runAs<PERSON>ser", "min": 0, "max": "1"}}, {"id": "Bot.auditEventTrigger", "path": "Bot.auditEventTrigger", "definition": "Criteria for creating an AuditEvent as a result of the bot invocation. Possible values are 'always', 'never', 'on-error', or 'on-output'. Default value is 'always'.", "min": 0, "max": "1", "base": {"path": "Bot.auditEventTrigger", "min": 0, "max": "1"}, "type": [{"code": "code"}], "binding": {"strength": "required", "valueSet": "https://medplum.com/fhir/ValueSet/bot-audit-event-trigger|4.0.1"}}, {"id": "Bot.auditEventDestination", "path": "Bot.auditEventDestination", "definition": "The destination system in which the AuditEvent is to be sent. Possible values are 'log' or 'resource'. Default value is 'resource'.", "min": 0, "max": "*", "base": {"path": "Bot.auditEventDestination", "min": 0, "max": "*"}, "type": [{"code": "code"}], "binding": {"strength": "required", "valueSet": "https://medplum.com/fhir/ValueSet/bot-audit-event-destination|4.0.1"}}, {"id": "Bot.sourceCode", "path": "Bot.sourceCode", "definition": "Bot logic in original source code form written by developers.", "min": 0, "max": "1", "base": {"path": "Bot.sourceCode", "min": 0, "max": "1"}, "type": [{"code": "Attachment"}]}, {"id": "Bot.executableCode", "path": "Bot.executableCode", "definition": "Bot logic in executable form as a result of compiling and bundling source code.", "min": 0, "max": "1", "base": {"path": "Bot.executableCode", "min": 0, "max": "1"}, "type": [{"code": "Attachment"}]}, {"id": "Bot.code", "path": "Bot.code", "definition": "@deprecated Bo<PERSON> logic script. Use Bot.sourceCode or Bot.executableCode instead.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "Bot.code", "min": 0, "max": "1"}}]}}}, {"fullUrl": "https://medplum.com/fhir/StructureDefinition/Login", "resource": {"resourceType": "StructureDefinition", "id": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "url": "https://medplum.com/fhir/StructureDefinition/Login", "status": "active", "kind": "resource", "abstract": false, "type": "<PERSON><PERSON>", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/DomainResource", "description": "Login event and session details.", "snapshot": {"element": [{"id": "<PERSON><PERSON>", "path": "<PERSON><PERSON>", "short": "Login event and session details.", "definition": "Login event and session details.", "min": 0, "max": "*", "base": {"path": "<PERSON><PERSON>", "min": 0, "max": "*"}}, {"id": "Login.id", "path": "Login.id", "short": "Logical id of this artifact", "definition": "The logical id of the resource, as used in the URL for the resource. Once assigned, this value never changes.", "min": 0, "max": "1", "base": {"path": "Resource.id", "min": 0, "max": "1"}, "type": [{"code": "string"}]}, {"id": "Login.meta", "path": "Login.meta", "definition": "The metadata about the resource. This is content that is maintained by the infrastructure. Changes to the content might not always be associated with version changes to the resource.", "min": 0, "max": "1", "base": {"path": "Resource.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}]}, {"id": "Login.implicitRules", "path": "Login.implicitRules", "definition": "A reference to a set of rules that were followed when the resource was constructed, and which must be understood when processing the content. Often, this is a reference to an implementation guide that defines the special rules along with other profiles etc.", "min": 0, "max": "1", "base": {"path": "Resource.implicitRules", "min": 0, "max": "1"}, "type": [{"code": "uri"}]}, {"id": "Login.language", "path": "Login.language", "definition": "The base language in which the resource is written.", "min": 0, "max": "1", "base": {"path": "Resource.language", "min": 0, "max": "1"}, "type": [{"code": "code"}]}, {"id": "Login.text", "path": "Login.text", "short": "Text summary of the resource, for human interpretation", "definition": "A human-readable narrative that contains a summary of the resource and can be used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative. Resource definitions may define what content should be represented in the narrative to ensure clinical safety.", "min": 0, "max": "1", "base": {"path": "DomainResource.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}]}, {"id": "Login.contained", "path": "Login.contained", "short": "Contained, inline Resources", "definition": "These resources do not have an independent existence apart from the resource that contains them - they cannot be identified independently, and nor can they have their own independent transaction scope.", "min": 0, "max": "*", "base": {"path": "DomainResource.contained", "min": 0, "max": "*"}, "type": [{"code": "Resource"}]}, {"id": "Login.extension", "path": "Login.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the resource. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "min": 0, "max": "*", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "Login.modifierExtension", "path": "Login.modifierExtension", "short": "Extensions that cannot be ignored", "definition": "May be used to represent additional information that is not part of the basic definition of the resource and that modifies the understanding of the element that contains it and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer is allowed to define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "min": 0, "max": "*", "base": {"path": "DomainResource.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "Login.client", "path": "Login.client", "definition": "The client requesting the code.", "min": 0, "max": "1", "type": [{"code": "Reference", "targetProfile": ["https://medplum.com/fhir/StructureDefinition/ClientApplication"]}], "base": {"path": "Login.client", "min": 0, "max": "1"}}, {"id": "Login.profileType", "path": "Login.profileType", "definition": "Optional required profile resource type.", "min": 0, "max": "1", "type": [{"code": "code"}], "binding": {"strength": "required", "valueSet": "http://hl7.org/fhir/ValueSet/resource-types|4.0.1"}, "base": {"path": "Login.profileType", "min": 0, "max": "1"}}, {"id": "Login.project", "path": "Login.project", "definition": "Optional required project for the login.", "min": 0, "max": "1", "type": [{"code": "Reference", "targetProfile": ["https://medplum.com/fhir/StructureDefinition/Project"]}], "base": {"path": "Login.project", "min": 0, "max": "1"}}, {"id": "Login.user", "path": "Login.user", "definition": "The user requesting the code.", "min": 1, "max": "1", "type": [{"code": "Reference", "targetProfile": ["https://medplum.com/fhir/StructureDefinition/Bot", "https://medplum.com/fhir/StructureDefinition/ClientApplication", "https://medplum.com/fhir/StructureDefinition/User"]}], "base": {"path": "Login.user", "min": 1, "max": "1"}}, {"id": "Login.membership", "path": "Login.membership", "definition": "Reference to the project membership which includes FHIR identity (patient, practitioner, etc), access policy, and user configuration.", "min": 0, "max": "1", "type": [{"code": "Reference", "targetProfile": ["https://medplum.com/fhir/StructureDefinition/ProjectMembership"]}], "base": {"path": "Login.membership", "min": 0, "max": "1"}}, {"id": "Login.scope", "path": "Login.scope", "definition": "OAuth scope or scopes.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "Login.scope", "min": 0, "max": "1"}}, {"id": "Login.authMethod", "path": "Login.authMethod", "definition": "The authentication method used to obtain the code (password or google).", "min": 1, "max": "1", "type": [{"code": "code"}], "base": {"path": "Login.authMethod", "min": 1, "max": "1"}, "binding": {"strength": "required", "valueSet": "https://medplum.com/fhir/ValueSet/login-auth-method|4.0.1"}}, {"id": "Login.authTime", "path": "Login.authTime", "definition": "Time when the End-User authentication occurred.", "min": 1, "max": "1", "type": [{"code": "instant"}], "base": {"path": "Login.authTime", "min": 1, "max": "1"}}, {"id": "Login.cookie", "path": "Login.cookie", "definition": "The cookie value that can be used for session management.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "Login.cookie", "min": 0, "max": "1"}}, {"id": "Login.code", "path": "Login.code", "definition": "The authorization code generated by the authorization server.  The authorization code MUST expire shortly after it is issued to mitigate the risk of leaks.  A maximum authorization code lifetime of 10 minutes is RECOMMENDED.  The client MUST NOT use the authorization code more than once.  If an authorization code is used more than once, the authorization server MUST deny the request and SHOULD revoke (when possible) all tokens previously issued based on that authorization code.  The authorization code is bound to the client identifier and redirection URI.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "Login.code", "min": 0, "max": "1"}}, {"id": "Login.codeChallenge", "path": "Login.codeChallenge", "definition": "PKCE code challenge presented in the authorization request.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "Login.codeChallenge", "min": 0, "max": "1"}}, {"id": "Login.codeChallengeMethod", "path": "Login.codeChallengeMethod", "definition": "OPTIONAL, defaults to \"plain\" if not present in the request.  Code verifier transformation method is \"S256\" or \"plain\".", "min": 0, "max": "1", "type": [{"code": "code"}], "base": {"path": "Login.codeChallengeMethod", "min": 0, "max": "1"}, "binding": {"strength": "required", "valueSet": "https://medplum.com/fhir/ValueSet/login-code-challenge-method|4.0.1"}}, {"id": "Login.refreshSecret", "path": "Login.refreshSecret", "definition": "Optional secure random string that can be used in an OAuth refresh token.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "Login.refreshSecret", "min": 0, "max": "1"}}, {"id": "Login.nonce", "path": "Login.nonce", "definition": "Optional cryptographically random string that your app adds to the initial request and the authorization server includes inside the ID Token, used to prevent token replay attacks.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "Login.nonce", "min": 0, "max": "1"}}, {"id": "Login.mfaVerified", "path": "Login.mfaVerified", "definition": "Whether the user has verified using multi-factor authentication (MFA). This will only be set is the user has MFA enabled (see User.mfaEnrolled).", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "Login.mfaVerified", "min": 0, "max": "1"}}, {"id": "Login.granted", "path": "Login.granted", "definition": "Whether a token has been granted for this login.", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "Login.granted", "min": 0, "max": "1"}}, {"id": "Login.revoked", "path": "Login.revoked", "definition": "Whether this login has been revoked or invalidated.", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "Login.revoked", "min": 0, "max": "1"}}, {"id": "Login.admin", "path": "Login.admin", "definition": "@deprecated", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "Login.admin", "min": 0, "max": "1"}}, {"id": "Login.superAdmin", "path": "Login.superAdmin", "definition": "@deprecated", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "Login.superAdmin", "min": 0, "max": "1"}}, {"id": "Login.launch", "path": "Login.launch", "definition": "Optional SMART App Launch context for this login.", "min": 0, "max": "1", "type": [{"code": "Reference", "targetProfile": ["https://medplum.com/fhir/StructureDefinition/SmartAppLaunch"]}], "base": {"path": "Login.launch", "min": 0, "max": "1"}}, {"id": "Login.remoteAddress", "path": "Login.remoteAddress", "definition": "The Internet Protocol (IP) address of the client or last proxy that sent the request.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "Login.remoteAddress", "min": 0, "max": "1"}}, {"id": "Login.userAgent", "path": "Login.userAgent", "definition": "The User-Agent request header as sent by the client.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "Login.userAgent", "min": 0, "max": "1"}}]}}}, {"fullUrl": "https://medplum.com/fhir/StructureDefinition/PasswordChangeRequest", "resource": {"resourceType": "StructureDefinition", "id": "PasswordChangeRequest", "name": "PasswordChangeRequest", "url": "https://medplum.com/fhir/StructureDefinition/PasswordChangeRequest", "status": "active", "kind": "resource", "abstract": false, "type": "PasswordChangeRequest", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/DomainResource", "description": "@deprecated Password change request for the 'forgot password' flow. Use UserSecurityRequest instead.", "snapshot": {"element": [{"id": "PasswordChangeRequest", "path": "PasswordChangeRequest", "short": "Password change request for the 'forgot password' flow.", "definition": "Password change request for the 'forgot password' flow.", "min": 0, "max": "*", "base": {"path": "PasswordChangeRequest", "min": 0, "max": "*"}}, {"id": "PasswordChangeRequest.id", "path": "PasswordChangeRequest.id", "short": "Logical id of this artifact", "definition": "The logical id of the resource, as used in the URL for the resource. Once assigned, this value never changes.", "min": 0, "max": "1", "base": {"path": "Resource.id", "min": 0, "max": "1"}, "type": [{"code": "string"}]}, {"id": "PasswordChangeRequest.meta", "path": "PasswordChangeRequest.meta", "definition": "The metadata about the resource. This is content that is maintained by the infrastructure. Changes to the content might not always be associated with version changes to the resource.", "min": 0, "max": "1", "base": {"path": "Resource.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}]}, {"id": "PasswordChangeRequest.implicitRules", "path": "PasswordChangeRequest.implicitRules", "definition": "A reference to a set of rules that were followed when the resource was constructed, and which must be understood when processing the content. Often, this is a reference to an implementation guide that defines the special rules along with other profiles etc.", "min": 0, "max": "1", "base": {"path": "Resource.implicitRules", "min": 0, "max": "1"}, "type": [{"code": "uri"}]}, {"id": "PasswordChangeRequest.language", "path": "PasswordChangeRequest.language", "definition": "The base language in which the resource is written.", "min": 0, "max": "1", "base": {"path": "Resource.language", "min": 0, "max": "1"}, "type": [{"code": "code"}]}, {"id": "PasswordChangeRequest.text", "path": "PasswordChangeRequest.text", "short": "Text summary of the resource, for human interpretation", "definition": "A human-readable narrative that contains a summary of the resource and can be used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative. Resource definitions may define what content should be represented in the narrative to ensure clinical safety.", "min": 0, "max": "1", "base": {"path": "DomainResource.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}]}, {"id": "PasswordChangeRequest.contained", "path": "PasswordChangeRequest.contained", "short": "Contained, inline Resources", "definition": "These resources do not have an independent existence apart from the resource that contains them - they cannot be identified independently, and nor can they have their own independent transaction scope.", "min": 0, "max": "*", "base": {"path": "DomainResource.contained", "min": 0, "max": "*"}, "type": [{"code": "Resource"}]}, {"id": "PasswordChangeRequest.extension", "path": "PasswordChangeRequest.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the resource. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "min": 0, "max": "*", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "PasswordChangeRequest.modifierExtension", "path": "PasswordChangeRequest.modifierExtension", "short": "Extensions that cannot be ignored", "definition": "May be used to represent additional information that is not part of the basic definition of the resource and that modifies the understanding of the element that contains it and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer is allowed to define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "min": 0, "max": "*", "base": {"path": "DomainResource.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "PasswordChangeRequest.type", "path": "PasswordChangeRequest.type", "definition": "The type of password change request (invite or reset).", "min": 0, "max": "1", "type": [{"code": "code"}], "base": {"path": "PasswordChangeRequest.type", "min": 0, "max": "1"}, "binding": {"strength": "required", "valueSet": "https://medplum.com/fhir/ValueSet/password-change-request-type|4.0.1"}}, {"id": "PasswordChangeRequest.user", "path": "PasswordChangeRequest.user", "definition": "The user requesting the password change.", "min": 1, "max": "1", "type": [{"code": "Reference", "targetProfile": ["https://medplum.com/fhir/StructureDefinition/User"]}], "base": {"path": "PasswordChangeRequest.user", "min": 1, "max": "1"}}, {"id": "PasswordChangeRequest.secret", "path": "PasswordChangeRequest.secret", "definition": "Secret string used to verify the identity of the user.", "min": 1, "max": "1", "type": [{"code": "string"}], "base": {"path": "PasswordChangeRequest.secret", "min": 1, "max": "1"}}, {"id": "PasswordChangeRequest.used", "path": "PasswordChangeRequest.used", "definition": "Whether this request has been used, and is therefore no longer valid.", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "PasswordChangeRequest.used", "min": 0, "max": "1"}}, {"id": "PasswordChangeRequest.redirectUri", "path": "PasswordChangeRequest.redirectUri", "definition": "Redirect URI used when redirecting a client back to the client application.", "min": 0, "max": "1", "type": [{"code": "uri"}], "base": {"path": "PasswordChangeRequest.redirectUri", "min": 0, "max": "1"}}]}}}, {"fullUrl": "https://medplum.com/fhir/StructureDefinition/UserSecurityRequest", "resource": {"resourceType": "StructureDefinition", "id": "UserSecurityRequest", "name": "UserSecurityRequest", "url": "https://medplum.com/fhir/StructureDefinition/UserSecurityRequest", "status": "active", "kind": "resource", "abstract": false, "type": "UserSecurityRequest", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/DomainResource", "description": "User security request for the 'forgot password' flow, email verification, etc.", "snapshot": {"element": [{"id": "UserSecurityRequest", "path": "UserSecurityRequest", "short": "User security request for the 'forgot password' flow, email verification, etc.", "definition": "User security request for the 'forgot password' flow, email verification, etc.", "min": 0, "max": "*", "base": {"path": "UserSecurityRequest", "min": 0, "max": "*"}}, {"id": "UserSecurityRequest.id", "path": "UserSecurityRequest.id", "short": "Logical id of this artifact", "definition": "The logical id of the resource, as used in the URL for the resource. Once assigned, this value never changes.", "min": 0, "max": "1", "base": {"path": "Resource.id", "min": 0, "max": "1"}, "type": [{"code": "string"}]}, {"id": "UserSecurityRequest.meta", "path": "UserSecurityRequest.meta", "definition": "The metadata about the resource. This is content that is maintained by the infrastructure. Changes to the content might not always be associated with version changes to the resource.", "min": 0, "max": "1", "base": {"path": "Resource.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}]}, {"id": "UserSecurityRequest.implicitRules", "path": "UserSecurityRequest.implicitRules", "definition": "A reference to a set of rules that were followed when the resource was constructed, and which must be understood when processing the content. Often, this is a reference to an implementation guide that defines the special rules along with other profiles etc.", "min": 0, "max": "1", "base": {"path": "Resource.implicitRules", "min": 0, "max": "1"}, "type": [{"code": "uri"}]}, {"id": "UserSecurityRequest.language", "path": "UserSecurityRequest.language", "definition": "The base language in which the resource is written.", "min": 0, "max": "1", "base": {"path": "Resource.language", "min": 0, "max": "1"}, "type": [{"code": "code"}]}, {"id": "UserSecurityRequest.text", "path": "UserSecurityRequest.text", "short": "Text summary of the resource, for human interpretation", "definition": "A human-readable narrative that contains a summary of the resource and can be used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative. Resource definitions may define what content should be represented in the narrative to ensure clinical safety.", "min": 0, "max": "1", "base": {"path": "DomainResource.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}]}, {"id": "UserSecurityRequest.contained", "path": "UserSecurityRequest.contained", "short": "Contained, inline Resources", "definition": "These resources do not have an independent existence apart from the resource that contains them - they cannot be identified independently, and nor can they have their own independent transaction scope.", "min": 0, "max": "*", "base": {"path": "DomainResource.contained", "min": 0, "max": "*"}, "type": [{"code": "Resource"}]}, {"id": "UserSecurityRequest.extension", "path": "UserSecurityRequest.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the resource. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "min": 0, "max": "*", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "UserSecurityRequest.modifierExtension", "path": "UserSecurityRequest.modifierExtension", "short": "Extensions that cannot be ignored", "definition": "May be used to represent additional information that is not part of the basic definition of the resource and that modifies the understanding of the element that contains it and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer is allowed to define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "min": 0, "max": "*", "base": {"path": "DomainResource.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "UserSecurityRequest.type", "path": "UserSecurityRequest.type", "definition": "The type of user security request.", "min": 0, "max": "1", "type": [{"code": "code"}], "base": {"path": "UserSecurityRequest.type", "min": 0, "max": "1"}, "binding": {"strength": "required", "valueSet": "https://medplum.com/fhir/ValueSet/user-security-request-type"}}, {"id": "UserSecurityRequest.user", "path": "UserSecurityRequest.user", "definition": "The user performing the security request.", "min": 1, "max": "1", "type": [{"code": "Reference", "targetProfile": ["https://medplum.com/fhir/StructureDefinition/User"]}], "base": {"path": "UserSecurityRequest.user", "min": 1, "max": "1"}}, {"id": "UserSecurityRequest.secret", "path": "UserSecurityRequest.secret", "definition": "Secret string used to verify the identity of the user.", "min": 1, "max": "1", "type": [{"code": "string"}], "base": {"path": "UserSecurityRequest.secret", "min": 1, "max": "1"}}, {"id": "UserSecurityRequest.used", "path": "UserSecurityRequest.used", "definition": "Whether this request has been used, and is therefore no longer valid.", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "UserSecurityRequest.used", "min": 0, "max": "1"}}, {"id": "UserSecurityRequest.redirectUri", "path": "UserSecurityRequest.redirectUri", "definition": "Redirect URI used when redirecting a client back to the client application.", "min": 0, "max": "1", "type": [{"code": "uri"}], "base": {"path": "UserSecurityRequest.redirectUri", "min": 0, "max": "1"}}]}}}, {"fullUrl": "https://medplum.com/fhir/StructureDefinition/JsonWebKey", "resource": {"resourceType": "StructureDefinition", "id": "JsonWebKey", "name": "JsonWebKey", "url": "https://medplum.com/fhir/StructureDefinition/JsonWebKey", "status": "active", "kind": "resource", "abstract": false, "type": "JsonWebKey", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/DomainResource", "description": "A JSON object that represents a cryptographic key. The members of the object represent properties of the key, including its value.", "snapshot": {"element": [{"id": "JsonWebKey", "path": "JsonWebKey", "short": "A JSON object that represents a cryptographic key.", "definition": "A JSON object that represents a cryptographic key. The members of the object represent properties of the key, including its value.", "min": 0, "max": "*", "base": {"path": "JsonWebKey", "min": 0, "max": "*"}}, {"id": "JsonWebKey.id", "path": "JsonWebKey.id", "short": "Logical id of this artifact", "definition": "The logical id of the resource, as used in the URL for the resource. Once assigned, this value never changes.", "min": 0, "max": "1", "base": {"path": "Resource.id", "min": 0, "max": "1"}, "type": [{"code": "string"}]}, {"id": "JsonWebKey.meta", "path": "JsonWebKey.meta", "definition": "The metadata about the resource. This is content that is maintained by the infrastructure. Changes to the content might not always be associated with version changes to the resource.", "min": 0, "max": "1", "base": {"path": "Resource.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}]}, {"id": "JsonWebKey.implicitRules", "path": "JsonWebKey.implicitRules", "definition": "A reference to a set of rules that were followed when the resource was constructed, and which must be understood when processing the content. Often, this is a reference to an implementation guide that defines the special rules along with other profiles etc.", "min": 0, "max": "1", "base": {"path": "Resource.implicitRules", "min": 0, "max": "1"}, "type": [{"code": "uri"}]}, {"id": "JsonWebKey.language", "path": "JsonWebKey.language", "definition": "The base language in which the resource is written.", "min": 0, "max": "1", "base": {"path": "Resource.language", "min": 0, "max": "1"}, "type": [{"code": "code"}]}, {"id": "JsonWebKey.text", "path": "JsonWebKey.text", "short": "Text summary of the resource, for human interpretation", "definition": "A human-readable narrative that contains a summary of the resource and can be used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative. Resource definitions may define what content should be represented in the narrative to ensure clinical safety.", "min": 0, "max": "1", "base": {"path": "DomainResource.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}]}, {"id": "JsonWebKey.contained", "path": "JsonWebKey.contained", "short": "Contained, inline Resources", "definition": "These resources do not have an independent existence apart from the resource that contains them - they cannot be identified independently, and nor can they have their own independent transaction scope.", "min": 0, "max": "*", "base": {"path": "DomainResource.contained", "min": 0, "max": "*"}, "type": [{"code": "Resource"}]}, {"id": "JsonWebKey.extension", "path": "JsonWebKey.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the resource. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "min": 0, "max": "*", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "JsonWebKey.modifierExtension", "path": "JsonWebKey.modifierExtension", "short": "Extensions that cannot be ignored", "definition": "May be used to represent additional information that is not part of the basic definition of the resource and that modifies the understanding of the element that contains it and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer is allowed to define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "min": 0, "max": "*", "base": {"path": "DomainResource.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "JsonWebKey.active", "path": "JsonWebKey.active", "definition": "Whether this key is in active use.", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "JsonWebKey.active", "min": 0, "max": "1"}}, {"id": "JsonWebKey.alg", "path": "JsonWebKey.alg", "definition": "The specific cryptographic algorithm used with the key.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "JsonWebKey.alg", "min": 0, "max": "1"}}, {"id": "JsonWebKey.kty", "path": "JsonWebKey.kty", "definition": "The family of cryptographic algorithms used with the key.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "JsonWebKey.kty", "min": 0, "max": "1"}}, {"id": "JsonWebKey.use", "path": "JsonWebKey.use", "definition": "How the key was meant to be used; sig represents the signature.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "JsonWebKey.use", "min": 0, "max": "1"}}, {"id": "JsonWebKey.key_ops", "path": "JsonWebKey.key_ops", "definition": "The operation(s) for which the key is intended to be used.", "min": 0, "max": "*", "type": [{"code": "string"}], "base": {"path": "JsonWebKey.key_ops", "min": 0, "max": "*"}}, {"id": "JsonWebKey.x5c", "path": "JsonWebKey.x5c", "definition": "The x.509 certificate chain. The first entry in the array is the certificate to use for token verification; the other certificates can be used to verify this first certificate.", "min": 0, "max": "*", "type": [{"code": "string"}], "base": {"path": "JsonWebKey.x5c", "min": 0, "max": "*"}}, {"id": "JsonWebKey.n", "path": "JsonWebKey.n", "definition": "The modulus for the RSA public key.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "JsonWebKey.n", "min": 0, "max": "1"}}, {"id": "JsonWebKey.e", "path": "JsonWebKey.e", "definition": "The exponent for the RSA public key.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "JsonWebKey.e", "min": 0, "max": "1"}}, {"id": "JsonWebKey.kid", "path": "JsonWebKey.kid", "definition": "The unique identifier for the key.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "JsonWebKey.kid", "min": 0, "max": "1"}}, {"id": "JsonWebKey.x5t", "path": "JsonWebKey.x5t", "definition": "The thumbprint of the x.509 cert (SHA-1 thumbprint).", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "JsonWebKey.x5t", "min": 0, "max": "1"}}, {"id": "JsonWebKey.d", "path": "JsonWebKey.d", "definition": "The exponent for the RSA private key.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "JsonWebKey.d", "min": 0, "max": "1"}}, {"id": "JsonWebKey.p", "path": "JsonWebKey.p", "definition": "The first prime factor.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "JsonWebKey.p", "min": 0, "max": "1"}}, {"id": "JsonWebKey.q", "path": "JsonWebKey.q", "definition": "The second prime factor.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "JsonWebKey.q", "min": 0, "max": "1"}}, {"id": "JsonWebKey.dp", "path": "JsonWebKey.dp", "definition": "The first factor CRT exponent.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "JsonWebKey.dp", "min": 0, "max": "1"}}, {"id": "JsonWebKey.dq", "path": "JsonWebKey.dq", "definition": "The second factor CRT exponent.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "JsonWebKey.dq", "min": 0, "max": "1"}}, {"id": "JsonWebKey.qi", "path": "JsonWebKey.qi", "definition": "The first CRT coefficient.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "JsonWebKey.qi", "min": 0, "max": "1"}}]}}}, {"fullUrl": "https://medplum.com/fhir/StructureDefinition/AccessPolicy", "resource": {"resourceType": "StructureDefinition", "id": "AccessPolicy", "name": "AccessPolicy", "url": "https://medplum.com/fhir/StructureDefinition/AccessPolicy", "status": "active", "kind": "resource", "abstract": false, "type": "AccessPolicy", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/DomainResource", "description": "Access Policy for user or user group that defines how entities can or cannot access resources.", "snapshot": {"element": [{"id": "AccessPolicy", "path": "AccessPolicy", "short": "Access Policy for user or user group that defines how entities can or cannot access resources.", "definition": "Access Policy for user or user group that defines how entities can or cannot access resources.", "min": 0, "max": "*", "base": {"path": "AccessPolicy", "min": 0, "max": "*"}}, {"id": "AccessPolicy.id", "path": "AccessPolicy.id", "short": "Logical id of this artifact", "definition": "The logical id of the resource, as used in the URL for the resource. Once assigned, this value never changes.", "min": 0, "max": "1", "base": {"path": "Resource.id", "min": 0, "max": "1"}, "type": [{"code": "string"}]}, {"id": "AccessPolicy.meta", "path": "AccessPolicy.meta", "definition": "The metadata about the resource. This is content that is maintained by the infrastructure. Changes to the content might not always be associated with version changes to the resource.", "min": 0, "max": "1", "base": {"path": "Resource.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}]}, {"id": "AccessPolicy.implicitRules", "path": "AccessPolicy.implicitRules", "definition": "A reference to a set of rules that were followed when the resource was constructed, and which must be understood when processing the content. Often, this is a reference to an implementation guide that defines the special rules along with other profiles etc.", "min": 0, "max": "1", "base": {"path": "Resource.implicitRules", "min": 0, "max": "1"}, "type": [{"code": "uri"}]}, {"id": "AccessPolicy.language", "path": "AccessPolicy.language", "definition": "The base language in which the resource is written.", "min": 0, "max": "1", "base": {"path": "Resource.language", "min": 0, "max": "1"}, "type": [{"code": "code"}]}, {"id": "AccessPolicy.text", "path": "AccessPolicy.text", "short": "Text summary of the resource, for human interpretation", "definition": "A human-readable narrative that contains a summary of the resource and can be used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative. Resource definitions may define what content should be represented in the narrative to ensure clinical safety.", "min": 0, "max": "1", "base": {"path": "DomainResource.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}]}, {"id": "AccessPolicy.contained", "path": "AccessPolicy.contained", "short": "Contained, inline Resources", "definition": "These resources do not have an independent existence apart from the resource that contains them - they cannot be identified independently, and nor can they have their own independent transaction scope.", "min": 0, "max": "*", "base": {"path": "DomainResource.contained", "min": 0, "max": "*"}, "type": [{"code": "Resource"}]}, {"id": "AccessPolicy.extension", "path": "AccessPolicy.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the resource. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "min": 0, "max": "*", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "AccessPolicy.modifierExtension", "path": "AccessPolicy.modifierExtension", "short": "Extensions that cannot be ignored", "definition": "May be used to represent additional information that is not part of the basic definition of the resource and that modifies the understanding of the element that contains it and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer is allowed to define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "min": 0, "max": "*", "base": {"path": "DomainResource.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "AccessPolicy.name", "path": "AccessPolicy.name", "definition": "A name associated with the AccessPolicy.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "AccessPolicy.name", "min": 0, "max": "1"}}, {"id": "AccessPolicy.basedOn", "path": "AccessPolicy.basedOn", "definition": "Other access policies used to derive this access policy.", "min": 0, "max": "*", "base": {"path": "AccessPolicy.basedOn", "min": 0, "max": "*"}, "type": [{"code": "Reference", "targetProfile": ["https://medplum.com/fhir/StructureDefinition/AccessPolicy"]}]}, {"id": "AccessPolicy.compartment", "path": "AccessPolicy.compartment", "definition": "Optional compartment for newly created resources.  If this field is set, any resources created by a user with this access policy will automatically be included in the specified compartment.", "min": 0, "max": "1", "type": [{"code": "Reference"}], "base": {"path": "AccessPolicy.compartment", "min": 0, "max": "1"}}, {"id": "AccessPolicy.resource", "path": "AccessPolicy.resource", "definition": "Access details for a resource type.", "min": 0, "max": "*", "type": [{"code": "BackboneElement"}], "base": {"path": "AccessPolicy.resource", "min": 0, "max": "*"}, "constraint": [{"key": "axp-3", "severity": "error", "human": "Criteria must be a valid FHIR search string on the correct resource type, e.g. Patient?identifier=123", "expression": "criteria.exists() implies criteria.startsWith(%context.resourceType & '?')"}]}, {"id": "AccessPolicy.resource.resourceType", "path": "AccessPolicy.resource.resourceType", "definition": "The resource type.", "min": 1, "max": "1", "type": [{"code": "string"}], "base": {"path": "AccessPolicy.resource.resourceType", "min": 1, "max": "1"}}, {"id": "AccessPolicy.resource.compartment", "path": "AccessPolicy.resource.compartment", "definition": "@deprecated Optional compartment restriction for the resource type.", "min": 0, "max": "1", "type": [{"code": "Reference"}], "base": {"path": "AccessPolicy.resource.compartment", "min": 0, "max": "1"}}, {"id": "AccessPolicy.resource.criteria", "path": "AccessPolicy.resource.criteria", "definition": "The rules that the server should use to determine which resources to allow.", "comment": "The rules are search criteria (without the [base] part). Like Bundle.entry.request.url, it has no leading \"/\".", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "AccessPolicy.resource.criteria", "min": 0, "max": "1"}}, {"id": "AccessPolicy.resource.readonly", "path": "AccessPolicy.resource.readonly", "definition": "@deprecated Use AccessPolicy.resource.interaction = ['search', 'read', 'vread', 'history']", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "AccessPolicy.resource.readonly", "min": 0, "max": "1"}}, {"id": "AccessPolicy.resource.interaction", "path": "AccessPolicy.resource.interaction", "definition": "Permitted FHIR interactions with this resource type", "min": 0, "max": "*", "type": [{"code": "code"}], "base": {"path": "AccessPolicy.resource.interaction", "min": 0, "max": "*"}, "binding": {"strength": "required", "valueSet": "https://medplum.com/fhir/ValueSet/access-poliicy-interactions"}}, {"id": "AccessPolicy.resource.hiddenFields", "path": "AccessPolicy.resource.hiddenFields", "definition": "Optional list of hidden fields.  Hidden fields are not readable or writeable.", "min": 0, "max": "*", "type": [{"code": "string"}], "base": {"path": "AccessPolicy.resource.hiddenFields", "min": 0, "max": "*"}}, {"id": "AccessPolicy.resource.readonlyFields", "path": "AccessPolicy.resource.readonlyFields", "definition": "Optional list of read-only fields.  Read-only fields are readable but not writeable.", "min": 0, "max": "*", "type": [{"code": "string"}], "base": {"path": "AccessPolicy.resource.readonlyFields", "min": 0, "max": "*"}}, {"id": "AccessPolicy.resource.writeConstraint", "path": "AccessPolicy.resource.writeConstraint", "definition": "Invariants that must be satisfied for the resource to be written.  Can include %before and %after placeholders to refer to the resource before and after the updates are applied.", "min": 0, "max": "*", "type": [{"code": "Expression"}], "base": {"path": "AccessPolicy.resource.writeConstraint", "min": 0, "max": "*"}, "constraint": [{"key": "axp-1", "severity": "error", "human": "Write constraint expressions must be literal text/fhirpath strings", "expression": "expression.exists() and language = 'text/fhirpath'"}]}, {"id": "AccessPolicy.ipAccessRule", "path": "AccessPolicy.ipAccessRule", "definition": "Use IP Access Rules to allowlist, block, and challenge traffic based on the visitor IP address.", "min": 0, "max": "*", "type": [{"code": "BackboneElement"}], "base": {"path": "AccessPolicy.ipAccessRule", "min": 0, "max": "*"}}, {"id": "AccessPolicy.ipAccessRule.name", "path": "AccessPolicy.ipAccessRule.name", "definition": "Friendly name that will make it easy for you to identify the IP Access Rule in the future.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "AccessPolicy.ipAccessRule.name", "min": 0, "max": "1"}}, {"id": "AccessPolicy.ipAccessRule.value", "path": "AccessPolicy.ipAccessRule.value", "definition": "An IP Access rule will apply a certain action to incoming traffic based on the visitor IP address or IP range.", "min": 1, "max": "1", "type": [{"code": "string"}], "base": {"path": "AccessPolicy.ipAccessRule.value", "min": 1, "max": "1"}}, {"id": "AccessPolicy.ipAccessRule.action", "path": "AccessPolicy.ipAccessRule.action", "definition": "Access rule can perform one of the following actions: \"allow\" | \"block\".", "min": 1, "max": "1", "type": [{"code": "code"}], "base": {"path": "AccessPolicy.ipAccessRule.action", "min": 1, "max": "1"}, "binding": {"strength": "required", "valueSet": "https://medplum.com/fhir/ValueSet/ip-access-rule-action|4.0.1"}}]}}}, {"fullUrl": "https://medplum.com/fhir/StructureDefinition/UserConfiguration", "resource": {"resourceType": "StructureDefinition", "id": "UserConfiguration", "name": "UserConfiguration", "url": "https://medplum.com/fhir/StructureDefinition/UserConfiguration", "status": "active", "kind": "resource", "abstract": false, "type": "UserConfiguration", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/DomainResource", "description": "User specific configuration for the Medplum application.", "snapshot": {"element": [{"id": "UserConfiguration", "path": "UserConfiguration", "short": "User specific configuration for the Medplum application.", "definition": "User specific configuration for the Medplum application.", "min": 0, "max": "*", "base": {"path": "UserConfiguration", "min": 0, "max": "*"}}, {"id": "UserConfiguration.id", "path": "UserConfiguration.id", "short": "Logical id of this artifact", "definition": "The logical id of the resource, as used in the URL for the resource. Once assigned, this value never changes.", "min": 0, "max": "1", "base": {"path": "Resource.id", "min": 0, "max": "1"}, "type": [{"code": "string"}]}, {"id": "UserConfiguration.meta", "path": "UserConfiguration.meta", "definition": "The metadata about the resource. This is content that is maintained by the infrastructure. Changes to the content might not always be associated with version changes to the resource.", "min": 0, "max": "1", "base": {"path": "Resource.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}]}, {"id": "UserConfiguration.implicitRules", "path": "UserConfiguration.implicitRules", "definition": "A reference to a set of rules that were followed when the resource was constructed, and which must be understood when processing the content. Often, this is a reference to an implementation guide that defines the special rules along with other profiles etc.", "min": 0, "max": "1", "base": {"path": "Resource.implicitRules", "min": 0, "max": "1"}, "type": [{"code": "uri"}]}, {"id": "UserConfiguration.language", "path": "UserConfiguration.language", "definition": "The base language in which the resource is written.", "min": 0, "max": "1", "base": {"path": "Resource.language", "min": 0, "max": "1"}, "type": [{"code": "code"}]}, {"id": "UserConfiguration.text", "path": "UserConfiguration.text", "short": "Text summary of the resource, for human interpretation", "definition": "A human-readable narrative that contains a summary of the resource and can be used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative. Resource definitions may define what content should be represented in the narrative to ensure clinical safety.", "min": 0, "max": "1", "base": {"path": "DomainResource.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}]}, {"id": "UserConfiguration.contained", "path": "UserConfiguration.contained", "short": "Contained, inline Resources", "definition": "These resources do not have an independent existence apart from the resource that contains them - they cannot be identified independently, and nor can they have their own independent transaction scope.", "min": 0, "max": "*", "base": {"path": "DomainResource.contained", "min": 0, "max": "*"}, "type": [{"code": "Resource"}]}, {"id": "UserConfiguration.extension", "path": "UserConfiguration.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the resource. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "min": 0, "max": "*", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "UserConfiguration.modifierExtension", "path": "UserConfiguration.modifierExtension", "short": "Extensions that cannot be ignored", "definition": "May be used to represent additional information that is not part of the basic definition of the resource and that modifies the understanding of the element that contains it and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer is allowed to define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "min": 0, "max": "*", "base": {"path": "DomainResource.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "UserConfiguration.name", "path": "UserConfiguration.name", "definition": "A name associated with the UserConfiguration.", "min": 0, "max": "1", "type": [{"code": "string"}], "base": {"path": "UserConfiguration.name", "min": 0, "max": "1"}}, {"id": "UserConfiguration.menu", "path": "UserConfiguration.menu", "definition": "Optional menu of shortcuts to URLs.", "min": 0, "max": "*", "type": [{"code": "BackboneElement"}], "base": {"path": "UserConfiguration.menu", "min": 0, "max": "*"}}, {"id": "UserConfiguration.menu.title", "path": "UserConfiguration.menu.title", "definition": "Title of the menu.", "min": 1, "max": "1", "type": [{"code": "string"}], "base": {"path": "UserConfiguration.menu.title", "min": 1, "max": "1"}}, {"id": "UserConfiguration.menu.link", "path": "UserConfiguration.menu.link", "definition": "Shortcut links to URLs.", "min": 0, "max": "*", "type": [{"code": "BackboneElement"}], "base": {"path": "UserConfiguration.menu.link", "min": 0, "max": "*"}}, {"id": "UserConfiguration.menu.link.name", "path": "UserConfiguration.menu.link.name", "definition": "The human friendly name of the link.", "min": 1, "max": "1", "type": [{"code": "string"}], "base": {"path": "UserConfiguration.menu.link.name", "min": 1, "max": "1"}}, {"id": "UserConfiguration.menu.link.target", "path": "UserConfiguration.menu.link.target", "definition": "The URL target of the link.", "min": 1, "max": "1", "type": [{"code": "url"}], "base": {"path": "UserConfiguration.menu.link.target", "min": 1, "max": "1"}}, {"id": "UserConfiguration.search", "path": "UserConfiguration.search", "definition": "Shortcut links to URLs.", "min": 0, "max": "*", "type": [{"code": "BackboneElement"}], "base": {"path": "UserConfiguration.search", "min": 0, "max": "*"}}, {"id": "UserConfiguration.search.name", "path": "UserConfiguration.search.name", "definition": "The human friendly name of the link.", "min": 1, "max": "1", "type": [{"code": "string"}], "base": {"path": "UserConfiguration.search.name", "min": 1, "max": "1"}}, {"id": "UserConfiguration.search.criteria", "path": "UserConfiguration.search.criteria", "definition": "The rules that the server should use to determine which resources to return.", "comment": "The rules are search criteria (without the [base] part). Like Bundle.entry.request.url, it has no leading \"/\".", "min": 1, "max": "1", "type": [{"code": "string"}], "base": {"path": "UserConfiguration.search.criteria", "min": 1, "max": "1"}}, {"id": "UserConfiguration.option", "path": "UserConfiguration.option", "definition": "User options that control the display of the application.", "min": 0, "max": "*", "type": [{"code": "BackboneElement"}], "base": {"path": "UserConfiguration.option", "min": 0, "max": "*"}}, {"id": "UserConfiguration.option.id", "path": "UserConfiguration.option.id", "definition": "The unique identifier of the option.", "min": 1, "max": "1", "type": [{"code": "code"}], "base": {"path": "UserConfiguration.option.id", "min": 1, "max": "1"}}, {"id": "UserConfiguration.option.value[x]", "path": "UserConfiguration.option.value[x]", "short": "Value of option", "definition": "Value of option - must be one of a constrained set of the data types (see [Extensibility](extensibility.html) for a list).", "min": 1, "max": "1", "type": [{"code": "boolean"}, {"code": "code"}, {"code": "decimal"}, {"code": "integer"}, {"code": "string"}], "base": {"path": "UserConfiguration.option.value[x]", "min": 1, "max": "1"}}]}}}, {"fullUrl": "https://medplum.com/fhir/StructureDefinition/BulkDataExport", "resource": {"resourceType": "StructureDefinition", "id": "BulkDataExport", "name": "BulkDataExport", "url": "https://medplum.com/fhir/StructureDefinition/BulkDataExport", "status": "active", "kind": "resource", "abstract": false, "type": "BulkDataExport", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/DomainResource", "description": "User specific configuration for the Medplum application.", "snapshot": {"element": [{"id": "BulkDataExport", "path": "BulkDataExport", "short": "User specific configuration for the Medplum application.", "definition": "User specific configuration for the Medplum application.", "min": 0, "max": "*", "base": {"path": "BulkDataExport", "min": 0, "max": "*"}}, {"id": "BulkDataExport.id", "path": "BulkDataExport.id", "short": "Logical id of this artifact", "definition": "The logical id of the resource, as used in the URL for the resource. Once assigned, this value never changes.", "min": 0, "max": "1", "base": {"path": "Resource.id", "min": 0, "max": "1"}, "type": [{"code": "string"}]}, {"id": "BulkDataExport.meta", "path": "BulkDataExport.meta", "definition": "The metadata about the resource. This is content that is maintained by the infrastructure. Changes to the content might not always be associated with version changes to the resource.", "min": 0, "max": "1", "base": {"path": "Resource.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}]}, {"id": "BulkDataExport.implicitRules", "path": "BulkDataExport.implicitRules", "definition": "A reference to a set of rules that were followed when the resource was constructed, and which must be understood when processing the content. Often, this is a reference to an implementation guide that defines the special rules along with other profiles etc.", "min": 0, "max": "1", "base": {"path": "Resource.implicitRules", "min": 0, "max": "1"}, "type": [{"code": "uri"}]}, {"id": "BulkDataExport.language", "path": "BulkDataExport.language", "definition": "The base language in which the resource is written.", "min": 0, "max": "1", "base": {"path": "Resource.language", "min": 0, "max": "1"}, "type": [{"code": "code"}]}, {"id": "BulkDataExport.text", "path": "BulkDataExport.text", "short": "Text summary of the resource, for human interpretation", "definition": "A human-readable narrative that contains a summary of the resource and can be used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative. Resource definitions may define what content should be represented in the narrative to ensure clinical safety.", "min": 0, "max": "1", "base": {"path": "DomainResource.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}]}, {"id": "BulkDataExport.contained", "path": "BulkDataExport.contained", "short": "Contained, inline Resources", "definition": "These resources do not have an independent existence apart from the resource that contains them - they cannot be identified independently, and nor can they have their own independent transaction scope.", "min": 0, "max": "*", "base": {"path": "DomainResource.contained", "min": 0, "max": "*"}, "type": [{"code": "Resource"}]}, {"id": "BulkDataExport.extension", "path": "BulkDataExport.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the resource. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "min": 0, "max": "*", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "BulkDataExport.modifierExtension", "path": "BulkDataExport.modifierExtension", "short": "Extensions that cannot be ignored", "definition": "May be used to represent additional information that is not part of the basic definition of the resource and that modifies the understanding of the element that contains it and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer is allowed to define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "min": 0, "max": "*", "base": {"path": "DomainResource.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "BulkDataExport.status", "path": "BulkDataExport.status", "short": "active | error | completed", "definition": "The status of the request.", "min": 1, "max": "1", "base": {"path": "BulkDataExport.status", "min": 1, "max": "1"}, "type": [{"code": "code"}], "binding": {"strength": "required", "valueSet": "https://medplum.com/fhir/ValueSet/async-job-status|4.0.1"}}, {"id": "BulkDataExport.requestTime", "path": "BulkDataExport.requestTime", "definition": "Indicates the server's time when the query is requested.", "min": 1, "max": "1", "type": [{"code": "instant"}], "base": {"path": "BulkDataExport.requestTime", "min": 1, "max": "1"}}, {"id": "BulkDataExport.transactionTime", "path": "BulkDataExport.transactionTime", "definition": "Indicates the server's time when the query is run. The response SHOULD NOT include any resources modified after this instant, and SHALL include any matching resources modified up to and including this instant.", "min": 0, "max": "1", "type": [{"code": "instant"}], "base": {"path": "BulkDataExport.transactionTime", "min": 0, "max": "1"}}, {"id": "BulkDataExport.request", "path": "BulkDataExport.request", "definition": "The full URL of the original Bulk Data kick-off request. In the case of a POST request, this URL will not include the request parameters.", "min": 1, "max": "1", "type": [{"code": "uri"}], "base": {"path": "BulkDataExport.request", "min": 1, "max": "1"}}, {"id": "BulkDataExport.requiresAccessToken", "path": "BulkDataExport.requiresAccessToken", "definition": "Indicates whether downloading the generated files requires the same authorization mechanism as the $export operation itself.", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "BulkDataExport.requiresAccessToken", "min": 0, "max": "1"}}, {"id": "BulkDataExport.output", "path": "BulkDataExport.output", "definition": "An array of file items with one entry for each generated file. If no resources are returned from the kick-off request, the server SHOULD return an empty array.", "min": 0, "max": "*", "type": [{"code": "BackboneElement"}], "base": {"path": "BulkDataExport.output", "min": 0, "max": "*"}}, {"id": "BulkDataExport.output.type", "path": "BulkDataExport.output.type", "definition": "The FHIR resource type that is contained in the file.", "min": 1, "max": "1", "type": [{"code": "code"}], "base": {"path": "BulkDataExport.output.type", "min": 1, "max": "1"}, "binding": {"strength": "required", "description": "One of the resource types defined as part of this version of FHIR.", "valueSet": "http://hl7.org/fhir/ValueSet/resource-types|4.0.1"}}, {"id": "BulkDataExport.output.url", "path": "BulkDataExport.output.url", "definition": "The absolute path to the file. The format of the file SHOULD reflect that requested in the _outputFormat parameter of the initial kick-off request.", "min": 1, "max": "1", "type": [{"code": "uri"}], "base": {"path": "BulkDataExport.output.url", "min": 1, "max": "1"}}, {"id": "BulkDataExport.deleted", "path": "BulkDataExport.deleted", "definition": "An array of deleted file items following the same structure as the output array.", "min": 0, "max": "*", "type": [{"code": "BackboneElement"}], "base": {"path": "BulkDataExport.deleted", "min": 0, "max": "*"}}, {"id": "BulkDataExport.deleted.type", "path": "BulkDataExport.deleted.type", "definition": "The FHIR resource type that is contained in the file.", "min": 1, "max": "1", "type": [{"code": "code"}], "base": {"path": "BulkDataExport.deleted.type", "min": 1, "max": "1"}, "binding": {"strength": "required", "description": "One of the resource types defined as part of this version of FHIR.", "valueSet": "http://hl7.org/fhir/ValueSet/resource-types|4.0.1"}}, {"id": "BulkDataExport.deleted.url", "path": "BulkDataExport.deleted.url", "definition": "The absolute path to the file. The format of the file SHOULD reflect that requested in the _outputFormat parameter of the initial kick-off request.", "min": 1, "max": "1", "type": [{"code": "uri"}], "base": {"path": "BulkDataExport.deleted.url", "min": 1, "max": "1"}}, {"id": "BulkDataExport.error", "path": "BulkDataExport.error", "definition": "Array of message file items following the same structure as the output array.", "min": 0, "max": "*", "type": [{"code": "BackboneElement"}], "base": {"path": "BulkDataExport.error", "min": 0, "max": "*"}}, {"id": "BulkDataExport.error.type", "path": "BulkDataExport.error.type", "definition": "The FHIR resource type that is contained in the file.", "min": 1, "max": "1", "type": [{"code": "code"}], "base": {"path": "BulkDataExport.error.type", "min": 1, "max": "1"}, "binding": {"strength": "required", "description": "One of the resource types defined as part of this version of FHIR.", "valueSet": "http://hl7.org/fhir/ValueSet/resource-types|4.0.1"}}, {"id": "BulkDataExport.error.url", "path": "BulkDataExport.error.url", "definition": "The absolute path to the file. The format of the file SHOULD reflect that requested in the _outputFormat parameter of the initial kick-off request.", "min": 1, "max": "1", "type": [{"code": "uri"}], "base": {"path": "BulkDataExport.error.url", "min": 1, "max": "1"}}]}}}, {"fullUrl": "https://medplum.com/fhir/StructureDefinition/SmartAppLaunch", "resource": {"resourceType": "StructureDefinition", "id": "SmartAppLaunch", "name": "SmartAppLaunch", "url": "https://medplum.com/fhir/StructureDefinition/SmartAppLaunch", "status": "active", "kind": "resource", "abstract": false, "type": "SmartAppLaunch", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/DomainResource", "description": "This resource contains context details for a SMART App Launch.", "snapshot": {"element": [{"id": "SmartAppLaunch", "path": "SmartAppLaunch", "short": "This resource contains context details for a SMART App Launch.", "definition": "This resource contains context details for a SMART App Launch.", "min": 0, "max": "*", "base": {"path": "SmartAppLaunch", "min": 0, "max": "*"}}, {"id": "SmartAppLaunch.id", "path": "SmartAppLaunch.id", "short": "Logical id of this artifact", "definition": "The logical id of the resource, as used in the URL for the resource. Once assigned, this value never changes.", "min": 0, "max": "1", "base": {"path": "Resource.id", "min": 0, "max": "1"}, "type": [{"code": "string"}]}, {"id": "SmartAppLaunch.meta", "path": "SmartAppLaunch.meta", "definition": "The metadata about the resource. This is content that is maintained by the infrastructure. Changes to the content might not always be associated with version changes to the resource.", "min": 0, "max": "1", "base": {"path": "Resource.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}]}, {"id": "SmartAppLaunch.implicitRules", "path": "SmartAppLaunch.implicitRules", "definition": "A reference to a set of rules that were followed when the resource was constructed, and which must be understood when processing the content. Often, this is a reference to an implementation guide that defines the special rules along with other profiles etc.", "min": 0, "max": "1", "base": {"path": "Resource.implicitRules", "min": 0, "max": "1"}, "type": [{"code": "uri"}]}, {"id": "SmartAppLaunch.language", "path": "SmartAppLaunch.language", "definition": "The base language in which the resource is written.", "min": 0, "max": "1", "type": [{"code": "code"}], "base": {"path": "Resource.language", "min": 0, "max": "1"}}, {"id": "SmartAppLaunch.text", "path": "SmartAppLaunch.text", "short": "Text summary of the resource, for human interpretation", "definition": "A human-readable narrative that contains a summary of the resource and can be used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative. Resource definitions may define what content should be represented in the narrative to ensure clinical safety.", "min": 0, "max": "1", "base": {"path": "DomainResource.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}]}, {"id": "SmartAppLaunch.contained", "path": "SmartAppLaunch.contained", "short": "Contained, inline Resources", "definition": "These resources do not have an independent existence apart from the resource that contains them - they cannot be identified independently, and nor can they have their own independent transaction scope.", "min": 0, "max": "*", "base": {"path": "DomainResource.contained", "min": 0, "max": "*"}, "type": [{"code": "Resource"}]}, {"id": "SmartAppLaunch.extension", "path": "SmartAppLaunch.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the resource. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "min": 0, "max": "*", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "SmartAppLaunch.modifierExtension", "path": "SmartAppLaunch.modifierExtension", "short": "Extensions that cannot be ignored", "definition": "May be used to represent additional information that is not part of the basic definition of the resource and that modifies the understanding of the element that contains it and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer is allowed to define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "min": 0, "max": "*", "base": {"path": "DomainResource.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "SmartAppLaunch.patient", "path": "SmartAppLaunch.patient", "definition": "Optional patient indicating that the app was launched in the patient context.", "min": 0, "max": "1", "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Patient"]}], "base": {"path": "SmartAppLaunch.patient", "min": 0, "max": "1"}}, {"id": "SmartAppLaunch.encounter", "path": "SmartAppLaunch.encounter", "definition": "Optional encounter indicating that the app was launched in the encounter context.", "min": 0, "max": "1", "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Encounter"]}], "base": {"path": "SmartAppLaunch.encounter", "min": 0, "max": "1"}}]}}}, {"fullUrl": "https://medplum.com/fhir/StructureDefinition/DomainConfiguration", "resource": {"resourceType": "StructureDefinition", "id": "DomainConfiguration", "name": "DomainConfiguration", "url": "https://medplum.com/fhir/StructureDefinition/DomainConfiguration", "status": "active", "kind": "resource", "abstract": false, "type": "DomainConfiguration", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/DomainResource", "description": "Domain specific configuration for the Medplum application.", "snapshot": {"element": [{"id": "DomainConfiguration", "path": "DomainConfiguration", "short": "Domain specific configuration for the Medplum application.", "definition": "Domain specific configuration for the Medplum application.", "min": 0, "max": "*", "base": {"path": "DomainConfiguration", "min": 0, "max": "*"}}, {"id": "DomainConfiguration.id", "path": "DomainConfiguration.id", "short": "Logical id of this artifact", "definition": "The logical id of the resource, as used in the URL for the resource. Once assigned, this value never changes.", "min": 0, "max": "1", "base": {"path": "Resource.id", "min": 0, "max": "1"}, "type": [{"code": "string"}]}, {"id": "DomainConfiguration.meta", "path": "DomainConfiguration.meta", "definition": "The metadata about the resource. This is content that is maintained by the infrastructure. Changes to the content might not always be associated with version changes to the resource.", "min": 0, "max": "1", "base": {"path": "Resource.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}]}, {"id": "DomainConfiguration.implicitRules", "path": "DomainConfiguration.implicitRules", "definition": "A reference to a set of rules that were followed when the resource was constructed, and which must be understood when processing the content. Often, this is a reference to an implementation guide that defines the special rules along with other profiles etc.", "min": 0, "max": "1", "base": {"path": "Resource.implicitRules", "min": 0, "max": "1"}, "type": [{"code": "uri"}]}, {"id": "DomainConfiguration.language", "path": "DomainConfiguration.language", "definition": "The base language in which the resource is written.", "min": 0, "max": "1", "type": [{"code": "code"}], "base": {"path": "Resource.language", "min": 0, "max": "1"}}, {"id": "DomainConfiguration.text", "path": "DomainConfiguration.text", "short": "Text summary of the resource, for human interpretation", "definition": "A human-readable narrative that contains a summary of the resource and can be used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative. Resource definitions may define what content should be represented in the narrative to ensure clinical safety.", "min": 0, "max": "1", "base": {"path": "DomainResource.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}]}, {"id": "DomainConfiguration.contained", "path": "DomainConfiguration.contained", "short": "Contained, inline Resources", "definition": "These resources do not have an independent existence apart from the resource that contains them - they cannot be identified independently, and nor can they have their own independent transaction scope.", "min": 0, "max": "*", "base": {"path": "DomainResource.contained", "min": 0, "max": "*"}, "type": [{"code": "Resource"}]}, {"id": "DomainConfiguration.extension", "path": "DomainConfiguration.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the resource. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "min": 0, "max": "*", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "DomainConfiguration.modifierExtension", "path": "DomainConfiguration.modifierExtension", "short": "Extensions that cannot be ignored", "definition": "May be used to represent additional information that is not part of the basic definition of the resource and that modifies the understanding of the element that contains it and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer is allowed to define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "min": 0, "max": "*", "base": {"path": "DomainResource.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "DomainConfiguration.domain", "path": "DomainConfiguration.domain", "definition": "Globally unique domain name for this configuration.", "min": 1, "max": "1", "type": [{"code": "uri"}], "base": {"path": "DomainConfiguration.domain", "min": 1, "max": "1"}}, {"id": "DomainConfiguration.identityProvider", "path": "DomainConfiguration.identityProvider", "definition": "Optional external Identity Provider (IdP) for the domain name.", "min": 0, "max": "1", "type": [{"code": "IdentityProvider"}], "base": {"path": "DomainConfiguration.identityProvider", "min": 0, "max": "1"}}]}}}, {"fullUrl": "https://medplum.com/fhir/StructureDefinition/IdentityProvider", "resource": {"resourceType": "StructureDefinition", "id": "IdentityProvider", "name": "IdentityProvider", "url": "https://medplum.com/fhir/StructureDefinition/IdentityProvider", "status": "active", "kind": "complex-type", "abstract": false, "type": "IdentityProvider", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/Element", "description": "External Identity Provider (IdP) configuration details.", "snapshot": {"element": [{"id": "IdentityProvider", "path": "IdentityProvider", "short": "External Identity Provider (IdP) configuration details.", "definition": "External Identity Provider (IdP) configuration details.", "min": 0, "max": "*", "base": {"path": "IdentityProvider", "min": 0, "max": "*"}}, {"id": "IdentityProvider.authorizeUrl", "path": "IdentityProvider.authorizeUrl", "definition": "Remote URL for the external Identity Provider authorize endpoint.", "min": 1, "max": "1", "type": [{"code": "string"}], "base": {"path": "IdentityProvider.authorizeUrl", "min": 1, "max": "1"}}, {"id": "IdentityProvider.tokenUrl", "path": "IdentityProvider.tokenUrl", "definition": "Remote URL for the external Identity Provider token endpoint.", "min": 1, "max": "1", "type": [{"code": "string"}], "base": {"path": "IdentityProvider.tokenUrl", "min": 1, "max": "1"}}, {"id": "IdentityProvider.tokenAuthMethod", "path": "IdentityProvider.tokenAuthMethod", "definition": "Client Authentication method used by Clients to authenticate to the Authorization Server when using the Token Endpoint. If no method is registered, the default method is client_secret_basic.", "min": 0, "max": "1", "base": {"path": "IdentityProvider.tokenAuthMethod", "min": 0, "max": "1"}, "type": [{"code": "code"}], "binding": {"strength": "required", "valueSet": "https://medplum.com/fhir/ValueSet/token-endpoint-auth-methods-supported|4.0.1"}}, {"id": "IdentityProvider.userInfoUrl", "path": "IdentityProvider.userInfoUrl", "definition": "Remote URL for the external Identity Provider userinfo endpoint.", "min": 1, "max": "1", "type": [{"code": "string"}], "base": {"path": "IdentityProvider.userInfoUrl", "min": 1, "max": "1"}}, {"id": "IdentityProvider.clientId", "path": "IdentityProvider.clientId", "definition": "External Identity Provider client ID.", "min": 1, "max": "1", "type": [{"code": "string"}], "base": {"path": "IdentityProvider.clientId", "min": 1, "max": "1"}}, {"id": "IdentityProvider.clientSecret", "path": "IdentityProvider.clientSecret", "definition": "External Identity Provider client secret.", "min": 1, "max": "1", "type": [{"code": "string"}], "base": {"path": "IdentityProvider.clientSecret", "min": 1, "max": "1"}}, {"id": "IdentityProvider.usePkce", "path": "IdentityProvider.usePkce", "short": "Optional flag to use PKCE in the token request.", "definition": "Optional flag to use PKCE in the token request.", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "IdentityProvider.usePkce", "min": 0, "max": "1"}}, {"id": "IdentityProvider.useSubject", "path": "IdentityProvider.useSubject", "short": "Optional flag to use the subject field instead of the email field.", "definition": "Optional flag to use the subject field instead of the email field.", "min": 0, "max": "1", "type": [{"code": "boolean"}], "base": {"path": "IdentityProvider.useSubject", "min": 0, "max": "1"}}]}}}, {"fullUrl": "https://medplum.com/fhir/StructureDefinition/AsyncJob", "resource": {"resourceType": "StructureDefinition", "id": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://medplum.com/fhir/StructureDefinition/AsyncJob", "status": "active", "kind": "resource", "abstract": false, "type": "<PERSON><PERSON><PERSON><PERSON>", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/DomainResource", "description": "Contains details of long running asynchronous/background jobs.", "snapshot": {"element": [{"id": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>", "short": "Contains details of long running asynchronous/background jobs.", "definition": "Contains details of long running asynchronous/background jobs.", "min": 0, "max": "*", "base": {"path": "<PERSON><PERSON><PERSON><PERSON>", "min": 0, "max": "*"}, "constraint": [{"key": "ajb-1", "severity": "error", "human": "Async jobs of type 'data-migration' require a 'dataVersion' and 'minServerVersion'.", "expression": "type.exists() and type = 'data-migration' implies dataVersion.exists() and minServerVersion.exists()"}]}, {"id": "AsyncJob.id", "path": "AsyncJob.id", "short": "Logical id of this artifact", "definition": "The logical id of the resource, as used in the URL for the resource. Once assigned, this value never changes.", "min": 0, "max": "1", "base": {"path": "Resource.id", "min": 0, "max": "1"}, "type": [{"code": "string"}]}, {"id": "AsyncJob.meta", "path": "AsyncJob.meta", "definition": "The metadata about the resource. This is content that is maintained by the infrastructure. Changes to the content might not always be associated with version changes to the resource.", "min": 0, "max": "1", "base": {"path": "Resource.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}]}, {"id": "AsyncJob.implicitRules", "path": "AsyncJob.implicitRules", "definition": "A reference to a set of rules that were followed when the resource was constructed, and which must be understood when processing the content. Often, this is a reference to an implementation guide that defines the special rules along with other profiles etc.", "min": 0, "max": "1", "base": {"path": "Resource.implicitRules", "min": 0, "max": "1"}, "type": [{"code": "uri"}]}, {"id": "AsyncJob.language", "path": "AsyncJob.language", "definition": "The base language in which the resource is written.", "min": 0, "max": "1", "base": {"path": "Resource.language", "min": 0, "max": "1"}, "type": [{"code": "code"}]}, {"id": "AsyncJob.text", "path": "AsyncJob.text", "short": "Text summary of the resource, for human interpretation", "definition": "A human-readable narrative that contains a summary of the resource and can be used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative. Resource definitions may define what content should be represented in the narrative to ensure clinical safety.", "min": 0, "max": "1", "base": {"path": "DomainResource.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}]}, {"id": "AsyncJob.contained", "path": "AsyncJob.contained", "short": "Contained, inline Resources", "definition": "These resources do not have an independent existence apart from the resource that contains them - they cannot be identified independently, and nor can they have their own independent transaction scope.", "min": 0, "max": "*", "base": {"path": "DomainResource.contained", "min": 0, "max": "*"}, "type": [{"code": "Resource"}]}, {"id": "AsyncJob.extension", "path": "AsyncJob.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the resource. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "min": 0, "max": "*", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "AsyncJob.modifierExtension", "path": "AsyncJob.modifierExtension", "short": "Extensions that cannot be ignored", "definition": "May be used to represent additional information that is not part of the basic definition of the resource and that modifies the understanding of the element that contains it and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer is allowed to define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "min": 0, "max": "*", "base": {"path": "DomainResource.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "AsyncJob.status", "path": "AsyncJob.status", "short": "accepted | error | completed | cancelled", "definition": "The status of the request.", "min": 1, "max": "1", "base": {"path": "AsyncJob.status", "min": 1, "max": "1"}, "type": [{"code": "code"}], "binding": {"strength": "required", "valueSet": "https://medplum.com/fhir/ValueSet/async-job-status|4.0.1"}}, {"id": "AsyncJob.requestTime", "path": "AsyncJob.requestTime", "definition": "Indicates the server's time when the query is requested.", "min": 1, "max": "1", "type": [{"code": "instant"}], "base": {"path": "AsyncJob.requestTime", "min": 1, "max": "1"}}, {"id": "AsyncJob.transactionTime", "path": "AsyncJob.transactionTime", "definition": "Indicates the server's time when the query is run. The response SHOULD NOT include any resources modified after this instant, and SHALL include any matching resources modified up to and including this instant.", "min": 0, "max": "1", "type": [{"code": "instant"}], "base": {"path": "AsyncJob.transactionTime", "min": 0, "max": "1"}}, {"id": "AsyncJob.request", "path": "AsyncJob.request", "definition": "The full URL of the original kick-off request. In the case of a POST request, this URL will not include the request parameters.", "min": 1, "max": "1", "type": [{"code": "uri"}], "base": {"path": "AsyncJob.request", "min": 1, "max": "1"}}, {"id": "AsyncJob.output", "path": "AsyncJob.output", "definition": "Outputs resulting from the async job.", "min": 0, "max": "1", "type": [{"code": "Parameters"}], "base": {"path": "AsyncJob.output", "min": 0, "max": "1"}}, {"id": "AsyncJob.type", "path": "AsyncJob.type", "short": "data-migration", "definition": "The type of the AsyncJob.", "min": 0, "max": "1", "base": {"path": "AsyncJob.type", "min": 0, "max": "1"}, "type": [{"code": "code"}], "binding": {"strength": "required", "valueSet": "https://medplum.com/fhir/ValueSet/async-job-type|4.0.1"}}, {"id": "AsyncJob.dataVersion", "path": "AsyncJob.dataVersion", "definition": "The data version of the migration this job represents.", "min": 0, "max": "1", "base": {"path": "AsyncJob.dataVersion", "min": 0, "max": "1"}, "type": [{"code": "integer"}]}, {"id": "AsyncJob.minServerVersion", "path": "AsyncJob.minServerVersion", "definition": "The minimum Medplum server version required to run this job.", "min": 0, "max": "1", "base": {"path": "AsyncJob.minServerVersion", "min": 0, "max": "1"}, "type": [{"code": "string"}]}]}}}, {"fullUrl": "https://medplum.com/fhir/StructureDefinition/Agent", "resource": {"resourceType": "StructureDefinition", "id": "Agent", "name": "Agent", "url": "https://medplum.com/fhir/StructureDefinition/Agent", "status": "active", "kind": "resource", "abstract": false, "type": "Agent", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/DomainResource", "description": "Configuration details for an instance of the Medplum agent application.", "snapshot": {"element": [{"id": "Agent", "path": "Agent", "short": "Configuration details for an instance of the Medplum agent application.", "definition": "Configuration details for an instance of the Medplum agent application.", "min": 0, "max": "*", "base": {"path": "Agent", "min": 0, "max": "*"}}, {"id": "Agent.id", "path": "Agent.id", "short": "Logical id of this artifact", "definition": "The logical id of the resource, as used in the URL for the resource. Once assigned, this value never changes.", "min": 0, "max": "1", "base": {"path": "Resource.id", "min": 0, "max": "1"}, "type": [{"code": "string"}]}, {"id": "Agent.meta", "path": "Agent.meta", "definition": "The metadata about the resource. This is content that is maintained by the infrastructure. Changes to the content might not always be associated with version changes to the resource.", "min": 0, "max": "1", "base": {"path": "Resource.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}]}, {"id": "Agent.implicitRules", "path": "Agent.implicitRules", "definition": "A reference to a set of rules that were followed when the resource was constructed, and which must be understood when processing the content. Often, this is a reference to an implementation guide that defines the special rules along with other profiles etc.", "min": 0, "max": "1", "base": {"path": "Resource.implicitRules", "min": 0, "max": "1"}, "type": [{"code": "uri"}]}, {"id": "Agent.language", "path": "Agent.language", "definition": "The base language in which the resource is written.", "min": 0, "max": "1", "base": {"path": "Resource.language", "min": 0, "max": "1"}, "type": [{"code": "code"}]}, {"id": "Agent.text", "path": "Agent.text", "short": "Text summary of the resource, for human interpretation", "definition": "A human-readable narrative that contains a summary of the resource and can be used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative. Resource definitions may define what content should be represented in the narrative to ensure clinical safety.", "min": 0, "max": "1", "base": {"path": "DomainResource.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}]}, {"id": "Agent.contained", "path": "Agent.contained", "short": "Contained, inline Resources", "definition": "These resources do not have an independent existence apart from the resource that contains them - they cannot be identified independently, and nor can they have their own independent transaction scope.", "min": 0, "max": "*", "base": {"path": "DomainResource.contained", "min": 0, "max": "*"}, "type": [{"code": "Resource"}]}, {"id": "Agent.extension", "path": "Agent.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the resource. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "min": 0, "max": "*", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "Agent.modifierExtension", "path": "Agent.modifierExtension", "short": "Extensions that cannot be ignored", "definition": "May be used to represent additional information that is not part of the basic definition of the resource and that modifies the understanding of the element that contains it and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer is allowed to define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "min": 0, "max": "*", "base": {"path": "DomainResource.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}]}, {"id": "Agent.identifier", "path": "Agent.identifier", "short": "An identifier for this agent", "definition": "An identifier for this agent.", "min": 0, "max": "*", "type": [{"code": "Identifier"}], "base": {"path": "Agent.identifier", "min": 0, "max": "*"}}, {"id": "Agent.name", "path": "Agent.name", "short": "The human readable friendly name of the agent.", "definition": "The human readable friendly name of the agent.", "min": 1, "max": "1", "base": {"path": "Agent.name", "min": 1, "max": "1"}, "type": [{"code": "string"}]}, {"id": "Agent.status", "path": "Agent.status", "short": "active | off | error", "definition": "The status of the agent.", "min": 1, "max": "1", "base": {"path": "Agent.status", "min": 1, "max": "1"}, "type": [{"code": "code"}], "binding": {"strength": "required", "valueSet": "https://medplum.com/fhir/ValueSet/agent-status|4.0.1"}}, {"id": "Agent.device", "path": "Agent.device", "definition": "Optional device resource representing the device running the agent.", "min": 0, "max": "1", "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Device"]}], "base": {"path": "Agent.device", "min": 0, "max": "1"}}, {"id": "Agent.setting", "path": "Agent.setting", "definition": "The settings for the agent.", "min": 0, "max": "*", "type": [{"code": "BackboneElement"}], "base": {"path": "Agent.setting", "min": 0, "max": "*"}}, {"id": "Agent.setting.name", "path": "Agent.setting.name", "definition": "The setting name.", "min": 1, "max": "1", "type": [{"code": "string"}], "base": {"path": "Agent.setting.name", "min": 1, "max": "1"}}, {"id": "Agent.setting.value[x]", "path": "Agent.setting.value[x]", "definition": "The setting value.", "min": 1, "max": "1", "type": [{"code": "string"}, {"code": "boolean"}, {"code": "decimal"}, {"code": "integer"}], "base": {"path": "Agent.setting.value[x]", "min": 1, "max": "1"}}, {"id": "Agent.channel", "path": "Agent.channel", "short": "The channel on which to report matches to the criteria", "definition": "Details where to send notifications when resources are received that meet the criteria.", "min": 0, "max": "*", "base": {"path": "Agent.channel", "min": 1, "max": "*"}, "type": [{"code": "BackboneElement"}]}, {"id": "Agent.channel.name", "path": "Agent.channel.name", "definition": "The channel name.", "min": 1, "max": "1", "type": [{"code": "string"}], "base": {"path": "Agent.channel.name", "min": 1, "max": "1"}}, {"id": "Agent.channel.endpoint", "path": "Agent.channel.endpoint", "definition": "The channel endpoint definition including protocol and network binding details.", "min": 1, "max": "1", "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Endpoint"]}], "base": {"path": "Agent.channel.endpoint", "min": 1, "max": "1"}}, {"id": "Agent.channel.target[x]", "path": "Agent.channel.target[x]", "definition": "The target resource where channel messages will be delivered.", "min": 1, "max": "1", "type": [{"code": "Reference", "targetProfile": ["https://medplum.com/fhir/StructureDefinition/Bot"]}, {"code": "url"}], "base": {"path": "Agent.channel.target[x]", "min": 1, "max": "1"}}]}}}, {"fullUrl": "http://hl7.org/fhir/uv/sql-on-fhir/StructureDefinition/ViewDefinition", "resource": {"resourceType": "StructureDefinition", "id": "ViewDefinition", "url": "http://hl7.org/fhir/uv/sql-on-fhir/StructureDefinition/ViewDefinition", "version": "0.0.1-pre", "name": "ViewDefinition", "title": "View Definition", "status": "draft", "date": "2024-06-11T00:39:12+00:00", "publisher": "HL7", "contact": [{"name": "HL7", "telecom": [{"system": "url", "value": "http://example.org/example-publisher"}]}], "description": "View definitions represent a tabular projection of a FHIR resource, where the columns and inclusion \ncriteria are defined by FHIRPath expressions. ", "jurisdiction": [{"coding": [{"system": "http://unstats.un.org/unsd/methods/m49/m49.htm", "code": "001", "display": "World"}]}], "fhirVersion": "5.0.0", "mapping": [{"identity": "rim", "uri": "http://hl7.org/v3", "name": "RIM Mapping"}], "kind": "logical", "abstract": false, "type": "http://hl7.org/fhir/uv/sql-on-fhir/StructureDefinition/ViewDefinition", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/Base", "derivation": "specialization", "snapshot": {"element": [{"id": "ViewDefinition", "path": "ViewDefinition", "short": "View Definition", "definition": "View definitions represent a tabular projection of a FHIR resource, where the columns and inclusion \ncriteria are defined by FHIRPath expressions. ", "min": 0, "max": "*", "base": {"path": "Base", "min": 0, "max": "*"}, "isModifier": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "ViewDefinition.url", "path": "ViewDefinition.url", "short": "Canonical identifier for this view definition, represented as a URI (globally unique)", "definition": "Canonical identifier for this view definition, represented as a URI (globally unique)", "min": 0, "max": "1", "base": {"path": "ViewDefinition.url", "min": 0, "max": "1"}, "type": [{"code": "uri"}]}, {"id": "ViewDefinition.identifier", "path": "ViewDefinition.identifier", "short": "Additional identifier for the view definition", "definition": "Additional identifier for the view definition", "min": 0, "max": "1", "base": {"path": "ViewDefinition.identifier", "min": 0, "max": "1"}, "type": [{"code": "Identifier"}]}, {"id": "ViewDefinition.name", "path": "ViewDefinition.name", "short": "Name of view definition (computer and database friendly)", "definition": "Name of the view definition, must be in a database-friendly format.", "min": 0, "max": "1", "base": {"path": "ViewDefinition.name", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "sql-name", "severity": "error", "human": "Name is limited to letters, numbers, or underscores and cannot start with an\nunderscore -- i.e. with a regular expression of: ^[A-Za-z][A-Za-z0-9_]*$ \n\n\nThis makes it usable as table names in a wide variety of databases.", "expression": "empty() or matches('^[A-Za-z][A-Za-z0-9_]*$')", "source": "http://hl7.org/fhir/uv/sql-on-fhir/StructureDefinition/ViewDefinition"}]}, {"id": "ViewDefinition.title", "path": "ViewDefinition.title", "short": "Name for this view definition (human friendly)", "definition": "A optional human-readable description of the view.", "min": 0, "max": "1", "base": {"path": "ViewDefinition.title", "min": 0, "max": "1"}, "type": [{"code": "string"}]}, {"id": "ViewDefinition.meta", "path": "ViewDefinition.meta", "short": "<PERSON><PERSON><PERSON> about the view definition", "definition": "<PERSON><PERSON><PERSON> about the view definition", "min": 0, "max": "1", "base": {"path": "ViewDefinition.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}]}, {"id": "ViewDefinition.status", "path": "ViewDefinition.status", "short": "draft | active | retired | unknown", "definition": "draft | active | retired | unknown", "min": 1, "max": "1", "base": {"path": "ViewDefinition.status", "min": 1, "max": "1"}, "type": [{"code": "code"}], "binding": {"strength": "required", "valueSet": "http://hl7.org/fhir/ValueSet/publication-status"}}, {"id": "ViewDefinition.experimental", "path": "ViewDefinition.experimental", "short": "For testing purposes, not real usage", "definition": "For testing purposes, not real usage", "min": 0, "max": "1", "base": {"path": "ViewDefinition.experimental", "min": 0, "max": "1"}, "type": [{"code": "boolean"}]}, {"id": "ViewDefinition.publisher", "path": "ViewDefinition.publisher", "short": "Name of the publisher/steward (organization or individual)", "definition": "Name of the publisher/steward (organization or individual)", "min": 0, "max": "1", "base": {"path": "ViewDefinition.publisher", "min": 0, "max": "1"}, "type": [{"code": "string"}]}, {"id": "ViewDefinition.contact", "path": "ViewDefinition.contact", "short": "Contact details for the publisher", "definition": "Contact details for the publisher", "min": 0, "max": "*", "base": {"path": "ViewDefinition.contact", "min": 0, "max": "*"}, "type": [{"code": "ContactDetail"}]}, {"id": "ViewDefinition.description", "path": "ViewDefinition.description", "short": "Natural language description of the view definition", "definition": "Natural language description of the view definition", "min": 0, "max": "1", "base": {"path": "ViewDefinition.description", "min": 0, "max": "1"}, "type": [{"code": "markdown"}]}, {"id": "ViewDefinition.useContext", "path": "ViewDefinition.useContext", "short": "The context that the content is intended to support", "definition": "The context that the content is intended to support", "min": 0, "max": "*", "base": {"path": "ViewDefinition.useContext", "min": 0, "max": "*"}, "type": [{"code": "UsageContext"}]}, {"id": "ViewDefinition.copyright", "path": "ViewDefinition.copyright", "short": "Use and/or publishing restrictions", "definition": "Use and/or publishing restrictions", "min": 0, "max": "1", "base": {"path": "ViewDefinition.copyright", "min": 0, "max": "1"}, "type": [{"code": "markdown"}]}, {"id": "ViewDefinition.resource", "path": "ViewDefinition.resource", "short": "FHIR resource for the ViewDefinition", "definition": "The FHIR resource that the view is based upon, e.g. 'Patient' or 'Observation'.", "min": 1, "max": "1", "base": {"path": "ViewDefinition.resource", "min": 1, "max": "1"}, "type": [{"code": "code"}], "binding": {"strength": "required", "valueSet": "http://hl7.org/fhir/ValueSet/resource-types"}}, {"id": "ViewDefinition.fhirVersion", "path": "ViewDefinition.fhirVersion", "short": "FHIR version(s) of the resource for the ViewDefinition", "definition": "The FHIR version(s) for the FHIR resource. The value of this element is the\nformal version of the specification, without the revision number, e.g.\n[publication].[major].[minor].", "min": 0, "max": "*", "base": {"path": "ViewDefinition.fhirVersion", "min": 0, "max": "*"}, "type": [{"code": "code"}], "binding": {"strength": "required", "valueSet": "http://hl7.org/fhir/ValueSet/FHIR-version"}}, {"id": "ViewDefinition.constant", "path": "ViewDefinition.constant", "short": "Constant that can be used in FHIRPath expressions", "definition": "A constant is a value that is injected into a FHIRPath expression through the use of a FHIRPath\nexternal constant with the same name.", "min": 0, "max": "*", "base": {"path": "ViewDefinition.constant", "min": 0, "max": "*"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "source": "http://hl7.org/fhir/StructureDefinition/Element"}]}, {"id": "ViewDefinition.constant.id", "path": "ViewDefinition.constant.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "id"}], "code": "http://hl7.org/fhirpath/System.String"}], "condition": ["ele-1"], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "ViewDefinition.constant.extension", "path": "ViewDefinition.constant.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and managable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "ViewDefinition.constant.modifierExtension", "path": "ViewDefinition.constant.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and managable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R5/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "ViewDefinition.constant.name", "path": "ViewDefinition.constant.name", "short": "Name of constant (referred to in FHIRPath as %[name])", "definition": "Name of constant (referred to in FHIRPath as %[name])", "min": 1, "max": "1", "base": {"path": "ViewDefinition.constant.name", "min": 1, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "sql-name", "severity": "error", "human": "Name is limited to letters, numbers, or underscores and cannot start with an\nunderscore -- i.e. with a regular expression of: ^[A-Za-z][A-Za-z0-9_]*$ \n\n\nThis makes it usable as table names in a wide variety of databases.", "expression": "empty() or matches('^[A-Za-z][A-Za-z0-9_]*$')", "source": "http://hl7.org/fhir/uv/sql-on-fhir/StructureDefinition/ViewDefinition"}]}, {"id": "ViewDefinition.constant.value[x]", "path": "ViewDefinition.constant.value[x]", "short": "Value of constant", "definition": "The value that will be substituted in place of the constant reference. This\nis done by including `%your_constant_name` in a FHIRPath expression, which effectively converts\nthe FHIR literal defined here to a FHIRPath literal used in the path expression.\n\nSupport for additional types may be added in the future.", "min": 1, "max": "1", "base": {"path": "ViewDefinition.constant.value[x]", "min": 1, "max": "1"}, "type": [{"code": "base64Binary"}, {"code": "boolean"}, {"code": "canonical"}, {"code": "code"}, {"code": "date"}, {"code": "dateTime"}, {"code": "decimal"}, {"code": "id"}, {"code": "instant"}, {"code": "integer"}, {"code": "integer64"}, {"code": "oid"}, {"code": "string"}, {"code": "positiveInt"}, {"code": "time"}, {"code": "unsignedInt"}, {"code": "uri"}, {"code": "url"}, {"code": "uuid"}]}, {"id": "ViewDefinition.select", "path": "ViewDefinition.select", "short": "A collection of columns and nested selects to include in the view.", "definition": "The select structure defines the columns to be used in the resulting view. These are expressed\nin the `column` structure below, or in nested `select`s for nested resources.", "min": 1, "max": "*", "base": {"path": "ViewDefinition.select", "min": 1, "max": "*"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "sql-expressions", "severity": "error", "human": "Can only have at most one of `forEach` or `forEachOrNull`.", "expression": "(forEach | forEachOrNull).count() <= 1", "source": "http://hl7.org/fhir/uv/sql-on-fhir/StructureDefinition/ViewDefinition"}, {"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "source": "http://hl7.org/fhir/StructureDefinition/Element"}]}, {"id": "ViewDefinition.select.id", "path": "ViewDefinition.select.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "id"}], "code": "http://hl7.org/fhirpath/System.String"}], "condition": ["ele-1"], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "ViewDefinition.select.extension", "path": "ViewDefinition.select.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and managable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "ViewDefinition.select.modifierExtension", "path": "ViewDefinition.select.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and managable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R5/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "ViewDefinition.select.column", "path": "ViewDefinition.select.column", "short": "A column to be produced in the resulting table.", "definition": "A column to be produced in the resulting table. The column is relative to the select structure\nthat contains it.", "min": 0, "max": "*", "base": {"path": "ViewDefinition.select.column", "min": 0, "max": "*"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "source": "http://hl7.org/fhir/StructureDefinition/Element"}]}, {"id": "ViewDefinition.select.column.id", "path": "ViewDefinition.select.column.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "id"}], "code": "http://hl7.org/fhirpath/System.String"}], "condition": ["ele-1"], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "ViewDefinition.select.column.extension", "path": "ViewDefinition.select.column.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and managable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "ViewDefinition.select.column.modifierExtension", "path": "ViewDefinition.select.column.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and managable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R5/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "ViewDefinition.select.column.path", "path": "ViewDefinition.select.column.path", "short": "FHIRPath expression that creates a column and defines its content", "definition": "A FHIRPath expression that evaluates to the value that will be output in the column for each \nresource. The input context is the collection of resources of the type specified in the resource \nelement. Constants defined in Reference({constant}) can be referenced as %[name].", "min": 1, "max": "1", "base": {"path": "ViewDefinition.select.column.path", "min": 1, "max": "1"}, "type": [{"code": "string"}]}, {"id": "ViewDefinition.select.column.name", "path": "ViewDefinition.select.column.name", "short": "Column name produced in the output", "definition": "Name of the column produced in the output, must be in a database-friendly format. The column \nnames in the output must not have any duplicates.", "min": 1, "max": "1", "base": {"path": "ViewDefinition.select.column.name", "min": 1, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "sql-name", "severity": "error", "human": "Name is limited to letters, numbers, or underscores and cannot start with an\nunderscore -- i.e. with a regular expression of: ^[A-Za-z][A-Za-z0-9_]*$ \n\n\nThis makes it usable as table names in a wide variety of databases.", "expression": "empty() or matches('^[A-Za-z][A-Za-z0-9_]*$')", "source": "http://hl7.org/fhir/uv/sql-on-fhir/StructureDefinition/ViewDefinition"}]}, {"id": "ViewDefinition.select.column.description", "path": "ViewDefinition.select.column.description", "short": "Description of the column", "definition": "A human-readable description of the column.", "min": 0, "max": "1", "base": {"path": "ViewDefinition.select.column.description", "min": 0, "max": "1"}, "type": [{"code": "markdown"}]}, {"id": "ViewDefinition.select.column.collection", "path": "ViewDefinition.select.column.collection", "short": "Indicates whether the column may have multiple values.", "definition": "Indicates whether the column may have multiple values. Defaults to `false` if unset.\n\nViewDefinitions must have this set to `true` if multiple values may be returned. Implementations SHALL\nreport an error if multiple values are produced when that is not the case.", "min": 0, "max": "1", "base": {"path": "ViewDefinition.select.column.collection", "min": 0, "max": "1"}, "type": [{"code": "boolean"}]}, {"id": "ViewDefinition.select.column.type", "path": "ViewDefinition.select.column.type", "short": "A FHIR StructureDefinition URI for the column's type.", "definition": "A FHIR StructureDefinition URI for the column's type. Relative URIs are implicitly given\nthe 'http://hl7.org/fhir/StructureDefinition/' prefix. The URI may also use FHIR element ID notation to indicate\na backbone element within a structure. For instance, `Observation.referenceRange` may be specified to indicate\nthe returned type is that backbone element.\n\nThis field *must* be provided if a ViewDefinition returns a non-primitive type. Implementations should report an error\nif the returned type does not match the type set here, or if a non-primitive type is returned but this field is unset.", "min": 0, "max": "1", "base": {"path": "ViewDefinition.select.column.type", "min": 0, "max": "1"}, "type": [{"code": "uri"}]}, {"id": "ViewDefinition.select.column.tag", "path": "ViewDefinition.select.column.tag", "short": "Additional metadata describing the column", "definition": "Tags can be used to attach additional metadata to columns, such as implementation-specific \ndirectives or database-specific type hints.", "min": 0, "max": "*", "base": {"path": "ViewDefinition.select.column.tag", "min": 0, "max": "*"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "source": "http://hl7.org/fhir/StructureDefinition/Element"}]}, {"id": "ViewDefinition.select.column.tag.id", "path": "ViewDefinition.select.column.tag.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "id"}], "code": "http://hl7.org/fhirpath/System.String"}], "condition": ["ele-1"], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "ViewDefinition.select.column.tag.extension", "path": "ViewDefinition.select.column.tag.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and managable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "ViewDefinition.select.column.tag.modifierExtension", "path": "ViewDefinition.select.column.tag.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and managable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R5/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "ViewDefinition.select.column.tag.name", "path": "ViewDefinition.select.column.tag.name", "short": "Name of tag", "definition": "A name that identifies the meaning of the tag. A namespace should be used to scope the tag to \na particular context. For example, 'ansi/type' could be used to indicate the type that should \nbe used to represent the value within an ANSI SQL database.", "min": 1, "max": "1", "base": {"path": "ViewDefinition.select.column.tag.name", "min": 1, "max": "1"}, "type": [{"code": "string"}]}, {"id": "ViewDefinition.select.column.tag.value", "path": "ViewDefinition.select.column.tag.value", "short": "Value of tag", "definition": "Value of tag", "min": 1, "max": "1", "base": {"path": "ViewDefinition.select.column.tag.value", "min": 1, "max": "1"}, "type": [{"code": "string"}]}, {"id": "ViewDefinition.select.select", "path": "ViewDefinition.select.select", "short": "Nested select relative to a parent expression.", "definition": "Nested select relative to a parent expression. If the parent `select` has a `forEach` or `forEachOrNull`, this child select will apply for each item in that expression.", "min": 0, "max": "*", "base": {"path": "ViewDefinition.select.select", "min": 0, "max": "*"}, "contentReference": "http://hl7.org/fhir/uv/sql-on-fhir/StructureDefinition/ViewDefinition#ViewDefinition.select"}, {"id": "ViewDefinition.select.forEach", "path": "ViewDefinition.select.forEach", "short": "A FHIRPath expression to retrieve the parent element(s) used in the containing select. The default is effectively `$this`.", "definition": "A FHIRPath expression to retrieve the parent element(s) used in the containing select, relative to the root resource or parent `select`,\nif applicable. `forEach` will produce a row for each element selected in the expression. For example, using forEach on `address` in Patient will\ngenerate a new row for each address, with columns defined in the corresponding `column` structure.", "min": 0, "max": "1", "base": {"path": "ViewDefinition.select.forEach", "min": 0, "max": "1"}, "type": [{"code": "string"}]}, {"id": "ViewDefinition.select.forEachOrNull", "path": "ViewDefinition.select.forEachOrNull", "short": "Same as for<PERSON>ach, but will produce a row with null values if the collection is empty.", "definition": "Same as forEach, but produces a single row with null values in the nested expression if the collection is empty. For example,\nwith a Patient resource, a `forEachOrNull` on address will produce a row for each patient even if there are no addresses; it will\nsimply set the address columns to `null`.", "min": 0, "max": "1", "base": {"path": "ViewDefinition.select.forEachOrNull", "min": 0, "max": "1"}, "type": [{"code": "string"}]}, {"id": "ViewDefinition.select.unionAll", "path": "ViewDefinition.select.unionAll", "short": "Creates a union of all rows in the given selection structures.", "definition": "A `unionAll` combines the results of multiple selection structures. Each structure under the `unionAll` must produce the same column names\nand types. The results from each nested selection will then have their own row.", "min": 0, "max": "*", "base": {"path": "ViewDefinition.select.unionAll", "min": 0, "max": "*"}, "contentReference": "http://hl7.org/fhir/uv/sql-on-fhir/StructureDefinition/ViewDefinition#ViewDefinition.select"}, {"id": "ViewDefinition.where", "path": "ViewDefinition.where", "short": "A series of zero or more FHIRPath constraints to filter resources for the view.", "definition": "A series of zero or more FHIRPath constraints to filter resources for the view. Every constraint\nmust evaluate to true for the resource to be included in the view.", "min": 0, "max": "*", "base": {"path": "ViewDefinition.where", "min": 0, "max": "*"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "source": "http://hl7.org/fhir/StructureDefinition/Element"}]}, {"id": "ViewDefinition.where.id", "path": "ViewDefinition.where.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "id"}], "code": "http://hl7.org/fhirpath/System.String"}], "condition": ["ele-1"], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "ViewDefinition.where.extension", "path": "ViewDefinition.where.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and managable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false, "mapping": [{"identity": "rim", "map": "n/a"}]}, {"id": "ViewDefinition.where.modifierExtension", "path": "ViewDefinition.where.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and managable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R5/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true, "mapping": [{"identity": "rim", "map": "N/A"}]}, {"id": "ViewDefinition.where.path", "path": "ViewDefinition.where.path", "short": "A FHIRPath expression defining a filter condition", "definition": "A FHIRPath expression that defines a filter that must evaluate to true for a resource to be\nincluded in the output. The input context is the collection of resources of the type specified in\nthe resource element. Constants defined in Reference({constant}) can be referenced as %[name].", "min": 1, "max": "1", "base": {"path": "ViewDefinition.where.path", "min": 1, "max": "1"}, "type": [{"code": "string"}]}, {"id": "ViewDefinition.where.description", "path": "ViewDefinition.where.description", "short": "A human-readable description of the above where constraint.", "definition": "A human-readable description of the above where constraint.", "min": 0, "max": "1", "base": {"path": "ViewDefinition.where.description", "min": 0, "max": "1"}, "type": [{"code": "string"}]}]}, "differential": {"element": [{"id": "ViewDefinition", "path": "ViewDefinition", "short": "View Definition", "definition": "View definitions represent a tabular projection of a FHIR resource, where the columns and inclusion \ncriteria are defined by FHIRPath expressions. "}, {"id": "ViewDefinition.url", "path": "ViewDefinition.url", "short": "Canonical identifier for this view definition, represented as a URI (globally unique)", "definition": "Canonical identifier for this view definition, represented as a URI (globally unique)", "min": 0, "max": "1", "type": [{"code": "uri"}]}, {"id": "ViewDefinition.identifier", "path": "ViewDefinition.identifier", "short": "Additional identifier for the view definition", "definition": "Additional identifier for the view definition", "min": 0, "max": "1", "type": [{"code": "Identifier"}]}, {"id": "ViewDefinition.name", "path": "ViewDefinition.name", "short": "Name of view definition (computer and database friendly)", "definition": "Name of the view definition, must be in a database-friendly format.", "min": 0, "max": "1", "type": [{"code": "string"}], "constraint": [{"key": "sql-name", "severity": "error", "human": "Name is limited to letters, numbers, or underscores and cannot start with an\nunderscore -- i.e. with a regular expression of: ^[A-Za-z][A-Za-z0-9_]*$ \n\n\nThis makes it usable as table names in a wide variety of databases.", "expression": "empty() or matches('^[A-Za-z][A-Za-z0-9_]*$')", "source": "http://hl7.org/fhir/uv/sql-on-fhir/StructureDefinition/ViewDefinition"}]}, {"id": "ViewDefinition.title", "path": "ViewDefinition.title", "short": "Name for this view definition (human friendly)", "definition": "A optional human-readable description of the view.", "min": 0, "max": "1", "type": [{"code": "string"}]}, {"id": "ViewDefinition.meta", "path": "ViewDefinition.meta", "short": "<PERSON><PERSON><PERSON> about the view definition", "definition": "<PERSON><PERSON><PERSON> about the view definition", "min": 0, "max": "1", "type": [{"code": "Meta"}]}, {"id": "ViewDefinition.status", "path": "ViewDefinition.status", "short": "draft | active | retired | unknown", "definition": "draft | active | retired | unknown", "min": 1, "max": "1", "type": [{"code": "code"}], "binding": {"strength": "required", "valueSet": "http://hl7.org/fhir/ValueSet/publication-status"}}, {"id": "ViewDefinition.experimental", "path": "ViewDefinition.experimental", "short": "For testing purposes, not real usage", "definition": "For testing purposes, not real usage", "min": 0, "max": "1", "type": [{"code": "boolean"}]}, {"id": "ViewDefinition.publisher", "path": "ViewDefinition.publisher", "short": "Name of the publisher/steward (organization or individual)", "definition": "Name of the publisher/steward (organization or individual)", "min": 0, "max": "1", "type": [{"code": "string"}]}, {"id": "ViewDefinition.contact", "path": "ViewDefinition.contact", "short": "Contact details for the publisher", "definition": "Contact details for the publisher", "min": 0, "max": "*", "type": [{"code": "ContactDetail"}]}, {"id": "ViewDefinition.description", "path": "ViewDefinition.description", "short": "Natural language description of the view definition", "definition": "Natural language description of the view definition", "min": 0, "max": "1", "type": [{"code": "markdown"}]}, {"id": "ViewDefinition.useContext", "path": "ViewDefinition.useContext", "short": "The context that the content is intended to support", "definition": "The context that the content is intended to support", "min": 0, "max": "*", "type": [{"code": "UsageContext"}]}, {"id": "ViewDefinition.copyright", "path": "ViewDefinition.copyright", "short": "Use and/or publishing restrictions", "definition": "Use and/or publishing restrictions", "min": 0, "max": "1", "type": [{"code": "markdown"}]}, {"id": "ViewDefinition.resource", "path": "ViewDefinition.resource", "short": "FHIR resource for the ViewDefinition", "definition": "The FHIR resource that the view is based upon, e.g. 'Patient' or 'Observation'.", "min": 1, "max": "1", "type": [{"code": "code"}], "binding": {"strength": "required", "valueSet": "http://hl7.org/fhir/ValueSet/resource-types"}}, {"id": "ViewDefinition.fhirVersion", "path": "ViewDefinition.fhirVersion", "short": "FHIR version(s) of the resource for the ViewDefinition", "definition": "The FHIR version(s) for the FHIR resource. The value of this element is the\nformal version of the specification, without the revision number, e.g.\n[publication].[major].[minor].", "min": 0, "max": "*", "type": [{"code": "code"}], "binding": {"strength": "required", "valueSet": "http://hl7.org/fhir/ValueSet/FHIR-version"}}, {"id": "ViewDefinition.constant", "path": "ViewDefinition.constant", "short": "Constant that can be used in FHIRPath expressions", "definition": "A constant is a value that is injected into a FHIRPath expression through the use of a FHIRPath\nexternal constant with the same name.", "min": 0, "max": "*", "type": [{"code": "BackboneElement"}]}, {"id": "ViewDefinition.constant.name", "path": "ViewDefinition.constant.name", "short": "Name of constant (referred to in FHIRPath as %[name])", "definition": "Name of constant (referred to in FHIRPath as %[name])", "min": 1, "max": "1", "type": [{"code": "string"}], "constraint": [{"key": "sql-name", "severity": "error", "human": "Name is limited to letters, numbers, or underscores and cannot start with an\nunderscore -- i.e. with a regular expression of: ^[A-Za-z][A-Za-z0-9_]*$ \n\n\nThis makes it usable as table names in a wide variety of databases.", "expression": "empty() or matches('^[A-Za-z][A-Za-z0-9_]*$')", "source": "http://hl7.org/fhir/uv/sql-on-fhir/StructureDefinition/ViewDefinition"}]}, {"id": "ViewDefinition.constant.value[x]", "path": "ViewDefinition.constant.value[x]", "short": "Value of constant", "definition": "The value that will be substituted in place of the constant reference. This\nis done by including `%your_constant_name` in a FHIRPath expression, which effectively converts\nthe FHIR literal defined here to a FHIRPath literal used in the path expression.\n\nSupport for additional types may be added in the future.", "min": 1, "max": "1", "type": [{"code": "base64Binary"}, {"code": "boolean"}, {"code": "canonical"}, {"code": "code"}, {"code": "date"}, {"code": "dateTime"}, {"code": "decimal"}, {"code": "id"}, {"code": "instant"}, {"code": "integer"}, {"code": "integer64"}, {"code": "oid"}, {"code": "string"}, {"code": "positiveInt"}, {"code": "time"}, {"code": "unsignedInt"}, {"code": "uri"}, {"code": "url"}, {"code": "uuid"}]}, {"id": "ViewDefinition.select", "path": "ViewDefinition.select", "short": "A collection of columns and nested selects to include in the view.", "definition": "The select structure defines the columns to be used in the resulting view. These are expressed\nin the `column` structure below, or in nested `select`s for nested resources.", "min": 1, "max": "*", "type": [{"code": "BackboneElement"}], "constraint": [{"key": "sql-expressions", "severity": "error", "human": "Can only have at most one of `forEach` or `forEachOrNull`.", "expression": "(forEach | forEachOrNull).count() <= 1", "source": "http://hl7.org/fhir/uv/sql-on-fhir/StructureDefinition/ViewDefinition"}]}, {"id": "ViewDefinition.select.column", "path": "ViewDefinition.select.column", "short": "A column to be produced in the resulting table.", "definition": "A column to be produced in the resulting table. The column is relative to the select structure\nthat contains it.", "min": 0, "max": "*", "type": [{"code": "BackboneElement"}]}, {"id": "ViewDefinition.select.column.path", "path": "ViewDefinition.select.column.path", "short": "FHIRPath expression that creates a column and defines its content", "definition": "A FHIRPath expression that evaluates to the value that will be output in the column for each \nresource. The input context is the collection of resources of the type specified in the resource \nelement. Constants defined in Reference({constant}) can be referenced as %[name].", "min": 1, "max": "1", "type": [{"code": "string"}]}, {"id": "ViewDefinition.select.column.name", "path": "ViewDefinition.select.column.name", "short": "Column name produced in the output", "definition": "Name of the column produced in the output, must be in a database-friendly format. The column \nnames in the output must not have any duplicates.", "min": 1, "max": "1", "type": [{"code": "string"}], "constraint": [{"key": "sql-name", "severity": "error", "human": "Name is limited to letters, numbers, or underscores and cannot start with an\nunderscore -- i.e. with a regular expression of: ^[A-Za-z][A-Za-z0-9_]*$ \n\n\nThis makes it usable as table names in a wide variety of databases.", "expression": "empty() or matches('^[A-Za-z][A-Za-z0-9_]*$')", "source": "http://hl7.org/fhir/uv/sql-on-fhir/StructureDefinition/ViewDefinition"}]}, {"id": "ViewDefinition.select.column.description", "path": "ViewDefinition.select.column.description", "short": "Description of the column", "definition": "A human-readable description of the column.", "min": 0, "max": "1", "type": [{"code": "markdown"}]}, {"id": "ViewDefinition.select.column.collection", "path": "ViewDefinition.select.column.collection", "short": "Indicates whether the column may have multiple values.", "definition": "Indicates whether the column may have multiple values. Defaults to `false` if unset.\n\nViewDefinitions must have this set to `true` if multiple values may be returned. Implementations SHALL\nreport an error if multiple values are produced when that is not the case.", "min": 0, "max": "1", "type": [{"code": "boolean"}]}, {"id": "ViewDefinition.select.column.type", "path": "ViewDefinition.select.column.type", "short": "A FHIR StructureDefinition URI for the column's type.", "definition": "A FHIR StructureDefinition URI for the column's type. Relative URIs are implicitly given\nthe 'http://hl7.org/fhir/StructureDefinition/' prefix. The URI may also use FHIR element ID notation to indicate\na backbone element within a structure. For instance, `Observation.referenceRange` may be specified to indicate\nthe returned type is that backbone element.\n\nThis field *must* be provided if a ViewDefinition returns a non-primitive type. Implementations should report an error\nif the returned type does not match the type set here, or if a non-primitive type is returned but this field is unset.", "min": 0, "max": "1", "type": [{"code": "uri"}]}, {"id": "ViewDefinition.select.column.tag", "path": "ViewDefinition.select.column.tag", "short": "Additional metadata describing the column", "definition": "Tags can be used to attach additional metadata to columns, such as implementation-specific \ndirectives or database-specific type hints.", "min": 0, "max": "*", "type": [{"code": "BackboneElement"}]}, {"id": "ViewDefinition.select.column.tag.name", "path": "ViewDefinition.select.column.tag.name", "short": "Name of tag", "definition": "A name that identifies the meaning of the tag. A namespace should be used to scope the tag to \na particular context. For example, 'ansi/type' could be used to indicate the type that should \nbe used to represent the value within an ANSI SQL database.", "min": 1, "max": "1", "type": [{"code": "string"}]}, {"id": "ViewDefinition.select.column.tag.value", "path": "ViewDefinition.select.column.tag.value", "short": "Value of tag", "definition": "Value of tag", "min": 1, "max": "1", "type": [{"code": "string"}]}, {"id": "ViewDefinition.select.select", "path": "ViewDefinition.select.select", "short": "Nested select relative to a parent expression.", "definition": "Nested select relative to a parent expression. If the parent `select` has a `forEach` or `forEachOrNull`, this child select will apply for each item in that expression. ", "min": 0, "max": "*", "contentReference": "http://hl7.org/fhir/uv/sql-on-fhir/StructureDefinition/ViewDefinition#ViewDefinition.select"}, {"id": "ViewDefinition.select.forEach", "path": "ViewDefinition.select.forEach", "short": "A FHIRPath expression to retrieve the parent element(s) used in the containing select. The default is effectively `$this`.", "definition": "A FHIRPath expression to retrieve the parent element(s) used in the containing select, relative to the root resource or parent `select`,\nif applicable. `forEach` will produce a row for each element selected in the expression. For example, using forEach on `address` in Patient will\ngenerate a new row for each address, with columns defined in the corresponding `column` structure.", "min": 0, "max": "1", "type": [{"code": "string"}]}, {"id": "ViewDefinition.select.forEachOrNull", "path": "ViewDefinition.select.forEachOrNull", "short": "Same as for<PERSON>ach, but will produce a row with null values if the collection is empty.", "definition": "Same as forEach, but produces a single row with null values in the nested expression if the collection is empty. For example,\nwith a Patient resource, a `forEachOrNull` on address will produce a row for each patient even if there are no addresses; it will\nsimply set the address columns to `null`.", "min": 0, "max": "1", "type": [{"code": "string"}]}, {"id": "ViewDefinition.select.unionAll", "path": "ViewDefinition.select.unionAll", "short": "Creates a union of all rows in the given selection structures.", "definition": "A `unionAll` combines the results of multiple selection structures. Each structure under the `unionAll` must produce the same column names\nand types. The results from each nested selection will then have their own row.", "min": 0, "max": "*", "contentReference": "http://hl7.org/fhir/uv/sql-on-fhir/StructureDefinition/ViewDefinition#ViewDefinition.select"}, {"id": "ViewDefinition.where", "path": "ViewDefinition.where", "short": "A series of zero or more FHIRPath constraints to filter resources for the view.", "definition": "A series of zero or more FHIRPath constraints to filter resources for the view. Every constraint\nmust evaluate to true for the resource to be included in the view.", "min": 0, "max": "*", "type": [{"code": "BackboneElement"}]}, {"id": "ViewDefinition.where.path", "path": "ViewDefinition.where.path", "short": "A FHIRPath expression defining a filter condition", "definition": "A FHIRPath expression that defines a filter that must evaluate to true for a resource to be\nincluded in the output. The input context is the collection of resources of the type specified in\nthe resource element. Constants defined in Reference({constant}) can be referenced as %[name].", "min": 1, "max": "1", "type": [{"code": "string"}]}, {"id": "ViewDefinition.where.description", "path": "ViewDefinition.where.description", "short": "A human-readable description of the above where constraint.", "definition": "A human-readable description of the above where constraint.", "min": 0, "max": "1", "type": [{"code": "string"}]}]}}}]}