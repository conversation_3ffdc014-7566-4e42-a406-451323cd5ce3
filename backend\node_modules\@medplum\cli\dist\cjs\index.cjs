#!/usr/bin/env node
"use strict";var Oi=Object.create;var be=Object.defineProperty;var xi=Object.getOwnPropertyDescriptor;var $i=Object.getOwnPropertyNames;var Ni=Object.getPrototypeOf,Li=Object.prototype.hasOwnProperty;var ki=(t,e,r)=>e in t?be(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var w=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),Di=(t,e)=>{for(var r in e)be(t,r,{get:e[r],enumerable:!0})},_r=(t,e,r,o)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of $i(e))!Li.call(t,n)&&n!==r&&be(t,n,{get:()=>e[n],enumerable:!(o=xi(e,n))||o.enumerable});return t};var $=(t,e,r)=>(r=t!=null?Oi(Ni(t)):{},_r(e||!t||!t.__esModule?be(r,"default",{value:t,enumerable:!0}):r,t)),Mi=t=>_r(be({},"__esModule",{value:!0}),t);var v=(t,e,r)=>ki(t,typeof e!="symbol"?e+"":e,r);var Le=w((Wd,No)=>{"use strict";var Ea="2.0.0",Sa=Number.MAX_SAFE_INTEGER||9007199254740991,Ra=16,Aa=250,Ia=["major","premajor","minor","preminor","patch","prepatch","prerelease"];No.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:Ra,MAX_SAFE_BUILD_LENGTH:Aa,MAX_SAFE_INTEGER:Sa,RELEASE_TYPES:Ia,SEMVER_SPEC_VERSION:Ea,FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}});var ke=w((Kd,Lo)=>{"use strict";var va=typeof process=="object"&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...t)=>console.error("SEMVER",...t):()=>{};Lo.exports=va});var he=w((W,ko)=>{"use strict";var{MAX_SAFE_COMPONENT_LENGTH:or,MAX_SAFE_BUILD_LENGTH:ba,MAX_LENGTH:Pa}=Le(),Ca=ke();W=ko.exports={};var Ta=W.re=[],Oa=W.safeRe=[],d=W.src=[],xa=W.safeSrc=[],m=W.t={},$a=0,nr="[a-zA-Z0-9-]",Na=[["\\s",1],["\\d",Pa],[nr,ba]],La=t=>{for(let[e,r]of Na)t=t.split(`${e}*`).join(`${e}{0,${r}}`).split(`${e}+`).join(`${e}{1,${r}}`);return t},E=(t,e,r)=>{let o=La(e),n=$a++;Ca(t,n,e),m[t]=n,d[n]=e,xa[n]=o,Ta[n]=new RegExp(e,r?"g":void 0),Oa[n]=new RegExp(o,r?"g":void 0)};E("NUMERICIDENTIFIER","0|[1-9]\\d*");E("NUMERICIDENTIFIERLOOSE","\\d+");E("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${nr}*`);E("MAINVERSION",`(${d[m.NUMERICIDENTIFIER]})\\.(${d[m.NUMERICIDENTIFIER]})\\.(${d[m.NUMERICIDENTIFIER]})`);E("MAINVERSIONLOOSE",`(${d[m.NUMERICIDENTIFIERLOOSE]})\\.(${d[m.NUMERICIDENTIFIERLOOSE]})\\.(${d[m.NUMERICIDENTIFIERLOOSE]})`);E("PRERELEASEIDENTIFIER",`(?:${d[m.NONNUMERICIDENTIFIER]}|${d[m.NUMERICIDENTIFIER]})`);E("PRERELEASEIDENTIFIERLOOSE",`(?:${d[m.NONNUMERICIDENTIFIER]}|${d[m.NUMERICIDENTIFIERLOOSE]})`);E("PRERELEASE",`(?:-(${d[m.PRERELEASEIDENTIFIER]}(?:\\.${d[m.PRERELEASEIDENTIFIER]})*))`);E("PRERELEASELOOSE",`(?:-?(${d[m.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${d[m.PRERELEASEIDENTIFIERLOOSE]})*))`);E("BUILDIDENTIFIER",`${nr}+`);E("BUILD",`(?:\\+(${d[m.BUILDIDENTIFIER]}(?:\\.${d[m.BUILDIDENTIFIER]})*))`);E("FULLPLAIN",`v?${d[m.MAINVERSION]}${d[m.PRERELEASE]}?${d[m.BUILD]}?`);E("FULL",`^${d[m.FULLPLAIN]}$`);E("LOOSEPLAIN",`[v=\\s]*${d[m.MAINVERSIONLOOSE]}${d[m.PRERELEASELOOSE]}?${d[m.BUILD]}?`);E("LOOSE",`^${d[m.LOOSEPLAIN]}$`);E("GTLT","((?:<|>)?=?)");E("XRANGEIDENTIFIERLOOSE",`${d[m.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`);E("XRANGEIDENTIFIER",`${d[m.NUMERICIDENTIFIER]}|x|X|\\*`);E("XRANGEPLAIN",`[v=\\s]*(${d[m.XRANGEIDENTIFIER]})(?:\\.(${d[m.XRANGEIDENTIFIER]})(?:\\.(${d[m.XRANGEIDENTIFIER]})(?:${d[m.PRERELEASE]})?${d[m.BUILD]}?)?)?`);E("XRANGEPLAINLOOSE",`[v=\\s]*(${d[m.XRANGEIDENTIFIERLOOSE]})(?:\\.(${d[m.XRANGEIDENTIFIERLOOSE]})(?:\\.(${d[m.XRANGEIDENTIFIERLOOSE]})(?:${d[m.PRERELEASELOOSE]})?${d[m.BUILD]}?)?)?`);E("XRANGE",`^${d[m.GTLT]}\\s*${d[m.XRANGEPLAIN]}$`);E("XRANGELOOSE",`^${d[m.GTLT]}\\s*${d[m.XRANGEPLAINLOOSE]}$`);E("COERCEPLAIN",`(^|[^\\d])(\\d{1,${or}})(?:\\.(\\d{1,${or}}))?(?:\\.(\\d{1,${or}}))?`);E("COERCE",`${d[m.COERCEPLAIN]}(?:$|[^\\d])`);E("COERCEFULL",d[m.COERCEPLAIN]+`(?:${d[m.PRERELEASE]})?(?:${d[m.BUILD]})?(?:$|[^\\d])`);E("COERCERTL",d[m.COERCE],!0);E("COERCERTLFULL",d[m.COERCEFULL],!0);E("LONETILDE","(?:~>?)");E("TILDETRIM",`(\\s*)${d[m.LONETILDE]}\\s+`,!0);W.tildeTrimReplace="$1~";E("TILDE",`^${d[m.LONETILDE]}${d[m.XRANGEPLAIN]}$`);E("TILDELOOSE",`^${d[m.LONETILDE]}${d[m.XRANGEPLAINLOOSE]}$`);E("LONECARET","(?:\\^)");E("CARETTRIM",`(\\s*)${d[m.LONECARET]}\\s+`,!0);W.caretTrimReplace="$1^";E("CARET",`^${d[m.LONECARET]}${d[m.XRANGEPLAIN]}$`);E("CARETLOOSE",`^${d[m.LONECARET]}${d[m.XRANGEPLAINLOOSE]}$`);E("COMPARATORLOOSE",`^${d[m.GTLT]}\\s*(${d[m.LOOSEPLAIN]})$|^$`);E("COMPARATOR",`^${d[m.GTLT]}\\s*(${d[m.FULLPLAIN]})$|^$`);E("COMPARATORTRIM",`(\\s*)${d[m.GTLT]}\\s*(${d[m.LOOSEPLAIN]}|${d[m.XRANGEPLAIN]})`,!0);W.comparatorTrimReplace="$1$2$3";E("HYPHENRANGE",`^\\s*(${d[m.XRANGEPLAIN]})\\s+-\\s+(${d[m.XRANGEPLAIN]})\\s*$`);E("HYPHENRANGELOOSE",`^\\s*(${d[m.XRANGEPLAINLOOSE]})\\s+-\\s+(${d[m.XRANGEPLAINLOOSE]})\\s*$`);E("STAR","(<|>)?=?\\s*\\*");E("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$");E("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")});var rt=w((qd,Do)=>{"use strict";var ka=Object.freeze({loose:!0}),Da=Object.freeze({}),Ma=t=>t?typeof t!="object"?ka:t:Da;Do.exports=Ma});var sr=w((Hd,Uo)=>{"use strict";var Mo=/^[0-9]+$/,_o=(t,e)=>{let r=Mo.test(t),o=Mo.test(e);return r&&o&&(t=+t,e=+e),t===e?0:r&&!o?-1:o&&!r?1:t<e?-1:1},_a=(t,e)=>_o(e,t);Uo.exports={compareIdentifiers:_o,rcompareIdentifiers:_a}});var P=w((Gd,Fo)=>{"use strict";var ot=ke(),{MAX_LENGTH:jo,MAX_SAFE_INTEGER:nt}=Le(),{safeRe:st,t:it}=he(),Ua=rt(),{compareIdentifiers:ge}=sr(),ir=class t{constructor(e,r){if(r=Ua(r),e instanceof t){if(e.loose===!!r.loose&&e.includePrerelease===!!r.includePrerelease)return e;e=e.version}else if(typeof e!="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>jo)throw new TypeError(`version is longer than ${jo} characters`);ot("SemVer",e,r),this.options=r,this.loose=!!r.loose,this.includePrerelease=!!r.includePrerelease;let o=e.trim().match(r.loose?st[it.LOOSE]:st[it.FULL]);if(!o)throw new TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+o[1],this.minor=+o[2],this.patch=+o[3],this.major>nt||this.major<0)throw new TypeError("Invalid major version");if(this.minor>nt||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>nt||this.patch<0)throw new TypeError("Invalid patch version");o[4]?this.prerelease=o[4].split(".").map(n=>{if(/^[0-9]+$/.test(n)){let s=+n;if(s>=0&&s<nt)return s}return n}):this.prerelease=[],this.build=o[5]?o[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(ot("SemVer.compare",this.version,this.options,e),!(e instanceof t)){if(typeof e=="string"&&e===this.version)return 0;e=new t(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof t||(e=new t(e,this.options)),ge(this.major,e.major)||ge(this.minor,e.minor)||ge(this.patch,e.patch)}comparePre(e){if(e instanceof t||(e=new t(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let r=0;do{let o=this.prerelease[r],n=e.prerelease[r];if(ot("prerelease compare",r,o,n),o===void 0&&n===void 0)return 0;if(n===void 0)return 1;if(o===void 0)return-1;if(o===n)continue;return ge(o,n)}while(++r)}compareBuild(e){e instanceof t||(e=new t(e,this.options));let r=0;do{let o=this.build[r],n=e.build[r];if(ot("build compare",r,o,n),o===void 0&&n===void 0)return 0;if(n===void 0)return 1;if(o===void 0)return-1;if(o===n)continue;return ge(o,n)}while(++r)}inc(e,r,o){if(e.startsWith("pre")){if(!r&&o===!1)throw new Error("invalid increment argument: identifier is empty");if(r){let n=`-${r}`.match(this.options.loose?st[it.PRERELEASELOOSE]:st[it.PRERELEASE]);if(!n||n[1]!==r)throw new Error(`invalid identifier: ${r}`)}}switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",r,o);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",r,o);break;case"prepatch":this.prerelease.length=0,this.inc("patch",r,o),this.inc("pre",r,o);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",r,o),this.inc("pre",r,o);break;case"release":if(this.prerelease.length===0)throw new Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":{let n=Number(o)?1:0;if(this.prerelease.length===0)this.prerelease=[n];else{let s=this.prerelease.length;for(;--s>=0;)typeof this.prerelease[s]=="number"&&(this.prerelease[s]++,s=-2);if(s===-1){if(r===this.prerelease.join(".")&&o===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(n)}}if(r){let s=[r,n];o===!1&&(s=[r]),ge(this.prerelease[0],r)===0?isNaN(this.prerelease[1])&&(this.prerelease=s):this.prerelease=s}break}default:throw new Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}};Fo.exports=ir});var ce=w((Vd,Wo)=>{"use strict";var Bo=P(),ja=(t,e,r=!1)=>{if(t instanceof Bo)return t;try{return new Bo(t,e)}catch(o){if(!r)return null;throw o}};Wo.exports=ja});var qo=w((Jd,Ko)=>{"use strict";var Fa=ce(),Ba=(t,e)=>{let r=Fa(t,e);return r?r.version:null};Ko.exports=Ba});var Go=w((Xd,Ho)=>{"use strict";var Wa=ce(),Ka=(t,e)=>{let r=Wa(t.trim().replace(/^[=v]+/,""),e);return r?r.version:null};Ho.exports=Ka});var Xo=w((zd,Jo)=>{"use strict";var Vo=P(),qa=(t,e,r,o,n)=>{typeof r=="string"&&(n=o,o=r,r=void 0);try{return new Vo(t instanceof Vo?t.version:t,r).inc(e,o,n).version}catch{return null}};Jo.exports=qa});var Zo=w((Yd,Yo)=>{"use strict";var zo=ce(),Ha=(t,e)=>{let r=zo(t,null,!0),o=zo(e,null,!0),n=r.compare(o);if(n===0)return null;let s=n>0,i=s?r:o,c=s?o:r,l=!!i.prerelease.length;if(!!c.prerelease.length&&!l){if(!c.patch&&!c.minor)return"major";if(c.compareMain(i)===0)return c.minor&&!c.patch?"minor":"patch"}let h=l?"pre":"";return r.major!==o.major?h+"major":r.minor!==o.minor?h+"minor":r.patch!==o.patch?h+"patch":"prerelease"};Yo.exports=Ha});var en=w((Zd,Qo)=>{"use strict";var Ga=P(),Va=(t,e)=>new Ga(t,e).major;Qo.exports=Va});var rn=w((Qd,tn)=>{"use strict";var Ja=P(),Xa=(t,e)=>new Ja(t,e).minor;tn.exports=Xa});var nn=w((em,on)=>{"use strict";var za=P(),Ya=(t,e)=>new za(t,e).patch;on.exports=Ya});var an=w((tm,sn)=>{"use strict";var Za=ce(),Qa=(t,e)=>{let r=Za(t,e);return r&&r.prerelease.length?r.prerelease:null};sn.exports=Qa});var L=w((rm,ln)=>{"use strict";var cn=P(),ec=(t,e,r)=>new cn(t,r).compare(new cn(e,r));ln.exports=ec});var pn=w((om,un)=>{"use strict";var tc=L(),rc=(t,e,r)=>tc(e,t,r);un.exports=rc});var mn=w((nm,dn)=>{"use strict";var oc=L(),nc=(t,e)=>oc(t,e,!0);dn.exports=nc});var at=w((sm,hn)=>{"use strict";var fn=P(),sc=(t,e,r)=>{let o=new fn(t,r),n=new fn(e,r);return o.compare(n)||o.compareBuild(n)};hn.exports=sc});var yn=w((im,gn)=>{"use strict";var ic=at(),ac=(t,e)=>t.sort((r,o)=>ic(r,o,e));gn.exports=ac});var En=w((am,wn)=>{"use strict";var cc=at(),lc=(t,e)=>t.sort((r,o)=>cc(o,r,e));wn.exports=lc});var De=w((cm,Sn)=>{"use strict";var uc=L(),pc=(t,e,r)=>uc(t,e,r)>0;Sn.exports=pc});var ct=w((lm,Rn)=>{"use strict";var dc=L(),mc=(t,e,r)=>dc(t,e,r)<0;Rn.exports=mc});var ar=w((um,An)=>{"use strict";var fc=L(),hc=(t,e,r)=>fc(t,e,r)===0;An.exports=hc});var cr=w((pm,In)=>{"use strict";var gc=L(),yc=(t,e,r)=>gc(t,e,r)!==0;In.exports=yc});var lt=w((dm,vn)=>{"use strict";var wc=L(),Ec=(t,e,r)=>wc(t,e,r)>=0;vn.exports=Ec});var ut=w((mm,bn)=>{"use strict";var Sc=L(),Rc=(t,e,r)=>Sc(t,e,r)<=0;bn.exports=Rc});var lr=w((fm,Pn)=>{"use strict";var Ac=ar(),Ic=cr(),vc=De(),bc=lt(),Pc=ct(),Cc=ut(),Tc=(t,e,r,o)=>{switch(e){case"===":return typeof t=="object"&&(t=t.version),typeof r=="object"&&(r=r.version),t===r;case"!==":return typeof t=="object"&&(t=t.version),typeof r=="object"&&(r=r.version),t!==r;case"":case"=":case"==":return Ac(t,r,o);case"!=":return Ic(t,r,o);case">":return vc(t,r,o);case">=":return bc(t,r,o);case"<":return Pc(t,r,o);case"<=":return Cc(t,r,o);default:throw new TypeError(`Invalid operator: ${e}`)}};Pn.exports=Tc});var Tn=w((hm,Cn)=>{"use strict";var Oc=P(),xc=ce(),{safeRe:pt,t:dt}=he(),$c=(t,e)=>{if(t instanceof Oc)return t;if(typeof t=="number"&&(t=String(t)),typeof t!="string")return null;e=e||{};let r=null;if(!e.rtl)r=t.match(e.includePrerelease?pt[dt.COERCEFULL]:pt[dt.COERCE]);else{let l=e.includePrerelease?pt[dt.COERCERTLFULL]:pt[dt.COERCERTL],u;for(;(u=l.exec(t))&&(!r||r.index+r[0].length!==t.length);)(!r||u.index+u[0].length!==r.index+r[0].length)&&(r=u),l.lastIndex=u.index+u[1].length+u[2].length;l.lastIndex=-1}if(r===null)return null;let o=r[2],n=r[3]||"0",s=r[4]||"0",i=e.includePrerelease&&r[5]?`-${r[5]}`:"",c=e.includePrerelease&&r[6]?`+${r[6]}`:"";return xc(`${o}.${n}.${s}${i}${c}`,e)};Cn.exports=$c});var xn=w((gm,On)=>{"use strict";var ur=class{constructor(){this.max=1e3,this.map=new Map}get(e){let r=this.map.get(e);if(r!==void 0)return this.map.delete(e),this.map.set(e,r),r}delete(e){return this.map.delete(e)}set(e,r){if(!this.delete(e)&&r!==void 0){if(this.map.size>=this.max){let n=this.map.keys().next().value;this.delete(n)}this.map.set(e,r)}return this}};On.exports=ur});var k=w((ym,kn)=>{"use strict";var Nc=/\s+/g,pr=class t{constructor(e,r){if(r=kc(r),e instanceof t)return e.loose===!!r.loose&&e.includePrerelease===!!r.includePrerelease?e:new t(e.raw,r);if(e instanceof dr)return this.raw=e.value,this.set=[[e]],this.formatted=void 0,this;if(this.options=r,this.loose=!!r.loose,this.includePrerelease=!!r.includePrerelease,this.raw=e.trim().replace(Nc," "),this.set=this.raw.split("||").map(o=>this.parseRange(o.trim())).filter(o=>o.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){let o=this.set[0];if(this.set=this.set.filter(n=>!Nn(n[0])),this.set.length===0)this.set=[o];else if(this.set.length>1){for(let n of this.set)if(n.length===1&&Bc(n[0])){this.set=[n];break}}}this.formatted=void 0}get range(){if(this.formatted===void 0){this.formatted="";for(let e=0;e<this.set.length;e++){e>0&&(this.formatted+="||");let r=this.set[e];for(let o=0;o<r.length;o++)o>0&&(this.formatted+=" "),this.formatted+=r[o].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){let o=((this.options.includePrerelease&&jc)|(this.options.loose&&Fc))+":"+e,n=$n.get(o);if(n)return n;let s=this.options.loose,i=s?O[C.HYPHENRANGELOOSE]:O[C.HYPHENRANGE];e=e.replace(i,Yc(this.options.includePrerelease)),A("hyphen replace",e),e=e.replace(O[C.COMPARATORTRIM],Mc),A("comparator trim",e),e=e.replace(O[C.TILDETRIM],_c),A("tilde trim",e),e=e.replace(O[C.CARETTRIM],Uc),A("caret trim",e);let c=e.split(" ").map(f=>Wc(f,this.options)).join(" ").split(/\s+/).map(f=>zc(f,this.options));s&&(c=c.filter(f=>(A("loose invalid filter",f,this.options),!!f.match(O[C.COMPARATORLOOSE])))),A("range list",c);let l=new Map,u=c.map(f=>new dr(f,this.options));for(let f of u){if(Nn(f))return[f];l.set(f.value,f)}l.size>1&&l.has("")&&l.delete("");let h=[...l.values()];return $n.set(o,h),h}intersects(e,r){if(!(e instanceof t))throw new TypeError("a Range is required");return this.set.some(o=>Ln(o,r)&&e.set.some(n=>Ln(n,r)&&o.every(s=>n.every(i=>s.intersects(i,r)))))}test(e){if(!e)return!1;if(typeof e=="string")try{e=new Dc(e,this.options)}catch{return!1}for(let r=0;r<this.set.length;r++)if(Zc(this.set[r],e,this.options))return!0;return!1}};kn.exports=pr;var Lc=xn(),$n=new Lc,kc=rt(),dr=Me(),A=ke(),Dc=P(),{safeRe:O,t:C,comparatorTrimReplace:Mc,tildeTrimReplace:_c,caretTrimReplace:Uc}=he(),{FLAG_INCLUDE_PRERELEASE:jc,FLAG_LOOSE:Fc}=Le(),Nn=t=>t.value==="<0.0.0-0",Bc=t=>t.value==="",Ln=(t,e)=>{let r=!0,o=t.slice(),n=o.pop();for(;r&&o.length;)r=o.every(s=>n.intersects(s,e)),n=o.pop();return r},Wc=(t,e)=>(A("comp",t,e),t=Hc(t,e),A("caret",t),t=Kc(t,e),A("tildes",t),t=Vc(t,e),A("xrange",t),t=Xc(t,e),A("stars",t),t),T=t=>!t||t.toLowerCase()==="x"||t==="*",Kc=(t,e)=>t.trim().split(/\s+/).map(r=>qc(r,e)).join(" "),qc=(t,e)=>{let r=e.loose?O[C.TILDELOOSE]:O[C.TILDE];return t.replace(r,(o,n,s,i,c)=>{A("tilde",t,o,n,s,i,c);let l;return T(n)?l="":T(s)?l=`>=${n}.0.0 <${+n+1}.0.0-0`:T(i)?l=`>=${n}.${s}.0 <${n}.${+s+1}.0-0`:c?(A("replaceTilde pr",c),l=`>=${n}.${s}.${i}-${c} <${n}.${+s+1}.0-0`):l=`>=${n}.${s}.${i} <${n}.${+s+1}.0-0`,A("tilde return",l),l})},Hc=(t,e)=>t.trim().split(/\s+/).map(r=>Gc(r,e)).join(" "),Gc=(t,e)=>{A("caret",t,e);let r=e.loose?O[C.CARETLOOSE]:O[C.CARET],o=e.includePrerelease?"-0":"";return t.replace(r,(n,s,i,c,l)=>{A("caret",t,n,s,i,c,l);let u;return T(s)?u="":T(i)?u=`>=${s}.0.0${o} <${+s+1}.0.0-0`:T(c)?s==="0"?u=`>=${s}.${i}.0${o} <${s}.${+i+1}.0-0`:u=`>=${s}.${i}.0${o} <${+s+1}.0.0-0`:l?(A("replaceCaret pr",l),s==="0"?i==="0"?u=`>=${s}.${i}.${c}-${l} <${s}.${i}.${+c+1}-0`:u=`>=${s}.${i}.${c}-${l} <${s}.${+i+1}.0-0`:u=`>=${s}.${i}.${c}-${l} <${+s+1}.0.0-0`):(A("no pr"),s==="0"?i==="0"?u=`>=${s}.${i}.${c}${o} <${s}.${i}.${+c+1}-0`:u=`>=${s}.${i}.${c}${o} <${s}.${+i+1}.0-0`:u=`>=${s}.${i}.${c} <${+s+1}.0.0-0`),A("caret return",u),u})},Vc=(t,e)=>(A("replaceXRanges",t,e),t.split(/\s+/).map(r=>Jc(r,e)).join(" ")),Jc=(t,e)=>{t=t.trim();let r=e.loose?O[C.XRANGELOOSE]:O[C.XRANGE];return t.replace(r,(o,n,s,i,c,l)=>{A("xRange",t,o,n,s,i,c,l);let u=T(s),h=u||T(i),f=h||T(c),S=f;return n==="="&&S&&(n=""),l=e.includePrerelease?"-0":"",u?n===">"||n==="<"?o="<0.0.0-0":o="*":n&&S?(h&&(i=0),c=0,n===">"?(n=">=",h?(s=+s+1,i=0,c=0):(i=+i+1,c=0)):n==="<="&&(n="<",h?s=+s+1:i=+i+1),n==="<"&&(l="-0"),o=`${n+s}.${i}.${c}${l}`):h?o=`>=${s}.0.0${l} <${+s+1}.0.0-0`:f&&(o=`>=${s}.${i}.0${l} <${s}.${+i+1}.0-0`),A("xRange return",o),o})},Xc=(t,e)=>(A("replaceStars",t,e),t.trim().replace(O[C.STAR],"")),zc=(t,e)=>(A("replaceGTE0",t,e),t.trim().replace(O[e.includePrerelease?C.GTE0PRE:C.GTE0],"")),Yc=t=>(e,r,o,n,s,i,c,l,u,h,f,S)=>(T(o)?r="":T(n)?r=`>=${o}.0.0${t?"-0":""}`:T(s)?r=`>=${o}.${n}.0${t?"-0":""}`:i?r=`>=${r}`:r=`>=${r}${t?"-0":""}`,T(u)?l="":T(h)?l=`<${+u+1}.0.0-0`:T(f)?l=`<${u}.${+h+1}.0-0`:S?l=`<=${u}.${h}.${f}-${S}`:t?l=`<${u}.${h}.${+f+1}-0`:l=`<=${l}`,`${r} ${l}`.trim()),Zc=(t,e,r)=>{for(let o=0;o<t.length;o++)if(!t[o].test(e))return!1;if(e.prerelease.length&&!r.includePrerelease){for(let o=0;o<t.length;o++)if(A(t[o].semver),t[o].semver!==dr.ANY&&t[o].semver.prerelease.length>0){let n=t[o].semver;if(n.major===e.major&&n.minor===e.minor&&n.patch===e.patch)return!0}return!1}return!0}});var Me=w((wm,Fn)=>{"use strict";var _e=Symbol("SemVer ANY"),hr=class t{static get ANY(){return _e}constructor(e,r){if(r=Dn(r),e instanceof t){if(e.loose===!!r.loose)return e;e=e.value}e=e.trim().split(/\s+/).join(" "),fr("comparator",e,r),this.options=r,this.loose=!!r.loose,this.parse(e),this.semver===_e?this.value="":this.value=this.operator+this.semver.version,fr("comp",this)}parse(e){let r=this.options.loose?Mn[_n.COMPARATORLOOSE]:Mn[_n.COMPARATOR],o=e.match(r);if(!o)throw new TypeError(`Invalid comparator: ${e}`);this.operator=o[1]!==void 0?o[1]:"",this.operator==="="&&(this.operator=""),o[2]?this.semver=new Un(o[2],this.options.loose):this.semver=_e}toString(){return this.value}test(e){if(fr("Comparator.test",e,this.options.loose),this.semver===_e||e===_e)return!0;if(typeof e=="string")try{e=new Un(e,this.options)}catch{return!1}return mr(e,this.operator,this.semver,this.options)}intersects(e,r){if(!(e instanceof t))throw new TypeError("a Comparator is required");return this.operator===""?this.value===""?!0:new jn(e.value,r).test(this.value):e.operator===""?e.value===""?!0:new jn(this.value,r).test(e.semver):(r=Dn(r),r.includePrerelease&&(this.value==="<0.0.0-0"||e.value==="<0.0.0-0")||!r.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0"))?!1:!!(this.operator.startsWith(">")&&e.operator.startsWith(">")||this.operator.startsWith("<")&&e.operator.startsWith("<")||this.semver.version===e.semver.version&&this.operator.includes("=")&&e.operator.includes("=")||mr(this.semver,"<",e.semver,r)&&this.operator.startsWith(">")&&e.operator.startsWith("<")||mr(this.semver,">",e.semver,r)&&this.operator.startsWith("<")&&e.operator.startsWith(">")))}};Fn.exports=hr;var Dn=rt(),{safeRe:Mn,t:_n}=he(),mr=lr(),fr=ke(),Un=P(),jn=k()});var Ue=w((Em,Bn)=>{"use strict";var Qc=k(),el=(t,e,r)=>{try{e=new Qc(e,r)}catch{return!1}return e.test(t)};Bn.exports=el});var Kn=w((Sm,Wn)=>{"use strict";var tl=k(),rl=(t,e)=>new tl(t,e).set.map(r=>r.map(o=>o.value).join(" ").trim().split(" "));Wn.exports=rl});var Hn=w((Rm,qn)=>{"use strict";var ol=P(),nl=k(),sl=(t,e,r)=>{let o=null,n=null,s=null;try{s=new nl(e,r)}catch{return null}return t.forEach(i=>{s.test(i)&&(!o||n.compare(i)===-1)&&(o=i,n=new ol(o,r))}),o};qn.exports=sl});var Vn=w((Am,Gn)=>{"use strict";var il=P(),al=k(),cl=(t,e,r)=>{let o=null,n=null,s=null;try{s=new al(e,r)}catch{return null}return t.forEach(i=>{s.test(i)&&(!o||n.compare(i)===1)&&(o=i,n=new il(o,r))}),o};Gn.exports=cl});var zn=w((Im,Xn)=>{"use strict";var gr=P(),ll=k(),Jn=De(),ul=(t,e)=>{t=new ll(t,e);let r=new gr("0.0.0");if(t.test(r)||(r=new gr("0.0.0-0"),t.test(r)))return r;r=null;for(let o=0;o<t.set.length;++o){let n=t.set[o],s=null;n.forEach(i=>{let c=new gr(i.semver.version);switch(i.operator){case">":c.prerelease.length===0?c.patch++:c.prerelease.push(0),c.raw=c.format();case"":case">=":(!s||Jn(c,s))&&(s=c);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${i.operator}`)}}),s&&(!r||Jn(r,s))&&(r=s)}return r&&t.test(r)?r:null};Xn.exports=ul});var Zn=w((vm,Yn)=>{"use strict";var pl=k(),dl=(t,e)=>{try{return new pl(t,e).range||"*"}catch{return null}};Yn.exports=dl});var mt=w((bm,rs)=>{"use strict";var ml=P(),ts=Me(),{ANY:fl}=ts,hl=k(),gl=Ue(),Qn=De(),es=ct(),yl=ut(),wl=lt(),El=(t,e,r,o)=>{t=new ml(t,o),e=new hl(e,o);let n,s,i,c,l;switch(r){case">":n=Qn,s=yl,i=es,c=">",l=">=";break;case"<":n=es,s=wl,i=Qn,c="<",l="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(gl(t,e,o))return!1;for(let u=0;u<e.set.length;++u){let h=e.set[u],f=null,S=null;if(h.forEach(p=>{p.semver===fl&&(p=new ts(">=0.0.0")),f=f||p,S=S||p,n(p.semver,f.semver,o)?f=p:i(p.semver,S.semver,o)&&(S=p)}),f.operator===c||f.operator===l||(!S.operator||S.operator===c)&&s(t,S.semver))return!1;if(S.operator===l&&i(t,S.semver))return!1}return!0};rs.exports=El});var ns=w((Pm,os)=>{"use strict";var Sl=mt(),Rl=(t,e,r)=>Sl(t,e,">",r);os.exports=Rl});var is=w((Cm,ss)=>{"use strict";var Al=mt(),Il=(t,e,r)=>Al(t,e,"<",r);ss.exports=Il});var ls=w((Tm,cs)=>{"use strict";var as=k(),vl=(t,e,r)=>(t=new as(t,r),e=new as(e,r),t.intersects(e,r));cs.exports=vl});var ps=w((Om,us)=>{"use strict";var bl=Ue(),Pl=L();us.exports=(t,e,r)=>{let o=[],n=null,s=null,i=t.sort((h,f)=>Pl(h,f,r));for(let h of i)bl(h,e,r)?(s=h,n||(n=h)):(s&&o.push([n,s]),s=null,n=null);n&&o.push([n,null]);let c=[];for(let[h,f]of o)h===f?c.push(h):!f&&h===i[0]?c.push("*"):f?h===i[0]?c.push(`<=${f}`):c.push(`${h} - ${f}`):c.push(`>=${h}`);let l=c.join(" || "),u=typeof e.raw=="string"?e.raw:String(e);return l.length<u.length?l:e}});var ys=w((xm,gs)=>{"use strict";var ds=k(),wr=Me(),{ANY:yr}=wr,je=Ue(),Er=L(),Cl=(t,e,r={})=>{if(t===e)return!0;t=new ds(t,r),e=new ds(e,r);let o=!1;e:for(let n of t.set){for(let s of e.set){let i=Ol(n,s,r);if(o=o||i!==null,i)continue e}if(o)return!1}return!0},Tl=[new wr(">=0.0.0-0")],ms=[new wr(">=0.0.0")],Ol=(t,e,r)=>{if(t===e)return!0;if(t.length===1&&t[0].semver===yr){if(e.length===1&&e[0].semver===yr)return!0;r.includePrerelease?t=Tl:t=ms}if(e.length===1&&e[0].semver===yr){if(r.includePrerelease)return!0;e=ms}let o=new Set,n,s;for(let p of t)p.operator===">"||p.operator===">="?n=fs(n,p,r):p.operator==="<"||p.operator==="<="?s=hs(s,p,r):o.add(p.semver);if(o.size>1)return null;let i;if(n&&s){if(i=Er(n.semver,s.semver,r),i>0)return null;if(i===0&&(n.operator!==">="||s.operator!=="<="))return null}for(let p of o){if(n&&!je(p,String(n),r)||s&&!je(p,String(s),r))return null;for(let K of e)if(!je(p,String(K),r))return!1;return!0}let c,l,u,h,f=s&&!r.includePrerelease&&s.semver.prerelease.length?s.semver:!1,S=n&&!r.includePrerelease&&n.semver.prerelease.length?n.semver:!1;f&&f.prerelease.length===1&&s.operator==="<"&&f.prerelease[0]===0&&(f=!1);for(let p of e){if(h=h||p.operator===">"||p.operator===">=",u=u||p.operator==="<"||p.operator==="<=",n){if(S&&p.semver.prerelease&&p.semver.prerelease.length&&p.semver.major===S.major&&p.semver.minor===S.minor&&p.semver.patch===S.patch&&(S=!1),p.operator===">"||p.operator===">="){if(c=fs(n,p,r),c===p&&c!==n)return!1}else if(n.operator===">="&&!je(n.semver,String(p),r))return!1}if(s){if(f&&p.semver.prerelease&&p.semver.prerelease.length&&p.semver.major===f.major&&p.semver.minor===f.minor&&p.semver.patch===f.patch&&(f=!1),p.operator==="<"||p.operator==="<="){if(l=hs(s,p,r),l===p&&l!==s)return!1}else if(s.operator==="<="&&!je(s.semver,String(p),r))return!1}if(!p.operator&&(s||n)&&i!==0)return!1}return!(n&&u&&!s&&i!==0||s&&h&&!n&&i!==0||S||f)},fs=(t,e,r)=>{if(!t)return e;let o=Er(t.semver,e.semver,r);return o>0?t:o<0||e.operator===">"&&t.operator===">="?e:t},hs=(t,e,r)=>{if(!t)return e;let o=Er(t.semver,e.semver,r);return o<0?t:o>0||e.operator==="<"&&t.operator==="<="?e:t};gs.exports=Cl});var Rr=w(($m,Ss)=>{"use strict";var Sr=he(),ws=Le(),xl=P(),Es=sr(),$l=ce(),Nl=qo(),Ll=Go(),kl=Xo(),Dl=Zo(),Ml=en(),_l=rn(),Ul=nn(),jl=an(),Fl=L(),Bl=pn(),Wl=mn(),Kl=at(),ql=yn(),Hl=En(),Gl=De(),Vl=ct(),Jl=ar(),Xl=cr(),zl=lt(),Yl=ut(),Zl=lr(),Ql=Tn(),eu=Me(),tu=k(),ru=Ue(),ou=Kn(),nu=Hn(),su=Vn(),iu=zn(),au=Zn(),cu=mt(),lu=ns(),uu=is(),pu=ls(),du=ps(),mu=ys();Ss.exports={parse:$l,valid:Nl,clean:Ll,inc:kl,diff:Dl,major:Ml,minor:_l,patch:Ul,prerelease:jl,compare:Fl,rcompare:Bl,compareLoose:Wl,compareBuild:Kl,sort:ql,rsort:Hl,gt:Gl,lt:Vl,eq:Jl,neq:Xl,gte:zl,lte:Yl,cmp:Zl,coerce:Ql,Comparator:eu,Range:tu,satisfies:ru,toComparators:ou,maxSatisfying:nu,minSatisfying:su,minVersion:iu,validRange:au,outside:cu,gtr:lu,ltr:uu,intersects:pu,simplifyRange:du,subset:mu,SemVer:xl,re:Sr.re,src:Sr.src,tokens:Sr.t,SEMVER_SPEC_VERSION:ws.SEMVER_SPEC_VERSION,RELEASE_TYPES:ws.RELEASE_TYPES,compareIdentifiers:Es.compareIdentifiers,rcompareIdentifiers:Es.rcompareIdentifiers}});var Zu={};Di(Zu,{handleError:()=>Pi,main:()=>bi,run:()=>Ci});module.exports=Mi(Zu);var ve=require("@medplum/core"),He=require("commander"),vi=$(require("dotenv"));var se=require("@medplum/core"),tt=require("commander");var Fr=require("@medplum/core");var Ur=require("@medplum/core"),q=require("node:fs"),jr=require("node:os"),Mt=require("node:path"),j=class extends Ur.ClientStorage{constructor(e){super(),this.dirName=(0,Mt.resolve)((0,jr.homedir)(),".medplum"),this.fileName=(0,Mt.resolve)(this.dirName,e+".json")}clear(){this.writeFile({})}getString(e){return this.readFile()?.[e]}setString(e,r){let o=this.readFile()??{};r?o[e]=r:delete o[e],this.writeFile(o)}getObject(e){let r=this.getString(e);return r?JSON.parse(r):void 0}setObject(e,r){this.setString(e,r?JSON.stringify(r):void 0)}readFile(){if((0,q.existsSync)(this.fileName))return JSON.parse((0,q.readFileSync)(this.fileName,"utf8"))}writeFile(e){(0,q.existsSync)(this.dirName)||(0,q.mkdirSync)(this.dirName),(0,q.writeFileSync)(this.fileName,JSON.stringify(e,null,2),"utf8")}};async function R(t,e=!0){let r=t.profile??"default",o=new j(r),n=o.getObject("options");if(r!=="default"&&!n)throw new Error(`Profile "${r}" does not exist`);let{baseUrl:s,fhirUrlPath:i,accessToken:c,tokenUrl:l,authorizeUrl:u,clientId:h,clientSecret:f}=_i(t,o),S=t.fetch??fetch,p=new Fr.MedplumClient({fetch:S,baseUrl:s,tokenUrl:l,fhirUrlPath:i,authorizeUrl:u,storage:o,onUnauthenticated:Ui,verbose:t.verbose});return e&&(c?p.setAccessToken(c):h&&f&&(p.setBasicAuth(h,f),n?.authType!=="basic"&&await p.startClientLogin(h,f))),p}function _i(t,e){let r=e.getObject("options"),o=t.baseUrl??r?.baseUrl??process.env.MEDPLUM_BASE_URL??"https://api.medplum.com/",n=t.fhirUrlPath??r?.fhirUrlPath??process.env.MEDPLUM_FHIR_URL_PATH,s=t.accessToken??r?.accessToken??process.env.MEDPLUM_CLIENT_ACCESS_TOKEN,i=t.tokenUrl??r?.tokenUrl??process.env.MEDPLUM_TOKEN_URL,c=t.authorizeUrl??r?.authorizeUrl??process.env.MEDPLUM_AUTHORIZE_URL,l=t.clientId??r?.clientId??process.env.MEDPLUM_CLIENT_ID,u=t.clientSecret??r?.clientSecret??process.env.MEDPLUM_CLIENT_SECRET;return{baseUrl:o,fhirUrlPath:n,accessToken:s,tokenUrl:i,authorizeUrl:c,clientId:l,clientSecret:u}}function Ui(){console.log("Unauthenticated: run `npx medplum login` to sign in")}var ne=require("@medplum/core"),uo=require("commander");var Wr=require("node:buffer");var te=new TextEncoder,_t=new TextDecoder,np=2**32;function Br(...t){let e=t.reduce((n,{length:s})=>n+s,0),r=new Uint8Array(e),o=0;for(let n of t)r.set(n,o),o+=n.length;return r}var Ge=t=>Wr.Buffer.from(t).toString("base64url");var re=class extends Error{constructor(r,o){super(r,o);v(this,"code","ERR_JOSE_GENERIC");this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}};v(re,"code","ERR_JOSE_GENERIC");var N=class extends re{constructor(){super(...arguments);v(this,"code","ERR_JOSE_NOT_SUPPORTED")}};v(N,"code","ERR_JOSE_NOT_SUPPORTED");var H=class extends re{constructor(){super(...arguments);v(this,"code","ERR_JWS_INVALID")}};v(H,"code","ERR_JWS_INVALID");var Pe=class extends re{constructor(){super(...arguments);v(this,"code","ERR_JWT_INVALID")}};v(Pe,"code","ERR_JWT_INVALID");var Kr,qr,Ut=class extends(qr=re,Kr=Symbol.asyncIterator,qr){constructor(r="multiple matching keys found in the JSON Web Key Set",o){super(r,o);v(this,Kr);v(this,"code","ERR_JWKS_MULTIPLE_MATCHING_KEYS")}};v(Ut,"code","ERR_JWKS_MULTIPLE_MATCHING_KEYS");var Hr=$(require("node:util"),1),Ve=t=>Hr.types.isKeyObject(t);var Gr=$(require("node:crypto"),1),Vr=$(require("node:util"),1),Fi=Gr.webcrypto,Jr=Fi,pe=t=>Vr.types.isCryptoKey(t);function F(t,e="algorithm.name"){return new TypeError(`CryptoKey does not support this operation, its ${e} must be ${t}`)}function Ce(t,e){return t.name===e}function jt(t){return parseInt(t.name.slice(4),10)}function Bi(t){switch(t){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw new Error("unreachable")}}function Wi(t,e){if(e.length&&!e.some(r=>t.usages.includes(r))){let r="CryptoKey does not support this operation, its usages must include ";if(e.length>2){let o=e.pop();r+=`one of ${e.join(", ")}, or ${o}.`}else e.length===2?r+=`one of ${e[0]} or ${e[1]}.`:r+=`${e[0]}.`;throw new TypeError(r)}}function Xr(t,e,...r){switch(e){case"HS256":case"HS384":case"HS512":{if(!Ce(t.algorithm,"HMAC"))throw F("HMAC");let o=parseInt(e.slice(2),10);if(jt(t.algorithm.hash)!==o)throw F(`SHA-${o}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!Ce(t.algorithm,"RSASSA-PKCS1-v1_5"))throw F("RSASSA-PKCS1-v1_5");let o=parseInt(e.slice(2),10);if(jt(t.algorithm.hash)!==o)throw F(`SHA-${o}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!Ce(t.algorithm,"RSA-PSS"))throw F("RSA-PSS");let o=parseInt(e.slice(2),10);if(jt(t.algorithm.hash)!==o)throw F(`SHA-${o}`,"algorithm.hash");break}case"EdDSA":{if(t.algorithm.name!=="Ed25519"&&t.algorithm.name!=="Ed448")throw F("Ed25519 or Ed448");break}case"Ed25519":{if(!Ce(t.algorithm,"Ed25519"))throw F("Ed25519");break}case"ES256":case"ES384":case"ES512":{if(!Ce(t.algorithm,"ECDSA"))throw F("ECDSA");let o=Bi(e);if(t.algorithm.namedCurve!==o)throw F(o,"algorithm.namedCurve");break}default:throw new TypeError("CryptoKey does not support this operation")}Wi(t,r)}function zr(t,e,...r){if(r=r.filter(Boolean),r.length>2){let o=r.pop();t+=`one of type ${r.join(", ")}, or ${o}.`}else r.length===2?t+=`one of type ${r[0]} or ${r[1]}.`:t+=`of type ${r[0]}.`;return e==null?t+=` Received ${e}`:typeof e=="function"&&e.name?t+=` Received function ${e.name}`:typeof e=="object"&&e!=null&&e.constructor?.name&&(t+=` Received an instance of ${e.constructor.name}`),t}var Te=(t,...e)=>zr("Key must be ",t,...e);function Ft(t,e,...r){return zr(`Key for the ${t} algorithm must be `,e,...r)}var Bt=t=>Ve(t)||pe(t),G=["KeyObject"];(globalThis.CryptoKey||Jr?.CryptoKey)&&G.push("CryptoKey");var Ki=(...t)=>{let e=t.filter(Boolean);if(e.length===0||e.length===1)return!0;let r;for(let o of e){let n=Object.keys(o);if(!r||r.size===0){r=new Set(n);continue}for(let s of n){if(r.has(s))return!1;r.add(s)}}return!0},Yr=Ki;function qi(t){return typeof t=="object"&&t!==null}function Oe(t){if(!qi(t)||Object.prototype.toString.call(t)!=="[object Object]")return!1;if(Object.getPrototypeOf(t)===null)return!0;let e=t;for(;Object.getPrototypeOf(e)!==null;)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e}var ro=require("node:crypto");function z(t){return Oe(t)&&typeof t.kty=="string"}function Zr(t){return t.kty!=="oct"&&typeof t.d=="string"}function Qr(t){return t.kty!=="oct"&&typeof t.d>"u"}function eo(t){return z(t)&&t.kty==="oct"&&typeof t.k=="string"}var Hi=t=>{switch(t){case"prime256v1":return"P-256";case"secp384r1":return"P-384";case"secp521r1":return"P-521";case"secp256k1":return"secp256k1";default:throw new N("Unsupported key curve for this operation")}},Gi=(t,e)=>{let r;if(pe(t))r=ro.KeyObject.from(t);else if(Ve(t))r=t;else{if(z(t))return t.crv;throw new TypeError(Te(t,...G))}if(r.type==="secret")throw new TypeError('only "private" or "public" type keys can be used for this operation');switch(r.asymmetricKeyType){case"ed25519":case"ed448":return`Ed${r.asymmetricKeyType.slice(2)}`;case"x25519":case"x448":return`X${r.asymmetricKeyType.slice(1)}`;case"ec":{let o=r.asymmetricKeyDetails.namedCurve;return e?o:Hi(o)}default:throw new TypeError("Invalid asymmetric key type for this operation")}},oo=Gi;var no=require("node:crypto"),Wt=(t,e)=>{let r;try{t instanceof no.KeyObject?r=t.asymmetricKeyDetails?.modulusLength:r=Buffer.from(t.n,"base64url").byteLength<<3}catch{}if(typeof r!="number"||r<2048)throw new TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)};var de=t=>t?.[Symbol.toStringTag],Kt=(t,e,r)=>{if(e.use!==void 0&&e.use!=="sig")throw new TypeError("Invalid key for this operation, when present its use must be sig");if(e.key_ops!==void 0&&e.key_ops.includes?.(r)!==!0)throw new TypeError(`Invalid key for this operation, when present its key_ops must include ${r}`);if(e.alg!==void 0&&e.alg!==t)throw new TypeError(`Invalid key for this operation, when present its alg must be ${t}`);return!0},Vi=(t,e,r,o)=>{if(!(e instanceof Uint8Array)){if(o&&z(e)){if(eo(e)&&Kt(t,e,r))return;throw new TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!Bt(e))throw new TypeError(Ft(t,e,...G,"Uint8Array",o?"JSON Web Key":null));if(e.type!=="secret")throw new TypeError(`${de(e)} instances for symmetric algorithms must be of type "secret"`)}},Ji=(t,e,r,o)=>{if(o&&z(e))switch(r){case"sign":if(Zr(e)&&Kt(t,e,r))return;throw new TypeError("JSON Web Key for this operation be a private JWK");case"verify":if(Qr(e)&&Kt(t,e,r))return;throw new TypeError("JSON Web Key for this operation be a public JWK")}if(!Bt(e))throw new TypeError(Ft(t,e,...G,o?"JSON Web Key":null));if(e.type==="secret")throw new TypeError(`${de(e)} instances for asymmetric algorithms must not be of type "secret"`);if(r==="sign"&&e.type==="public")throw new TypeError(`${de(e)} instances for asymmetric algorithm signing must be of type "private"`);if(r==="decrypt"&&e.type==="public")throw new TypeError(`${de(e)} instances for asymmetric algorithm decryption must be of type "private"`);if(e.algorithm&&r==="verify"&&e.type==="private")throw new TypeError(`${de(e)} instances for asymmetric algorithm verifying must be of type "public"`);if(e.algorithm&&r==="encrypt"&&e.type==="private")throw new TypeError(`${de(e)} instances for asymmetric algorithm encryption must be of type "public"`)};function so(t,e,r,o){e.startsWith("HS")||e==="dir"||e.startsWith("PBES2")||/^A\d{3}(?:GCM)?KW$/.test(e)?Vi(e,r,o,t):Ji(e,r,o,t)}var Tp=so.bind(void 0,!1),io=so.bind(void 0,!0);function Xi(t,e,r,o,n){if(n.crit!==void 0&&o?.crit===void 0)throw new t('"crit" (Critical) Header Parameter MUST be integrity protected');if(!o||o.crit===void 0)return new Set;if(!Array.isArray(o.crit)||o.crit.length===0||o.crit.some(i=>typeof i!="string"||i.length===0))throw new t('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');let s;r!==void 0?s=new Map([...Object.entries(r),...e.entries()]):s=e;for(let i of o.crit){if(!s.has(i))throw new N(`Extension Header Parameter "${i}" is not recognized`);if(n[i]===void 0)throw new t(`Extension Header Parameter "${i}" is missing`);if(s.get(i)&&o[i]===void 0)throw new t(`Extension Header Parameter "${i}" MUST be integrity protected`)}return new Set(o.crit)}var ao=Xi;function qt(t){switch(t){case"PS256":case"RS256":case"ES256":case"ES256K":return"sha256";case"PS384":case"RS384":case"ES384":return"sha384";case"PS512":case"RS512":case"ES512":return"sha512";case"Ed25519":case"EdDSA":return;default:throw new N(`alg ${t} is not supported either by JOSE or your javascript runtime`)}}var xe=require("node:crypto");var zi=new Map([["ES256","P-256"],["ES256K","secp256k1"],["ES384","P-384"],["ES512","P-521"]]);function Ht(t,e){let r,o,n;if(e instanceof xe.KeyObject)r=e.asymmetricKeyType,o=e.asymmetricKeyDetails;else switch(n=!0,e.kty){case"RSA":r="rsa";break;case"EC":r="ec";break;case"OKP":{if(e.crv==="Ed25519"){r="ed25519";break}if(e.crv==="Ed448"){r="ed448";break}throw new TypeError("Invalid key for this operation, its crv must be Ed25519 or Ed448")}default:throw new TypeError("Invalid key for this operation, its kty must be RSA, OKP, or EC")}let s;switch(t){case"Ed25519":if(r!=="ed25519")throw new TypeError("Invalid key for this operation, its asymmetricKeyType must be ed25519");break;case"EdDSA":if(!["ed25519","ed448"].includes(r))throw new TypeError("Invalid key for this operation, its asymmetricKeyType must be ed25519 or ed448");break;case"RS256":case"RS384":case"RS512":if(r!=="rsa")throw new TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa");Wt(e,t);break;case"PS256":case"PS384":case"PS512":if(r==="rsa-pss"){let{hashAlgorithm:i,mgf1HashAlgorithm:c,saltLength:l}=o,u=parseInt(t.slice(-3),10);if(i!==void 0&&(i!==`sha${u}`||c!==i))throw new TypeError(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of "alg" ${t}`);if(l!==void 0&&l>u>>3)throw new TypeError(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of "alg" ${t}`)}else if(r!=="rsa")throw new TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa or rsa-pss");Wt(e,t),s={padding:xe.constants.RSA_PKCS1_PSS_PADDING,saltLength:xe.constants.RSA_PSS_SALTLEN_DIGEST};break;case"ES256":case"ES256K":case"ES384":case"ES512":{if(r!=="ec")throw new TypeError("Invalid key for this operation, its asymmetricKeyType must be ec");let i=oo(e),c=zi.get(t);if(i!==c)throw new TypeError(`Invalid key curve for the algorithm, its curve must be ${c}, got ${i}`);s={dsaEncoding:"ieee-p1363"};break}default:throw new N(`alg ${t} is not supported either by JOSE or your javascript runtime`)}return n?{format:"jwk",key:e,...s}:s?{...s,key:e}:e}var Je=$(require("node:crypto"),1),co=require("node:util");function Gt(t){switch(t){case"HS256":return"sha256";case"HS384":return"sha384";case"HS512":return"sha512";default:throw new N(`alg ${t} is not supported either by JOSE or your javascript runtime`)}}var me=require("node:crypto");function Vt(t,e,r){if(e instanceof Uint8Array){if(!t.startsWith("HS"))throw new TypeError(Te(e,...G));return(0,me.createSecretKey)(e)}if(e instanceof me.KeyObject)return e;if(pe(e))return Xr(e,t,r),me.KeyObject.from(e);if(z(e))return t.startsWith("HS")?(0,me.createSecretKey)(Buffer.from(e.k,"base64url")):e;throw new TypeError(Te(e,...G,"Uint8Array","JSON Web Key"))}var Yi=(0,co.promisify)(Je.sign),Zi=async(t,e,r)=>{let o=Vt(t,e,"sign");if(t.startsWith("HS")){let n=Je.createHmac(Gt(t),o);return n.update(r),n.digest()}return Yi(qt(t),r,Ht(t,o))},lo=Zi;var Y=t=>Math.floor(t.getTime()/1e3);var Qi=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,Xe=t=>{let e=Qi.exec(t);if(!e||e[4]&&e[1])throw new TypeError("Invalid time period format");let r=parseFloat(e[2]),o=e[3].toLowerCase(),n;switch(o){case"sec":case"secs":case"second":case"seconds":case"s":n=Math.round(r);break;case"minute":case"minutes":case"min":case"mins":case"m":n=Math.round(r*60);break;case"hour":case"hours":case"hr":case"hrs":case"h":n=Math.round(r*3600);break;case"day":case"days":case"d":n=Math.round(r*86400);break;case"week":case"weeks":case"w":n=Math.round(r*604800);break;default:n=Math.round(r*31557600);break}return e[1]==="-"||e[4]==="ago"?-n:n};var ze=class{constructor(e){v(this,"_payload");v(this,"_protectedHeader");v(this,"_unprotectedHeader");if(!(e instanceof Uint8Array))throw new TypeError("payload must be an instance of Uint8Array");this._payload=e}setProtectedHeader(e){if(this._protectedHeader)throw new TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setUnprotectedHeader(e){if(this._unprotectedHeader)throw new TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}async sign(e,r){if(!this._protectedHeader&&!this._unprotectedHeader)throw new H("either setProtectedHeader or setUnprotectedHeader must be called before #sign()");if(!Yr(this._protectedHeader,this._unprotectedHeader))throw new H("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let o={...this._protectedHeader,...this._unprotectedHeader},n=ao(H,new Map([["b64",!0]]),r?.crit,this._protectedHeader,o),s=!0;if(n.has("b64")&&(s=this._protectedHeader.b64,typeof s!="boolean"))throw new H('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:i}=o;if(typeof i!="string"||!i)throw new H('JWS "alg" (Algorithm) Header Parameter missing or invalid');io(i,e,"sign");let c=this._payload;s&&(c=te.encode(Ge(c)));let l;this._protectedHeader?l=te.encode(Ge(JSON.stringify(this._protectedHeader))):l=te.encode("");let u=Br(l,te.encode("."),c),h=await lo(i,e,u),f={signature:Ge(h),payload:""};return s&&(f.payload=_t.decode(c)),this._unprotectedHeader&&(f.header=this._unprotectedHeader),this._protectedHeader&&(f.protected=_t.decode(l)),f}};var Ye=class{constructor(e){v(this,"_flattened");this._flattened=new ze(e)}setProtectedHeader(e){return this._flattened.setProtectedHeader(e),this}async sign(e,r){let o=await this._flattened.sign(e,r);if(o.payload===void 0)throw new TypeError("use the flattened module for creating JWS with b64: false");return`${o.protected}.${o.payload}.${o.signature}`}};function oe(t,e){if(!Number.isFinite(e))throw new TypeError(`Invalid ${t} input`);return e}var Ze=class{constructor(e={}){v(this,"_payload");if(!Oe(e))throw new TypeError("JWT Claims Set MUST be an object");this._payload=e}setIssuer(e){return this._payload={...this._payload,iss:e},this}setSubject(e){return this._payload={...this._payload,sub:e},this}setAudience(e){return this._payload={...this._payload,aud:e},this}setJti(e){return this._payload={...this._payload,jti:e},this}setNotBefore(e){return typeof e=="number"?this._payload={...this._payload,nbf:oe("setNotBefore",e)}:e instanceof Date?this._payload={...this._payload,nbf:oe("setNotBefore",Y(e))}:this._payload={...this._payload,nbf:Y(new Date)+Xe(e)},this}setExpirationTime(e){return typeof e=="number"?this._payload={...this._payload,exp:oe("setExpirationTime",e)}:e instanceof Date?this._payload={...this._payload,exp:oe("setExpirationTime",Y(e))}:this._payload={...this._payload,exp:Y(new Date)+Xe(e)},this}setIssuedAt(e){return typeof e>"u"?this._payload={...this._payload,iat:Y(new Date)}:e instanceof Date?this._payload={...this._payload,iat:oe("setIssuedAt",Y(e))}:typeof e=="string"?this._payload={...this._payload,iat:oe("setIssuedAt",Y(new Date)+Xe(e))}:this._payload={...this._payload,iat:oe("setIssuedAt",e)},this}};var $e=class extends Ze{constructor(){super(...arguments);v(this,"_protectedHeader")}setProtectedHeader(r){return this._protectedHeader=r,this}async sign(r,o){let n=new Ye(te.encode(JSON.stringify(this._payload)));if(n.setProtectedHeader(this._protectedHeader),Array.isArray(this._protectedHeader?.crit)&&this._protectedHeader.crit.includes("b64")&&this._protectedHeader.b64===!1)throw new Pe("JWTs MUST NOT use unencoded payload");return n.sign(r,o)}};var fe=require("node:crypto"),Z=require("node:fs"),V=require("node:path"),po=require("node:util/types"),mo=require("tar");function J(t){console.log(JSON.stringify(t,null,2))}async function Jt(t,e,r){let o=e.source,n=Qe(o);if(!n)return;console.log("Saving source code...");let s=await t.createAttachment({data:n,filename:(0,V.basename)(o),contentType:ra(o)});console.log("Updating bot...");let i=await t.updateResource({...r,sourceCode:s});console.log("Success! New bot version: "+i.meta?.versionId)}async function Xt(t,e,r){let o=e.dist??e.source,n=Qe(o);if(!n)return;console.log("Deploying bot...");let s=await t.post(t.fhirUrl("Bot",r.id,"$deploy"),{code:n,filename:(0,V.basename)(o)});console.log("Deploy result: "+s.issue?.[0]?.details?.text)}async function zt(t,e,r,o,n,s,i){let c={name:e,description:"",runtimeVersion:s},l=await t.post("admin/projects/"+r+"/bot",c),u=await t.readResource("Bot",l.id),h={name:e,id:l.id,source:o,dist:n};await Jt(t,h,u),await Xt(t,h,u),console.log(`Success! Bot created: ${u.id}`),i&&ea(h)}function fo(t){let e=new RegExp("^"+ta(t).replace(/\\\*/g,".*")+"$"),r=B()?.bots?.filter(o=>e.test(o.name));return r||[]}function M(t,e){if(e?.file)return e.file;let r=["medplum"];return t&&r.push(t),r.push("config"),e?.server&&r.push("server"),r.push("json"),r.join(".")}function b(t,e){(0,Z.writeFileSync)((0,V.resolve)(t),JSON.stringify(e,void 0,2),"utf-8")}function B(t,e){let r=M(t,e),o=Qe(r);if(o)return JSON.parse(o)}function ho(t){let e=Qe(M(t,{server:!0}));if(e)return JSON.parse(e)}function Qe(t){let e=(0,V.resolve)(t);return(0,Z.existsSync)(e)?(0,Z.readFileSync)(e,"utf8"):""}function ea(t){let e=B()??{};e.bots||(e.bots=[]),e.bots.push(t),(0,Z.writeFileSync)("medplum.config.json",JSON.stringify(e,null,2),"utf8"),console.log(`Bot added to config: ${t.id}`)}function ta(t){return t.replace(/[/\-\\^$*+?.()|[\]{}]/g,"\\$&")}function go(t){let o=0,n=0;return(0,mo.extract)({cwd:t,filter:(s,i)=>{if(o++,o>100)throw new Error("Tar extractor reached max number of files");if(n+=i.size,n>10485760)throw new Error("Tar extractor reached max size");return!0}})}function Yt(){return{url:"http://hl7.org/fhir/StructureDefinition/data-absent-reason",valueCode:"unsupported"}}function ra(t){let e=(0,V.extname)(t).toLowerCase();return[".cjs",".mjs",".js"].includes(e)?ne.ContentType.JAVASCRIPT:[".cts",".mts",".ts"].includes(e)?ne.ContentType.TYPESCRIPT:ne.ContentType.TEXT}function et(t,e){let r=new j(t),o={name:t,...e};return r.setObject("options",o),o}function yo(t){return new j(t).getObject("options")}async function wo(t,e){let r={typ:"JWT",alg:"HS256"},o=Math.floor(Date.now()/1e3),n={aud:`${e.baseUrl}${e.audience}`,iss:e.issuer,sub:e.subject,nbf:o,iat:o,exp:o+604800},s=(0,ne.encodeBase64)(JSON.stringify(r)),i=(0,ne.encodeBase64)(JSON.stringify(n)),c=`${s}.${i}`,l=(0,fe.createHmac)("sha256",e.clientSecret).update(c).digest("base64url"),u=`${c}.${l}`;await t.startJwtBearerLogin(e.clientId,u,e.scope??"")}async function Eo(t,e){let r=(0,fe.createPrivateKey)((0,Z.readFileSync)((0,V.resolve)(e.privateKeyPath))),o=await new $e({}).setProtectedHeader({alg:"RS384",typ:"JWT"}).setIssuer(e.clientId).setSubject(e.clientId).setAudience(`${e.baseUrl}${e.audience}`).setJti((0,fe.randomBytes)(16).toString("hex")).setIssuedAt().setExpirationTime("5m").sign(r);await t.startJwtAssertionLogin(o)}function y(t,e){e.configureHelp({showGlobalOptions:!0}),t.addCommand(e)}var g=class extends uo.Command{action(e){let r=oa(this,e);return super._actionHandler=r,this}resetOptionDefaults(){this._optionValues={};for(let e of this.options)e.defaultValue!==void 0&&(this._optionValues[e.attributeName()]=e.defaultValue)}};function oa(t,e){return async r=>{let o=t.registeredArguments.length,n=r.slice(0,o);n[o]=t.optsWithGlobals();try{let s=e(...n);(0,po.isPromise)(s)&&await s}finally{t.resetOptionDefaults()}}}var So=new g("status").aliases(["info","list","ls"]),Ro=new g("ping"),Ao=new g("push"),Io=new g("reload-config"),vo=new g("upgrade"),ie=new g("agent");y(ie,So);y(ie,Ro);y(ie,Ao);y(ie,Io);y(ie,vo);So.description("Get the status of a specified agent").argument("[agentIds...]","The ID(s) of the agent(s) to get the status of").option("--criteria <criteria>","An optional FHIR search criteria to resolve the agent to get the status of. Mutually exclusive with [agentIds...] arg").addOption(new tt.Option("--output <format>","An optional output format, defaults to table").choices(["table","json"]).default("table")).action(async(t,e)=>{await Zt({operation:"$bulk-status",agentIds:t,options:e,parseSuccessfulResponse:r=>{let o=ia(r.result,{required:["status","version"],optional:["lastUpdated"]});return{id:r.agent.id,name:r.agent.name,enabledStatus:r.agent.status,version:o.version,connectionStatus:o.status,statusLastUpdated:o.lastUpdated??"N/A"}}})});Ro.description("Ping a host from a specified agent").argument("<ipOrDomain>","The IPv4 address or domain name to ping").argument("[agentId]","Conditionally optional ID of the agent to ping from. Mutually exclusive with --criteria <criteria> option").option("--count <count>","An optional amount of pings to issue before returning results","1").option("--criteria <criteria>","An optional FHIR search criteria to resolve the agent to ping from. Mutually exclusive with [agentId] arg").action(async(t,e,r)=>{let o=await R(r),n=await bo(o,e,r),s=Number.parseInt(r.count,10);if(Number.isNaN(s))throw new Error("--count <count> must be an integer if specified");try{let i=await o.pushToAgent(n,t,`PING ${s}`,se.ContentType.PING,!0,{maxRetries:0});console.info(i)}catch(i){throw new Error("Unexpected response from agent while pinging",{cause:i})}});Ao.description("Push a message to a target device via a specified agent").argument("<deviceId>","The ID of the device to push the message to").argument("<message>","The message to send to the destination device").argument("[agentId]","Conditionally optional ID of the agent to send the message from. Mutually exclusive with --criteria <criteria> option").option("--content-type <contentType>","The content type of the message",se.ContentType.HL7_V2).option("--no-wait","Tells the server not to wait for a response from the destination device").option("--criteria <criteria>","An optional FHIR search criteria to resolve the agent to ping from. Mutually exclusive with [agentId] arg").action(async(t,e,r,o)=>{let n=await R(o),s=await bo(n,r,o),i;try{i=await n.pushToAgent(s,{reference:`Device/${t}`},e,o.contentType,o.wait!==!1,{maxRetries:0})}catch(c){throw new Error("Unexpected response from agent while pushing message to agent",{cause:c})}console.info(i)});Io.description("Reload the config for the specified agent(s)").argument("[agentIds...]","The ID(s) of the agent(s) for which the config should be reloaded. Mutually exclusive with --criteria <criteria> flag").option("--criteria <criteria>","An optional FHIR search criteria to resolve the agent(s) for which to notify to reload their config. Mutually exclusive with [agentIds...] arg").addOption(new tt.Option("--output <format>","An optional output format, defaults to table").choices(["table","json"]).default("table")).action(async(t,e)=>{await Zt({operation:"$reload-config",agentIds:t,options:e,parseSuccessfulResponse:r=>({id:r.agent.id,name:r.agent.name})})});vo.description("Upgrade the version for the specified agent(s)").argument("[agentIds...]","The ID(s) of the agent(s) that should be upgraded. Mutually exclusive with --criteria <criteria> flag").option("--criteria <criteria>","An optional FHIR search criteria to resolve the agent(s) to upgrade. Mutually exclusive with [agentIds...] arg").option("--version <version>","An optional version to upgrade to. Defaults to the latest version if flag not included").addOption(new tt.Option("--output <format>","An optional output format, defaults to table").choices(["table","json"]).default("table")).action(async(t,e)=>{await Zt({operation:"$upgrade",agentIds:t,options:e,parseSuccessfulResponse:r=>({id:r.agent.id,name:r.agent.name,version:e.version??"latest"})})});async function Zt({operation:t,agentIds:e,options:r,parseSuccessfulResponse:o}){let n=ca(e,r),s=await R(r),i=n.type==="criteria"?n.criteria:`Agent?_id=${n.ids.join(",")}`,c=new URLSearchParams(i.split("?")[1]),l;try{let p=s.fhirUrl("Agent",t);p.search=c.toString(),l=await s.get(p,{cache:"reload"})}catch(p){throw new Error(`Operation '${t}' failed`,{cause:p})}if(r.output==="json"){console.info(JSON.stringify(l,null,2));return}let u=[],h=[];switch(l.resourceType){case"Bundle":{let p=na(l);for(let K of p)K.result.resourceType==="Parameters"||(0,se.isOk)(K.result)?u.push(K):h.push(K);break}case"Parameters":case"OperationOutcome":{let p=await s.searchOne("Agent",c,{cache:"reload"});if(!p)throw new Error("Agent not found");l.resourceType==="Parameters"?u.push({agent:p,result:l}):h.push({agent:p,result:l});break}default:throw new Error(`Invalid result received for '${t}' operation: ${JSON.stringify(l)}`)}let f=[];for(let p of u){let K=o(p);f.push(K)}let S=[];for(let p of h){let Dt=p.result.issue?.[0],Ti={id:p.agent.id,name:p.agent.name,severity:Dt.severity,code:Dt.code,details:Dt.details?.text??"No details to show"};S.push(Ti)}console.info(`
${f.length} successful response(s):
`),console.table(f.length?f:"No successful responses received"),console.info(),S.length&&(console.info(`${S.length} failed response(s):`),console.info(),console.table(S))}async function bo(t,e,r){if(!(e||r.criteria))throw new Error("This command requires either an [agentId] or a --criteria <criteria> flag");if(e&&r.criteria)throw new Error("Ambiguous arguments and options combination; [agentId] arg and --criteria <criteria> flag are mutually exclusive");let o;if(e)o=e;else{Po(r.criteria);let n=await t.search("Agent",`${r.criteria.split("?")[1]}&_count=2`);if(!n?.entry?.length)throw new Error("Could not find an agent matching the provided criteria");if(n.entry.length!==1)throw new Error("Found more than one agent matching this criteria. This operation requires the criteria to resolve to exactly one agent");o=n.entry[0].resource?.id}return{reference:`Agent/${o}`}}function na(t){let e=[];for(let r of t.entry??[]){if(!r.resource)throw new Error("No Parameter resource found in entry");e.push(sa(r.resource))}return e}function sa(t){let e=t.parameter?.find(o=>o.name==="agent")?.resource;if(!e)throw new Error("Agent bulk operation response missing 'agent'");if(e.resourceType!=="Agent")throw new Error(`Agent bulk operation returned 'agent' with type '${e.resourceType}'`);let r=t.parameter?.find(o=>o.name==="result")?.resource;if(!r)throw new Error("Agent bulk operation response missing result'");if(!(r.resourceType==="Parameters"||r.resourceType==="OperationOutcome"))throw new Error(`Agent bulk operation returned 'result' with type '${r.resourceType}'`);return{agent:e,result:r}}function ia(t,e){let r={},o=e.required,n=e.optional;for(let s of o){let i=t.parameter?.find(l=>l.name===s);if(!i)throw new Error(`Failed to find parameter '${s}'`);let c;for(let l in i)if(l.startsWith("value")){if(c)throw new Error(`Found multiple values for parameter '${s}'`);c=l}if(!c)throw new Error(`Failed to find a value for parameter '${s}'`);r[s]=i[c]}if(n?.length)for(let s of n){let i=t.parameter?.find(l=>l.name===s);if(!i)continue;let c=aa(s,i);r[s]=c}return r}function aa(t,e){let r;for(let o in e)if(o.startsWith("value")){if(r)throw new Error(`Found multiple values for parameter '${t}'`);r=o}if(!r)throw new Error(`Failed to find a value for parameter '${t}'`);return e[r]}function ca(t,e){if(!Array.isArray(t))throw new Error("Invalid agent IDs array");if(t.length){if(e.criteria)throw new Error("Ambiguous arguments and options combination; [agentIds...] arg and --criteria <criteria> flag are mutually exclusive");for(let r of t)if(!(0,se.isUUID)(r))throw new Error(`Input '${r}' is not a valid agentId`);return{type:"ids",ids:t}}if(e.criteria)return Po(e.criteria),{type:"criteria",criteria:e.criteria};throw new Error("Either an [agentId...] arg or a --criteria <criteria> flag is required")}function Po(t){let e="Criteria must be formatted as a string containing the resource type (Agent) followed by a '?' and valid URL search query params, eg. `Agent?name=Test Agent`";if(typeof t!="string")throw new Error(e);let[r,o]=t.split("?");if(r!=="Agent"||!o)throw new Error(e);try{new URLSearchParams(o)}catch(n){throw new Error(e,{cause:n})}if(!o.includes("="))throw new Error(e,{cause:new Error("Query string lacks at least one `=`")})}var _=require("@medplum/core"),Co=require("node:child_process"),To=require("node:http"),Oo=require("node:os");var xo=_.MEDPLUM_CLI_CLIENT_ID,$o="http://localhost:9615",Qt=new g("login"),er=new g("whoami"),tr=new g("token");Qt.action(async t=>{let e=t.profile??"default",r=et(e,t),o=await R(t,!1);await la(o,r)});er.action(async t=>{let e=await R(t);da(e)});tr.action(async t=>{let e=await R(t);await e.getProfileAsync();let r=e.getAccessToken();if(!r)throw new Error("Not logged in");console.log(r)});async function la(t,e){switch(e?.authType??"authorization-code"){case"authorization-code":await ma(t);break;case"basic":t.setBasicAuth(e.clientId,e.clientSecret);break;case"client-credentials":t.setBasicAuth(e.clientId,e.clientSecret),await t.startClientLogin(e.clientId,e.clientSecret);break;case"jwt-bearer":await wo(t,e);break;case"jwt-assertion":await Eo(t,e);break}}async function ua(t){let e=(0,To.createServer)(async(r,o)=>{let n=new URL(r.url,"http://localhost:9615"),s=n.searchParams.get("code");if(r.method==="OPTIONS"){o.writeHead(200,{Allow:"GET, POST","Content-Type":_.ContentType.TEXT}),o.end("OK");return}if(n.pathname==="/"&&s)try{let i=await t.processCode(s,{clientId:xo,redirectUri:$o});o.writeHead(200,{"Content-Type":_.ContentType.TEXT}),o.end(`Signed in as ${(0,_.getDisplayString)(i)}. You may close this window.`)}catch(i){o.writeHead(400,{"Content-Type":_.ContentType.TEXT}),o.end(`Error: ${(0,_.normalizeErrorString)(i)}`)}finally{e.close()}else o.writeHead(404,{"Content-Type":_.ContentType.TEXT}),o.end("Not found")}).listen(9615)}async function pa(t){let e=(0,Oo.platform)(),r;switch(e){case"openbsd":case"linux":r=`xdg-open '${t}'`;break;case"darwin":r=`open '${t}'`;break;case"win32":r=`cmd /c start "" "${t}"`;break;default:throw new Error("Unsupported platform: "+e)}return new Promise((o,n)=>{(0,Co.exec)(r,(s,i,c)=>{if(s){n(s);return}if(c){n(new Error("Could not open browser: "+c));return}o()})})}function da(t){let e=t.getActiveLogin();e?(console.log(`Server:  ${t.getBaseUrl()}`),console.log(`Profile: ${e.profile.display} (${e.profile.reference})`),console.log(`Project: ${e.project.display} (${e.project.reference})`)):console.log("Not logged in")}async function ma(t){await ua(t);let e=new URL(t.getAuthorizeUrl());e.searchParams.set("client_id",xo),e.searchParams.set("redirect_uri",$o),e.searchParams.set("scope","openid"),e.searchParams.set("response_type","code"),e.searchParams.set("prompt","login"),await pa(e.toString())}var Ne="\x1B[0m",fa="\x1B[1m",ha="\x1B[31m",ga="\x1B[32m",ya="\x1B[33m",wa="\x1B[34m",ae={red:t=>`${ha}${t}${Ne}`,green:t=>`${ga}${t}${Ne}`,yellow:t=>`${ya}${t}${Ne}`,blue:t=>`${wa}${t}${Ne}`,bold:t=>`${fa}${t}${Ne}`},rr=t=>t.replace(/\*\*(.*?)\*\*/g,(e,r)=>ae.bold(r));var X=require("@aws-sdk/client-cloudformation"),Et=require("@aws-sdk/client-cloudfront"),Is=require("@aws-sdk/client-ecs"),vs=require("@aws-sdk/client-s3"),we=require("@aws-sdk/client-ssm"),St=require("@aws-sdk/client-sts"),bs=require("@medplum/core"),Ps=$(require("node-fetch")),Cs=require("node:fs"),Ts=$(Rr());var Rs=$(require("node:readline")),ft;function ht(){ft=Rs.default.createInterface({input:process.stdin,output:process.stdout})}function gt(){ft.close()}function a(t){ft.write(t+`
`)}function I(t){a(`
`+t+`
`)}function x(t,e=""){return new Promise(r=>{ft.question(t+(e?" ("+e+")":"")+" ",o=>{r(o||e.toString())})})}async function yt(t,e,r=""){let o=t+" ["+e.map(n=>n===r?"("+n+")":n).join("|")+"]";for(;;){let n=await x(o)||r;if(e.includes(n))return n;a("Please choose one of the following options: "+e.join(", "))}}async function ye(t,e,r){return parseInt(await yt(t,e.map(o=>o.toString()),r.toString()),10)}async function Q(t){return(await yt(t,["y","n"])).toLowerCase()==="y"}async function Fe(t){if(!await Q(t))throw a("Exiting..."),new Error("User cancelled")}var wt=new X.CloudFormationClient({}),fu=new Et.CloudFrontClient({region:"us-east-1"}),Um=new Is.ECSClient({}),Be=new vs.S3Client({}),hu="medplum:environment";async function Ar(){let t=[],e=(0,X.paginateListStacks)({client:wt},{StackStatusFilter:["CREATE_COMPLETE","CREATE_FAILED","CREATE_IN_PROGRESS","DELETE_FAILED","DELETE_IN_PROGRESS","IMPORT_COMPLETE","IMPORT_IN_PROGRESS","IMPORT_ROLLBACK_COMPLETE","IMPORT_ROLLBACK_FAILED","IMPORT_ROLLBACK_IN_PROGRESS","REVIEW_IN_PROGRESS","ROLLBACK_COMPLETE","ROLLBACK_FAILED","ROLLBACK_IN_PROGRESS","UPDATE_COMPLETE","UPDATE_COMPLETE_CLEANUP_IN_PROGRESS","UPDATE_FAILED","UPDATE_IN_PROGRESS","UPDATE_ROLLBACK_COMPLETE","UPDATE_ROLLBACK_COMPLETE_CLEANUP_IN_PROGRESS","UPDATE_ROLLBACK_FAILED","UPDATE_ROLLBACK_IN_PROGRESS"]});for await(let r of e)if(r.StackSummaries)for(let o of r.StackSummaries)t.push(o);return t}async function Ee(t){let e=await Ar();for(let r of e){let o=r.StackName,n=await Ir(o);if(n?.tag===t)return n}}async function Ir(t){let e={};if(await As(wt,t,e),await wt.config.region()!=="us-east-1")try{await As(new X.CloudFormationClient({region:"us-east-1"}),t+"-us-east-1",e)}catch{}return e}async function As(t,e,r){let o=new X.DescribeStacksCommand({StackName:e}),s=(await t.send(o))?.Stacks?.[0],i=s?.Tags?.find(l=>l.Key===hu);if(!i)return;let c=await t.send(new X.DescribeStackResourcesCommand({StackName:e}));if(c.StackResources){t===wt&&(r.stack=s,r.tag=i.Value);for(let l of c.StackResources)gu(l,r)}}function gu(t,e){t.ResourceType==="AWS::ECS::Cluster"?e.ecsCluster=t:t.ResourceType==="AWS::ECS::Service"?e.ecsService=t:t.ResourceType==="AWS::S3::Bucket"&&t.LogicalResourceId?.startsWith("FrontEndAppBucket")?e.appBucket=t:t.ResourceType==="AWS::CloudFront::Distribution"&&t.LogicalResourceId?.startsWith("FrontEndAppDistribution")?e.appDistribution=t:t.ResourceType==="AWS::CloudFront::CloudFrontOriginAccessIdentity"&&t.LogicalResourceId?.startsWith("FrontEndOriginAccessIdentity")?e.appOriginAccessIdentity=t:t.ResourceType==="AWS::S3::Bucket"&&t.LogicalResourceId?.startsWith("StorageStorageBucket")?e.storageBucket=t:t.ResourceType==="AWS::CloudFront::Distribution"&&t.LogicalResourceId?.startsWith("StorageStorageDistribution")?e.storageDistribution=t:t.ResourceType==="AWS::CloudFront::CloudFrontOriginAccessIdentity"&&t.LogicalResourceId?.startsWith("StorageOriginAccessIdentity")&&(e.storageOriginAccessIdentity=t)}function Rt(t){console.log(`Medplum Tag:           ${t.tag}`),console.log(`Stack Name:            ${t.stack?.StackName}`),console.log(`Stack ID:              ${t.stack?.StackId}`),console.log(`Status:                ${t.stack?.StackStatus}`),console.log(`ECS Cluster:           ${t.ecsCluster?.PhysicalResourceId}`),console.log(`ECS Service:           ${yu(t.ecsService)}`),console.log(`App Bucket:            ${t.appBucket?.PhysicalResourceId}`),console.log(`App Distribution:      ${t.appDistribution?.PhysicalResourceId}`),console.log(`App OAI:               ${t.appOriginAccessIdentity?.PhysicalResourceId}`),console.log(`Storage Bucket:        ${t.storageBucket?.PhysicalResourceId}`),console.log(`Storage Distribution:  ${t.storageDistribution?.PhysicalResourceId}`),console.log(`Storage OAI:           ${t.storageOriginAccessIdentity?.PhysicalResourceId}`)}function yu(t){return t?.PhysicalResourceId?.split("/")?.pop()||""}async function At(t){let e=await fu.send(new Et.CreateInvalidationCommand({DistributionId:t,InvalidationBatch:{CallerReference:`invalidate-all-${Date.now()}`,Paths:{Quantity:1,Items:["/*"]}}}));console.log(`Created invalidation with ID: ${e.Invalidation?.Id}`)}async function It(t){let o=(await(await(0,Ps.default)("https://api.github.com/repos/medplum/medplum/releases?per_page=100",{headers:{Accept:"application/vnd.github+json","X-GitHub-Api-Version":"2022-11-28"}})).json()).map(n=>n.tag_name.startsWith("v")?n.tag_name.slice(1):n.tag_name);return o.sort((n,s)=>Ts.compare(s,n)),t?o.slice(0,o.indexOf(t)):o}async function vt(t,e,r){let o=new we.SSMClient({region:t});for(let[n,s]of Object.entries(r)){let i=e+n,c=s.toString(),l=await wu(o,i);l!==void 0&&l!==c&&(a(`Parameter "${i}" exists with different value.`),await Fe(`Do you want to overwrite "${i}"?`)),await Eu(o,i,c)}}async function wu(t,e){let r=new we.GetParameterCommand({Name:e,WithDecryption:!0});try{return(await t.send(r)).Parameter?.Value}catch(o){if(o.name==="ParameterNotFound")return;throw o}}async function Eu(t,e,r){let o=new we.PutParameterCommand({Name:e,Value:r,Type:"SecureString",Overwrite:!0});await t.send(o)}function ee(t,e){if(console.log(`Config not found: ${t} (${M(t,e)})`),e){let o=Object.entries(e);if(o.length>0){console.log("Additional options:");for(let[n,s]of o)console.log(`  ${n}: ${s}`)}}console.log();let r=(0,Cs.readdirSync)(".",{withFileTypes:!0});if(r=r.filter(o=>o.isFile()&&o.name.startsWith("medplum.")&&o.name.endsWith(".json")).map(o=>o.name),r.length===0)console.log("No configs found");else{console.log("Available configs:");for(let o of r)console.log(`  ${o.replaceAll("medplum.","").replaceAll(".config","").replaceAll(".server","").replaceAll(".json","").padEnd(40," ")} (${o})`)}}async function Se(t){console.log(`Stack not found: ${t}`),console.log();try{let e=new St.STSClient,r=new St.GetCallerIdentityCommand({}),o=await e.send(r),n=await e.config.region();console.log("AWS Region:        ",n),console.log("AWS Account ID:    ",o.Account),console.log("AWS Account ARN:   ",o.Arn),console.log("AWS User ID:       ",o.UserId)}catch(e){console.log("Warning: Unable to get AWS account ID",(0,bs.normalizeErrorString)(e))}}async function Os(t){let e=await Ee(t);if(!e)throw await Se(t),new Error(`Stack not found: ${t}`);Rt(e)}var le=require("@aws-sdk/client-acm"),bt=require("@aws-sdk/client-cloudfront"),Pt=require("@aws-sdk/client-sts"),$s=require("@medplum/core"),We=require("node:crypto"),Ns=require("node:fs");var Su=t=>`${t}DomainName`,Ls=t=>`${t}SslCertArn`;async function ks(){let t={apiPort:8103,region:"us-east-1"};ht(),I("MEDPLUM"),a("This tool prepares the necessary prerequisites for deploying Medplum in your AWS account."),a(""),a("Most Medplum infrastructure is deployed using the AWS CDK."),a("However, some AWS resources must be created manually, such as email addresses and SSL certificates."),a("This tool will help you create those resources."),a(""),a("Upon completion, this tool will:"),a("  1. Generate a Medplum CDK config file (i.e., medplum.demo.config.json)"),a("  2. Optionally generate an AWS CloudFront signing key"),a("  3. Optionally request SSL certificates from AWS Certificate Manager"),a("  4. Optionally write server config settings to AWS Parameter Store"),a(""),a("The Medplum infra config file is an input to the Medplum CDK."),a("The Medplum CDK will create and manage the necessary AWS resources."),a(""),a("We will ask a series of questions to generate your infra config file."),a("Some questions have predefined options in [square brackets]."),a("Some questions have default values in (parentheses), which you can accept by pressing Enter."),a("Press Ctrl+C at any time to exit.");let e=await Ru(t.region);e||(a("It appears that you do not have AWS credentials configured."),a("AWS credentials are not strictly required, but will enable some additional features."),a("If you intend to use AWS credentials, please configure them now."),await Fe("Do you want to continue without AWS credentials?")),I("ENVIRONMENT NAME"),a('Medplum deployments have a short environment name such as "prod", "staging", "alice", or "demo".'),a("The environment name is used in multiple places:"),a("  1. As part of config file names (i.e., medplum.demo.config.json)"),a("  2. As the base of CloudFormation stack names (i.e., MedplumDemo)"),a("  3. AWS Parameter Store keys (i.e., /medplum/demo/...)"),t.name=await x("What is your environment name?","demo"),a('Using environment name "'+t.name+'"...'),I("CONFIG FILE"),a("Medplum Infrastructure will create a config file in the current directory.");let r=await x("What is the config file name?",`medplum.${t.name}.config.json`);(0,Ns.existsSync)(r)&&(a("Config file already exists."),await Fe("Do you want to overwrite the config file?")),a('Using config file "'+r+'"...'),b(r,t),I("AWS REGION"),a("Most Medplum resources will be created in a single AWS region."),t.region=await x("Enter your AWS region:","us-east-1"),b(r,t),I("AWS ACCOUNT NUMBER"),a("Medplum Infrastructure will use your AWS account number to create AWS resources."),e&&a("Using the AWS CLI, your current account ID is: "+e),t.accountNumber=await x("What is your AWS account number?",e),b(r,t),I("STACK NAME"),a("Medplum will create a CloudFormation stack to manage AWS resources."),a("AWS CloudFormation stack names ");let o="Medplum"+t.name.charAt(0).toUpperCase()+t.name.slice(1);for(t.stackName=await x("Enter your CloudFormation stack name?",o),b(r,t),I("BASE DOMAIN NAME"),a("Please enter the base domain name for your Medplum deployment."),a(""),a("Medplum deploys multiple subdomains for various services."),a(""),a('For example, "api." for the REST API and "app." for the web application.'),a("The base domain name is the common suffix for all subdomains."),a(""),a('For example, if your base domain name is "example.com",'),a('then the REST API will be "api.example.com".'),a(""),a('The base domain should include the TLD (i.e., ".com", ".org", ".net").'),a(""),a("Note that you must own the base domain, and it must use Route53 DNS.");!t.domainName;)t.domainName=await x("Enter your base domain name:");b(r,t),I("SUPPORT EMAIL"),a("Medplum sends transactional emails to users."),a("For example, emails to new users or for password reset."),a("Medplum will use the support email address to send these emails."),a("Note that you must verify the support email address in SES.");let n=await x("Enter your support email address:");I("API DOMAIN NAME"),a("Medplum deploys a REST API for the backend services."),t.apiDomainName=await x("Enter your REST API domain name:","api."+t.domainName),t.baseUrl=`https://${t.apiDomainName}/`,b(r,t),I("APP DOMAIN NAME"),a("Medplum deploys a web application for the user interface."),t.appDomainName=await x("Enter your web application domain name:","app."+t.domainName),b(r,t),I("STORAGE DOMAIN NAME"),a("Medplum deploys a storage service for file uploads."),t.storageDomainName=await x("Enter your storage domain name:","storage."+t.domainName),b(r,t),I("STORAGE BUCKET"),a("Medplum uses an S3 bucket to store binary content such as file uploads."),a("Medplum will create a the S3 bucket as part of the CloudFormation stack."),t.storageBucketName=await x("Enter your storage bucket name:",t.storageDomainName),b(r,t),I("MAX AVAILABILITY ZONES"),a("Medplum API servers can be deployed in multiple availability zones."),a("This provides redundancy and high availability."),a("However, it also increases the cost of the deployment."),a("If you want to use all availability zones, choose a large number such as 99."),a("If you want to restrict the number, for example to manage EIP limits,"),a("then choose a small number such as 2 or 3."),t.maxAzs=await ye("Enter the maximum number of availability zones:",[2,3,99],2),I("DATABASE INSTANCES"),a("Medplum uses a relational database to store data."),a("Medplum can create a new RDS database as part of the CloudFormation stack,"),a("or can set up your own database and enter the database name, username, and password."),await Q("Do you want to create a new RDS database as part of the CloudFormation stack?")?(a("Medplum will create a new RDS database as part of the CloudFormation stack."),a(""),a("If you need high availability, you can choose multiple instances."),a("Use 1 for a single instance, or 2 for a primary and a standby."),t.rdsInstances=await ye("Enter the number of database instances:",[1,2],1)):(a("Medplum will not create a new RDS database."),a("Please create a new RDS database and enter the database name, username, and password."),a('Set the AWS Secrets Manager secret ARN in the config file in the "rdsSecretsArn" setting.'),t.rdsSecretsArn="TODO"),b(r,t),I("SERVER INSTANCES"),a("Medplum uses AWS Fargate to run the API servers."),a("Medplum will create a new Fargate cluster as part of the CloudFormation stack."),a("Fargate will automatically scale the number of servers up and down."),a("If you need high availability, you can choose multiple instances."),t.desiredServerCount=await ye("Enter the number of server instances:",[1,2,3,4,6,8],1),b(r,t),I("SERVER MEMORY"),a("You can choose the amount of memory for each server instance."),a("The default is 512 MB, which is sufficient for getting started."),a("Note that only certain CPU units are compatible with memory units."),a('Consult AWS Fargate "Task Definition Parameters" for more information.'),t.serverMemory=await ye("Enter the server memory (MB):",[512,1024,2048,4096,8192,16384],512),b(r,t),I("SERVER CPU"),a("You can choose the amount of CPU for each server instance."),a("CPU is expressed as an integer using AWS CPU units"),a("The default is 256, which is sufficient for getting started."),a("Note that only certain CPU units are compatible with memory units."),a('Consult AWS Fargate "Task Definition Parameters" for more information.'),t.serverCpu=await ye("Enter the server CPU:",[256,512,1024,2048,4096,8192,16384],256),b(r,t),I("SERVER IMAGE"),a("Medplum uses Docker images for the API servers."),a("You can choose the image to use for the servers."),a("Docker images can be loaded from either Docker Hub or AWS ECR."),a("The default is the latest Medplum release.");let s=(await It())[0]??"latest";t.serverImage=await x("Enter the server image:",`medplum/medplum-server:${s}`),b(r,t),I("SIGNING KEY"),a("Medplum uses AWS CloudFront Presigned URLs for binary content such as file uploads.");let i=await bu(t.region,t.stackName+"SigningKey");i?(t.signingKeyId=i.keyId,t.storagePublicKey=i.publicKey,b(r,t)):(a("Unable to generate signing key."),a("Please manually create a signing key and enter the key ID and public key in the config file."),a('You must set the "signingKeyId", "signingKey", and "signingKeyPassphrase" settings.')),I("SSL CERTIFICATES"),a("Medplum will now check for existing SSL certificates for the subdomains.");let c=await Au(t.region);a("Found "+c.length+" certificate(s).");for(let{region:u,certName:h}of[{region:t.region,certName:"api"},{region:"us-east-1",certName:"app"},{region:"us-east-1",certName:"storage"}]){a("");let f=await Iu(t,c,u,h);t[Ls(h)]=f,b(r,t)}I("AWS PARAMETER STORE"),a("Medplum uses AWS Parameter Store to store sensitive configuration values."),a("These values will be encrypted at rest."),a(`The values will be stored in the "/medplum/${t.name}" path.`);let l={port:t.apiPort,baseUrl:t.baseUrl,appBaseUrl:`https://${t.appDomainName}/`,storageBaseUrl:`https://${t.storageDomainName}/binary/`,binaryStorage:`s3:${t.storageBucketName}`,supportEmail:n};if(i&&(l.signingKeyId=i.keyId,l.signingKey=i.privateKey,l.signingKeyPassphrase=i.passphrase),a(JSON.stringify({...l,signingKey:"****",signingKeyPassphrase:"****"},null,2)),await Q("Do you want to store these values in AWS Parameter Store?"))await vt(t.region,`/medplum/${t.name}/`,l);else{let u=M(t.name,{server:!0});b(u,l),a("Skipping AWS Parameter Store."),a(`Writing values to local config file: ${u}`),a("Please add these values to AWS Parameter Store manually.")}I("DONE!"),a("Medplum configuration complete."),a("You can now proceed to deploying the Medplum infrastructure with CDK."),a("Run:"),a(""),a(`    npx cdk bootstrap -c config=${r}`),a(`    npx cdk synth -c config=${r}`),t.region==="us-east-1"?a(`    npx cdk deploy -c config=${r}`):a(`    npx cdk deploy -c config=${r} --all`),a(""),a("See Medplum documentation for more information:"),a(""),a("    https://www.medplum.com/docs/self-hosting/install-on-aws"),a(""),gt()}async function Ru(t){try{let e=new Pt.STSClient({region:t}),r=new Pt.GetCallerIdentityCommand({});return(await e.send(r)).Account}catch(e){console.log("Warning: Unable to get AWS account ID",e.message);return}}async function Au(t){let e=await xs(t);if(t!=="us-east-1"){let r=await xs("us-east-1");e.push(...r)}return e}async function xs(t){try{let e=new le.ACMClient({region:t}),r=new le.ListCertificatesCommand({MaxItems:1e3});return(await e.send(r)).CertificateSummaryList}catch(e){return console.log("Warning: Unable to list certificates",e.message),[]}}async function Iu(t,e,r,o){let n=t[Su(o)],s=e.find(c=>c.CertificateArn?.includes(r)&&c.DomainName===n);if(s)return a(`Found existing certificate for "${n}" in "${r}.`),s.CertificateArn;if(a(`No existing certificate found for "${n}" in "${r}.`),!await Q("Do you want to request a new certificate?"))return a(`Please add your certificate ARN to the config file in the "${Ls(o)}" setting.`),"TODO";let i=await vu(r,n);return a("Certificate ARN: "+i),i}async function vu(t,e){try{let r=await yt("Validate certificate using DNS or email validation?",["dns","email"],"dns"),o=new le.ACMClient({region:t}),n=new le.RequestCertificateCommand({DomainName:e,ValidationMethod:r.toUpperCase()});return(await o.send(n)).CertificateArn}catch(r){return console.log("Error: Unable to request certificate",r.message),"TODO"}}async function bu(t,e){let r=(0,We.randomUUID)(),o=(0,We.generateKeyPairSync)("rsa",{modulusLength:2048,publicKeyEncoding:{type:"spki",format:"pem"},privateKeyEncoding:{type:"pkcs1",format:"pem",cipher:"aes-256-cbc",passphrase:r}});try{return{keyId:(await new bt.CloudFrontClient({region:t}).send(new bt.CreatePublicKeyCommand({PublicKeyConfig:{Name:e,CallerReference:(0,We.randomUUID)(),EncodedKey:o.publicKey}}))).PublicKey?.Id,publicKey:o.publicKey,privateKey:o.privateKey,passphrase:r}}catch(n){console.log("Error: Unable to create signing key: ",(0,$s.normalizeErrorString)(n));return}}async function Ds(){let t=await Ar();for(let e of t){let r=e.StackName,o=await Ir(r);o&&(Rt(o),console.log(""))}}var Ms=require("@aws-sdk/client-s3"),U=require("@medplum/core"),_s=$(require("fast-glob")),vr=$(require("node-fetch")),D=require("node:fs"),Us=require("node:os"),ue=require("node:path"),js=require("node:stream/promises");async function Fs(t,e){let r=B(t,e);if(!r)throw ee(t,e),new Error(`Config not found: ${t}`);let o=await Ee(t);if(!o)throw await Se(t),new Error(`Stack not found: ${t}`);let n=o.appBucket;if(!n)throw new Error(`App bucket not found for stack ${t}`);let s;if(e.tarPath)s=e.tarPath;else{let i=e?.toVersion??"latest";s=await Cu("@medplum/app",i)}Bs(s,{MEDPLUM_BASE_URL:r.baseUrl,MEDPLUM_CLIENT_ID:r.clientId??"",GOOGLE_CLIENT_ID:r.googleClientId??"",RECAPTCHA_SITE_KEY:r.recaptchaSiteKey??"",MEDPLUM_REGISTER_ENABLED:r.registerEnabled?"true":"false"}),await Ou(s,n.PhysicalResourceId,e),o.appDistribution?.PhysicalResourceId&&!e.dryrun&&await At(o.appDistribution.PhysicalResourceId),console.log("Done")}async function Pu(t,e){let r=`https://registry.npmjs.org/${t}/${e}`;return(await(0,vr.default)(r)).json()}async function Cu(t,e){let o=(await Pu(t,e)).dist.tarball,n=(0,D.mkdtempSync)((0,ue.join)((0,Us.tmpdir)(),"tarball-"));try{let s=await(0,vr.default)(o),i=go(n);return await(0,js.pipeline)(s.body,i),(0,ue.join)(n,"package","dist")}catch(s){throw(0,D.rmSync)(n,{recursive:!0,force:!0}),s}}function Bs(t,e){for(let r of(0,D.readdirSync)(t,{withFileTypes:!0})){let o=(0,ue.join)(t,r.name);r.isDirectory()?Bs(o,e):r.isFile()&&o.endsWith(".js")&&Tu(o,e)}}function Tu(t,e){let r=(0,D.readFileSync)(t,"utf-8");for(let[o,n]of Object.entries(e))r=r.replaceAll(`__${o}__`,n);(0,D.writeFileSync)(t,r)}async function Ou(t,e,r){let o=[["assets/**/*.css",U.ContentType.CSS,!0],["assets/**/*.css.map",U.ContentType.JSON,!0],["assets/**/*.js",U.ContentType.JAVASCRIPT,!0],["assets/**/*.js.map",U.ContentType.JSON,!0],["assets/**/*.txt",U.ContentType.TEXT,!0],["assets/**/*.ico",U.ContentType.FAVICON,!0],["img/**/*.png",U.ContentType.PNG,!0],["img/**/*.svg",U.ContentType.SVG,!0],["robots.txt",U.ContentType.TEXT,!0],["index.html",U.ContentType.HTML,!1]];for(let n of o)await xu({rootDir:t,bucketName:e,fileNamePattern:n[0],contentType:n[1],cached:n[2],dryrun:r.dryrun})}async function xu(t){let e=_s.default.sync(t.fileNamePattern,{cwd:t.rootDir});for(let r of e)await $u((0,ue.join)(t.rootDir,r),t)}async function $u(t,e){let r=(0,D.createReadStream)(t),o=t.substring(e.rootDir.length+1).split(ue.sep).join("/"),n={Bucket:e.bucketName,Key:o,Body:r,ContentType:e.contentType,CacheControl:e.cached?"public, max-age=31536000":"no-cache, no-store, must-revalidate"};console.log(`Uploading ${o} to ${e.bucketName}...`),e.dryrun||await Be.send(new Ms.PutObjectCommand(n))}var Ct=require("@aws-sdk/client-s3");async function Ks(t,e){if(!B(t,e))throw ee(t,e),new Error(`Config not found: ${t}`);let o=await Ee(t);if(!o)throw await Se(t),new Error(`Stack not found: ${t}`);await Ws("App",o.appBucket,o.appDistribution,o.appOriginAccessIdentity,e),await Ws("Storage",o.storageBucket,o.storageDistribution,o.storageOriginAccessIdentity,e),console.log("Done")}async function Ws(t,e,r,o,n){if(!e?.PhysicalResourceId)throw new Error(`${t} bucket not found`);if(!r?.PhysicalResourceId)throw new Error(`${t} distribution not found`);if(!o?.PhysicalResourceId)throw new Error(`${t} OAI not found`);let s=e.PhysicalResourceId,i=o.PhysicalResourceId,c=await Nu(s);if(ku(c,s,i))throw new Error(`${t} bucket already has policy statement`);Du(c,s,i),console.log(`${t} bucket policy:`),console.log(JSON.stringify(c,void 0,2)),n.dryrun?console.log("Dry run - skipping updates"):(console.log("Updating bucket policy..."),await Lu(s,c),console.log("Bucket policy updated"),console.log("Creating CloudFront invalidation..."),await At(r.PhysicalResourceId),console.log("CloudFront invalidation created"),console.log(`${t} bucket policy updated`))}async function Nu(t){let e=await Be.send(new Ct.GetBucketPolicyCommand({Bucket:t}));return JSON.parse(e.Policy??"{}")}async function Lu(t,e){await Be.send(new Ct.PutBucketPolicyCommand({Bucket:t,Policy:JSON.stringify(e)}))}function ku(t,e,r){return!!t?.Statement?.some(o=>o?.Effect==="Allow"&&o?.Principal?.AWS===`arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity ${r}`&&Array.isArray(o?.Action)&&o?.Action?.includes("s3:GetObject*")&&o?.Action?.includes("s3:GetBucket*")&&o?.Action?.includes("s3:List*")&&Array.isArray(o?.Resource)&&o?.Resource?.includes(`arn:aws:s3:::${e}`)&&o?.Resource?.includes(`arn:aws:s3:::${e}/*`))}function Du(t,e,r){t.Version||(t.Version="2012-10-17"),t.Statement||(t.Statement=[]),t.Statement.push({Effect:"Allow",Principal:{AWS:`arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity ${r}`},Action:["s3:GetObject*","s3:GetBucket*","s3:List*"],Resource:[`arn:aws:s3:::${e}`,`arn:aws:s3:::${e}/*`]})}async function qs(t,e){try{ht();let r=B(t,e);if(!r)throw ee(t,e),new Error(`Config not found: ${t}`);let o=ho(t)??{};if(!e.yes&&Object.keys(o).length===0){let n=M(t,{server:!0});if(console.log(ae.yellow(`Config file ${n} not found!`)),!await Q("Do you want to proceed?")){console.log(ae.red(`Run Aborted, please ensure ${n} is present and try again.`));return}}Mu(r,o),Uu(r,o),a("Medplum uses AWS Parameter Store to store sensitive configuration values."),a("These values will be encrypted at rest."),a(`The values will be stored in the "/medplum/${r.name}" path.`),a(JSON.stringify({...o,signingKey:"****",signingKeyPassphrase:"****"},null,2)),e.dryrun?console.log(ae.yellow("Dry run - skipping updates!")):(e.yes||await Q("Do you want to store these values in AWS Parameter Store?"))&&await vt(r.region,`/medplum/${r.name}/`,o)}finally{gt()}}function Mu(t,e){Tt(t.apiPort,e.port,`Infra "apiPort" (${t.apiPort}) does not match server "port" (${e.port})`),Tt(t.baseUrl,e.baseUrl,`Infra "baseUrl" (${t.baseUrl}) does not match server "baseUrl" (${e.baseUrl})`),Tt(t.appDomainName&&`https://${t.appDomainName}/`,e.appBaseUrl,`Infra "appDomainName" (${t.appDomainName}) does not match server "appBaseUrl" (${e.appBaseUrl})`),Tt(t.storageDomainName&&`https://${t.storageDomainName}/binary/`,e.storageBaseUrl,`Infra "storageDomainName" (${t.storageDomainName}) does not match server "storageBaseUrl" (${e.storageBaseUrl})`)}function Tt(t,e,r){if(_u(t,e))throw new Error(r)}function _u(t,e){return t!==void 0&&e!==void 0&&t!==e}function Uu(t,e){t.apiPort&&(e.port=t.apiPort),t.baseUrl&&(e.baseUrl=t.baseUrl),t.appDomainName&&(e.appBaseUrl=`https://${t.appDomainName}/`),t.storageDomainName&&(e.storageBaseUrl=`https://${t.storageDomainName}/`)}var Gs=require("node:child_process"),Re=$(Rr());async function Vs(t,e){let r=await R(e),o=B(t,e);if(!o)throw console.log(`Configuration file ${M(t)} not found`),ee(t,e),new Error(`Config not found: ${t}`);let n=o.serverImage.lastIndexOf(":"),s=o.serverImage.slice(0,n),i=await ju(r,o),c=await Hs(i);for(;c;){if(e.toVersion&&Re.gt(c,e.toVersion)){console.log(`Skipping update to v${c}`);break}console.log(`Performing update to v${c}`),o.serverImage=`${s}:${c}`,Fu(t,o),await r.startAsyncRequest("/admin/super/migrate"),c=await Hs(c)}}async function ju(t,e){let r=e.serverImage.lastIndexOf(":"),o=e.serverImage.slice(r+1);if(o==="latest"){o=(await t.get("/healthcheck")).version;let s=o.indexOf("-");s>-1&&(o=o.slice(0,s))}return o}async function Hs(t,e){let r=await It(t),o=r[0];return r.filter(n=>n===o||n===e||Re.gte(n,Re.inc(t,"minor"))).pop()}function Fu(t,e){let r=M(t);b(r,e);let o=`npx cdk deploy -c config=${r}${e.region!=="us-east-1"?" --all":""}`;console.log("> "+o);let n=(0,Gs.spawnSync)(o,{stdio:"inherit"});if(n.status!==0)throw new Error(`Deploy of ${e.serverImage} failed (exit code ${n.status}): ${n.stderr}`);console.log(n.stdout)}function Js(){let t=new g("aws").description("Commands to manage AWS resources");return t.command("init").description("Initialize a new Medplum AWS CloudFormation stacks").action(ks),t.command("list").description("List Medplum AWS CloudFormation stacks").action(Ds),t.command("describe").description("Describe a Medplum AWS CloudFormation stack by tag").argument("<tag>","The Medplum stack tag").action(Os),t.command("update-config").alias("deploy-config").summary("Update the AWS Parameter Store config values.").description(rr(`Update the AWS Parameter Store config values.

Configuration values come from a file named **medplum.<tag>.config.server.json** where **<tag>** is the Medplum stack tag.

`+ae.yellow("**Services must be restarted to apply changes.**"))).argument("<tag>","The Medplum stack tag").option("--file [file]",rr("File to provide overrides for **apiPort**, **baseUrl**, **appDomainName** and **storageDomainName** values that appear in the config file.")).option("--dryrun","Displays the operations that would be performed using the specified command without actually running them.").option("--yes","Automatically confirm the update").action(qs),y(t,new g("update-server").alias("deploy-server").description("Update the server image").argument("<tag>","The Medplum stack tag").option("--file [file]","Specifies the config file to use. If not specified, the file is based on the tag.").option("--to-version [version]","Specifies the version of the configuration to update. If not specified, the latest version is updated.").action(Vs)),t.command("update-app").alias("deploy-app").description("Update the app site").argument("<tag>","The Medplum stack tag").option("--file [file]","Specifies the config file to use. If not specified, the file is based on the tag.").option("--to-version [version]","Specifies the version of the configuration to update. If not specified, the latest version is updated.").option("--dryrun","Displays the operations that would be performed using the specified command without actually running them.").option("--tar-path [tarPath]","Specifies the path to the extracted tarball of the @medplum/app package.").action(Fs),t.command("update-bucket-policies").description("Update S3 bucket policies").argument("<tag>","The Medplum stack tag").option("--file [file]","Specifies the config file to use. If not specified, the file is based on the tag.").option("--dryrun","Displays the operations that would be performed using the specified command without actually running them.").action(Ks),t}var Xs=new g("save"),zs=new g("deploy"),Ys=new g("create"),Ke=new g("bot");y(Ke,Xs);y(Ke,zs);y(Ke,Ys);var br=new g("save-bot"),Pr=new g("deploy-bot"),Cr=new g("create-bot");Xs.description("Saving the bot").argument("<botName>").action(async(t,e)=>{let r=await R(e);await Ot(r,t)});zs.description("Deploy the app to AWS").argument("<botName>").action(async(t,e)=>{let r=await R(e);await Ot(r,t,!0)});Ys.arguments("<botName> <projectId> <sourceFile> <distFile>").description("Creating a bot").option("--runtime-version <runtimeVersion>","Runtime version (awslambda, vmcontext)").option("--no-write-config","Do not write bot to config").action(async(t,e,r,o,n)=>{let s=await R(n);await zt(s,t,e,r,o,n.runtimeVersion,!!n.writeConfig)});async function Ot(t,e,r=!1){let o=fo(e),n=[],s=[],i=0,c=0;for(let l of o)try{let u=await t.readResource("Bot",l.id);await Jt(t,l,u),i++,r&&(await Xt(t,l,u),c++)}catch(u){n.push(u),s.push(`${l.name} [${l.id}]`)}if(console.log(`Number of bots saved: ${i}`),console.log(`Number of bots deployed: ${c}`),console.log(`Number of errors: ${n.length}`),n.length)throw new Error(`${n.length} bot(s) had failures. Bots with failures:

    ${s.join(`
    `)}`,{cause:n})}br.description("Saves the bot").argument("<botName>").action(async(t,e)=>{let r=await R(e);await Ot(r,t)});Pr.description("Deploy the bot to AWS").argument("<botName>").action(async(t,e)=>{let r=await R(e);await Ot(r,t,!0)});Cr.arguments("<botName> <projectId> <sourceFile> <distFile>").description("Creates and saves the bot").action(async(t,e,r,o,n)=>{let s=await R(n);await zt(s,t,e,r,o)});var xt=require("node:fs"),Tr=require("node:path"),Qs=require("node:readline");var ei=new g("export"),ti=new g("import"),$t=new g("bulk");y($t,ei);y($t,ti);ei.option("-e, --export-level <exportLevel>",'Optional export level. Defaults to system level export. "Group/:id" - Group of Patients, "Patient" - All Patients.').option("-t, --types <types>","optional resource types to export").option("-s, --since <since>","optional Resources will be included in the response if their state has changed after the supplied time (e.g. if Resource.meta.lastUpdated is later than the supplied _since time).").option("-d, --target-directory <targetDirectory>","optional target directory to save files from the bulk export operations.").action(async t=>{let{exportLevel:e,types:r,since:o,targetDirectory:n}=t,s=await R(t);(await s.bulkExport(e,r,o,{pollStatusOnAccepted:!0})).output?.forEach(async({type:c,url:l})=>{let u=new URL(l),h=await s.download(l),f=`${c}_${u.pathname}`.replace(/[^a-zA-Z0-9]+/g,"_")+".ndjson",S=(0,Tr.resolve)(n??"",f);(0,xt.writeFile)(`${S}`,await h.text(),()=>{console.log(`${S} is created`)})})});ti.argument("<filename>","File Name").option("--num-resources-per-request <numResourcesPerRequest>","optional number of resources to import per batch request. Defaults to 25.","25").option("--add-extensions-for-missing-values","optional flag to add extensions for missing values in a resource",!1).option("-d, --target-directory <targetDirectory>","optional target directory of file to be imported").action(async(t,e)=>{let{numResourcesPerRequest:r,addExtensionsForMissingValues:o,targetDirectory:n}=e,s=(0,Tr.resolve)(n??process.cwd(),t),i=await R(e);await Bu(s,Number.parseInt(r,10),i,o)});async function Bu(t,e,r,o){let n=[],s=(0,xt.createReadStream)(t),i=(0,Qs.createInterface)({input:s});for await(let c of i){let l=Wu(c,o);n.push({resource:l,request:{method:"POST",url:l.resourceType}}),n.length%e===0&&(await Zs(n,r),n=[])}n.length>0&&await Zs(n,r)}async function Zs(t,e){(await e.executeBatch({resourceType:"Bundle",type:"transaction",entry:t})).entry?.forEach(o=>{J(o.response)})}function Wu(t,e){let r=JSON.parse(t);return e?Ku(r):r}function Ku(t){return t.resourceType==="ExplanationOfBenefit"?qu(t):t}function qu(t){return t.provider||(t.provider=Yt()),t.item?.forEach(e=>{e?.productOrService||(e.productOrService=Yt())}),t}var Lt=require("@medplum/core");var oi=require("node:net"),ni=require("@medplum/core"),Or=$(require("iconv-lite"),1),ci=$(require("node:net"),1),ri=class extends EventTarget{addEventListener(t,e,r){super.addEventListener(t,e,r)}removeEventListener(t,e,r){super.removeEventListener(t,e,r)}};var Hu=class extends Event{constructor(t,e){super("message"),this.connection=t,this.message=e}},Nt=class extends Event{constructor(t){super("error"),this.error=t}},si=class extends Event{constructor(){super("close")}},ii=class extends ri{constructor(t,e="utf-8",r=!1){super(),this.chunks=[],this.messageQueue=[],this.socket=t,this.encoding=e,this.enhancedMode=r,t.on("data",o=>{try{if(this.appendData(o),o.at(-2)===28&&o.at(-1)===13){let n=Buffer.concat(this.chunks),s=n.subarray(1,n.length-2),i=Or.default.decode(s,this.encoding),c=ni.Hl7Message.parse(i);this.dispatchEvent(new Hu(this,c)),this.resetBuffer()}}catch(n){this.dispatchEvent(new Nt(n))}}),t.on("error",o=>{this.resetBuffer(),this.dispatchEvent(new Nt(o))}),t.on("end",()=>{this.close()}),this.addEventListener("message",o=>{r&&this.send(o.message.buildAck({ackCode:"CA"}));let n=this.messageQueue.shift();if(!n){this.dispatchEvent(new Nt(new Error(`Received a message when no pending messages were in the queue. Message: ${o.message}`)));return}n.resolve?.(o.message)})}sendImpl(t,e){this.messageQueue.push(e);let r=t.toString(),o=Or.default.encode(r,this.encoding),n=Buffer.alloc(o.length+3);n.writeInt8(11,0),o.copy(n,1),n.writeInt8(28,o.length+1),n.writeInt8(13,o.length+2),this.socket.write(n)}send(t){this.sendImpl(t,{message:t})}async sendAndWait(t){return new Promise((e,r)=>{let o={message:t,resolve:e,reject:r};this.sendImpl(t,o)})}close(){this.socket.end(),this.socket.destroy(),this.dispatchEvent(new si)}appendData(t){this.chunks.push(t)}resetBuffer(){this.chunks=[]}},ai=class extends ri{constructor(t){super(),this.options=t,this.host=this.options.host,this.port=this.options.port,this.encoding=this.options.encoding,this.keepAlive=this.options.keepAlive??!1,this.connectTimeout=this.options.connectTimeout??3e4}connect(){return this.connection?Promise.resolve(this.connection):(this.socket&&(this.socket.removeAllListeners(),this.socket.destroy(),this.socket=void 0),new Promise((t,e)=>{this.socket=(0,oi.connect)({host:this.host,port:this.port,keepAlive:this.keepAlive}),this.connectTimeout>0&&(this.socket.setTimeout(this.connectTimeout),this.socket.on("timeout",()=>{let r=new Error(`Connection timeout after ${this.connectTimeout}ms`);this.socket&&(this.socket.destroy(),this.socket=void 0),e(r)})),this.socket.on("connect",()=>{if(!this.socket)return;let r;this.connection=r=new ii(this.socket,this.encoding),this.socket.setTimeout(0),r.addEventListener("close",()=>{this.socket=void 0,this.dispatchEvent(new si)}),r.addEventListener("error",o=>{this.dispatchEvent(new Nt(o.error))}),t(this.connection)}),this.socket.on("error",r=>{this.socket&&(this.socket.destroy(),this.socket=void 0),e(r)})}))}async send(t){return(await this.connect()).send(t)}async sendAndWait(t){return(await this.connect()).sendAndWait(t)}close(){this.socket&&(this.socket.removeAllListeners(),this.socket.destroy(),this.socket=void 0),this.connection&&(this.connection.close(),delete this.connection)}},li=class{constructor(t){this.handler=t}start(t,e,r=!1){let o=ci.default.createServer(n=>{let s=new ii(n,e,r);this.handler(s)});o.listen(t),this.server=o}async stop(){return new Promise((t,e)=>{if(!this.server){e(new Error("Stop was called but there is no server running"));return}this.server.close(r=>{if(r){e(r);return}t()}),this.server=void 0})}};var ui=require("node:fs");var Gu=new g("send").description("Send an HL7 v2 message via MLLP").argument("<host>","The destination host name or IP address").argument("<port>","The destination port number").argument("[body]","Optional HL7 message body").option("--generate-example","Generate a sample HL7 message").option("--file <file>","Read the HL7 message from a file").option("--encoding <encoding>","The encoding to use").action(async(t,e,r,o)=>{if(o.generateExample?r=Ju():o.file&&(r=(0,ui.readFileSync)(o.file,"utf8")),!r)throw new Error("Missing HL7 message body");let n=new ai({host:t,port:Number.parseInt(e,10),encoding:o.encoding});try{let s=await n.sendAndWait(Lt.Hl7Message.parse(r));console.log(s.toString().replaceAll("\r",`
`))}finally{n.close()}}),Vu=new g("listen").description("Starts an HL7 v2 MLLP server").argument("<port>").option("--encoding <encoding>","The encoding to use").action(async(t,e)=>{new li(o=>{o.addEventListener("message",({message:n})=>{console.log(n.toString().replaceAll("\r",`
`)),o.send(n.buildAck())})}).start(Number.parseInt(t,10),e.encoding),console.log("Listening on port "+t)}),kt=new g("hl7");y(kt,Gu);y(kt,Vu);function Ju(){let t=(0,Lt.formatHl7DateTime)(new Date),e=Date.now().toString();return`MSH|^~\\&|ADTSYS|HOSPITAL|RECEIVER|DEST|${t}||ADT^A01|${e}|P|2.5|
EVN|A01|${t}||
PID|1|12345|12345^^^HOSP^MR|123456|DOE^JOHN^MIDDLE^SUFFIX|19800101|M|||123 STREET^APT 4B^CITY^ST^12345-6789||555-555-5555||S|
PV1|1|I|2000^2012^01||||12345^DOCTOR^DOC||||||||||1234567^DOCTOR^DOC||AMB|||||||||||||||||||||||||202309280900|`}var pi=require("node:fs"),di=require("node:os"),mi=require("node:path");var fi=new g("set"),hi=new g("remove"),gi=new g("list"),yi=new g("describe"),Ae=new g("profile");y(Ae,fi);y(Ae,hi);y(Ae,gi);y(Ae,yi);fi.argument("<profileName>","Name of the profile").description("Create a new profile or replace it with the given name and its associated properties").action(async(t,e)=>{et(t,e)});hi.argument("<profileName>","Name of the profile").description("Remove a profile by name").action(async t=>{new j(t).setObject("options",void 0),console.log(`${t} profile removed`)});gi.description("List all profiles saved").action(async()=>{let t=(0,mi.resolve)((0,di.homedir)(),".medplum"),e=(0,pi.readdirSync)(t),r=[];e.forEach(o=>{let n=o.split(".")[0],i=new j(n).getObject("options");i&&r.push({profileName:n,profile:i})}),console.log(r)});yi.argument("<profileName>","Name of the profile").description("Describes a profile").action(async t=>{let e=yo(t);console.log(e)});var wi=require("commander");var Ei=new g("list"),Si=new g("current"),Ri=new g("switch"),Ai=new g("invite"),Ie=new g("project");y(Ie,Ei);y(Ie,Si);y(Ie,Ri);y(Ie,Ai);Ei.description("List of current projects").action(async t=>{let e=await R(t);Xu(e)});function Xu(t){let r=t.getLogins().map(o=>`${o.project.display} (${o.project.reference})`).join(`

`);console.log(r)}Si.description("Project you are currently on").action(async t=>{let r=(await R(t)).getActiveLogin();if(!r)throw new Error("Unauthenticated: run `npx medplum login` to login");console.log(`${r.project.display} (${r.project.reference})`)});Ri.description("Switching to another project from the current one").argument("<projectId>").action(async(t,e)=>{let r=await R(e);await zu(r,t)});Ai.description("Invite a member to your current project (run npx medplum project current to confirm)").arguments("<firstName> <lastName> <email>").option("--send-email","If you want to send the email when inviting the user").option("--admin","If the user you are inviting is an admin").addOption(new wi.Option("-r, --role <role>","Role of user").choices(["Practitioner","Patient","RelatedPerson"]).default("Practitioner")).action(async(t,e,r,o)=>{let n=await R(o),s=n.getActiveLogin();if(!s)throw new Error("Unauthenticated: run `npx medplum login` to login");if(!s?.project?.reference)throw new Error("No current project to invite user to");let i=s.project.reference.split("/")[1],c={resourceType:o.role,firstName:t,lastName:e,email:r,sendEmail:!!o.sendEmail,admin:!!o.admin};await Yu(i,c,n)});async function zu(t,e){let o=t.getLogins().find(n=>n.project.reference?.includes(e));if(!o)throw new Error(`Project ${e} not found. Make sure you are added as a user to this project`);await t.setActiveLogin(o),console.log(`Switched to project ${e}
`)}async function Yu(t,e,r){await r.invite(t,e),e.sendEmail&&console.log("Email sent"),console.log("See your users at https://app.medplum.com/admin/users")}var Ii=require("@medplum/core");var xr=new g("delete"),$r=new g("get"),Nr=new g("patch"),Lr=new g("post"),kr=new g("put");xr.argument("<url>","Resource/$id").action(async(t,e)=>{let r=await R(e);J(await r.delete(qe(r,t)))});$r.argument("<url>","Resource/$id").option("--as-transaction","Print out the bundle as a transaction type").action(async(t,e)=>{let r=await R(e),o=await r.get(qe(r,t));e.asTransaction?J((0,Ii.convertToTransactionBundle)(o)):J(o)});Nr.arguments("<url> <body>").action(async(t,e,r)=>{let o=await R(r);J(await o.patch(qe(o,t),Dr(e)))});Lr.arguments("<url> <body>").option("--prefer-async",'Sets the Prefer header to "respond-async"').action(async(t,e,r)=>{let o=await R(r),n=r.preferAsync?{Prefer:"respond-async"}:void 0;J(await o.post(qe(o,t),Dr(e),void 0,{headers:n}))});kr.arguments("<url> <body>").action(async(t,e,r)=>{let o=await R(r);J(await o.put(qe(o,t),Dr(e)))});function Dr(t){if(t)try{return JSON.parse(t)}catch{return t}}function qe(t,e){return["admin/","auth/","fhir/R4"].some(o=>e.startsWith(o))?e:t.fhirUrl(e).toString()}async function bi(t){let e=new g("medplum").description("Command to access Medplum CLI").option("--client-id <clientId>","FHIR server client id").option("--client-secret <clientSecret>","FHIR server client secret").option("--base-url <baseUrl>","FHIR server base URL, must be absolute").option("--token-url <tokenUrl>","FHIR server token URL, absolute or relative to base URL").option("--authorize-url <authorizeUrl>","FHIR server authorize URL, absolute or relative to base URL").option("--fhir-url, --fhir-url-path <fhirUrlPath>","FHIR server URL, absolute or relative to base URL").option("--scope <scope>","JWT scope").option("--access-token <accessToken>","Access token for token exchange authentication").option("--callback-url <callbackUrl>","Callback URL for authorization code flow").option("--subject <subject>","Subject for JWT authentication").option("--audience <audience>","Audience for JWT authentication").option("--issuer <issuer>","Issuer for JWT authentication").option("--private-key-path <privateKeyPath>","Private key path for JWT assertion").option("-p, --profile <profile>","Profile name").option("-v --verbose","Verbose output").addOption(new He.Option("--auth-type <authType>","Type of authentication").choices(["basic","client-credentials","authorization-code","jwt-bearer","token-exchange","jwt-assertion"])).on("option:verbose",()=>{process.env.VERBOSE="1"});e.exitOverride(),e.version(ve.MEDPLUM_VERSION),e.configureHelp({showGlobalOptions:!0}),y(e,Qt),y(e,er),y(e,tr),y(e,$r),y(e,Lr),y(e,Nr),y(e,kr),y(e,xr),y(e,Ie),y(e,$t),y(e,Ke),y(e,ie),y(e,br),y(e,Pr),y(e,Cr),y(e,Ae),y(e,Js()),y(e,kt);try{await e.parseAsync(t)}catch(r){Pi(r)}}function Pi(t){let e=1,r=!0;if(t instanceof He.CommanderError&&(process.env.VERBOSE||(r=!1),e=t.exitCode),e!==0&&r){Mr(t,!!process.env.VERBOSE);let o=t.cause;if(process.env.VERBOSE)if(Array.isArray(o))for(let n of o)Mr(n,!0);else o instanceof Error&&Mr(o,!0)}process.exit(e)}function Mr(t,e=!1){if(e){console.error(t);return}t instanceof He.CommanderError?process.stderr.write(`${(0,ve.normalizeErrorString)(t)}
`):process.stderr.write(`Error: ${(0,ve.normalizeErrorString)(t)}
`)}async function Ci(){vi.default.config(),await bi(process.argv)}require.main===module&&Ci().catch(t=>{console.error("Unhandled error:",(0,ve.normalizeErrorString)(t)),process.exit(1)});0&&(module.exports={handleError,main,run});
//# sourceMappingURL=index.cjs.map
