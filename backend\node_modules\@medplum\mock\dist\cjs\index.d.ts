import { AccessPolicy } from '@medplum/fhirtypes';
import { Agent } from '@medplum/fhirtypes';
import { AuditEvent } from '@medplum/fhirtypes';
import { Binary } from '@medplum/fhirtypes';
import { BinarySource } from '@medplum/core';
import { Bot } from '@medplum/fhirtypes';
import { ClientApplication } from '@medplum/fhirtypes';
import { Communication } from '@medplum/fhirtypes';
import { CreateBinaryOptions } from '@medplum/core';
import type { CustomTableLayout } from 'pdfmake/interfaces';
import { Device } from '@medplum/fhirtypes';
import { DiagnosticReport } from '@medplum/fhirtypes';
import { Encounter } from '@medplum/fhirtypes';
import { FhirRouter } from '@medplum/fhir-router';
import { Group } from '@medplum/fhirtypes';
import { IReconnectingWebSocket } from '@medplum/core';
import { LoginState } from '@medplum/core';
import { Media } from '@medplum/fhirtypes';
import { MedplumClient } from '@medplum/core';
import { MedplumClientOptions } from '@medplum/core';
import { MedplumRequestOptions } from '@medplum/core';
import { MemoryRepository } from '@medplum/fhir-router';
import { Observation } from '@medplum/fhirtypes';
import { Organization } from '@medplum/fhirtypes';
import { Patient } from '@medplum/fhirtypes';
import { PlanDefinition } from '@medplum/fhirtypes';
import { Practitioner } from '@medplum/fhirtypes';
import { ProfileResource } from '@medplum/core';
import { Questionnaire } from '@medplum/fhirtypes';
import { QuestionnaireResponse } from '@medplum/fhirtypes';
import { Reference } from '@medplum/fhirtypes';
import { RelatedPerson } from '@medplum/fhirtypes';
import { RequestGroup } from '@medplum/fhirtypes';
import { Schedule } from '@medplum/fhirtypes';
import { ServiceRequest } from '@medplum/fhirtypes';
import { Slot } from '@medplum/fhirtypes';
import { Specimen } from '@medplum/fhirtypes';
import { StructureDefinition } from '@medplum/fhirtypes';
import { Subscription } from '@medplum/fhirtypes';
import { SubscriptionEmitter } from '@medplum/core';
import { SubscriptionEventMap } from '@medplum/core';
import { SubscriptionManager } from '@medplum/core';
import { Task } from '@medplum/fhirtypes';
import type { TDocumentDefinitions } from 'pdfmake/interfaces';
import type { TFontDictionary } from 'pdfmake/interfaces';
import { TypedEventTarget } from '@medplum/core';
import { UserConfiguration } from '@medplum/fhirtypes';
import { ValueSet } from '@medplum/fhirtypes';
import { WebSocketEventMap as WebSocketEventMap_2 } from '@medplum/core';
import { WithId } from '@medplum/core';

export declare const BartSimpson: Patient;

export declare const BlinkyTheFish: Patient;

/**
 * Creates a fake JWT token with the provided claims for testing.
 *
 * **NOTE: This function does not create a real signed JWT. Attempting to read the header or signature will fail.**
 *
 * @param claims - The claims to encode in the body of the fake JWT.
 * @returns A stringified fake JWT token.
 */
export declare function createFakeJwt(claims: Record<string, string | number>): string;

export declare const DifferentOrganization: WithId<Organization>;

export declare const DrAliceSmith: WithId<Practitioner>;

export declare const DrAliceSmithPreviousVersion: WithId<Practitioner>;

export declare const DrAliceSmithSchedule: WithId<Schedule>;

export declare const ExampleAccessPolicy: WithId<AccessPolicy>;

export declare const ExampleAuditEvent: AuditEvent;

export declare const ExampleBot: Bot;

export declare const ExampleBotSourceCode: Binary;

export declare const ExampleClient: ClientApplication;

export declare const ExampleQuestionnaire: Questionnaire;

export declare const ExampleQuestionnaireResponse: QuestionnaireResponse;

export declare const ExampleStatusValueSet: WithId<ValueSet>;

export declare const ExampleSubscription: Subscription;

export declare const ExampleThreadHeader: {
    id: string;
    resourceType: "Communication";
    topic: {
        text: string;
    };
    sender: Reference<WithId<Practitioner>> & {
        reference: string;
    };
    recipient: ((Reference<Patient> & {
        reference: string;
    }) | (Reference<WithId<Practitioner>> & {
        reference: string;
    }))[];
    sent: string;
    status: "in-progress";
};

export declare const ExampleThreadMessages: ({
    id: string;
    payload: {
        contentString: string;
    }[];
    sent: string;
    received: string;
    sender: Reference<WithId<Practitioner>> & {
        reference: string;
    };
    recipient: (Reference<Patient> & {
        reference: string;
    })[];
    resourceType: "Communication";
    status: "completed";
    partOf: (Reference<    {
    id: string;
    resourceType: "Communication";
    topic: {
    text: string;
    };
    sender: Reference<WithId<Practitioner>> & {
    reference: string;
    };
    recipient: ((Reference<Patient> & {
    reference: string;
    }) | (Reference<WithId<Practitioner>> & {
    reference: string;
    }))[];
    sent: string;
    status: "in-progress";
    }> & {
        reference: string;
    })[];
} | {
    id: string;
    payload: {
        contentString: string;
    }[];
    sent: string;
    received: string;
    sender: Reference<Patient> & {
        reference: string;
    };
    recipient: (Reference<WithId<Practitioner>> & {
        reference: string;
    })[];
    resourceType: "Communication";
    status: "completed";
    partOf: (Reference<    {
    id: string;
    resourceType: "Communication";
    topic: {
    text: string;
    };
    sender: Reference<WithId<Practitioner>> & {
    reference: string;
    };
    recipient: ((Reference<Patient> & {
    reference: string;
    }) | (Reference<WithId<Practitioner>> & {
    reference: string;
    }))[];
    sent: string;
    status: "in-progress";
    }> & {
        reference: string;
    })[];
} | {
    payload: {
        contentString: string;
    }[];
    sent: string;
    received: string;
    sender: Reference<Patient> & {
        reference: string;
    };
    recipient: (Reference<WithId<Practitioner>> & {
        reference: string;
    })[];
    resourceType: "Communication";
    status: "completed";
    partOf: (Reference<    {
    id: string;
    resourceType: "Communication";
    topic: {
    text: string;
    };
    sender: Reference<WithId<Practitioner>> & {
    reference: string;
    };
    recipient: ((Reference<Patient> & {
    reference: string;
    }) | (Reference<WithId<Practitioner>> & {
    reference: string;
    }))[];
    sent: string;
    status: "in-progress";
    }> & {
        reference: string;
    })[];
} | {
    id: string;
    status: "in-progress";
    payload: {
        contentString: string;
    }[];
    sent: string;
    sender: Reference<Patient> & {
        reference: string;
    };
    recipient: (Reference<WithId<Practitioner>> & {
        reference: string;
    })[];
    resourceType: "Communication";
    partOf: (Reference<    {
    id: string;
    resourceType: "Communication";
    topic: {
    text: string;
    };
    sender: Reference<WithId<Practitioner>> & {
    reference: string;
    };
    recipient: ((Reference<Patient> & {
    reference: string;
    }) | (Reference<WithId<Practitioner>> & {
    reference: string;
    }))[];
    sent: string;
    status: "in-progress";
    }> & {
        reference: string;
    })[];
})[];

export declare const ExampleUserConfiguration: WithId<UserConfiguration>;

export declare const exampleValueSet: ValueSet;

export declare const ExampleWorkflowPlanDefinition: WithId<PlanDefinition>;

export declare const ExampleWorkflowQuestionnaire1: WithId<Questionnaire>;

export declare const ExampleWorkflowQuestionnaire2: WithId<Questionnaire>;

export declare const ExampleWorkflowQuestionnaire3: WithId<Questionnaire>;

export declare const ExampleWorkflowQuestionnaireResponse1: WithId<QuestionnaireResponse>;

export declare const ExampleWorkflowRequestGroup: WithId<RequestGroup>;

export declare const ExampleWorkflowTask1: WithId<Task>;

export declare const ExampleWorkflowTask2: WithId<Task>;

export declare const ExampleWorkflowTask3: WithId<Task>;

export declare const FishPatientResources: {
    getFishPatientProfileSD: () => ProfileStructureDefinition;
    getFishSpeciesExtensionSD: () => ProfileStructureDefinition;
    getSampleFishPatient: () => Patient;
    getBlinkyTheFish: () => Patient;
};

export declare const HomerBartRelatedPerson: RelatedPerson;

export declare const HomerCommunication: Communication;

export declare const HomerDiagnosticReport: DiagnosticReport;

export declare const HomerEncounter: Encounter;

export declare const HomerLisaRelatedPerson: RelatedPerson;

export declare const HomerMedia: Media;

export declare const HomerObservation1: Observation;

export declare const HomerObservation2: Observation;

export declare const HomerObservation3: Observation;

export declare const HomerObservation4: Observation;

export declare const HomerObservation5: Observation;

export declare const HomerObservation6: Observation;

export declare const HomerObservation7: Observation;

export declare const HomerObservation8: Observation;

export declare const HomerServiceRequest: ServiceRequest;

export declare const HomerSimpson: Patient;

export declare const HomerSimpsonPreviousVersion: Patient;

export declare const HomerSimpsonSpecimen: Specimen;

export declare const HomerSimpsonUSCorePatient: Patient;

export declare const ImplantableDeviceKnee: Device;

export declare const LisaSimpson: Patient;

export declare const makeDrAliceSmithSlots: () => WithId<Slot>[];

export declare const MargeSimpson: Patient;

export declare class MockClient extends MedplumClient {
    readonly router: FhirRouter;
    readonly repo: MemoryRepository;
    readonly client: MockFetchClient;
    readonly debug: boolean;
    activeLoginOverride?: LoginState;
    private agentAvailable;
    private profile;
    subManager: MockSubscriptionManager | undefined;
    constructor(clientOptions?: MockClientOptions);
    clear(): void;
    getProfile(): ProfileResource | undefined;
    getUserConfiguration(): WithId<UserConfiguration> | undefined;
    setActiveLoginOverride(activeLoginOverride: LoginState): void;
    setProfile(profile: ProfileResource | undefined): void;
    getActiveLogin(): LoginState | undefined;
    getLogins(): LoginState[];
    /**
     * Creates a FHIR `Binary` resource with the provided data content.
     *
     * The return value is the newly created resource, including the ID and meta.
     *
     * The `data` parameter can be a string or a `File` object.
     *
     * A `File` object often comes from a `<input type="file">` element.
     *
     * @example
     * Example:
     *
     * ```typescript
     * const result = await medplum.createBinary(myFile, 'test.jpg', 'image/jpeg');
     * console.log(result.id);
     * ```
     *
     * See the FHIR "create" operation for full details: https://www.hl7.org/fhir/http.html#create
     *
     * @category Create
     * @param createBinaryOptions -The binary options. See `CreateBinaryOptions` for full details.
     * @param requestOptions - Optional fetch options. **NOTE:** only `options.signal` is respected when `onProgress` is also provided.
     * @returns The result of the create operation.
     */
    createBinary(createBinaryOptions: CreateBinaryOptions, requestOptions?: MedplumRequestOptions): Promise<WithId<Binary>>;
    /**
     * @category Create
     * @param data - The binary data to upload.
     * @param filename - Optional filename for the binary.
     * @param contentType - Content type for the binary.
     * @param onProgress - Optional callback for progress events. **NOTE:** only `options.signal` is respected when `onProgress` is also provided.
     * @param options - Optional fetch options. **NOTE:** only `options.signal` is respected when `onProgress` is also provided.
     * @returns The result of the create operation.
     * @deprecated Use `createBinary` with `CreateBinaryOptions` instead. To be removed in a future version.
     */
    createBinary(data: BinarySource, filename: string | undefined, contentType: string, onProgress?: (e: ProgressEvent) => void, options?: MedplumRequestOptions): Promise<WithId<Binary>>;
    pushToAgent(agent: Agent | Reference<Agent>, destination: Device | Reference<Device> | string, body: any, contentType?: string, _waitForResponse?: boolean, _options?: MedplumRequestOptions): Promise<any>;
    setAgentAvailable(value: boolean): void;
    getSubscriptionManager(): MockSubscriptionManager;
    setSubscriptionManager(subManager: MockSubscriptionManager): void;
    subscribeToCriteria(criteria: string, subscriptionProps?: Partial<Subscription>): SubscriptionEmitter;
    unsubscribeFromCriteria(criteria: string, subscriptionProps?: Partial<Subscription>): void;
    getMasterSubscriptionEmitter(): SubscriptionEmitter;
}

export declare interface MockClientOptions extends Pick<MedplumClientOptions, 'baseUrl' | 'clientId' | 'storage' | 'cacheTime' | 'fetch'> {
    readonly debug?: boolean;
    /**
     * Override currently logged in user. Specifying null results in
     * MedplumContext.profile returning undefined as if no one were logged in.
     */
    readonly profile?: ReturnType<MedplumClient['getProfile']> | null;
    /**
     * Override the `MockFetchClient` used by this `MockClient`.
     */
    readonly mockFetchOverride?: MockFetchOverrideOptions;
}

export declare type MockCriteriaEntry = {
    criteria: string;
    subscriptionProps?: Partial<Subscription>;
    emitter: SubscriptionEmitter;
    count: number;
};

export declare class MockFetchClient {
    readonly router: FhirRouter;
    readonly repo: MemoryRepository;
    readonly baseUrl: string;
    readonly debug: boolean;
    initialized: boolean;
    initPromise?: Promise<void>;
    constructor(router: FhirRouter, repo: MemoryRepository, baseUrl: string, debug?: boolean);
    mockFetch(url: string, options: any): Promise<Partial<Response>>;
    mockCreatePdf(docDefinition: TDocumentDefinitions, tableLayouts?: {
        [name: string]: CustomTableLayout;
    }, fonts?: TFontDictionary): Promise<any>;
    private mockHandler;
    private mockAdminHandler;
    private mockAuthHandler;
    private mockChangePasswordHandler;
    private mockLoginHandler;
    private mockSetPasswordHandler;
    private mockNewUserHandler;
    private mockResetPasswordHandler;
    private mockOAuthHandler;
    private initMockRepo;
    private mockFhirHandler;
}

/**
 * Override must contain all of `router`, `repo`, and `client`.
 */
export declare type MockFetchOverrideOptions = {
    client: MockFetchClient;
    router: FhirRouter;
    repo: MemoryRepository;
};

export declare class MockReconnectingWebSocket extends TypedEventTarget<WebSocketEventMap_2> implements IReconnectingWebSocket {
    readyState: WebSocket['OPEN'] | WebSocket['CLOSED'];
    close(code?: number, reason?: string): void;
    send(): void;
    reconnect(_code?: number, _reason?: string): void;
}

export declare interface MockSubManagerOptions {
    mockReconnectingWebSocket?: boolean;
}

export declare class MockSubscriptionManager extends SubscriptionManager {
    entries: Map<string, MockCriteriaEntry[]>;
    masterEmitter: SubscriptionEmitter;
    constructor(medplum: MedplumClient, _wsUrl: string, options?: MockSubManagerOptions);
    private maybeGetMockCriteriaEntry;
    private removeMockCriteriaEntry;
    private getAllMockCriteriaEmitters;
    addCriteria(criteria: string, subscriptionProps?: Partial<Subscription>): SubscriptionEmitter;
    removeCriteria(criteria: string, subscriptionProps?: Partial<Subscription>): void;
    closeWebSocket(): void;
    openWebSocket(): void;
    getCriteriaCount(): number;
    getMasterEmitter(): SubscriptionEmitter;
    emitEventForCriteria<K extends keyof SubscriptionEventMap = keyof SubscriptionEventMap>(criteria: string, event: SubscriptionEventMap[K], subscriptionProps?: Partial<Subscription>): void;
    getEmitter(criteria: string, subscriptionProps?: Partial<Subscription>): SubscriptionEmitter | undefined;
}

export declare type ProfileStructureDefinition = StructureDefinition & {
    url: string;
    name: string;
};

export declare const SimpsonsFamily: Group;

export declare const TestOrganization: WithId<Organization>;

export declare const USCoreStructureDefinitionList: StructureDefinition[];

export { }
