// app/patients/patientRoutes.ts
import { Router } from 'express';
import * as patientService from './patientService';
import { getMedplumClient } from '../helper/medplumHelper';
const router = Router();
/**
 * Create a new patient
 * POST /api/patients
 */
router.post('/', async (req, res) => {
    try {
        const patientData = req.body;
        // Validate required fields
        if (!patientData.family || !patientData.given || patientData.given.length === 0) {
            return res.status(400).json({
                error: 'Missing required fields',
                message: 'family and given names are required',
                required: ['family', 'given']
            });
        }
        // Get Medplum client
        const client = await getMedplumClient();
        // Create patient
        const patient = await patientService.createPatient(client, patientData);
        console.log(`Patient created:`, patient.id);
        res.status(201).json({
            success: true,
            message: 'Patient created successfully',
            data: patient
        });
    }
    catch (error) {
        console.error('Error creating patient:', error.message);
        res.status(400).json({
            error: 'Failed to create patient',
            message: error.message,
            details: error.response?.data || null
        });
    }
});
/**
 * Get patient by ID
 * GET /api/patients/:id
 */
router.get('/:id', async (req, res) => {
    try {
        const patientId = req.params.id;
        if (!patientId) {
            return res.status(400).json({
                error: 'Patient ID is required'
            });
        }
        // Get Medplum client
        const client = await getMedplumClient();
        // Get patient
        const patient = await patientService.getPatient(client, patientId);
        if (!patient) {
            return res.status(404).json({
                error: 'Patient not found',
                patientId
            });
        }
        console.log(`Patient retrieved:`, patient.id);
        res.json({
            success: true,
            data: patient
        });
    }
    catch (error) {
        console.error('Error retrieving patient:', error.message);
        if (error.message.includes('not found') || error.status === 404) {
            res.status(404).json({
                error: 'Patient not found',
                patientId: req.params.id
            });
        }
        else {
            res.status(500).json({
                error: 'Failed to retrieve patient',
                message: error.message
            });
        }
    }
});
/**
 * Update patient
 * PATCH /api/patients/:id
 */
router.patch('/:id', async (req, res) => {
    try {
        const patientId = req.params.id;
        const updateData = req.body;
        if (!patientId) {
            return res.status(400).json({
                error: 'Patient ID is required'
            });
        }
        // Get Medplum client
        const client = await getMedplumClient();
        // Update patient
        const updatedPatient = await patientService.updatePatient(client, patientId, updateData);
        console.log(`Patient updated:`, updatedPatient.id);
        res.json({
            success: true,
            message: 'Patient updated successfully',
            data: updatedPatient
        });
    }
    catch (error) {
        console.error('Error updating patient:', error.message);
        if (error.message.includes('not found') || error.status === 404) {
            res.status(404).json({
                error: 'Patient not found',
                patientId: req.params.id
            });
        }
        else {
            res.status(400).json({
                error: 'Failed to update patient',
                message: error.message
            });
        }
    }
});
/**
 * Delete patient
 * DELETE /api/patients/:id
 */
router.delete('/:id', async (req, res) => {
    try {
        const patientId = req.params.id;
        if (!patientId) {
            return res.status(400).json({
                error: 'Patient ID is required'
            });
        }
        // Get Medplum client
        const client = await getMedplumClient();
        // Delete patient
        await patientService.deletePatient(client, patientId);
        console.log(`🗑️ Patient deleted:`, patientId);
        res.status(204).send();
    }
    catch (error) {
        console.error('Error deleting patient:', error.message);
        if (error.message.includes('not found') || error.status === 404) {
            res.status(404).json({
                error: 'Patient not found',
                patientId: req.params.id
            });
        }
        else {
            res.status(500).json({
                error: 'Failed to delete patient',
                message: error.message
            });
        }
    }
});
/**
 * List patients with pagination
 * GET /api/patients
 */
router.get('/', async (req, res) => {
    try {
        const limit = Math.min(parseInt(req.query.limit) || 20, 100); // Max 100
        const offset = parseInt(req.query.offset) || 0;
        const search = req.query.search;
        // Get Medplum client
        const client = await getMedplumClient();
        // Use the search service instead of direct client call
        const searchResult = await patientService.searchPatients(client, {
            search,
            limit,
            offset
        });
        console.log(`📋 ${searchResult.patients.length} patients retrieved`);
        res.json({
            success: true,
            data: searchResult.patients,
            pagination: {
                total: searchResult.total,
                limit,
                offset,
                hasMore: searchResult.hasMore
            }
        });
    }
    catch (error) {
        console.error('Error listing patients:', error.message);
        res.status(500).json({
            error: 'Failed to retrieve patients',
            message: error.message
        });
    }
});
/**
 * Health check for patients API
 * GET /api/patients/health
 */
router.get('/health', async (req, res) => {
    try {
        // Test Medplum connection
        const client = await getMedplumClient();
        // Try a simple operation to verify connection (using search service)
        const testResult = await patientService.searchPatients(client, { limit: 1 });
        res.json({
            status: 'OK',
            service: 'Patients API',
            timestamp: new Date().toISOString(),
            medplum: {
                connected: true,
                baseUrl: process.env.MEDPLUM_BASE_URL || 'https://api.medplum.com/',
                testQueryResults: testResult.patients.length
            },
            endpoints: {
                'POST /': 'Create patient',
                'GET /': 'List patients',
                'GET /:id': 'Get patient by ID',
                'PATCH /:id': 'Update patient',
                'DELETE /:id': 'Delete patient',
            }
        });
    }
    catch (error) {
        res.status(500).json({
            status: 'ERROR',
            service: 'Patients API',
            error: error.message,
            medplum: {
                connected: false,
                error: error.message
            }
        });
    }
});
export default router;
//# sourceMappingURL=patientRoutes.js.map