import { z } from 'zod';
/**
 * @file Defines Zod schemas and TypeScript types for authentication-related data.
 */
export declare const LoginSchema: z.ZodObject<{
    email: z.ZodString;
    password: z.ZodString;
}, "strip", z.ZodTypeAny, {
    email: string;
    password: string;
}, {
    email: string;
    password: string;
}>;
/**
 * TypeScript type inferred from LoginSchema.
 * Represents the structure of a login request payload.
 */
export type LoginRequest = z.infer<typeof LoginSchema>;
/**
 * Interface for a User object, reflecting a simplified Medplum "Profile" or "User" resource.
 * This is what we expect to get back as the authenticated user's details.
 */
export interface User {
    email: string;
    firstName?: string;
    lastName?: string;
    role?: 'admin' | 'doctor' | 'nurse';
}
/**
 * Interface for a Login Response from AuthService.
 * This is what we expect to get back as the authenticated user's details.
 */
export interface LoginResponse {
    token: string;
    user: User;
}
//# sourceMappingURL=authSchema.d.ts.map