import { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>aUser<PERSON><PERSON>, <PERSON>aLock } from 'react-icons/fa';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import './Signup.css';

/**
 * Signup component for user registration with integrated captcha verification.
 * Handles new user account creation with comprehensive form validation and security.
 * Features a custom slider captcha for bot protection and enhanced user verification.
 * 
 * Features:
 * - Complete form validation with email format and password confirmation
 * - Custom slider captcha for security verification
 * - Error handling with specific user feedback
 * - Loading states and accessibility support
 * - Integration with AuthContext for compatibility
 * - Responsive design with custom CSS styling
 * 
 * @returns {JSX.Element} Signup form interface with captcha verification
 */
const Signup = () => {
  const navigate = useNavigate();
  const { medplum } = useAuth();

  // Form state management
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [captchaToken, setCaptchaToken] = useState(false);
  const [captchaCompleted, setCaptchaCompleted] = useState(false);


  /**
   * Initializes captcha token to true on component mount.
   * This ensures the captcha system is ready for user interaction.
   */
  useEffect(() => {
    setCaptchaToken(true);
  }, []);


  /**
   * Validates the signup form data comprehensively.
   * Performs validation for required fields, email format, password strength,
   * password confirmation, and captcha completion.
   * 
   * @returns {Object} Validation result with isValid flag and error message
   */
  const validateForm = () => {
    const { firstName, lastName, email, password, confirmPassword } = formData;

    // Check required fields
    if (!firstName.trim() || !lastName.trim() || !email.trim() || !password || !confirmPassword) {
      return { isValid: false, error: 'All fields are required.' };
    }

    // Validate name fields (letters, spaces, hyphens, apostrophes only)
    const nameRegex = /^[a-zA-Z\s'-]+$/;
    if (!nameRegex.test(firstName.trim())) {
      return { isValid: false, error: 'First name should only contain letters, spaces, hyphens, and apostrophes.' };
    }
    if (!nameRegex.test(lastName.trim())) {
      return { isValid: false, error: 'Last name should only contain letters, spaces, hyphens, and apostrophes.' };
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      return { isValid: false, error: 'Please enter a valid email address.' };
    }

    // Validate password strength
    if (password.length < 6) {
      return { isValid: false, error: 'Password must be at least 6 characters long.' };
    }

    // Check password confirmation
    if (password !== confirmPassword) {
      return { isValid: false, error: 'Passwords do not match.' };
    }

    // Validate captcha completion
    if (!captchaCompleted) {
      return { isValid: false, error: 'Please complete the captcha verification by sliding to the right.' };
    }

    return { isValid: true, error: '' };
  };


  /**
   * Handles input field changes in the signup form.
   * Updates form data state and clears errors when user starts typing.
   * 
   * @param {Event} e - Input change event
   */
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (error) {
      setError('');
    }
  };

  /**
   * Handles signup form submission with comprehensive validation.
   * Validates form data, checks captcha completion, simulates account creation,
   * and redirects to login on success.
   * 
   * @param {Event} e - Form submit event
   */
  const handleSignup = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Validate form data including captcha
    const validation = validateForm();
    if (!validation.isValid) {
      setError(validation.error);
      setIsLoading(false);
      return;
    }
    try {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call

      setIsLoading(false);
      alert('Signup successful! Please log in with your credentials.');
      navigate('/login');

    } catch (err) {
      console.error('Full Signup error:', err);
      setIsLoading(false);
      setError('An unexpected error occurred during signup. Please try again.');
    }
  };

  /**
  * Handles CAPTCHA verification completion.
  * Called when the slider captcha is successfully completed by the user.
  * Updates both captcha token and completion status for form validation.
  */
  const handleCaptchaVerification = () => {
    setCaptchaToken(true);  // Mark CAPTCHA as solved when slider completes
    setCaptchaCompleted(true);
    setError('');
  };

  /**
  * Initializes and manages the slider captcha functionality.
  * Sets up mouse event listeners for drag-and-drop captcha interaction.
  * Handles slider movement, position validation, and completion detection.
  */
  useEffect(() => {
    const slider = document.getElementById('slider');
    const sliderBlock = document.getElementById('slider-block');
    const captchaResponse = document.getElementById('captcha-response');


    // Early return if elements don't exist
    if (!slider || !sliderBlock || !captchaResponse) {
      return;
    }

    let isDragging = false;

    /**
     * Handles mouse down event on slider block to initiate dragging.
     * Sets up event listeners for mouse movement and release.
     * 
     * @param {MouseEvent} e - Mouse down event
     */
    const handleMouseDown = (e) => {
      isDragging = true;
      let startX = e.clientX;

      /**
       * Handles mouse movement during slider drag operation.
       * Calculates new position and constrains movement within slider bounds.
       * 
       * @param {MouseEvent} moveEvent - Mouse move event
      */
      const moveSlider = (moveEvent) => {
        if (isDragging) {
          const dx = moveEvent.clientX - startX;
          const newLeft = sliderBlock.offsetLeft + dx;
          const maxLeft = slider.offsetWidth - sliderBlock.offsetWidth;

          // Constrain movement within slider bounds
          if (newLeft >= 0 && newLeft <= maxLeft) {
            sliderBlock.style.left = newLeft + 'px';
            startX = moveEvent.clientX;

          }
        }
      };

      /**
      * Handles mouse release to complete drag operation.
      * Validates slider position and triggers captcha verification if successful.
      */
      const stopDragging = () => {
        isDragging = false;
        document.removeEventListener('mousemove', moveSlider);
        document.removeEventListener('mouseup', stopDragging);
        const maxLeft = slider.offsetWidth - sliderBlock.offsetWidth;
        const threshold = maxLeft * 0.9; //

        // Check if slider has reached the completion threshold
        if (sliderBlock.offsetLeft >= threshold) {
          captchaResponse.value = 'success';
          handleCaptchaVerification();

          // Visual feedback for successful completion
          sliderBlock.style.backgroundColor = '#10b981';
          slider.style.borderColor = '#10b981';
        } else {
          // Reset slider position if not completed
          sliderBlock.style.left = '0px';
          setCaptchaCompleted(false);
          setCaptchaToken(false);
        }
      };
      // Add event listeners for drag operation
      document.addEventListener('mousemove', moveSlider);
      document.addEventListener('mouseup', stopDragging);
    };

    // Add mouse down listener to slider block
    sliderBlock.addEventListener('mousedown', handleMouseDown);

    // Cleanup function to remove event listeners
    return () => {
      if (sliderBlock) {
        sliderBlock.removeEventListener('mousedown', handleMouseDown);
      }
    };
  }, []);  // Empty dependency array to run only once after the component mounts

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      {/*Header Section*/}
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-gradient-to-r from-emerald-500 to-green-600 shadow-lg">
          <FaUserPlus className="h-8 w-8 text-white" aria-hidden="true" />
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Create your Syncore account
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Or{' '}
          <Link to="/login"
            className="font-medium text-primary hover:text-primary-dark">
            Sign in to your existing account
          </Link>
        </p>
      </div>
      {/*Form Section*/}
      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSignup} noValidate>
            {/**Name input section */}
            <div className="grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-8">
              {/**First Name input section */}
              <div>
                <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
                  First name
                </label>
                <div className="mt-1 relative">
                  <input
                    id="firstName"
                    name="firstName"
                    type="text"
                    autoComplete="given-name"
                    required
                    value={formData.firstName}
                    onChange={handleInputChange}
                    disabled={isLoading}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                    placeholder="John"
                  />
                </div>
              </div>
              {/**Last Name input section */}
              <div>
                <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
                  Last name
                </label>
                <div className="mt-1 relative">
                  <input
                    id="lastName"
                    name="lastName"
                    type="text"
                    autoComplete="family-name"
                    required
                    value={formData.lastName}
                    onChange={handleInputChange}
                    disabled={isLoading}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                    placeholder='Doe'
                  />
                </div>
              </div>
            </div>
            {/**Email input section */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <div className="mt-1">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={formData.email}
                  onChange={handleInputChange}
                  disabled={isLoading}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                  placeholder='<EMAIL>'
                />
              </div>
            </div>
            {/**Password input section */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div className="mt-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaLock className="h-4 w-4 text-gray-400" aria-hidden="true" />
                </div>
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="new-password"
                  required
                  value={formData.password}
                  onChange={handleInputChange}
                  disabled={isLoading}
                  className="appearance-none block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 sm:text-sm disabled:bg-gray-100 disabled:cursor-not-allowed"
                  placeholder='********'
                />
              </div>
              <p className="mt-1 text-xs text-gray-500">
                Password must be at least 6 characters long
              </p>
            </div>
            {/* Confirm Password Field */}
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                Confirm password
              </label>
              <div className="mt-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaLock className="h-4 w-4 text-gray-400" aria-hidden="true" />
                </div>
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  autoComplete="new-password"
                  required
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  disabled={isLoading}
                  className="appearance-none block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 sm:text-sm disabled:bg-gray-100 disabled:cursor-not-allowed"
                  placeholder="Confirm your password"
                />
              </div>
            </div>
            {/* Error Banner */}
            {error && (
              <div className="rounded-md bg-red-50 p-4" role="alert">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-red-800">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Simple Line CAPTCHA (Slider) */}
            <div className="captcha-container" style={{ margin: '20px 0' }}>
              <div className='mb-2'>
                <label className="block text-sm font-medium text-gray-700">
                  Security Verification
                </label>
                <p className="text-xs text-gray-500">
                  Slide the block to the right to verify you're human
                </p>
              </div>
              <div
                id="slider"
                className="slider"
                role="slider"
                aria-label="Captcha verification slider"
                aria-valuemin="0"
                aria-valuemax="100"
                aria-valuenow={captchaCompleted ? "100" : "0"}
                tabIndex="0"
              >
                <div
                  id="slider-block"
                  className="slider-block"
                  style={{ backgroundColor: captchaCompleted ? '#10b981' : '#6b7280' }}
                ></div>
                <span className="slider-text" >
                  {captchaCompleted ? 'Verification Complete!' : ''}
                </span>
              </div>
              <input
                type="hidden"
                id="captcha-response"
                value={captchaCompleted ? 'success' : ''}
              />
            </div>
            {/* Submit Button */}
            <div>
              <button
                type="submit"
                disabled={isLoading || !captchaCompleted}
                className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${isLoading || !captchaCompleted
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-emerald-600 hover:bg-emerald-700'
                  } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-all duration-200`}
              >
                {isLoading ? (
                  <>
                    <FaSpinner className="animate-spin -ml-1 mr-3 h-5 w-5" aria-hidden="true" />
                    Creating account...
                  </>
                ) : (
                  'Create account'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Signup;
