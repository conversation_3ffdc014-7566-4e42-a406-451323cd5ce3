// app/patients/test-patients.ts
import dotenv from 'dotenv';
import { getMedplumClient, testMedplumConnection } from '../helper/medplumHelper';
import * as patientService from './patientService';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function testPatientAPI() {
    console.log('Testing Patient API...\n');

    try {
        // Test 1: Connection
        console.log('Testing Medplum connection...');
        const isConnected = await testMedplumConnection();
        if (!isConnected) {
            throw new Error('Failed to connect to Medplum');
        }
        console.log('Connection successful\n');

        // Test 2: Get client
        console.log('Getting Medplum client...');
        const client = await getMedplumClient();
        console.log('Client ready\n');

        // Test 3: Create patient
        console.log('Creating test patient...');
        const newPatient = await patientService.createPatient(client, {
            family: 'TestPatient',
            given: ['John', 'API'],
            gender: 'male',
            birthDate: '1990-01-01',
            intakeReason: 'API Testing'
        });
        console.log('Patient created:', newPatient.id);
        console.log('Name:', newPatient.given.join(' '), newPatient.family);
        console.log('Gender:', newPatient.gender);
        console.log('Birth Date:', newPatient.birthDate, '\n');

        // Test 4: Get patient
        console.log('4️Retrieving patient...');
        const retrievedPatient = await patientService.getPatient(client, newPatient.id);
        console.log('Patient retrieved:', retrievedPatient.id);
        console.log('Name matches:',
            retrievedPatient.family === newPatient.family &&
            JSON.stringify(retrievedPatient.given) === JSON.stringify(newPatient.given)
        );

        // Test 5: Update patient
        console.log('\nUpdating patient...');
        const updatedPatient = await patientService.updatePatient(client, newPatient.id, {
            intakeReason: 'Updated via API test'
        });
        console.log('Patient updated:', updatedPatient.id);

        // Test 6: Search patients
        console.log('\nSearching patients...');
        const searchResults = await patientService.searchPatients(client, {
            search: 'TestPatient',
            limit: 5
        });
        console.log('Search completed');
        console.log('Found:', searchResults.patients.length, 'patients');
        console.log('Total:', searchResults.total);

        // Test 7: Delete patient (cleanup)
        console.log('\nCleaning up - deleting test patient...');
        await patientService.deletePatient(client, newPatient.id);
        console.log('Test patient deleted\n');

        console.log('All tests passed!');

    } catch (error: any) {
        console.error('Test failed:', error.message);
    }
}

// ES module equivalent to run the file directly using (npx tsx test-patients.ts)
if (import.meta.url === `file://${__filename}` || process.argv[1] === __filename) {
    testPatientAPI().catch(console.error);
}

export { testPatientAPI };