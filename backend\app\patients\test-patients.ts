/**
 * @file test-patients.ts
 * @description Comprehensive test suite for patient API operations.
 * Tests CRUD operations, validation, and FHIR integration.
 */

import dotenv from 'dotenv';
import { getMedplumClient, testMedplumConnection } from '../helpers/medplumHelper.js';
import * as patientService from './patientService.js';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Load environment variables
dotenv.config();

// Constants for testing
const TEST_PATIENT_FAMILY = 'TestPatient';
const TEST_PATIENT_GIVEN = ['John', 'API'];
const TEST_PATIENT_GENDER = 'male';
const TEST_PATIENT_BIRTH_DATE = '1990-01-01';
const TEST_INTAKE_REASON = 'API Testing';
const UPDATED_INTAKE_REASON = 'Updated via API test';
const SEARCH_LIMIT = 5;

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Runs comprehensive tests for the Patient API.
 * Tests all CRUD operations and validates FHIR integration.
 * 
 * @returns {Promise<void>} Promise that resolves when all tests complete
 * @throws {Error} If any test fails or setup is invalid
 */
async function testPatientAPI(): Promise<void> {
    console.log('Testing Patient API...\n');

    try {
        // Test 1: Connection verification
        console.log('Testing Medplum connection...');
        const isConnected = await testMedplumConnection();
        if (!isConnected) {
            throw new Error('Failed to connect to Medplum');
        }
        console.log('Connection successful\n');

        // Test 2: Client initialization
        console.log('Getting Medplum client...');
        const client = await getMedplumClient();
        console.log('Client ready\n');

        // Test 3: Patient creation
        console.log('Creating test patient...');
        const newPatient = await patientService.createPatient(client, {
            family: TEST_PATIENT_FAMILY,
            given: TEST_PATIENT_GIVEN,
            gender: TEST_PATIENT_GENDER,
            birthDate: TEST_PATIENT_BIRTH_DATE,
            intakeReason: TEST_INTAKE_REASON
        });

        console.log('Patient created:', newPatient.id);
        console.log(`Name: ${newPatient.given.join(' ')} ${newPatient.family}`);
        console.log(`Gender: ${newPatient.gender}`);
        console.log(`Birth Date: ${newPatient.birthDate}\n`);

        // Test 4: Patient retrieval
        console.log('Retrieving patient...');
        const retrievedPatient = await patientService.getPatient(
            client,
            newPatient.id
        );

        console.log('Patient retrieved:', retrievedPatient.id);
        const nameMatches = retrievedPatient.family === newPatient.family &&
            JSON.stringify(retrievedPatient.given) ===
            JSON.stringify(newPatient.given);
        console.log(`Name matches: ${nameMatches ? '' : ''}`);

        // Test 5: Patient update
        console.log('\n5Updating patient...');
        const updatedPatient = await patientService.updatePatient(
            client,
            newPatient.id,
            {
                intakeReason: UPDATED_INTAKE_REASON
            }
        );

        console.log('Patient updated:', updatedPatient.id);
        console.log(`Updated intake reason: ${updatedPatient.intakeReason}`);

        // Test 6: Patient search
        console.log('\n Searching patients...');
        const searchResults = await patientService.searchPatients(client, {
            search: TEST_PATIENT_FAMILY,
            limit: SEARCH_LIMIT
        });

        console.log('Search completed');
        console.log(`Found: ${searchResults.patients.length} patients`);
        console.log(`Total in system: ${searchResults.total}`);
        console.log(`Has more results: ${searchResults.hasMore}`);

        // Test 7: Cleanup - Delete test patient
        console.log('\n Cleaning up - deleting test patient...');
        await patientService.deletePatient(client, newPatient.id);
        console.log(' Test patient deleted\n');

        // Final success message
        console.log(' All tests passed! Your Patient API is working correctly.');

    } catch (error: any) {
        console.error(' Test failed:', error.message);

        // Re-throw error to indicate test failure
        throw error;
    }
}

/**
 * Runs validation tests for patient data.
 * Tests edge cases and error handling.
 * 
 * @returns {Promise<void>} Promise that resolves when validation tests complete
 */
async function testPatientValidation(): Promise<void> {
    console.log('\nTesting patient validation...\n');

    try {
        const client = await getMedplumClient();

        // Test invalid patient data
        console.log('Testing validation with invalid data...');

        try {
            await patientService.createPatient(client, {
                family: '', // Empty family name
                given: [], // Empty given names
                gender: 'invalid' as any, // Invalid gender
                birthDate: 'invalid-date' // Invalid date format
            });
            console.log(' Validation should have failed but didn\'t');
        } catch (validationError) {
            console.log(' Validation correctly rejected invalid data');
        }

        console.log(' Validation tests passed\n');

    } catch (error: any) {
        console.error(' Validation test failed:', error.message);
        throw error;
    }
}

/**
 * Main test runner function.
 * Orchestrates all test suites.
 * 
 * @returns {Promise<void>} Promise that resolves when all test suites complete
 */
async function runAllTests(): Promise<void> {
    try {
        await testPatientAPI();
        await testPatientValidation();

        console.log(' All test suites completed successfully!');

    } catch (error: any) {
        console.error('Test suite failed:', error.message);
        process.exit(1);
    }
}

// ES module equivalent to run the file directly using (npx tsx test-patients.ts)
if (import.meta.url === `file://${__filename}` || process.argv[1] === __filename) {
    runAllTests().catch((error) => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}

export { testPatientAPI, testPatientValidation, runAllTests };