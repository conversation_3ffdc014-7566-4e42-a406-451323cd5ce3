import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
// Load environment variables
dotenv.config();
console.log('Starting progressive debug server...');
console.log('Environment check:', {
    CLIENT_ID: !!process.env.CLIENT_ID,
    CLIENT_SECRET: !!process.env.CLIENT_SECRET,
    PORT: process.env.PORT
});
const app = express();
const port = process.env.PORT || 4000;
// Add middleware progressively
console.log('📦 Adding middleware...');
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
// Add basic routes first
console.log('📝 Adding basic routes...');
app.get('/health', (req, res) => {
    console.log('✅ Health route called');
    res.json({ status: 'OK', message: 'Basic health check' });
});
app.get('/api/health', (req, res) => {
    console.log('✅ API Health route called');
    res.json({
        status: "OK",
        time: new Date().toISOString(),
        service: "EECP Backend API"
    });
});
app.get('/', (req, res) => {
    console.log('✅ Root route called');
    res.json({
        message: "EECP Backend API",
        status: "running",
        endpoints: [
            "GET /health",
            "GET /api/health",
            "GET /test"
        ]
    });
});
app.get('/test', (_req, res) => {
    console.log('✅ Test route called');
    res.json({ message: "Server is working!" });
});
// STEP 1: Test if server starts with basic routes
console.log('🚀 Step 1: Testing basic routes...');
// UNCOMMENT ONE AT A TIME TO FIND THE PROBLEMATIC ROUTE:
// STEP 2: Add patient routes (comment out initially)
console.log('📦 Step 2: Loading patient routes...');
try {
    const patientRoutes = await import('./patients/patientRoutes.js');
    app.use('/api/patients', patientRoutes.default);
    console.log('✅ Patient routes added successfully');
}
catch (error) {
    console.error('❌ Error loading patient routes:', error.message);
    console.error('Full error:', error);
}
// STEP 3: Add error handlers (test if these cause issues)
console.log('📝 Adding error handlers...');
// Error handling middleware
app.use((err, _req, res, _next) => {
    console.error('❌ Error handler called:', err.message);
    res.status(500).json({
        error: 'Something went wrong!',
        message: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
});
// 404 handler - Using a more compatible approach
app.use((req, res) => {
    // Filter out common browser requests to reduce log noise
    const ignorePaths = ['/favicon.ico', '/.well-known/', '/apple-touch-icon'];
    const shouldLog = !ignorePaths.some(path => req.originalUrl.includes(path));
    if (shouldLog) {
        console.log('❓ 404 route called for:', req.originalUrl);
    }
    res.status(404).json({
        error: 'Route not found',
        path: req.originalUrl
    });
});
console.log('🚀 Starting server...');
app.listen(port, () => {
    console.log(`✅ Progressive server listening at http://localhost:${port}`);
    console.log(`🧪 Test these routes:`);
    console.log(`   http://localhost:${port}/health`);
    console.log(`   http://localhost:${port}/api/health`);
    console.log(`   http://localhost:${port}/test`);
    console.log('\n📋 Next steps:');
    console.log('   1. If this works, uncomment STEP 2 in main.ts');
    console.log('   2. If STEP 2 fails, the issue is in patientRoutes.ts');
    console.log('   3. Run: npm run dev');
});
export default app;
//# sourceMappingURL=main.js.map