"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const authService_1 = require("./authService");
async function main() {
    const authService = new authService_1.AuthService();
    const invalidCredentials = { email: '<EMAIL>', password: 'test' };
    try {
        console.log('Testing login...');
        const result = await authService.login(invalidCredentials);
        console.log('Login successful:', result);
    }
    catch (error) {
        console.error('Login failed:', error.message);
    }
}
main().catch(console.error);
//# sourceMappingURL=test.js.map