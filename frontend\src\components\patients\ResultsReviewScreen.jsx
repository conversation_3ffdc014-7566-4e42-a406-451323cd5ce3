import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { 
  FaChartLine, 
  FaHeartbeat, 
  FaWalking, 
  FaChevronDown, 
  FaChevronRight, 
  FaExclamationTriangle,
  FaEdit,
  FaSave,
  FaArrowUp,
  FaArrowDown,
  FaMinus
} from 'react-icons/fa';

const ResultsReviewScreen = () => {
  const { patientId } = useParams();
  const { medplum } = useAuth();
  const [patientName, setPatientName] = useState('');
  const [mrn, setMrn] = useState('');
  const [loading, setLoading] = useState(true);
  const [observations, setObservations] = useState([]);
  const [visits, setVisits] = useState([]);
  const [expandedSections, setExpandedSections] = useState({
    vitals: true,
    functionalClassifications: true,
    exerciseCapacity: true,
    cardiacFunction: true,
    symptoms: true,
    qualityOfLife: true,
    biomarkers: true,
    vascularFunction: true,
    medicationAdherence: true
  });
  const [editingCell, setEditingCell] = useState(null);
  const [editValue, setEditValue] = useState('');

  // Mock data for demonstration purposes
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // In a real implementation, we would fetch patient details from Medplum
        // For the demo, we'll use mock data based on the patientId
        if (patientId === 'p1') {
          setPatientName('John Smith');
          setMrn('MRN12345');
        } else if (patientId === 'p2') {
          setPatientName('Mary Johnson');
          setMrn('MRN23456');
        } else if (patientId === 'p3') {
          setPatientName('Robert Davis');
          setMrn('MRN34567');
        } else if (patientId === 'p4') {
          setPatientName('Patricia Wilson');
          setMrn('MRN45678');
        } else {
          setPatientName('Unknown Patient');
          setMrn('Unknown MRN');
        }
        
        // Mock visits data
        const mockVisits = [
          { id: 'visit-1', name: 'Initial', date: '2024-12-01' },
          { id: 'visit-2', name: 'Follow-Up 1', date: '2025-01-15' },
          { id: 'visit-3', name: 'Follow-Up 2', date: '2025-02-28' },
          { id: 'visit-4', name: 'Follow-Up 3', date: '2025-04-10' }
        ];
        
        // Mock observations data (simplified for brevity)
        const mockObservations = [
          // Vitals
          { id: 'obs-1', code: 'height', text: 'Height', unit: 'cm', visits: { 'visit-1': 175, 'visit-2': 175, 'visit-3': 175, 'visit-4': 175 }, category: 'vitals' },
          { id: 'obs-2', code: 'weight', text: 'Weight', unit: 'kg', visits: { 'visit-1': 82, 'visit-2': 80, 'visit-3': 78.5, 'visit-4': 77 }, category: 'vitals' },
          { id: 'obs-3', code: 'bmi', text: 'BMI', unit: 'kg/m²', visits: { 'visit-1': 26.8, 'visit-2': 26.1, 'visit-3': 25.6, 'visit-4': 25.1 }, category: 'vitals' },
          { id: 'obs-8', code: 'heart-rate', text: 'Heart Rate', unit: 'bpm', visits: { 'visit-1': 78, 'visit-2': 76, 'visit-3': 74, 'visit-4': 72 }, category: 'vitals' },
          
          // Functional Classifications
          { id: 'obs-10', code: 'nyha', text: 'NYHA Class', unit: '', visits: { 'visit-1': 'III', 'visit-2': 'III', 'visit-3': 'II', 'visit-4': 'II' }, category: 'functionalClassifications' },
          
          // Exercise Capacity
          { id: 'obs-13', code: '6mwd', text: '6-Minute Walk Distance', unit: 'm', visits: { 'visit-1': 320, 'visit-2': 350, 'visit-3': 380, 'visit-4': 410 }, category: 'exerciseCapacity' },
          
          // Cardiac Function
          { id: 'obs-20', code: 'lvef', text: 'LVEF', unit: '%', visits: { 'visit-1': 32, 'visit-2': 35, 'visit-3': 38, 'visit-4': 42 }, category: 'cardiacFunction', alert: value => value < 35 },
          
          // Symptoms & Medication
          { id: 'obs-21', code: 'angina-episodes', text: 'Angina Episodes/Week', unit: '', visits: { 'visit-1': 5, 'visit-2': 3, 'visit-3': 2, 'visit-4': 1 }, category: 'symptoms' },
          
          // Quality of Life
          { id: 'obs-23', code: 'sf36', text: 'SF-36', unit: '', visits: { 'visit-1': 65, 'visit-2': 70, 'visit-3': 75, 'visit-4': 80 }, category: 'qualityOfLife' },
          
          // Biomarkers
          { id: 'obs-27', code: 'bnp', text: 'BNP', unit: 'pg/mL', visits: { 'visit-1': 450, 'visit-2': 380, 'visit-3': 320, 'visit-4': 280 }, category: 'biomarkers' },
          
          // Vascular Function
          { id: 'obs-40', code: 'fmd', text: 'FMD', unit: '%', visits: { 'visit-1': 4.2, 'visit-2': 4.8, 'visit-3': 5.5, 'visit-4': 6.2 }, category: 'vascularFunction' },
          
          // Medication Adherence
          { id: 'obs-43', code: 'adherence', text: 'Adherence Score', unit: '%', visits: { 'visit-1': 85, 'visit-2': 88, 'visit-3': 92, 'visit-4': 95 }, category: 'medicationAdherence' }
        ];
        
        setVisits(mockVisits);
        setObservations(mockObservations);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching data:', error);
        setLoading(false);
      }
    };
    
    fetchData();
  }, [medplum, patientId]);

  // Helper function to get trend direction
  const getTrendDirection = (observation, visitId) => {
    const visitIds = visits.map(v => v.id);
    const currentIndex = visitIds.indexOf(visitId);
    
    if (currentIndex <= 0) return null;
    
    const previousVisitId = visitIds[currentIndex - 1];
    const currentValue = observation.visits[visitId];
    const previousValue = observation.visits[previousVisitId];
    
    if (currentValue === previousValue) return 'unchanged';
    
    // For some metrics, lower is better (e.g., BNP)
    const lowerIsBetter = ['bnp', 'angina-episodes'].includes(observation.code);
    
    if (lowerIsBetter) {
      return currentValue < previousValue ? 'improved' : 'worsened';
    } else {
      return currentValue > previousValue ? 'improved' : 'worsened';
    }
  };

  // Helper function to get trend icon
  const getTrendIcon = (direction) => {
    if (!direction) return <FaMinus className="text-gray-400" />;
    
    if (direction === 'unchanged') return <FaMinus className="text-gray-400" />;
    if (direction === 'improved') return <FaArrowUp className="text-green-500" />;
    if (direction === 'worsened') return <FaArrowDown className="text-red-500" />;
  };

  // Helper function to check if a value should be flagged
  const shouldFlagValue = (observation, value) => {
    if (!observation.alert) return false;
    return observation.alert(value);
  };

  // Toggle section expansion
  const toggleSection = (section) => {
    setExpandedSections({
      ...expandedSections,
      [section]: !expandedSections[section]
    });
  };

  // Handle cell click for editing
  const handleCellClick = (observationId, visitId) => {
    const observation = observations.find(obs => obs.id === observationId);
    if (!observation) return;
    
    setEditingCell({ observationId, visitId });
    setEditValue(observation.visits[visitId]?.toString() || '');
  };

  // Handle edit value change
  const handleEditChange = (e) => {
    setEditValue(e.target.value);
  };

  // Handle save edit
  const handleSaveEdit = async () => {
    if (!editingCell) return;
    
    const { observationId, visitId } = editingCell;
    const observation = observations.find(obs => obs.id === observationId);
    if (!observation) return;
    
    // Parse value based on the original type
    let parsedValue = editValue;
    if (typeof observation.visits[visitId] === 'number') {
      parsedValue = parseFloat(editValue);
      if (isNaN(parsedValue)) return;
    }
    
    // Update local state
    const updatedObservations = observations.map(obs => {
      if (obs.id === observationId) {
        return {
          ...obs,
          visits: {
            ...obs.visits,
            [visitId]: parsedValue
          }
        };
      }
      return obs;
    });
    
    setObservations(updatedObservations);
    
    // Clear editing state
    setEditingCell(null);
    setEditValue('');
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setEditingCell(null);
    setEditValue('');
  };

  // Get observations by category
  const getObservationsByCategory = (category) => {
    return observations.filter(obs => obs.category === category);
  };

  // Calculate summary metrics
  const getSummaryMetrics = () => {
    const latestVisit = visits[visits.length - 1]?.id;
    if (!latestVisit) return {};
    
    const lvefObs = observations.find(obs => obs.code === 'lvef');
    const qolObs = observations.find(obs => obs.code === 'sf36');
    const adherenceObs = observations.find(obs => obs.code === 'adherence');
    
    // Count red flags (abnormal values)
    let redFlagCount = 0;
    observations.forEach(obs => {
      if (obs.alert && obs.alert(obs.visits[latestVisit])) {
        redFlagCount++;
      }
    });
    
    return {
      latestLvef: lvefObs?.visits[latestVisit] || 'N/A',
      averageQol: qolObs?.visits[latestVisit] || 'N/A',
      adherence: adherenceObs?.visits[latestVisit] || 'N/A',
      redFlagCount
    };
  };

  // Render a summary card
  const renderSummaryCard = (icon, title, value, unit, color) => (
    <div className={`bg-white rounded-lg shadow p-4 border-l-4 ${color}`}>
      <div className="flex items-center">
        <div className="mr-4">{icon}</div>
        <div>
          <h3 className="text-lg font-semibold text-gray-700">{title}</h3>
          <p className="text-2xl font-bold">
            {value} {unit && <span className="text-sm text-gray-500">{unit}</span>}
          </p>
        </div>
      </div>
    </div>
  );

  // Render a section header
  const renderSectionHeader = (title, category) => (
    <div 
      className="flex items-center justify-between p-3 bg-gray-100 rounded-t-lg cursor-pointer"
      onClick={() => toggleSection(category)}
    >
      <h3 className="text-lg font-semibold text-gray-700">{title}</h3>
      {expandedSections[category] ? 
        <FaChevronDown className="text-gray-500" /> : 
        <FaChevronRight className="text-gray-500" />
      }
    </div>
  );

  // Render a table cell
  const renderCell = (observation, visitId) => {
    const value = observation.visits[visitId];
    const direction = getTrendDirection(observation, visitId);
    const flagged = shouldFlagValue(observation, value);
    
    if (editingCell && editingCell.observationId === observation.id && editingCell.visitId === visitId) {
      return (
        <div className="flex items-center">
          <input
            type="text"
            value={editValue}
            onChange={handleEditChange}
            className="w-full p-1 border border-primary rounded-md focus:ring-2 focus:ring-primary focus:outline-none"
            autoFocus
          />
          <button 
            onClick={handleSaveEdit}
            className="ml-1 p-1 text-green-600 hover:text-green-800"
          >
            <FaSave />
          </button>
          <button 
            onClick={handleCancelEdit}
            className="ml-1 p-1 text-red-600 hover:text-red-800"
          >
            ✕
          </button>
        </div>
      );
    }
    
    return (
      <div 
        className={`p-2 flex items-center justify-between group ${flagged ? 'bg-red-50' : ''}`}
        onClick={() => handleCellClick(observation.id, visitId)}
      >
        <div className="flex items-center">
          <span className={flagged ? 'text-red-600 font-semibold' : ''}>
            {value !== undefined ? value : 'N/A'}
            {observation.unit && <span className="text-xs text-gray-500 ml-1">{observation.unit}</span>}
          </span>
          {flagged && <FaExclamationTriangle className="text-red-500 ml-1" />}
        </div>
        <div className="flex items-center">
          <span className="mr-1">{getTrendIcon(direction)}</span>
          <FaEdit className="text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
        </div>
      </div>
    );
  };

  // Render a table for a category
  const renderCategoryTable = (category, title) => {
    const categoryObservations = getObservationsByCategory(category);
    
    return (
      <div className="mb-4">
        {renderSectionHeader(title, category)}
        
        {expandedSections[category] && (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50 sticky top-0">
                <tr>
                  <th scope="col" className="sticky left-0 z-10 bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[200px]">
                    Metric
                  </th>
                  {visits.map(visit => (
                    <th key={visit.id} scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                      {visit.name}<br />
                      <span className="text-gray-400">{new Date(visit.date).toLocaleDateString()}</span>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {categoryObservations.map(observation => (
                  <tr key={observation.id}>
                    <td className="sticky left-0 z-10 bg-white px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      <div className="flex items-center">
                        <span className="mr-1">{observation.text}</span>
                        <span className="text-xs text-gray-500">{observation.unit}</span>
                      </div>
                    </td>
                    {visits.map(visit => (
                      <td key={visit.id} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {renderCell(observation, visit.id)}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  const summaryMetrics = getSummaryMetrics();

  return (
    <div className="max-w-full mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <header className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Baseline & Follow-Up Metrics for EECP Patients</h1>
        <p className="mt-1 text-sm text-gray-500">
          {patientName && <span>Patient: {patientName} {mrn && <span className="text-gray-400">MRN: {mrn}</span>}</span>}
        </p>
      </header>

      {/* Summary Dashboard */}
      <div className="mb-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {renderSummaryCard(
          <FaHeartbeat className="h-8 w-8 text-red-500" />,
          "Latest LVEF",
          summaryMetrics.latestLvef,
          "%",
          summaryMetrics.latestLvef < 35 ? "border-red-500" : "border-green-500"
        )}
        
        {renderSummaryCard(
          <FaChartLine className="h-8 w-8 text-blue-500" />,
          "Quality of Life",
          summaryMetrics.averageQol,
          "score",
          "border-blue-500"
        )}
        
        {renderSummaryCard(
          <FaWalking className="h-8 w-8 text-green-500" />,
          "Session Compliance",
          summaryMetrics.adherence,
          "%",
          "border-green-500"
        )}
        
        {renderSummaryCard(
          <FaExclamationTriangle className="h-8 w-8 text-yellow-500" />,
          "Red Flags",
          summaryMetrics.redFlagCount,
          "issues",
          summaryMetrics.redFlagCount > 0 ? "border-yellow-500" : "border-gray-300"
        )}
      </div>

      {/* Main Results Tables */}
      <div className="mb-8 overflow-x-auto">
        <div className="inline-block min-w-full align-middle">
          <div className="overflow-hidden border border-gray-200 rounded-lg">
            {renderCategoryTable('vitals', 'Vitals')}
            {renderCategoryTable('functionalClassifications', 'Functional Classifications')}
            {renderCategoryTable('exerciseCapacity', 'Exercise Capacity')}
            {renderCategoryTable('cardiacFunction', 'Cardiac Function')}
            {renderCategoryTable('symptoms', 'Symptoms & Medication')}
            {renderCategoryTable('qualityOfLife', 'Quality of Life')}
            {renderCategoryTable('biomarkers', 'Biomarkers')}
            {renderCategoryTable('vascularFunction', 'Vascular Function')}
            {renderCategoryTable('medicationAdherence', 'Medication Adherence')}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResultsReviewScreen;
