/**
 * CLINICAL RESPONSE PARSER
 * 
 * Medical AI responses need a multi-step validation pipeline because clinical data has strict accuracy requirements.
 * We can't just parse JSON and hope for the best - we need to validate data types, check medical standards, 
 * apply terminology mappings, perform safety checks, and sanitize for EHR integration. Each step catches 
 * different types of errors that could compromise patient safety or system reliability.
 */

const {
    DEMOGRAPHICS_SCHEMA,
    MEDICAL_HISTORY_SCHEMA,
    SYMPTOMS_SCHEMA,
    VITALS_SCHEMA,
    MEDICAL_STANDARDIZATION
} = require('../validation/clinicalSchemas');

class ClinicalResponseParser {
    constructor(options = {}) {
        this.enableLogging = options.enableLogging || false;
        this.strictValidation = options.strictValidation || false;
        this.sanitizeData = options.sanitizeData || false;
        this.parseErrors = [];
        this.validationStats = {
            totalParsed: 0,
            successfulParsed: 0,
            errors: 0,
            warnings: 0,
            startTime: Date.now()
        };
    }

    /**
     * <PERSON>IN PARSING METHOD - ORCHESTRATES THE ENTIRE VALIDATION PIPELINE
     * 
     * Logic: Take raw AI response and put it through a 6-step validation process:
     * 1. Check that inputs are valid (not null, correct type, etc.)
     * 2. Parse the JSON and handle malformed responses
     * 3. Validate against clinical schemas (required fields, data types)
     * 4. Apply medical standardization (map "MI" to "Myocardial Infarction")
     * 5. Run safety checks (flag dangerous values like LVEF < 30%)
     * 6. Sanitize data for secure EHR integration
     * Return structured result with success status, data, and any warnings
     *
     * @param {string} aiResponse - Raw response from OpenAI API
     * @param {string} extractionType - Type of clinical data extracted
     * @param {Object} context - Clinical context for validation
     * @returns {Promise<Object>} Parsed and validated clinical data
     * @throws {ClinicalParsingError} If parsing fails or validation errors occur
     */
    async parseResponse(aiResponse, extractionType, context = {}) {
        const startTime = Date.now();
        this.parseErrors = [];
        this.validationWarnings = [];

        try {
            this.validationStats.totalParsed++;

            //1. Validate input parameters
            this._validateInputParameters(aiResponse, extractionType);

            // Extract content from OpenAI response if it's in that format
            const responseContent = aiResponse?.choices?.[0]?.message?.content || aiResponse;

            // Check if response indicates no data found
            if (responseContent.includes("does not contain any") ||
                responseContent.includes("not possible to extract") ||
                responseContent.includes("no demographic") ||
                responseContent.includes("no medical history") ||
                responseContent.includes("no symptoms")) {

                // Return a valid response with empty/unknown values
                const emptyData = this._getEmptyDataStructure(extractionType);

                return {
                    success: true,
                    data: emptyData,
                    extractionType,
                    warnings: ["No relevant data found in the provided text"],
                    metadata: {
                        processingTime: Date.now() - startTime,
                        validatedFields: 0,
                        timestamp: new Date().toISOString(),
                    }
                };
            }

            // Parse json response
            const parsedData = this._parseJSONResponse(responseContent);

            //Validate against clinical schema
            const validatedData = await this._validateClinicalData(parsedData, extractionType, context);

            //Apply medical standardization
            const standardizedData = this._applyMedicalStandardization(validatedData, extractionType);

            //Run safety checks
            const safeData = this._performSafetyChecks(standardizedData, extractionType);

            //final data sanitization
            const sanitizedData = this._sanitizeData ? this._sanitizeData(safeData) : safeData;

            this.validationStats.successfulParsed++;

            if (this.enableLogging) {
                console.log('Clinical data parsed successfully', {
                    extractionType,
                    processingTime: Date.now() - startTime,
                    dataSize: JSON.stringify(sanitizedData).length,
                    warnings: this.validationWarnings.length,
                });
            }

            //return structured result
            return {
                success: true,
                data: sanitizedData,
                extractionType,
                warnings: this.validationWarnings,
                metadata: {
                    processingTime: Date.now() - startTime,
                    validatedFields: Object.keys(sanitizedData).length,
                    timestamp: new Date().toISOString(),
                }
            };
        } catch (error) {
            this.validationStats.errors++;

            if (this.enableLogging) {
                console.error('Clinical data parsing failed', {
                    extractionType,
                    error: error.message,
                    stack: error.stack,
                });
            }

            throw new ClinicalParsingError(
                `Failed to parse clinical data for ${extractionType}`,
                {
                    originalError: error,
                    context,
                    parseErrors: this.parseErrors,
                    aiResponse: typeof aiResponse === 'string'
                        ? aiResponse.substring(0, 500)
                        : JSON.stringify(aiResponse).substring(0, 500)
                }
            );
        }
    }

    /**
     * Parse multiple extraction types from comprehensive response
     * @param {string} aiResponse - Comprehensive AI response
     * @param {Array} extractionTypes - Array of extraction types expected
     * @param {Object} context - Clinical context
     * @returns {Promise<Object>} Object with all parsed data types
     */
    async parseComprehensiveResponse(aiResponse, extractionTypes, context = {}) {
        // Parse main JSON response
        try {
            const parsedData = this._parseJSONResponse(aiResponse);
            const results = {};
            // For each extraction type, extract that section and parse it individually
            for (const extractionType of extractionTypes) {
                if (parsedData[extractionType]) {
                    const typedResult = await this.parseResponse(
                        JSON.stringify(parsedData[extractionType]),
                        extractionType,
                        context
                    );

                    results[extractionType] = typedResult.data;
                } else {
                    this.validationWarnings.push(`Missing data found for ${extractionType}`);
                }
            }

            // Return comprehensive result object
            return {
                success: true,
                data: results,
                warnings: this.validationWarnings,
                extractionTypes
            };
        } catch (error) {
            throw new ClinicalParsingError(
                `Failed to parse comprehensive clinical data`,
                {
                    originalError: error,
                    context,
                    parseErrors: this.parseErrors,
                    aiResponse: aiResponse?.substring(0, 500)
                }
            );
        }
    }

    /**
     * Validate input parameters
     * @private
     */
    _validateInputParameters(aiResponse, extractionType) {
        if (!aiResponse) {
            throw new Error('AI response is required');
        }

        if (!extractionType) {
            throw new Error('Extraction type is required');
        }

        // Check if extraction type is supported
        const validExtractionTypes = ['demographics', 'medical_history', 'symptoms', 'vitals'];
        if (!validExtractionTypes.includes(extractionType)) {
            throw new Error(`Unsupported extraction type: ${extractionType}`);
        }
    }

    /**
     * Parse JSON response with error handling
     * @private
     */
    _parseJSONResponse(aiResponse) {
        try {
            // If it's already an object, return it
            if (typeof aiResponse === 'object' && aiResponse !== null) {
                return aiResponse;
            }

            // If it's a string, try to parse it
            if (typeof aiResponse === 'string') {
                try {
                    return JSON.parse(aiResponse);
                } catch (error) {
                    // If it's an OpenAI response format, try to parse the content
                    if (aiResponse.includes('"choices"') && aiResponse.includes('"message"')) {
                        const openAIResponse = JSON.parse(aiResponse);
                        const content = openAIResponse?.choices?.[0]?.message?.content;
                        if (content) {
                            return JSON.parse(content);
                        }
                    }
                    throw error;
                }
            }

            throw new Error('Invalid response format');
        } catch (error) {
            this.parseErrors.push({
                type: 'JSON_PARSE_ERROR',
                message: error.message,
                timestamp: new Date().toISOString()
            });

            throw new ClinicalParsingError(
                'Failed to parse JSON response',
                {
                    originalError: error,
                    response: typeof aiResponse === 'string' ? aiResponse.substring(0, 500) : 'Invalid format'
                }
            );
        }
    }

    /**
     * Get empty data structure for extraction type
     * @private
     */
    _getEmptyDataStructure(extractionType) {
        const emptyDataStructures = {
            'demographics': {
                firstName: 'Unknown',
                lastName: 'Unknown',
                dateOfBirth: 'Unknown',
                gender: 'Unknown',
                phoneNumber: 'Unknown',
                email: 'Unknown',
                address: {
                    street: 'Unknown',
                    city: 'Unknown',
                    state: 'Unknown',
                    zipCode: 'Unknown',
                    country: 'Unknown'
                }
            },
            'medical_history': {
                conditions: [],
                surgeries: [],
                medications: [],
                allergies: [],
                familyHistory: [],
                socialHistory: {
                    smokingStatus: 'Unknown',
                    alcoholUse: 'Unknown'
                }
            },
            'symptoms': {
                chiefComplaint: 'Unknown',
                presentIllness: 'Unknown',
                symptoms: [],
                functionalStatus: {
                    nyhaClass: 'Unknown',
                    ccsClass: 'Unknown',
                    exerciseTolerance: 'Unknown',
                    qualityOfLife: 'Unknown'
                }
            },
            'vitals': {
                bloodPressure: {
                    systolic: null,
                    diastolic: null,
                    unit: 'mmHg'
                },
                heartRate: {
                    value: null,
                    unit: 'bpm'
                },
                respiratoryRate: {
                    value: null,
                    unit: 'breaths/min'
                },
                temperature: {
                    value: null,
                    unit: '°C'
                },
                oxygenSaturation: {
                    value: null,
                    unit: '%'
                }
            }
        };

        return emptyDataStructures[extractionType] || {};
    }

    /**
     * Validate clinical data against schema
     * @private
     */
    async _validateClinicalData(data, extractionType, context) {
        // Get schema for extraction type
        const schemaMap = {
            'demographics': DEMOGRAPHICS_SCHEMA,
            'medical_history': MEDICAL_HISTORY_SCHEMA,
            'symptoms': SYMPTOMS_SCHEMA,
            'vitals': VITALS_SCHEMA
        };

        const schema = schemaMap[extractionType];
        if (!schema) {
            throw new Error(`Schema not found for extraction type: ${extractionType}`);
        }

        // Simple validation for MVP
        const validatedData = { ...data };
        const requiredFields = schema.required || [];

        // Check required fields
        for (const field of requiredFields) {
            if (validatedData[field] === undefined) {
                if (this.strictValidation) {
                    throw new Error(`Required field missing: ${field}`);
                } else {
                    this.validationWarnings.push(`Required field missing: ${field}`);
                    validatedData[field] = 'Unknown';
                }
            }
        }

        return validatedData;
    }

    /**
     * Apply medical standardization to data
     * @private
     */
    _applyMedicalStandardization(data, extractionType) {
        const standardizedData = { ...data };
        const standardizationRules = MEDICAL_STANDARDIZATION[extractionType] || {};

        // If no standardization rules for this extraction type, return as is
        if (Object.keys(standardizationRules).length === 0) {
            return standardizedData;
        }

        // Apply standardization rules
        for (const [field, rules] of Object.entries(standardizationRules)) {
            if (standardizedData[field]) {
                if (Array.isArray(standardizedData[field])) {
                    // Handle array fields (like medications, conditions)
                    standardizedData[field] = standardizedData[field].map(item => {
                        if (typeof item === 'string') {
                            // Check if item matches any rule
                            for (const [pattern, replacement] of Object.entries(rules)) {
                                if (item.toLowerCase().includes(pattern.toLowerCase())) {
                                    return replacement;
                                }
                            }
                        } else if (typeof item === 'object' && item.name) {
                            // Handle object items with name property
                            for (const [pattern, replacement] of Object.entries(rules)) {
                                if (item.name.toLowerCase().includes(pattern.toLowerCase())) {
                                    return { ...item, name: replacement };
                                }
                            }
                        }
                        return item;
                    });
                } else if (typeof standardizedData[field] === 'string') {
                    // Handle string fields
                    for (const [pattern, replacement] of Object.entries(rules)) {
                        if (standardizedData[field].toLowerCase().includes(pattern.toLowerCase())) {
                            standardizedData[field] = replacement;
                            break;
                        }
                    }
                }
            }
        }

        return standardizedData;
    }

    /**
     * Perform safety checks on clinical data
     * @private
     */
    _performSafetyChecks(data, extractionType) {
        const safeData = { ...data };

        const safetyCheckers = {
            'vitals': (data) => {
                const safeData = { ...data };

                // Check for critical vital signs
                if (safeData.bloodPressure && safeData.bloodPressure.systolic > 180) {
                    this.validationWarnings.push('Critical high systolic blood pressure detected');
                    safeData.criticalVitals = true;
                }

                if (safeData.bloodPressure && safeData.bloodPressure.diastolic > 120) {
                    this.validationWarnings.push('Critical high diastolic blood pressure detected');
                    safeData.criticalVitals = true;
                }

                if (safeData.heartRate && safeData.heartRate.value > 120) {
                    this.validationWarnings.push('Critical high heart rate detected');
                    safeData.criticalVitals = true;
                }

                if (safeData.heartRate && safeData.heartRate.value < 50) {
                    this.validationWarnings.push('Critical low heart rate detected');
                    safeData.criticalVitals = true;
                }

                if (safeData.oxygenSaturation && safeData.oxygenSaturation.value < 90) {
                    this.validationWarnings.push('Critical low oxygen saturation detected');
                    safeData.criticalVitals = true;
                }

                if (safeData.temperature && safeData.temperature.value > 39) {
                    this.validationWarnings.push('Critical high temperature detected');
                    safeData.criticalVitals = true;
                }

                if (safeData.cardiacFunction && safeData.cardiacFunction.lvef < 30) {
                    this.validationWarnings.push('Critical low ejection fraction detected');
                    safeData.criticalVitals = true;
                }

                return safeData;
            },

            'medical_history': (data) => {
                const safeData = { ...data };
                // Check for critical allergies
                const criticalAllergies = ['penicillin', 'sulfa', 'nsaids', 'contrast'];

                if (safeData.allergies && Array.isArray(safeData.allergies)) {
                    for (const allergy of safeData.allergies) {
                        for (const criticalAllergy of criticalAllergies) {
                            if (typeof allergy === 'string' && allergy.toLowerCase().includes(criticalAllergy)) {
                                this.validationWarnings.push(`Critical allergy detected: ${allergy}`);
                                safeData.critical_allergies = true;
                                break;
                            } else if (allergy.allergen && allergy.allergen.toLowerCase().includes(criticalAllergy)) {
                                this.validationWarnings.push(`Critical allergy detected: ${allergy.allergen}`);
                                safeData.critical_allergies = true;
                                break;
                            }
                        }
                    }
                }

                return safeData;
            },

            'symptoms': (data) => {
                const safeData = { ...data };
                // Check for critical symptoms
                const criticalSymptoms = ['chest pain', 'shortness of breath', 'difficulty breathing', 'loss of consciousness'];

                if (safeData.chiefComplaint) {
                    for (const symptom of criticalSymptoms) {
                        if (safeData.chiefComplaint.toLowerCase().includes(symptom)) {
                            this.validationWarnings.push(`Critical symptom detected in chief complaint: ${symptom}`);
                            safeData.critical_symptoms = true;
                            break;
                        }
                    }
                }

                if (safeData.currentSymptoms && Array.isArray(safeData.currentSymptoms)) {
                    for (const symptomObj of safeData.currentSymptoms) {
                        for (const criticalSymptom of criticalSymptoms) {
                            if (symptomObj.symptom && symptomObj.symptom.toLowerCase().includes(criticalSymptom)) {
                                this.validationWarnings.push(`Critical symptom detected: ${symptomObj.symptom}`);
                                safeData.critical_symptoms = true;
                                break;
                            }
                        }
                    }
                }

                return safeData;
            },

            'demographics': (data) => {
                // No specific safety checks for demographics
                return data;
            }
        };

        // Use the appropriate safety checker function or return data unchanged
        return safetyCheckers[extractionType] ? safetyCheckers[extractionType](safeData) : safeData;
    }

    /**
     * Sanitize data for secure EHR integration
     * @private
     */
    _sanitizeData(data) {
        // Deep clone to avoid modifying original
        const sanitizedData = JSON.parse(JSON.stringify(data));

        // Recursive function to sanitize all string values
        const sanitizeObject = (obj) => {
            if (!obj || typeof obj !== 'object') return obj;

            Object.keys(obj).forEach(key => {
                if (typeof obj[key] === 'string') {
                    // Sanitize string values - remove HTML tags, scripts, etc.
                    obj[key] = this._sanitizeString(obj[key]);
                } else if (Array.isArray(obj[key])) {
                    // Sanitize array items
                    obj[key] = obj[key].map(item => {
                        if (typeof item === 'string') {
                            return this._sanitizeString(item);
                        } else if (typeof item === 'object') {
                            return sanitizeObject(item);
                        }
                        return item;
                    });
                } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                    // Recursively sanitize nested objects
                    sanitizeObject(obj[key]);
                }
            });

            return obj;
        };

        return sanitizeObject(sanitizedData);
    }

    /**
     * Sanitize a string value
     * @private
     */
    _sanitizeString(str) {
        if (!str) return str;

        // Remove HTML tags
        let sanitized = str.replace(/<[^>]*>/g, '');

        // Remove script tags and content
        sanitized = sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');

        // Remove potentially dangerous attributes
        sanitized = sanitized.replace(/javascript:/gi, '');
        sanitized = sanitized.replace(/on\w+=/gi, '');

        // Remove excessive whitespace
        sanitized = sanitized.replace(/\s+/g, ' ').trim();

        return sanitized;
    }

    /**
     * Get parser statistics
     * @returns {Object} Parser statistics
     */
    getStats() {
        return {
            ...this.validationStats,
            successRate: this.validationStats.totalParsed > 0
                ? (this.validationStats.successfulParsed / this.validationStats.totalParsed) * 100
                : 0,
            runTime: Date.now() - this.validationStats.startTime,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Reset parser statistics
     */
    resetStats() {
        this.validationStats = {
            totalParsed: 0,
            successfulParsed: 0,
            errors: 0,
            warnings: 0,
            startTime: Date.now()
        };
    }
}

/**
 * Custom error class for clinical parsing errors
 */
class ClinicalParsingError extends Error {
    constructor(message, details = {}) {
        super(message);
        this.name = 'ClinicalParsingError';
        this.details = details;
        this.timestamp = new Date().toISOString();

        // Capture stack trace
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, ClinicalParsingError);
        }
    }
}

/**
 * Create a clinical parser with options
 * @param {Object} options - Parser options
 * @returns {ClinicalResponseParser} Configured parser instance
 */
function createClinicalParser(options = {}) {
    return new ClinicalResponseParser(options);
}

/**
 * Convenience function for quick parsing of clinical responses
 * @param {string} aiResponse - AI response to parse
 * @param {string} extractionType - Type of clinical data
 * @param {Object} options - Parser options
 * @returns {Promise<Object>} Parsed clinical data
 */
async function parseClinicaResponse(aiResponse, extractionType, options = {}) {
    const parser = createClinicalParser(options);
    return await parser.parseResponse(aiResponse, extractionType);
}

// Export configured singleton for application use
const clinicalParser = createClinicalParser({
    enableLogging: process.env.NODE_ENV === 'development',
    strictValidation: true,
    sanitizeData: true
});

module.exports = {
    ClinicalResponseParser,
    ClinicalParsingError,
    createClinicalParser,
    parseClinicaResponse,
    clinicalParser
};
