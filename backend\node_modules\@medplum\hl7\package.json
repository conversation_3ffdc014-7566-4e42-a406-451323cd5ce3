{"name": "@medplum/hl7", "version": "4.2.0", "description": "Medplum HL7 Utilities", "keywords": ["medplum", "fhir", "healthcare", "interoperability", "json", "serialization", "hl7", "standards", "clinical", "dstu2", "stu3", "r4", "normative"], "homepage": "https://www.medplum.com/", "bugs": {"url": "https://github.com/medplum/medplum/issues"}, "repository": {"type": "git", "url": "git+https://github.com/medplum/medplum.git", "directory": "packages/hl7"}, "license": "Apache-2.0", "author": "Medplum <<EMAIL>>", "sideEffects": false, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.mjs"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.cjs"}}, "main": "dist/cjs/index.cjs", "module": "dist/esm/index.mjs", "types": "dist/cjs/index.d.ts", "files": ["dist/cjs", "dist/esm"], "scripts": {"api-extractor": "api-extractor run --local && cp dist/types.d.ts dist/cjs/index.d.ts && cp dist/types.d.ts dist/esm/index.d.ts", "build": "npm run clean && tsc && node esbuild.mjs && npm run api-extractor", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint .", "test": "jest"}, "dependencies": {"@medplum/core": "4.2.0", "iconv-lite": "0.6.3"}, "devDependencies": {"@medplum/fhirtypes": "4.2.0"}, "engines": {"node": ">=20.0.0"}}