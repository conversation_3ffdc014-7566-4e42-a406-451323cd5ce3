"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.testPatientAPI = testPatientAPI;
// test-patients.ts (optional test file)
const medplumHelper_1 = require("../helper/medplumHelper");
const patientService = __importStar(require("./patientService"));
async function testPatientAPI() {
    console.log('🧪 Testing Patient API...\n');
    try {
        // Test 1: Connection
        console.log('1️Testing Medplum connection...');
        const isConnected = await (0, medplumHelper_1.testMedplumConnection)();
        if (!isConnected) {
            throw new Error('Failed to connect to Medplum');
        }
        console.log(' Connection successful\n');
        // Test 2: Get client
        console.log('2️Getting Medplum client...');
        const client = await (0, medplumHelper_1.getMedplumClient)();
        console.log(' Client ready\n');
        // Test 3: Create patient
        console.log('3️ Creating test patient...');
        const newPatient = await patientService.createPatient(client, {
            family: 'TestPatient',
            given: ['John', 'API'],
            gender: 'male',
            birthDate: '1990-01-01',
            intakeReason: 'API Testing'
        });
        console.log(' Patient created:', newPatient.id);
        console.log('   Name:', newPatient.given.join(' '), newPatient.family);
        console.log('   Gender:', newPatient.gender);
        console.log('   Birth Date:', newPatient.birthDate, '\n');
        // Test 4: Get patient
        console.log('4️Retrieving patient...');
        const retrievedPatient = await patientService.getPatient(client, newPatient.id);
        console.log('Patient retrieved:', retrievedPatient.id);
        console.log('   Name matches:', retrievedPatient.family === newPatient.family &&
            JSON.stringify(retrievedPatient.given) === JSON.stringify(newPatient.given));
        // Test 5: Update patient
        console.log('\n5️Updating patient...');
        const updatedPatient = await patientService.updatePatient(client, newPatient.id, {
            intakeReason: 'Updated via API test'
        });
        console.log('Patient updated:', updatedPatient.id);
        // Test 6: Search patients
        console.log('\n6️Searching patients...');
        const searchResults = await patientService.searchPatients(client, {
            search: 'TestPatient',
            limit: 5
        });
        console.log('Search completed');
        console.log('   Found:', searchResults.patients.length, 'patients');
        console.log('   Total:', searchResults.total);
        // Test 7: Delete patient (cleanup)
        console.log('\n7️Cleaning up - deleting test patient...');
        await patientService.deletePatient(client, newPatient.id);
        console.log('Test patient deleted\n');
        console.log('All tests passed! Your Patient API is working correctly.');
    }
    catch (error) {
        console.error('Test failed:', error.message);
        console.error('\nTroubleshooting tips:');
        console.error('   1. Check your .env file has CLIENT_ID and CLIENT_SECRET');
        console.error('   2. Verify your Medplum credentials are correct');
        console.error('   3. Ensure you have internet connection');
        console.error('   4. Check if Medplum service is available');
    }
}
// Run the test
if (require.main === module) {
    testPatientAPI();
}
//# sourceMappingURL=test-patients.js.map