## Possible File Structure

app/main.py

```
backend/
├── .github/                            # GitHub Actions workflows
│   └── workflows/
│       ├── ci.yml                      # runs tests, lint, type-check on every push/PR
│       └── cd.yml                      # builds Docker image and deploys on merge to main
│
├── scripts/                            # helper scripts for local dev & CI
│   ├── test.sh                         
│   ├── lint.sh                         # flake8 / isort / black checks
│   └── deploy.sh                       
|
├── app/

│   ├── main.ts                  # Node.js app + router registration
│   │
│   ├── core/
│   │   ├── config.ts            # pydantic BaseSettings (MEDPLUM_CLIENT_ID, SECRET, BASE_URL)
│   │   └── security.ts          # OAuth2PasswordBearer setup + `get_current_user` dependency
│   │
│   ├── Helpers/
│   │   └── medplumHelper.ts     # factory for medplumHelper (handles CRUD operations and additional needed functions).
│   │
├── auth/                          # all things authentication
│   ├── loginRoutes.ts             # POST /login, POST /refresh, GET /me
│   ├── authMiddleware.ts          # express middleware that protects routes
│   └── authSchemas.ts             # DTOs: LoginRequest, TokenResponse, etc.
│   │
│   ├── patients/
│   │   ├── patientRoutes.ts            # CRUD endpoints under `/patients` (like retrieving and displaying information about the current patient)
│   │   ├── patientSchemas.ts           # Pydantic models matching your Patient profile
│   │   └── patientService.ts           # calls medplum_client.create_resource("Patient", …)
│   │
│   ├── intake/
│   │   ├── intakeRoutes.ts            # CRUD endpoints under `/patients` (like retrieving and displaying information about the current patient)
│   │   ├── intakeSchemas.ts           # Pydantic models matching your Patient profile
│   │   └── intakeService.ts           # calls medplum_client.create_resource("Patient", …)
│   │
│   ├── EECP/
│   │   ├── eecpRoutes.ts              #CRUD endpoints under EECP (Saving/Retrieving a session from Medplum)
│   │   ├── eecpSchemas.ts             # Pydantic models for mapping data format for EECP. 
│   │   └── eecpService.ts             #Services using medplum
│   │
│   ├── Dashboard/
│   │   ├── dashboardRoutes.ts         #Retrieving data for the dashboard (may require usage of functions from different areas like patients)
│   │   ├── dashboardSchemas.ts        # Models for the dashboard (eg How data will be formatted into the dashboard before returning GET request)
│   │   └── dashboardService.ts 
│   │      
│   ├── Appointments/
│   │   ├── appointmentsRoutes.ts         #Retrieving data for the dashboard (may require usage of functions from different areas like patients)
│   │   ├── appointmentsSchemas.ts        # Models for the dashboard (eg How data will be formatted into the dashboard before returning GET request)
│   │   └── appointmentsService.ts      
│   │
│   └── 
│      
│
├── tests/
│   ├── conftest.ts              # fixtures (test client, override MedplumClient)
│   ├── testAuth.ts
│   ├── testPatients.ts
|   ├── testIntake.ts
|   ├── testDashboard.ts
│   └── testAppointments.ts
|
├── billing/
│   ├── billingSetup.ts          
│          
│
├── .env
├── package.json
├── tsconfig.json
├── Dockerfile
├── docker-compose.yml           # if you need Redis, Vault, etc.
└── README.md
```

eg Files:

## Schema File
- For setting up the Pydantic classes, Initiating the Necessary fields for GET/POST requests

```
export interface PatientCreate {
  family: string;
  given: string[];
  gender?: string;
  birthDate?: string;
  intakeReason?: string;
}

export interface PatientRead extends PatientCreate {
  id: string;
  meta: Record<string, unknown>;  // server‐populated metadata
}

class PatientUpdate(BaseModel):
    # only include fields you want to allow clients to change
    gender: Optional[str]
    birthDate: Optional[str]
    intakeReason: Optional[str]

export type PatientUpdate = Partial<
  Pick<PatientCreate, 'gender' | 'birthDate' | 'intakeReason'>
>;

```

## Service File
- Control Functions used to interact with Medplum and perform data operations.

```
# app/patients/service.py
import { MedplumHelper } from './medplumHelper';
import { PatientCreate, PatientUpdate, PatientRead } from './schemas';

export async function createPatient(
  client: MedplumClient,
  patientIn: PatientCreate
): Promise<PatientRead> {
  // exclude undefined fields automatically
  return client.createResource<PatientRead>('Patient', patientIn);
}

export async function getPatient(
  client: MedplumClient,
  patientId: string
): Promise<PatientRead> {
  return client.readResource<PatientRead>('Patient', patientId);
}

export async function updatePatient(
  client: MedplumClient,
  patientId: string,
  patientIn: PatientUpdate
): Promise<PatientRead> {
  return client.updateResource<PatientRead>('Patient', patientId, patientIn);
}

export async function deletePatient(
  client: MedplumClient,
  patientId: string
): Promise<void> {
  await client.deleteResource('Patient', patientId);
}
```

## Route Files
- Route file is needed to hold the necessary endpoints. 

```
# app/patients/routes.py
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status

from app.patients import schemas, service
from app.clients.medplum_client import get_medplum_client
from app.core.security import get_current_user

router = APIRouter(
    prefix="/patients",
    tags=["Patients"]
)

@router.post(
    "/",
    response_model=schemas.PatientRead,
    status_code=status.HTTP_201_CREATED
)
async def create_patient_endpoint(
    patient: schemas.PatientCreate,
    client = Depends(get_medplum_client),
    user = Depends(get_current_user),
):
    return await service.create_patient(client, patient)


@router.get(
    "/",
    response_model=List[schemas.PatientRead]
)
async def list_patients(
    limit: int = 20,
    client = Depends(get_medplum_client),
    user = Depends(get_current_user),
):
    bundle = await client.search("Patient", _count=limit)
    # FHIR search bundles return entries array
    return [entry["resource"] for entry in bundle.get("entry", [])]


@router.get(
    "/{patient_id}",
    response_model=schemas.PatientRead
)
async def get_patient_endpoint(
    patient_id: str,
    client = Depends(get_medplum_client),
    user = Depends(get_current_user),
):
    patient = await service.get_patient(client, patient_id)
    if not patient:
        raise HTTPException(
            status.HTTP_404_NOT_FOUND, "Patient not found"
        )
    return patient


@router.patch(
    "/{patient_id}",
    response_model=schemas.PatientRead
)
async def update_patient_endpoint(
    patient_id: str,
    patient: schemas.PatientUpdate,
    client = Depends(get_medplum_client),
    user = Depends(get_current_user),
):
    return await service.update_patient(client, patient_id, patient)


@router.delete(
    "/{patient_id}",
    status_code=status.HTTP_204_NO_CONTENT
)
async def delete_patient_endpoint(
    patient_id: str,
    client = Depends(get_medplum_client),
    user = Depends(get_current_user),
):
    await service.delete_patient(client, patient_id)

```

## Notes:
- Not all of these files will be created, There will be custom logic needed to be implemented for the python backend (eg calendar creation)


## Useful Commands
- initializing a new project:

- npm init -y (package.json) (similar to creating a python virtual environment)
- npx tsc --init (typescript config)