import React from 'react';
import { ChevronLeft, FileText, Calendar, Printer, MoreVertical } from 'lucide-react';
import { Link } from 'react-router-dom';

const PatientHeaderNew = ({ patient, onPrintClick }) => {
  return (
    <div className="flex items-center justify-between mb-8">
      <div className="flex items-center space-x-4">
        <Link to="/patients" className="text-gray-500 hover:text-gray-700">
          <ChevronLeft className="w-6 h-6" />
        </Link>
        
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-xl font-medium text-blue-700">
              {patient.name.split(' ').map(n => n[0]).join('')}
            </span>
          </div>
          
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">{patient.name}</h1>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <span>{patient.age} yrs • {patient.gender}</span>
              <span className="px-2 border-l border-r">ID: {patient.id}</span>
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                {patient.status}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="flex items-center space-x-3">
        <button 
          className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700"
        >
          <FileText className="w-4 h-4 mr-2" />
          Post-Treatment Evaluation
        </button>

        <button 
          className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
        >
          <Calendar className="w-4 h-4 mr-2" />
          Schedule Follow-up
        </button>

        <button 
          onClick={onPrintClick}
          className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
        >
          <Printer className="w-4 h-4 mr-2" />
          Print Summary
        </button>

        <button className="p-2 text-gray-400 hover:text-gray-600">
          <MoreVertical className="w-5 h-5" />
        </button>
      </div>
    </div>
  );
};

export default PatientHeaderNew; 