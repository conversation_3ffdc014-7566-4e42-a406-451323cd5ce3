// Mock data for the application
// git changes
// Status constants for better type safety and consistency
export const PATIENT_STATUS = {
  PENDING_INITIAL: 'pending_initial',      // Awaiting initial assessment
  ACTIVE: 'active',                        // Currently undergoing treatment
  ON_HOLD: 'on_hold',                      // Treatment temporarily paused
  COMPLETED: 'completed',                  // Completed all sessions
  PENDING_REVIEW: 'pending_review',        // Awaiting review/evaluation
  DISCHARGED: 'discharged',                // Treatment complete and discharged
  DISCONTINUED: 'discontinued'             // Treatment discontinued
};

// Status display configuration
export const STATUS_CONFIG = {
  [PATIENT_STATUS.PENDING_INITIAL]: {
    label: 'Pending Initial Assessment',
    color: 'gray',
    icon: 'clock',
    priority: 1
  },
  [PATIENT_STATUS.ACTIVE]: {
    label: 'Active Treatment',
    color: 'green',
    icon: 'heartbeat',
    priority: 2
  },
  [PATIENT_STATUS.ON_HOLD]: {
    label: 'On Hold',
    color: 'yellow',
    icon: 'pause',
    priority: 3
  },
  [PATIENT_STATUS.COMPLETED]: {
    label: 'Treatment Completed',
    color: 'blue',
    icon: 'check',
    priority: 4
  },
  [PATIENT_STATUS.PENDING_REVIEW]: {
    label: 'Pending Review',
    color: 'purple',
    icon: 'clipboard',
    priority: 5
  },
  [PATIENT_STATUS.DISCHARGED]: {
    label: 'Discharged',
    color: 'gray',
    icon: 'door-open',
    priority: 6
  },
  [PATIENT_STATUS.DISCONTINUED]: {
    label: 'Discontinued',
    color: 'red',
    icon: 'x',
    priority: 7
  }
};

// Single source of truth for all patient data
export const PATIENTS = [
  {
    id: 'p1',
    name: 'John Smith',
    age: 65,
    gender: 'Male',
    doctorId: 'd1',
    status: PATIENT_STATUS.COMPLETED,
    statusHistory: [
      {
        status: PATIENT_STATUS.PENDING_INITIAL,
        date: '2024-12-01',
        updatedBy: 'Dr. Michael Chen',
        notes: 'Initial referral received'
      },
      {
        status: PATIENT_STATUS.ACTIVE,
        date: '2024-12-15',
        updatedBy: 'Dr. Michael Chen',
        notes: 'Treatment plan approved, starting EECP'
      },
      {
        status: PATIENT_STATUS.COMPLETED,
        date: '2025-04-15',
        updatedBy: 'Dr. Sarah Johnson',
        notes: 'All 35 sessions completed successfully'
      }
    ],
    diagnosis: 'Coronary Artery Disease',
    session: {
      number: 35,
      type: 'Final Evaluation',
      progress: 100,
      total: 35,
      date: '2025-04-17',
      duration: 60,
      pressure: 245,
      frequency: '5 days/week',
      treatingDoctor: 'Dr. Michael Chen'
    },
    metrics: {
      lvef: {
        current: 44,
        previous: 38,
        change: '+6%'
      },
      ccsClass: {
        current: 1,
        previous: 3,
        change: '-2'
      },
      walkDistance: {
        current: 425,
        previous: 320,
        change: '+105m'
      },
      anginaEpisodes: {
        current: 0,
        previous: 12,
        change: '-12/week'
      },
      bp: {
        current: '118/75',
        previous: '138/88',
        change: '-20/-13'
      },
      qol: {
        current: 85,
        previous: 62,
        change: '+23'
      }
    },
    medicalHistory: {
      cardiovascular: {
        primaryDiagnosis: 'Coronary Artery Disease',
        cadStatus: {
          vessels: '3-vessel disease',
          severity: 'Moderate'
        },
        previousInterventions: [
          {
            type: 'PCI',
            date: '2023-12-15',
            location: 'Left Anterior Descending'
          }
        ],
        congestiveHeartFailure: {
          nyhaClass: 'II'
        }
      },
      riskFactors: {
        hypertension: true,
        diabetes: true,
        smoking: false,
        hyperlipidemia: true
      },
      medications: [
        {
          name: 'Aspirin',
          dose: '81mg',
          frequency: 'Daily'
        },
        {
          name: 'Atorvastatin',
          dose: '40mg',
          frequency: 'Daily'
        }
      ],
      allergies: [],
      familyHistory: {
        cardiovascular: true,
        diabetes: true
      }
    },
    summary: "Patient has shown excellent response to EECP with complete resolution of angina and improved functional capacity. LVEF increased 6%, BP normalized, and significant improvement in 6MWD. Recommend 3-month follow-up.",
    priority: {
      level: 'High',
      type: 'Post-Treatment Evaluation',
      dueDate: '2025-04-17',
      notes: 'Final evaluation required before discharge'
    },
    appointments: [
      {
        id: 'a1',
        type: 'Final Evaluation',
        date: '2025-04-17',
        time: '08:45',
        duration: 60,
        practitioner: 'Dr. Sarah Johnson',
        location: 'Room 103',
        status: 'scheduled'
      },
      {
        id: 'a6',
        type: 'EECP Session',
        date: '2025-04-21',
        time: '14:30',
        duration: 60,
        practitioner: 'Nurse Emma Thompson',
        location: 'Room 102',
        status: 'scheduled'
      },
      {
        id: 'a7',
        type: 'EECP Session',
        date: '2025-04-23',
        time: '10:00',
        duration: 60,
        practitioner: 'Nurse Emma Thompson',
        location: 'Room 102',
        status: 'scheduled'
      }
    ]
  },
  {
    id: 'p2',
    name: 'Mary Johnson',
    age: 58,
    gender: 'Female',
    doctorId: 'd1',
    status: PATIENT_STATUS.PENDING_INITIAL,
    statusHistory: [
      {
        status: PATIENT_STATUS.PENDING_INITIAL,
        date: '2025-04-15',
        updatedBy: 'Dr. Michael Chen',
        notes: 'Referred for EECP evaluation'
      }
    ],
    diagnosis: 'Congestive Heart Failure',
    session: {
      number: 'Initial',
      type: 'Initial Assessment',
      progress: 0,
      total: 35,
      date: '2025-04-17',
      duration: 45,
      treatingDoctor: 'Dr. Michael Chen'
    },
    vitals: {
      bloodPressure: '138/85',
      heartRate: 78,
      spo2: 97,
      height: 165,
      weight: 72,
      ccsClass: 3,
      nyhaClass: 2
    },
    metrics: {
      lvef: {
        current: 45,
        previous: null,
        change: null
      },
      ccsClass: {
        current: 3,
        previous: null,
        change: null
      }
    },
    medicalHistory: {
      cardiovascular: {
        primaryDiagnosis: 'Coronary Artery Disease',
        cadStatus: {
          vessels: '2-vessel disease',
          severity: 'Mild'
        },
        previousInterventions: [],
        congestiveHeartFailure: {
          nyhaClass: 'II'
        }
      },
      riskFactors: {
        hypertension: true,
        diabetes: false,
        smoking: false,
        hyperlipidemia: true
      },
      medications: [],
      allergies: [],
      familyHistory: {
        cardiovascular: true,
        diabetes: false
      }
    },
    summary: "New patient referred by Dr. Chen (Cardiology) for EECP evaluation. CCS Angina Class III with moderate limitation of ordinary physical activity. Reports angina during mild exertion and when climbing more than one flight of stairs. Complete medical history required.",
    priority: {
      level: 'High',
      type: 'Initial Assessment',
      dueDate: '2025-04-17',
      notes: 'Complete initial assessment and treatment plan'
    },
    appointments: [
      {
        id: 'a2',
        type: 'Initial Consultation',
        date: '2025-04-17',
        time: '10:30',
        duration: 45,
        practitioner: 'Dr. Michael Chen',
        location: 'Room 101',
        status: 'scheduled'
      },
      {
        id: 'a8',
        type: 'EECP Session',
        date: '2025-04-22',
        time: '15:30',
        duration: 60,
        practitioner: 'Nurse Emma Thompson',
        location: 'Room 102',
        status: 'scheduled'
      },
      {
        id: 'a9',
        type: 'Progress Review',
        date: '2025-04-24',
        time: '11:00',
        duration: 30,
        practitioner: 'Dr. Michael Chen',
        location: 'Room 101',
        status: 'scheduled'
      }
    ]
  },
  {
    id: 'p3',
    name: 'Robert Davis',
    age: 64,
    gender: 'Male',
    doctorId: 'd1',
    status: PATIENT_STATUS.ACTIVE,
    diagnosis: 'Angina',
    session: {
      number: 16,
      type: 'Standard',
      progress: 45.7,
      total: 35,
      date: '2025-04-19',
      duration: 60,
      pressure: 245,
      frequency: '5 days/week',
      treatingDoctor: 'Dr. Michael Chen'
    },
    metrics: {
      lvef: {
        current: 43,
        previous: 41,
        change: '+2%'
      },
      ccsClass: {
        current: 2,
        previous: 3,
        change: '-1'
      }
    },
    medicalHistory: {
      cardiovascular: {
        primaryDiagnosis: 'Coronary Artery Disease',
        cadStatus: {
          vessels: '2-vessel disease',
          severity: 'Moderate'
        },
        previousInterventions: [
          {
            type: 'PCI',
            date: '2023-10-20',
            location: 'Right Coronary Artery'
          }
        ],
        congestiveHeartFailure: {
          nyhaClass: 'II'
        }
      },
      riskFactors: {
        hypertension: true,
        diabetes: true,
        smoking: false,
        hyperlipidemia: true
      },
      medications: [
        {
          name: 'Metoprolol',
          dose: '50mg',
          frequency: 'Twice Daily'
        },
        {
          name: 'Atorvastatin',
          dose: '40mg',
          frequency: 'Daily'
        }
      ],
      allergies: [],
      familyHistory: {
        cardiovascular: true,
        diabetes: true
      }
    },
    summary: "Patient showing steady improvement at session 16 (45.7% complete). Angina frequency decreased 43% and nitroglycerin use reduced 66%. CCS class improved from III to II. BP trending toward normal range. Continue current protocol with gradual pressure increase as tolerated.",
    priority: {
      level: 'Medium',
      type: 'Progress Review',
      dueDate: '2025-04-17',
      notes: 'Review session 16 progress and adjust treatment plan if needed'
    },
    appointments: [
      {
        id: 'a3',
        type: 'EECP Session',
        date: '2025-04-17',
        time: '14:00',
        duration: 60,
        practitioner: 'Nurse Emma Thompson',
        location: 'Room 102',
        status: 'scheduled'
      },
      {
        id: 'a10',
        type: 'Progress Review',
        date: '2025-04-21',
        time: '09:00',
        duration: 30,
        practitioner: 'Dr. Sarah Johnson',
        location: 'Room 101',
        status: 'scheduled'
      },
      {
        id: 'a11',
        type: 'EECP Session',
        date: '2025-04-25',
        time: '14:00',
        duration: 60,
        practitioner: 'Nurse Emma Thompson',
        location: 'Room 102',
        status: 'scheduled'
      }
    ]
  },
  {
    id: 'p1744945946015',
    name: 'Aaryan Dhand',
    age: 62,
    gender: 'Male',
    doctorId: 'd1',
    status: PATIENT_STATUS.ACTIVE,
    diagnosis: 'CAD',
    session: {
      number: 25,
      type: 'Standard',
      progress: 71.4,
      total: 35,
      date: '2025-04-20',
      duration: 60,
      pressure: 240,
      frequency: '5 days/week',
      treatingDoctor: 'Dr. Sarah Johnson'
    },
    metrics: {
      lvef: {
        current: 42,
        previous: 39,
        change: '+3%'
      },
      ccsClass: {
        current: 2,
        previous: 3,
        change: '-1'
      }
    },
    appointments: [
      {
        id: 'a4',
        type: 'EECP Session',
        date: '2025-04-20',
        time: '09:00',
        duration: 60,
        practitioner: 'Nurse Emma Thompson',
        location: 'Room 102',
        status: 'scheduled'
      },
      {
        id: 'a12',
        type: 'Progress Review',
        date: '2025-04-22',
        time: '11:30',
        duration: 30,
        practitioner: 'Dr. Sarah Johnson',
        location: 'Room 101',
        status: 'scheduled'
      },
      {
        id: 'a13',
        type: 'EECP Session',
        date: '2025-04-24',
        time: '09:00',
        duration: 60,
        practitioner: 'Nurse Emma Thompson',
        location: 'Room 102',
        status: 'scheduled'
      }
    ]
  },
  {
    id: 'p1744946340017',
    name: 'Joe Jones',
    age: 70,
    gender: 'Male',
    doctorId: 'd1',
    status: PATIENT_STATUS.ACTIVE,
    diagnosis: 'CAD',
    session: {
      number: 8,
      type: 'Standard',
      progress: 22.9,
      total: 35,
      date: '2025-04-21',
      duration: 60,
      pressure: 235,
      frequency: '5 days/week',
      treatingDoctor: 'Dr. Michael Chen'
    },
    metrics: {
      lvef: {
        current: 40,
        previous: 38,
        change: '+2%'
      },
      ccsClass: {
        current: 3,
        previous: 3,
        change: '0'
      }
    },
    appointments: [
      {
        id: 'a5',
        type: 'EECP Session',
        date: '2025-04-21',
        time: '11:00',
        duration: 60,
        practitioner: 'Nurse Emma Thompson',
        location: 'Room 102',
        status: 'scheduled'
      },
      {
        id: 'a14',
        type: 'Progress Review',
        date: '2025-04-23',
        time: '13:00',
        duration: 30,
        practitioner: 'Dr. Michael Chen',
        location: 'Room 101',
        status: 'scheduled'
      },
      {
        id: 'a15',
        type: 'EECP Session',
        date: '2025-04-25',
        time: '11:00',
        duration: 60,
        practitioner: 'Nurse Emma Thompson',
        location: 'Room 102',
        status: 'scheduled'
      }
    ]
  }
];

export const PRACTITIONERS = [
  {
    id: 'd1',
    name: 'Dr. Michael Chen',
    role: 'Cardiologist',
    specialty: 'Interventional Cardiology'
  },
  {
    id: 'pr2',
    name: 'Dr. Sarah Johnson',
    role: 'Cardiologist',
    specialty: 'Non-invasive Cardiology'
  },
  {
    id: 'pr3',
    name: 'Nurse Emma Thompson',
    role: 'Nurse',
    specialty: 'EECP Therapy'
  }
];


/**
 * 
 * ALL LOGIC BELOW SHOULD BE MOVED, THIS IS JUST MOCK DATA
 * 
 * 
 */

// Helper functions for status management
export const isValidStatus = (status) => {
  return Object.values(PATIENT_STATUS).includes(status);
};

export const getStatusConfig = (status) => {
  return STATUS_CONFIG[status] || STATUS_CONFIG[PATIENT_STATUS.PENDING_INITIAL];
};

export const getPatientsByStatus = (status) => {
  if (!isValidStatus(status)) {
    console.warn(`Invalid status: ${status}`);
    return [];
  }
  return PATIENTS.filter(patient => patient.status === status);
};

export const getPatientsRequiringAttention = () => {
  return PATIENTS.filter(patient => 
    patient.status === PATIENT_STATUS.PENDING_INITIAL ||
    patient.status === PATIENT_STATUS.PENDING_REVIEW ||
    (patient.status === PATIENT_STATUS.ACTIVE && patient.priority?.level === 'High')
  ).sort((a, b) => {
    const statusPriorityA = STATUS_CONFIG[a.status].priority;
    const statusPriorityB = STATUS_CONFIG[b.status].priority;
    if (statusPriorityA !== statusPriorityB) {
      return statusPriorityA - statusPriorityB;
    }
    return (b.priority?.level === 'High') - (a.priority?.level === 'High');
  });
};

export const getPatientStatusSummary = (patient) => {
  const config = getStatusConfig(patient.status);
  const lastStatusChange = patient.statusHistory?.[patient.statusHistory.length - 1];
  
  return {
    current: patient.status,
    label: config.label,
    color: config.color,
    icon: config.icon,
    lastUpdated: lastStatusChange?.date || null,
    updatedBy: lastStatusChange?.updatedBy || null,
    notes: lastStatusChange?.notes || null,
    requiresAttention: patient.status === PATIENT_STATUS.PENDING_INITIAL || 
                      patient.status === PATIENT_STATUS.PENDING_REVIEW ||
                      (patient.status === PATIENT_STATUS.ACTIVE && patient.priority?.level === 'High')
  };
};

export const getPatientById = (id) => {
  return PATIENTS.find(patient => patient.id === id);
};

// Helper functions to get patients with priorities
export const getPatientsWithPriority = (priorityLevel) => {
  return PATIENTS.filter(patient => patient.priority?.level === priorityLevel);
};

export const getPatientsWithPendingReviews = () => {
  const priorityOrder = { 'High': 1, 'Medium': 2, 'Low': 3 };
  return PATIENTS
    .filter(patient => patient.priority)
    .sort((a, b) => priorityOrder[a.priority.level] - priorityOrder[b.priority.level]);
};

// Helper functions to get patients with upcoming sessions
export const getPatientsWithUpcomingSessions = () => {
  return PATIENTS.filter(patient => 
    patient.session?.date && 
    new Date(patient.session.date) > new Date()
  );
};

export const getPatientsWithInProgressSessions = () => {
  return PATIENTS.filter(patient => 
    patient.status === 'In Progress' && 
    patient.session?.progress < 100
  );
};

export const getPatientsWithCompletedSessions = () => {
  return PATIENTS.filter(patient => 
    patient.status === 'Treatment Complete' || 
    (patient.session?.progress === 100)
  );
};



// Helper functions for appointments
export const getAppointments = () => {
  return PATIENTS.flatMap(patient => 
    patient.appointments.map(apt => ({
      id: apt.id,
      patientId: patient.id,
      patientName: patient.name,
      practitionerId: apt.practitioner === 'Dr. Michael Chen' ? 'pr1' : 
                     apt.practitioner === 'Dr. Sarah Johnson' ? 'pr2' : 'pr3',
      practitionerName: apt.practitioner,
      appointmentType: apt.type,
      start: `${apt.date}T${apt.time}`,
      end: `${apt.date}T${addMinutesToTime(apt.time, apt.duration)}`,
      location: apt.location,
      status: apt.status,
      notes: `${apt.type} for ${patient.name}`
    }))
  );
};

// Helper function to add minutes to time string (HH:mm)
const addMinutesToTime = (timeStr, minutes) => {
  const [hours, mins] = timeStr.split(':').map(Number);
  const date = new Date(2025, 0, 1, hours, mins);
  date.setMinutes(date.getMinutes() + minutes);
  return date.toTimeString().slice(0, 5);
};


/**
 * Function used for TodaysAppointments widget
 * 
 * @returns {Array} List of today's appointments
 */
export const getTodaysAppointments = () => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  return getAppointments().filter(appointment => {
    const appointmentDate = new Date(appointment.start);
    return appointmentDate >= today && appointmentDate < tomorrow;
  }).sort((a, b) => new Date(a.start) - new Date(b.start));
};

export const getUpcomingAppointments = () => {
  const now = new Date();
  return getAppointments()
    .filter(appointment => new Date(appointment.start) > now)
    .sort((a, b) => new Date(a.start) - new Date(b.start));
};

export const getAppointmentsByPatientId = (patientId) => {
  const patient = getPatientById(patientId);
  if (!patient) return [];
  
  return patient.appointments.map(apt => ({
    id: apt.id,
    patientId: patient.id,
    patientName: patient.name,
    practitionerId: apt.practitioner === 'Dr. Michael Chen' ? 'pr1' : 
                   apt.practitioner === 'Dr. Sarah Johnson' ? 'pr2' : 'pr3',
    practitionerName: apt.practitioner,
    appointmentType: apt.type,
    start: `${apt.date}T${apt.time}`,
    end: `${apt.date}T${addMinutesToTime(apt.time, apt.duration)}`,
    location: apt.location,
    status: apt.status,
    notes: `${apt.type} for ${patient.name}`
  }));
};

export const getAppointmentsByDate = (date) => {
  const targetDate = new Date(date);
  targetDate.setHours(0, 0, 0, 0);
  const nextDate = new Date(targetDate);
  nextDate.setDate(nextDate.getDate() + 1);

  return getAppointments()
    .filter(appointment => {
      const appointmentDate = new Date(appointment.start);
      return appointmentDate >= targetDate && appointmentDate < nextDate;
    })
    .sort((a, b) => new Date(a.start) - new Date(b.start));
};

// For backward compatibility
export const APPOINTMENTS = getAppointments();
export const MOCK_APPOINTMENTS = APPOINTMENTS; 