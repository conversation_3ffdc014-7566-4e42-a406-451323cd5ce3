import React, { useState } from 'react';
import { FaHeartbeat, FaPills, FaAllergies, FaHospital, FaPlus, FaTimes, FaCalendarAlt, FaExclamationTriangle } from 'react-icons/fa';

const MedicalHistoryStep = ({ patientData, handleInputChange, handleArrayItemAdd, handleArrayItemRemove }) => {
  // Helper function to handle nested cardiovascular history changes

  //Changed this part because it was looping itself not allowing to continue to the next page, it now updates nested cardiovascular fields without causing re-render loop.
  // Old version used setPatientData directly and triggered useEffect too often.
  const handleCardioHistoryChange = (e) => {
  const { name, value, type, checked } = e.target;
  const fieldValue = type === 'checkbox' ? checked : value;

  handleInputChange({
    target: {
      name: `cardiovascularHistory.${name}`,
      value: fieldValue
    }
  });
  };
  
  const [newDiagnosis, setNewDiagnosis] = useState('');
  const [newMedication, setNewMedication] = useState('');
  const [newAllergy, setNewAllergy] = useState('');
  const [newSurgery, setNewSurgery] = useState('');

  const handleAddDiagnosis = (e) => {
    e.preventDefault();
    if (newDiagnosis.trim()) {
      handleArrayItemAdd('diagnoses', newDiagnosis.trim());
      setNewDiagnosis('');
    }
  };

  const handleAddMedication = (e) => {
    e.preventDefault();
    if (newMedication.trim()) {
      handleArrayItemAdd('medications', newMedication.trim());
      setNewMedication('');
    }
  };

  const handleAddAllergy = (e) => {
    e.preventDefault();
    if (newAllergy.trim()) {
      handleArrayItemAdd('allergies', newAllergy.trim());
      setNewAllergy('');
    }
  };

  const handleAddSurgery = (e) => {
    e.preventDefault();
    if (newSurgery.trim()) {
      handleArrayItemAdd('pastSurgeries', newSurgery.trim());
      setNewSurgery('');
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900">Medical History</h3>
        <p className="mt-1 text-sm text-gray-500">
          Enter the patient's medical history, medications, and allergies
        </p>
      </div>

      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h4 className="text-md font-medium text-gray-900 flex items-center mb-4">
          <FaHeartbeat className="mr-2 text-primary" />
          Diagnoses
        </h4>
        
        <div className="space-y-4">
          <div className="flex">
            <input
            // Giving names and ID's to this inputs
              id="newDiagnosis"
              name="newDiagnosis"
              type="text"
              value={newDiagnosis}
              onChange={(e) => setNewDiagnosis(e.target.value)}
              placeholder="Enter diagnosis (e.g., Coronary Artery Disease)"
              className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md rounded-r-none"
            />
            <button
              type="button"
              onClick={handleAddDiagnosis}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-r-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <FaPlus className="mr-1" /> Add
            </button>
          </div>
          
          <div className="mt-2">
            {patientData.diagnoses.length > 0 ? (
              <ul className="divide-y divide-gray-200 border border-gray-200 rounded-md">
                {patientData.diagnoses.map((diagnosis, index) => (
                  <li key={index} className="px-4 py-3 flex items-center justify-between">
                    <span className="text-sm text-gray-900">{diagnosis}</span>
                    <button
                      type="button"
                      onClick={() => handleArrayItemRemove('diagnoses', index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <FaTimes />
                    </button>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-sm text-gray-500 italic">No diagnoses added yet</p>
            )}
          </div>
        </div>
      </div>

      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h4 className="text-md font-medium text-gray-900 flex items-center mb-4">
          <FaPills className="mr-2 text-primary" />
          Medications
        </h4>
        
        <div className="space-y-4">
          <div className="flex">
            <input
              id="newMedication"
              name="newMedication"
              type="text"
              value={newMedication}
              onChange={(e) => setNewMedication(e.target.value)}
              placeholder="Enter medication (e.g., Aspirin 81mg daily)"
              className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md rounded-r-none"
            />
            <button
              type="button"
              onClick={handleAddMedication}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-r-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <FaPlus className="mr-1" /> Add
            </button>
          </div>
          
          <div className="mt-2">
            {patientData.medications.length > 0 ? (
              <ul className="divide-y divide-gray-200 border border-gray-200 rounded-md">
                {patientData.medications.map((medication, index) => (
                  <li key={index} className="px-4 py-3 flex items-center justify-between">
                    <span className="text-sm text-gray-900">{medication}</span>
                    <button
                      type="button"
                      onClick={() => handleArrayItemRemove('medications', index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <FaTimes />
                    </button>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-sm text-gray-500 italic">No medications added yet</p>
            )}
          </div>
        </div>
      </div>

      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h4 className="text-md font-medium text-gray-900 flex items-center mb-4">
          <FaAllergies className="mr-2 text-primary" />
          Allergies
        </h4>
        
        <div className="space-y-4">
          <div className="flex">
            <input
              id="newAllergy"
              name="newAllergy"
              type="text"
              value={newAllergy}
              onChange={(e) => setNewAllergy(e.target.value)}
              placeholder="Enter allergy (e.g., Penicillin)"
              className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md rounded-r-none"
            />
            <button
              type="button"
              onClick={handleAddAllergy}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-r-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <FaPlus className="mr-1" /> Add
            </button>
          </div>
          
          <div className="mt-2">
            {patientData.allergies.length > 0 ? (
              <ul className="divide-y divide-gray-200 border border-gray-200 rounded-md">
                {patientData.allergies.map((allergy, index) => (
                  <li key={index} className="px-4 py-3 flex items-center justify-between">
                    <span className="text-sm text-gray-900">{allergy}</span>
                    <button
                      type="button"
                      onClick={() => handleArrayItemRemove('allergies', index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <FaTimes />
                    </button>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-sm text-gray-500 italic">No allergies added yet</p>
            )}
          </div>
        </div>
      </div>

      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h4 className="text-md font-medium text-gray-900 flex items-center mb-4">
          <FaHospital className="mr-2 text-primary" />
          Past Surgeries
        </h4>
        
        <div className="space-y-4">
          <div className="flex">
            <input
              id="newSurgery"
              name="newSurgery"
              type="text"
              value={newSurgery}
              onChange={(e) => setNewSurgery(e.target.value)}
              placeholder="Enter surgery (e.g., CABG 2020)"
              className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md rounded-r-none"
            />
            <button
              type="button"
              onClick={handleAddSurgery}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-r-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <FaPlus className="mr-1" /> Add
            </button>
          </div>
          
          <div className="mt-2">
            {patientData.pastSurgeries.length > 0 ? (
              <ul className="divide-y divide-gray-200 border border-gray-200 rounded-md">
                {patientData.pastSurgeries.map((surgery, index) => (
                  <li key={index} className="px-4 py-3 flex items-center justify-between">
                    <span className="text-sm text-gray-900">{surgery}</span>
                    <button
                      type="button"
                      onClick={() => handleArrayItemRemove('pastSurgeries', index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <FaTimes />
                    </button>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-sm text-gray-500 italic">No past surgeries added yet</p>
            )}
          </div>
        </div>
      </div>

      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h4 className="text-md font-medium text-gray-900 flex items-center mb-4">
          <FaHeartbeat className="mr-2 text-red-500" />
          Cardiovascular History
        </h4>
        
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Myocardial Infarction */}
            <div className="space-y-2">
              <div className="flex items-center">
                <input
                  id="myocardialInfarction"
                  name="cardiovascularHistory.myocardialInfarction"
                  type="checkbox"
                  checked={patientData.cardiovascularHistory.myocardialInfarction}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <label htmlFor="myocardialInfarction" className="ml-2 block text-sm text-gray-900">
                  Myocardial Infarction (Heart Attack)
                </label>
              </div>
              
              {patientData.cardiovascularHistory.myocardialInfarction && (
                <div className="ml-6">
                  <label htmlFor="myocardialInfarctionDate" className="block text-xs text-gray-700">
                    Date of most recent MI
                  </label>
                  <div className="mt-1 flex items-center">
                    <FaCalendarAlt className="text-gray-400 mr-2" />
                    <input
                      type="date"
                      id="myocardialInfarctionDate"
                      name="myocardialInfarctionDate"
                      value={patientData.cardiovascularHistory.myocardialInfarctionDate}
                      onChange={handleCardioHistoryChange}
                      className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                </div>
              )}
            </div>
            
            {/* Coronary Artery Disease */}
            <div>
              <div className="flex items-center">
                <input
                  id="coronaryArteryDisease"
                  name="coronaryArteryDisease"
                  type="checkbox"
                  checked={patientData.cardiovascularHistory.coronaryArteryDisease}
                  onChange={handleCardioHistoryChange}
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <label htmlFor="coronaryArteryDisease" className="ml-2 block text-sm text-gray-900">
                  Coronary Artery Disease (CAD)
                </label>
              </div>
            </div>
            
            {/* Heart Failure */}
            <div className="space-y-2">
              <div className="flex items-center">
                <input
                  id="heartFailure"
                  name="heartFailure"
                  type="checkbox"
                  checked={patientData.cardiovascularHistory.heartFailure}
                  onChange={handleCardioHistoryChange}
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <label htmlFor="heartFailure" className="ml-2 block text-sm text-gray-900">
                  Heart Failure
                </label>
              </div>
              
              {patientData.cardiovascularHistory.heartFailure && (
                <div className="ml-6">
                  <label htmlFor="nyhaClass" className="block text-xs text-gray-700">
                    NYHA Class
                  </label>
                  <select
                    id="nyhaClass"
                    name="nyhaClass"
                    value={patientData.cardiovascularHistory.nyhaClass}
                    onChange={handleCardioHistoryChange}
                    className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                  >
                    <option value="">Select NYHA Class</option>
                    <option value="I">Class I - No limitation of physical activity</option>
                    <option value="II">Class II - Slight limitation of physical activity</option>
                    <option value="III">Class III - Marked limitation of physical activity</option>
                    <option value="IV">Class IV - Unable to carry out any physical activity without discomfort</option>
                  </select>
                </div>
              )}
            </div>
            
            {/* Valvular Disease */}
            <div>
              <div className="flex items-center">
                <input
                  id="valvularDisease"
                  name="valvularDisease"
                  type="checkbox"
                  checked={patientData.cardiovascularHistory.valvularDisease}
                  onChange={handleCardioHistoryChange}
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <label htmlFor="valvularDisease" className="ml-2 block text-sm text-gray-900">
                  Valvular Heart Disease
                </label>
              </div>
            </div>
            
            {/* Arrhythmia */}
            <div className="space-y-2">
              <div className="flex items-center">
                <input
                  id="arrhythmia"
                  name="arrhythmia"
                  type="checkbox"
                  checked={patientData.cardiovascularHistory.arrhythmia}
                  onChange={handleCardioHistoryChange}
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <label htmlFor="arrhythmia" className="ml-2 block text-sm text-gray-900">
                  Arrhythmia
                </label>
              </div>
              
              {patientData.cardiovascularHistory.arrhythmia && (
                <div className="ml-6">
                  <label htmlFor="arrhythmiaType" className="block text-xs text-gray-700">
                    Type of Arrhythmia
                  </label>
                  <input
                    type="text"
                    id="arrhythmiaType"
                    name="arrhythmiaType"
                    value={patientData.cardiovascularHistory.arrhythmiaType}
                    onChange={handleCardioHistoryChange}
                    placeholder="e.g., Atrial Fibrillation"
                    className="mt-1 shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                  />
                </div>
              )}
            </div>
            
            {/* Hypertension */}
            <div>
              <div className="flex items-center">
                <input
                  id="hypertension"
                  name="hypertension"
                  type="checkbox"
                  checked={patientData.cardiovascularHistory.hypertension}
                  onChange={handleCardioHistoryChange}
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <label htmlFor="hypertension" className="ml-2 block text-sm text-gray-900">
                  Hypertension
                </label>
              </div>
            </div>
            
            {/* Hyperlipidemia */}
            <div>
              <div className="flex items-center">
                <input
                  id="hyperlipidemia"
                  name="hyperlipidemia"
                  type="checkbox"
                  checked={patientData.cardiovascularHistory.hyperlipidemia}
                  onChange={handleCardioHistoryChange}
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <label htmlFor="hyperlipidemia" className="ml-2 block text-sm text-gray-900">
                  Hyperlipidemia
                </label>
              </div>
            </div>
            
            {/* Diabetes */}
            <div>
              <div className="flex items-center">
                <input
                  id="diabetes"
                  name="diabetes"
                  type="checkbox"
                  checked={patientData.cardiovascularHistory.diabetes}
                  onChange={handleCardioHistoryChange}
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <label htmlFor="diabetes" className="ml-2 block text-sm text-gray-900">
                  Diabetes Mellitus
                </label>
              </div>
            </div>
            
            {/* Peripheral Vascular Disease */}
            <div>
              <div className="flex items-center">
                <input
                  id="peripheralVascularDisease"
                  name="peripheralVascularDisease"
                  type="checkbox"
                  checked={patientData.cardiovascularHistory.peripheralVascularDisease}
                  onChange={handleCardioHistoryChange}
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <label htmlFor="peripheralVascularDisease" className="ml-2 block text-sm text-gray-900">
                  Peripheral Vascular Disease
                </label>
              </div>
            </div>
            
            {/* Stroke */}
            <div className="space-y-2">
              <div className="flex items-center">
                <input
                  id="stroke"
                  name="stroke"
                  type="checkbox"
                  checked={patientData.cardiovascularHistory.stroke}
                  onChange={handleCardioHistoryChange}
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <label htmlFor="stroke" className="ml-2 block text-sm text-gray-900">
                  Stroke / TIA
                </label>
              </div>
              
              {patientData.cardiovascularHistory.stroke && (
                <div className="ml-6">
                  <label htmlFor="strokeDate" className="block text-xs text-gray-700">
                    Date of most recent stroke
                  </label>
                  <div className="mt-1 flex items-center">
                    <FaCalendarAlt className="text-gray-400 mr-2" />
                    <input
                      type="date"
                      id="strokeDate"
                      name="strokeDate"
                      value={patientData.cardiovascularHistory.strokeDate}
                      onChange={handleCardioHistoryChange}
                      className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* EECP Contraindication Warning */}
          {(patientData.cardiovascularHistory.myocardialInfarction || 
            patientData.cardiovascularHistory.arrhythmia) && (
            <div className="mt-4 bg-yellow-50 border-l-4 border-yellow-400 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <FaExclamationTriangle className="h-5 w-5 text-yellow-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm text-yellow-700">
                    Some cardiovascular conditions may affect EECP therapy eligibility. 
                    A complete evaluation will be performed before treatment.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h4 className="text-md font-medium text-gray-900 mb-4">Family History</h4>
        
        <div>
          <textarea
            name="familyHistory"
            id="familyHistory"
            rows="3"
            value={patientData.familyHistory}
            onChange={handleInputChange}
            className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
            placeholder="Enter relevant family history (e.g., father had MI at age 55)"
          ></textarea>
        </div>
      </div>
    </div>
  );
};

export default MedicalHistoryStep;
