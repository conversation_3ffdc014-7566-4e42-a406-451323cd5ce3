import React from 'react';
import { TrendingUp, Info } from 'lucide-react';

const QualityOfLifeTab = ({ qualityOfLife, onChange }) => {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-6">Quality of Life Assessment</h3>
      
      <div className="space-y-6">
        {/* Physical Well-being */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Physical Well-being</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-xs text-gray-500 mb-1">Energy Level</label>
              <select 
                value={qualityOfLife.energyLevel}
                onChange={(e) => onChange('energyLevel', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Select Level</option>
                <option value="1">Very Low</option>
                <option value="2">Low</option>
                <option value="3">Moderate</option>
                <option value="4">High</option>
                <option value="5">Very High</option>
              </select>
            </div>
            <div>
              <label className="block text-xs text-gray-500 mb-1">Sleep Quality</label>
              <select 
                value={qualityOfLife.sleepQuality}
                onChange={(e) => onChange('sleepQuality', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Select Quality</option>
                <option value="1">Very Poor</option>
                <option value="2">Poor</option>
                <option value="3">Fair</option>
                <option value="4">Good</option>
                <option value="5">Excellent</option>
              </select>
            </div>
          </div>
          
          <div className="mt-4 p-2 bg-green-50 rounded border border-green-200">
            <div className="flex items-center">
              <TrendingUp size={16} className="text-green-600 mr-1" />
              <span className="text-sm text-green-800">
                Improved from 2 to 4 (Energy Level)
              </span>
            </div>
          </div>
        </div>

        {/* Emotional Well-being */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Emotional Well-being</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-xs text-gray-500 mb-1">Mood</label>
              <select 
                value={qualityOfLife.mood}
                onChange={(e) => onChange('mood', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Select Mood</option>
                <option value="1">Very Poor</option>
                <option value="2">Poor</option>
                <option value="3">Fair</option>
                <option value="4">Good</option>
                <option value="5">Excellent</option>
              </select>
            </div>
            <div>
              <label className="block text-xs text-gray-500 mb-1">Anxiety Level</label>
              <select 
                value={qualityOfLife.anxiety}
                onChange={(e) => onChange('anxiety', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Select Level</option>
                <option value="1">Severe</option>
                <option value="2">Moderate</option>
                <option value="3">Mild</option>
                <option value="4">Minimal</option>
                <option value="5">None</option>
              </select>
            </div>
          </div>
          
          <div className="mt-4 p-2 bg-green-50 rounded border border-green-200">
            <div className="flex items-center">
              <TrendingUp size={16} className="text-green-600 mr-1" />
              <span className="text-sm text-green-800">
                Anxiety reduced from 3 to 1
              </span>
            </div>
          </div>
        </div>

        {/* Social Well-being */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Social Well-being</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-xs text-gray-500 mb-1">Social Activity</label>
              <select 
                value={qualityOfLife.socialActivity}
                onChange={(e) => onChange('socialActivity', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Select Level</option>
                <option value="1">Very Limited</option>
                <option value="2">Limited</option>
                <option value="3">Moderate</option>
                <option value="4">Active</option>
                <option value="5">Very Active</option>
              </select>
            </div>
            <div>
              <label className="block text-xs text-gray-500 mb-1">Work/School Status</label>
              <select 
                value={qualityOfLife.workStatus}
                onChange={(e) => onChange('workStatus', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Select Status</option>
                <option value="1">Unable to Work</option>
                <option value="2">Part-time</option>
                <option value="3">Full-time with Limitations</option>
                <option value="4">Full-time</option>
                <option value="5">Full-time with No Limitations</option>
              </select>
            </div>
          </div>
          
          <div className="mt-4 p-2 bg-green-50 rounded border border-green-200">
            <div className="flex items-center">
              <Info size={16} className="text-green-600 mr-1" />
              <span className="text-sm text-green-800">
                Returned to full-time work
              </span>
            </div>
          </div>
        </div>

        {/* Overall QoL Score */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-blue-700 mb-2">Overall Quality of Life Score</h4>
          <div className="flex items-center justify-between">
            <div className="text-2xl font-bold text-blue-800">85%</div>
            <div className="flex items-center text-green-600 text-sm">
              <span className="mr-1">+25%</span>
              <TrendingUp size={14} />
            </div>
          </div>
          <div className="text-xs text-gray-500 mt-1">From 60% at baseline</div>
        </div>

        {/* Assessment Notes */}
        <div className="bg-blue-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-700 mb-2">Quality of Life Assessment Notes</h4>
          <textarea
            value={qualityOfLife.notes}
            onChange={(e) => onChange('notes', e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md h-24"
            placeholder="Enter notes about quality of life assessment..."
          ></textarea>
        </div>
      </div>
    </div>
  );
};

export default QualityOfLifeTab; 