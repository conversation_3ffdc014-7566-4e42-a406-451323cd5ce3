/*
  This file contains AI prompts snd schemas related to static clinical data extraction
  I have added predefined prompt templates for different medical scenarios 
*/

/**
SYSTEM PROMPTS = Acts as Base foundation for all medical extractions

Why System Prompts?
1. Establishes role of AI as a specialist for medical data extraction 
2. Sets consistent medical stnadards (ICD-10) across all extractions 
3. Prevents inaccuracies by instructing AI to appropriately mark any unclear data
4. Ensures HIPAA (Health Insurance Portability and Accountability Act) conscious handling of medical info.
5. Creates consistent output format (JSON) for easy integration with other systems

CLINICAL IMPORTANCE:
1. MEDICAL DATA needs to be extracted with higher accuracy standards compared to general data
2. STANDARDIZATION OF TERMINOLOGY prevents confusion and errors in data interpretation.
3. Consistent formatting enables reliable integration with EHR systems

*/


/**
* DEMOGRAPHIC EXTRACTION - Patient identity and Contact details
  * WHY? 
    1. Patient identity is crucial for linking data across different systems
    2. Contact details help in reaching out for clarifications/follow-ups 
    3. Demographics data affects treatment decisions and insurance claims
    4. Insurance and referral information drives billing and care coordination
       
  * CLINCIAL WORKFLOW:
    1. Patient registration
    2. Feeds into patient matching systems to prevent duplicate records
    3. Appointment scheduling
    4. Billing and insurance processing
       
  * INTGERATION POINTS:
    1. INSURANCE VERFICATION SYSTEMS
    2. APPOINTMENT SCHEDULING SYSTEMS
    3. EMERGENCY NOTIFICATION SYSTEMS
       
       
* MEDICAL HISTORY EXTRACTION - Comprehensive medical background
  * WHY?
    1. Helps in understanding current health status and treatment history
    2. Predicts future health risks and treatment outcomes
    3. 
  * CLINICAL IMPORTANCE:
    1. Contraindications for EECP (Electrical Excitation Cardioversion) therapy must be identified
    2. Drug allergies prevent prescriptions for certain medications
    3. Medical history influences treatment plans and prognosis 
       
  * EHR_INTEGRATION:
    1. Populates problem list with ICO-10 coded diagnosis
    2. Feeds into clinical decision support systems
    3. Trigger allergy alerts and medication interactions
    4. Links to medication reconciliation processes
       
       
* SYMPTOMS EXTRACTION - Current Clincal Presentation
  * WHY?
    1. Identifies specific symptoms that guide treatment selection
    2. Helps in differential diagnosis and treatment planning (urgency of care , e.g. chest_pain = emergency ? )
    3. Functional status NYHA (New York Heart Association) classification indicates treatment urgency
    4. Quality of life measures justify insurance coverage for certain treatments
       
  * EECP-SPECIFC IMPORATNCE:
    1. Angina frequency and severity determines referral to EECP 
    2. Excerise tolerance indicactes treatement success
    3. side effect monitoring (e.g. dizziness, nausea) ensures patient safety
       
  * CLINICAL DECISION MAKING:
    1.Helps determine if patient is a candidate for EECP
    2. Guides treatment selection (e.g. beta-blockers for angina vs EECP)
    3. Identify when to pause or modify treatment
    4. documents improvement for insurance continuation
       
       
* VITALS EXTRACTION - Objective Clinical Measurements
  * WHY?
    1. Monitors physiological status and treatment response
    2. Helps in diagnosing conditions (e.g. hypertension, heart failure)
    3. Guides treatment adjustments (e.g. diuretics for fluid overload)
    4. Monitors EECP response (e.g. heart rate, blood pressure) and cardiac function metrics (LVEF, BNP) determines treatment eligibility
    5. Insurance often requires objective measurements for coverage

  * SAFETY CONSIDERATIONS:
    1. EECP requires precise measurements (e.g. heart rate, blood pressure)
    2. Side effects (e.g. dizziness, nausea) must be monitored
    3. LVEF (Left Ventricular Function) < 30% indicates severe heart failure and EECP is not recommended
    4. BNP (Brain Natriuretic Peptide) levels help identify heart failure severity
       
  * TREATMENT MONITORING: 
    1. Pre/Post session vitals track treatment response
    2. Serial measurements help detect treatment efficacy and safety
    3. Anthropometric measurements (e.g. weight, height) tracks fluid balance
    4. Cardiac Markers (e.g. CK-MB, Troponin) monitor for myocardial injury
  

*/
const {
  DEMOGRAPHICS_SCHEMA,
  MEDICAL_HISTORY_SCHEMA,
  SYMPTOMS_SCHEMA,
  VITALS_SCHEMA,
  MEDICAL_STANDARDIZATION
} = require('../validation/clinicalSchemas');

const CLINICAL_EXTRACTION_PROMPTS = {

  /**
   * SYSTEM PROMPT - Foundation for all clinical extractions
   * Sets the AI's role, standards, and output format
   */
  SYSTEM_PROMPT: `You are a specialized medical AI assistant trained in clinical data extraction from doctor-patient conversations.

CORE RESPONSIBILITIES:
1. Extract structured clinical information from transcribed medical conversations
2. Convert unstructured medical text into standardized, EHR-compatible data
3. Apply medical terminology standardization using established healthcare coding systems
4. Ensure data accuracy and clinical safety through validation
5. Maintain patient privacy and HIPAA compliance

CRITICAL STANDARDS:
- Use full standardized medical terms (e.g., "Coronary Artery Disease" not "CAD")
- Include ICD-10 codes when extracting diagnoses
- Use generic medication names with standard dosage formats
- Apply unit standardization (mmHg, bpm, mg, etc.)
- Mark unclear information as "Unknown" rather than making assumptions
- Validate all numerical values against clinical norms

DATA STANDARDIZATION RULES:
${JSON.stringify(MEDICAL_STANDARDIZATION.CONDITION_MAPPINGS, null, 2)}

OUTPUT REQUIREMENTS:
- Return ONLY a valid JSON object
- Do not include any explanatory text, markdown formatting, or code blocks
- Do not wrap the JSON in backticks or any other formatting
- Include all required fields as specified in the schema
- Use standardized enums and validation patterns
- Ensure all data types match schema specifications
- Maintain clinical accuracy and safety at all times

EXAMPLE VALID OUTPUT:
{
  "firstName": "John",
  "lastName": "Smith",
  "dateOfBirth": "1965-03-15",
  "gender": "Male"
}

You can also use "Female", "Other", "Prefer not to say", or "Unknown" for the gender field.

PRIVACY & COMPLIANCE:
- Process only clinical information relevant to patient care
- Do not store or reference personal identifiers unnecessarily
- Follow HIPAA guidelines for medical data handling`,

  /**
   * DEMOGRAPHICS EXTRACTION PROMPT
   * Extracts patient identity, contact, and administrative information
   */
  DEMOGRAPHICS_PROMPT: `Extract patient demographic and administrative information from the conversation.

REQUIRED DATA STRUCTURE:
${JSON.stringify(DEMOGRAPHICS_SCHEMA, null, 2)}

EXTRACTION GUIDELINES:

PATIENT IDENTITY:
- Extract full legal name (firstName, middleName, lastName)
- Validate date of birth format as YYYY-MM-DD
- Use standardized gender values: Male, Female, Other, Prefer not to say
- Normalize phone numbers to international format when possible

CONTACT INFORMATION:
- Validate email addresses for proper format
- Structure address with all required components
- Ensure postal codes match regional format (Canadian: A1A 1A1)

EMERGENCY CONTACT:
- Extract full name and relationship to patient
- Validate phone number format
- Use standardized relationship values from schema enum

INSURANCE & REFERRAL:
- Extract insurance provider, member ID, and policy type
- Capture referring physician details including specialty
- Normalize insurance type using schema enums

VALIDATION RULES:
- Names must contain only letters, spaces, hyphens, and apostrophes
- Phone numbers must be 7-15 digits with optional country code
- All required fields must be present or marked as "Unknown"

IMPORTANT: Return ONLY a valid JSON object with the following required fields:
{
  "firstName": "string",
  "lastName": "string",
  "dateOfBirth": "YYYY-MM-DD",
  "gender": "Male|Female|Other|Prefer not to say|Unknown"
}

Additional fields are optional but must match the schema format. Do not include any explanatory text or markdown formatting in your response.`,

  /**
   * MEDICAL HISTORY EXTRACTION PROMPT
   * Extracts comprehensive medical background information
   */
  MEDICAL_HISTORY_PROMPT: `Extract comprehensive medical history from the conversation.

REQUIRED DATA STRUCTURE:
${JSON.stringify(MEDICAL_HISTORY_SCHEMA, null, 2)}

EXTRACTION GUIDELINES:

DIAGNOSES:
- Apply condition standardization: ${JSON.stringify(MEDICAL_STANDARDIZATION.CONDITION_MAPPINGS)}
- Include ICD-10 codes: ${JSON.stringify(MEDICAL_STANDARDIZATION.ICD10_CODES)}
- Classify status as: Active, Resolved, Chronic, Inactive, Unknown
- Extract onset dates in YYYY-MM-DD format when mentioned
- Assess severity: Mild, Moderate, Severe, Unknown

MEDICATIONS:
- Use standardized medication names: ${JSON.stringify(MEDICAL_STANDARDIZATION.MEDICATION_MAPPINGS)}
- Extract dosage with standard units (mg, g, ml, units, mcg)
- Standardize frequency using schema enums
- Include route of administration when mentioned
- Capture prescribing physician when available

ALLERGIES:
- Categorize as: Medication, Food, Environmental, Other
- Extract specific reactions and symptoms
- Assess severity: Mild, Moderate, Severe, Life-threatening, Unknown
- Include onset information when available

SURGERIES:
- Extract procedure names with standard medical terminology
- Include dates in YYYY-MM-DD format
- Capture hospital/facility and surgeon when mentioned
- Document any complications or recovery notes

FAMILY HISTORY:
- Standardize relationships: Father, Mother, Sibling, Child, Grandparent, Other
- Apply condition standardization from mappings
- Extract age of onset when mentioned (0-120 years)

VALIDATION REQUIREMENTS:
- All dosages must match pattern: number + unit (e.g., "100mg", "5ml")
- Dates must be valid and in correct format
- Severity levels must use exact schema enums
- Required fields: condition+status for diagnoses, name+dosage+frequency for medications

Return JSON matching MEDICAL_HISTORY_SCHEMA exactly.`,

  /**
   * SYMPTOMS EXTRACTION PROMPT
   * Extracts current clinical presentation and functional status
   */
  SYMPTOMS_PROMPT: `Extract current symptoms and clinical presentation from the conversation.

REQUIRED DATA STRUCTURE:
${JSON.stringify(SYMPTOMS_SCHEMA, null, 2)}

EXTRACTION GUIDELINES:

CHIEF COMPLAINT:
- Extract primary reason for visit in patient's own words
- Maximum 500 characters, capture essence of presenting problem
- Required field - must be present

CURRENT SYMPTOMS:
- Apply symptom standardization: ${JSON.stringify(MEDICAL_STANDARDIZATION.SYMPTOM_MAPPINGS)}
- Extract symptom characteristics using clinical methodology:
  * Onset: When did it start? (sudden, gradual, specific time)
  * Duration: How long has it lasted? (minutes, hours, days, weeks)
  * Severity: Pain scale 1-10 (1=mild, 10=severe)
  * Location: Where does it occur? (anatomical location)
  * Quality: How does it feel? (sharp, dull, burning, crushing)
  * Aggravating factors: What makes it worse?
  * Alleviating factors: What makes it better?

FUNCTIONAL STATUS:
- NYHA Classification (Heart Failure):
  * Class I: No symptoms with ordinary activity
  * Class II: Slight limitation with ordinary activity
  * Class III: Marked limitation with less than ordinary activity
  * Class IV: Symptoms at rest
- CCS Classification (Angina):
  * Class I: No angina with ordinary activity
  * Class II: Slight limitation with ordinary activity
  * Class III: Marked limitation with ordinary activity
  * Class IV: Angina with any activity or at rest
- Exercise tolerance description
- Quality of life scale 1-10 (1=poor, 10=excellent)

CARDIOVASCULAR-SPECIFIC SYMPTOMS:
- Chest pain: location, radiation, duration, triggers
- Dyspnea: exertional vs. rest, orthopnea, paroxysmal nocturnal
- Palpitations: frequency, triggers, associated symptoms
- Edema: location, severity, timing
- Fatigue: severity, impact on daily activities

VALIDATION REQUIREMENTS:
- Severity scores must be 1-10 integers
- Quality of life scores must be 1-10 integers
- NYHA/CCS classes must use exact enum values (I, II, III, IV, Unknown)
- Symptom names must be standardized using provided mappings

Return JSON matching SYMPTOMS_SCHEMA exactly.`,

  /**
   * VITALS EXTRACTION PROMPT
   * Extracts objective clinical measurements and test results
   */
  VITALS_PROMPT: `Extract vital signs and objective clinical measurements from the conversation.

REQUIRED DATA STRUCTURE:
${JSON.stringify(VITALS_SCHEMA, null, 2)}

EXTRACTION GUIDELINES:

VITAL SIGNS:
- Blood Pressure: Extract systolic and diastolic values in mmHg
  * Normal range: Systolic 90-140, Diastolic 60-90
  * Flag values outside 60-250 systolic, 30-150 diastolic
- Heart Rate: Extract value in beats per minute (bpm)
  * Normal range: 60-100 bpm
  * Flag values outside 30-200 bpm
- Respiratory Rate: Extract breaths per minute
  * Normal range: 12-20 breaths/min
  * Flag values outside 8-40 breaths/min
- Temperature: Extract in Celsius
  * Normal range: 36.5-37.5°C
  * Flag values outside 35-42°C
- Oxygen Saturation: Extract percentage
  * Normal range: 95-100%
  * Flag values below 90%

ANTHROPOMETRIC MEASUREMENTS:
- Height: Extract in centimeters (100-250 cm range)
- Weight: Extract in kilograms (20-300 kg range)
- BMI: Calculate if both height and weight available (weight/height²)
  * Underweight: <18.5
  * Normal: 18.5-24.9
  * Overweight: 25-29.9
  * Obese: ≥30

CARDIAC FUNCTION (EECP-Specific):
- LVEF (Left Ventricular Ejection Fraction): Extract percentage
  * Normal: ≥50%
  * Mild dysfunction: 40-49%
  * Moderate dysfunction: 30-39%
  * Severe dysfunction: <30%
- BNP (B-type Natriuretic Peptide): Extract in pg/mL
  * Normal: <100 pg/mL
  * Heart failure likely: >400 pg/mL
- Cardiac Output: Extract in L/min if mentioned
  * Normal range: 4-8 L/min

UNIT STANDARDIZATION:
Apply unit mappings: ${JSON.stringify(MEDICAL_STANDARDIZATION.UNIT_MAPPINGS)}

VALIDATION REQUIREMENTS:
- All numerical values must be within specified ranges
- Units must match schema enums exactly
- Blood pressure requires both systolic and diastolic values
- Flag any values outside normal clinical ranges for review
- Ensure proper data types (numbers for values, strings for units)

CRITICAL SAFETY CHECKS:
- LVEF <30% indicates severe heart failure (EECP contraindication consideration)
- Systolic BP >180 or <90 requires immediate attention
- Heart rate >120 or <50 may indicate arrhythmia
- Oxygen saturation <90% indicates hypoxemia

Return JSON matching VITALS_SCHEMA exactly.`,

  /**
   * COMPREHENSIVE EXTRACTION PROMPT
   * Extracts all clinical data categories in single pass
   */
  COMPREHENSIVE_PROMPT: `Extract ALL clinical information from the conversation into structured format.

EXTRACT ALL CATEGORIES:
1. Demographics (patient identity, contact, insurance)
2. Medical History (diagnoses, medications, allergies, surgeries, family history)
3. Current Symptoms (chief complaint, symptom analysis, functional status)
4. Vital Signs (measurements, cardiac function, anthropometrics)

RETURN FORMAT:
{
  "demographics": { /* DEMOGRAPHICS_SCHEMA structure */ },
  "medicalHistory": { /* MEDICAL_HISTORY_SCHEMA structure */ },
  "symptoms": { /* SYMPTOMS_SCHEMA structure */ },
  "vitals": { /* VITALS_SCHEMA structure */ }
}

APPLY ALL STANDARDIZATION RULES:
- Use condition mappings for diagnoses
- Apply medication name standardization
- Include ICD-10 codes where applicable
- Standardize symptom terminology
- Validate all measurements against clinical ranges
- Use exact schema enums and data types

VALIDATION CHECKLIST:
- All required fields present or marked "Unknown"
- Data types match schema specifications exactly
- Numerical values within clinical ranges
- Dates in YYYY-MM-DD format
- Units standardized according to mappings
- Medical terminology standardized
- JSON structure matches schemas exactly

Return complete JSON object with all four categories.`,

  /**
   * VALIDATION PROMPT
   * Reviews and validates extracted clinical data
   */
  VALIDATION_PROMPT: `Review the extracted clinical data for accuracy, completeness, and safety.

VALIDATION CHECKLIST:

CLINICAL SAFETY:
- LVEF values <30% flagged (severe heart failure)
- Blood pressure values >180/110 or <90/60 flagged
- Heart rate <50 or >120 flagged (arrhythmia risk)
- Drug allergies properly documented with severity
- Medication dosages within safe ranges

DATA QUALITY:
- All required schema fields present
- Data types match specifications exactly
- Dates in correct YYYY-MM-DD format
- Units standardized using provided mappings
- Medical terminology standardized using mappings
- ICD-10 codes included for diagnoses

COMPLETENESS:
- Patient demographics complete for identification
- Current medications with dosages and frequencies
- Known allergies documented with reactions
- Current symptoms with characteristics
- Vital signs with appropriate units

STANDARDIZATION:
- Condition names use standardized mappings
- Medication names use generic standards
- Symptom names use clinical terminology
- Units follow standard medical notation
- Severity/classification scales use schema enums

Return validation report with any issues identified and corrected data.`,

  /**
   * ERROR HANDLING PROMPT
   * Manages unclear or missing information appropriately
   */
  ERROR_HANDLING_PROMPT: `Handle unclear, incomplete, or missing clinical information appropriately.

GUIDELINES FOR UNCLEAR INFORMATION:

WHEN INFORMATION IS UNCLEAR:
- Mark field as "Unknown" rather than guessing
- Note ambiguity in separate "notes" field if available
- Request clarification for critical safety information
- Use "Approximately [year]" for unclear dates

WHEN INFORMATION IS MISSING:
- Leave optional fields empty or null
- Mark required fields as "Unknown"
- Do not fabricate or assume missing data
- Flag missing critical safety information

CRITICAL SAFETY DATA:
- Drug allergies: Always extract if mentioned
- Current medications: Include all mentioned
- Heart conditions: Prioritize cardiac-related information
- Vital signs: Extract all available measurements

CONFLICTING INFORMATION:
- Use most recent or specific information
- Note discrepancy in separate field
- Flag for provider review
- Prioritize safety-critical data

STANDARDIZATION PRIORITIES:
1. Patient safety information (allergies, contraindications)
2. Current active conditions and medications  
3. Vital signs and measurements
4. Historical information
5. Administrative details

Always prioritize patient safety over data completeness.`
};

/**
 * PROMPT BUILDER UTILITY
 * Constructs prompts with schema references for specific extraction needs
 */
class ClinicalPromptBuilder {

  /**
   * Build extraction prompt for specific category
   * @param {string} category - Type of clinical data to extract (demographics, medical_history, etc.)
   * @param {Object} options - Options for prompt building
   * @param {string} options.transcript - Clinical transcript text
   * @param {string} options.sessionType - Type of clinical session (intake, followup, etc.)
   * @param {number} options.sessionNumber - Session number for recurring visits
   * @param {Object} options.existingData - Previously extracted patient data
   * @returns {Object} Prompt object with systemPrompt and userPrompt
   */
  static buildExtractionPrompt(category, options) {
    const basePrompt = CLINICAL_EXTRACTION_PROMPTS.SYSTEM_PROMPT;
    const categoryKey = `${category.toUpperCase()}_PROMPT`;
    const categoryPrompt = CLINICAL_EXTRACTION_PROMPTS[categoryKey];

    if (!categoryPrompt) {
      throw new Error(`Unknown extraction category: ${category}`);
    }

    if (!options || !options.transcript) {
      throw new Error('Transcript is required for extraction prompt');
    }

    const systemPrompt = basePrompt;
    const userPrompt = `${categoryPrompt}

CONVERSATION TO ANALYZE:
"${options.transcript}"

IMPORTANT: Return ONLY a valid JSON object. Do not include any explanatory text, markdown formatting, or code blocks. The response should start with '{' and end with '}'.`;

    return {
      systemPrompt,
      userPrompt
    };
  }

  /**
   * Build comprehensive extraction prompt
   * @param {string} conversationText - Transcribed conversation
   * @returns {string} Complete prompt for all categories
   */
  static buildComprehensivePrompt(conversationText) {
    return `${CLINICAL_EXTRACTION_PROMPTS.SYSTEM_PROMPT}

${CLINICAL_EXTRACTION_PROMPTS.COMPREHENSIVE_PROMPT}

CONVERSATION TO ANALYZE:
"${conversationText}"

Extract ALL clinical information and return as complete JSON object.`;
  }

  /**
   * Build validation prompt for extracted data
   * @param {object} extractedData - Previously extracted clinical data
   * @returns {string} Validation prompt
   */
  static buildValidationPrompt(extractedData) {
    return `${CLINICAL_EXTRACTION_PROMPTS.SYSTEM_PROMPT}

${CLINICAL_EXTRACTION_PROMPTS.VALIDATION_PROMPT}

EXTRACTED DATA TO VALIDATE:
${JSON.stringify(extractedData, null, 2)}

Review for clinical safety, accuracy, and completeness. Return validation report and any corrections needed.`;
  }
}

/**
 * SCHEMA REFERENCE UTILITY
 * Provides easy access to schema definitions for validation
 */
class SchemaReference {

  static getDemographicsSchema() {
    return DEMOGRAPHICS_SCHEMA;
  }

  static getMedicalHistorySchema() {
    return MEDICAL_HISTORY_SCHEMA;
  }

  static getSymptomsSchema() {
    return SYMPTOMS_SCHEMA;
  }

  static getVitalsSchema() {
    return VITALS_SCHEMA;
  }

  static getAllSchemas() {
    return {
      DEMOGRAPHICS_SCHEMA,
      MEDICAL_HISTORY_SCHEMA,
      SYMPTOMS_SCHEMA,
      VITALS_SCHEMA
    };
  }

  static getStandardizationMappings() {
    return MEDICAL_STANDARDIZATION;
  }
}

module.exports = {
  CLINICAL_EXTRACTION_PROMPTS,
  ClinicalPromptBuilder,
  SchemaReference
};
