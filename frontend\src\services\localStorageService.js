/**
 * Local Storage Service - Refactored for Safety and Organization
 * 
 * PRIMARY PURPOSE: User authentication and session management
 * SECONDARY PURPOSE: Legacy data operations (deprecated - use JSON DB/Medplum API instead)
 * 
 * This service is organized into sections:
 * 1. Authentication Operations (KEEP) - User login, roles, sessions
 * 2. Legacy Data Operations (DEPRECATED) - Patient, appointment, etc. data
 * 
 */


// Keys for localStorage
const STORAGE_KEYS = {
  // Authentication keys
  USER: 'syncore_user',
  USER_ROLE: 'syncore_user_role',
  DOCTOR_ID: 'syncore_doctorId', // Added doctor ID key
  // Legacy data keys
  PATIENTS: 'syncore_patients',
  APPOINTMENTS: 'syncore_appointments',
  PRACTITIONERS: 'syncore_practitioners',
  EVALUATIONS: 'syncore_evaluations',
  SESSIONS: 'syncore_sessions'

};

/**
 * Safely initializes localStorage with default data if needed.
 * IMPORTANT: No longer uses localStorage.clear() to prevent breaking user sessions.
 * Only initializes missing keys to preserve existing authentication data.
 *
 * @throws {Error} If storage initialization fails or mock data import fails.
 */
const initializeStorage = () => {
  try {
    // Only initialize legacy data if it doesn't exist
    // This prevents overwriting existing user authentication data
    if (!localStorage.getItem(STORAGE_KEYS.PATIENTS)) {
      // Dynamic import to avoid bundling issues
      const { MOCK_PATIENTS, MOCK_PRACTITIONERS, MOCK_APPOINTMENTS } = require('../data/mockData');

      // Initialize only if keys don't exist (safe initialization)
      if (!localStorage.getItem(STORAGE_KEYS.PATIENTS)) {
        localStorage.setItem(STORAGE_KEYS.PATIENTS, JSON.stringify(MOCK_PATIENTS));
      }
      if (!localStorage.getItem(STORAGE_KEYS.PRACTITIONERS)) {
        localStorage.setItem(STORAGE_KEYS.PRACTITIONERS, JSON.stringify(MOCK_PRACTITIONERS));
      }
      if (!localStorage.getItem(STORAGE_KEYS.APPOINTMENTS)) {
        localStorage.setItem(STORAGE_KEYS.APPOINTMENTS, JSON.stringify(MOCK_APPOINTMENTS));
      }
      if (!localStorage.getItem(STORAGE_KEYS.EVALUATIONS)) {
        localStorage.setItem(STORAGE_KEYS.EVALUATIONS, JSON.stringify({}));
      }
      if (!localStorage.getItem(STORAGE_KEYS.SESSIONS)) {
        localStorage.setItem(STORAGE_KEYS.SESSIONS, JSON.stringify([]));
      }
    }
  } catch (error) {
    console.error('Error initializing storage:', error);
    throw new Error('Failed to initialize localStorage with default data');
  }
};


//
// User Authentication
// 

/**
 * Gets the currently authenticated user from localStorage.
 * Used by AuthContext to restore user sessions on app reload.
 *
 * @returns {Object|null} User object containing {id, name, email} or null if not logged in.
 * @throws {Error} If localStorage access fails or JSON parsing fails.
 */
const getCurrentUser = () => {
  try {
    const user = localStorage.getItem(STORAGE_KEYS.USER);
    return user ? JSON.parse(user) : null;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
};

/**
 * Sets the current authenticated user in localStorage.
 * Called during login/logout to persist authentication state.
 *
 * @param {Object|null} user - User object to store or null to clear authentication.
 * @throws {Error} If localStorage write operation fails.
 */
const setCurrentUser = (user) => {
  try {
    if (user) {
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));
    } else {
      localStorage.removeItem(STORAGE_KEYS.USER);
    }
  } catch (error) {
    console.error('Error setting current user:', error);
    throw new Error('Failed to save user authentication data');
  }
};

/**
 * Gets the current user's role from localStorage.
 * Used for role-based access control and navigation.
 *
 * @returns {string|null} User role ('doctor', 'nurse', 'admin') or null if not set.
 * @throws {Error} If localStorage access fails.
 */
const getUserRole = () => {
  try {
    return localStorage.getItem(STORAGE_KEYS.USER_ROLE);
  } catch (error) {
    console.error('Error getting user role:', error);
    return null;
  }
};

/**
 * Sets the current user's role in localStorage.
 * Called after login to establish user permissions.
 *
 * @param {string|null} role - User role to store ('doctor', 'nurse', 'admin') or null to clear.
 * @throws {Error} If localStorage write operation fails.
 */
const setUserRole = (role) => {
  try {
    if (role) {
      localStorage.setItem(STORAGE_KEYS.USER_ROLE, role);
    } else {
      localStorage.removeItem(STORAGE_KEYS.USER_ROLE);
    }
  } catch (error) {
    console.error('Error setting user role:', error);
    throw new Error('Failed to save user role data');
  }
};

/**
 * Clears all authentication data from localStorage.
 * Used during logout to remove user session data.
 * Note: Only clears authentication keys, preserves other app data.
 *
 * @throws {Error} If localStorage clear operation fails.
 */
const clearAuthData = () => {
  try {
    localStorage.removeItem(STORAGE_KEYS.USER);
    localStorage.removeItem(STORAGE_KEYS.USER_ROLE);
    localStorage.removeItem(STORAGE_KEYS.DOCTOR_ID); // Clear doctor ID on logout
  } catch (error) {
    console.error('Error clearing authentication data:', error);
    throw new Error('Failed to clear user authentication data');
  }
};


//
// PATIENT OPERATIONS
// 
/**
 * @deprecated Use JSON database or Medplum API instead.
 * Gets all patients from localStorage.
 *
 * @returns {Array} Array of patient objects.
 * @throws {Error} If localStorage access or JSON parsing fails.
 */
const getPatients = () => {
  try {
    const patients = localStorage.getItem(STORAGE_KEYS.PATIENTS);
    return patients && patients !== 'undefined' ? JSON.parse(patients) : [];
  } catch (error) {
    console.error('Error getting patients from localStorage:', error);
    return [];
  }
};

/**
 * @deprecated Use JSON database or Medplum API instead.
 * Gets a patient by ID.
 *
 * @param {string} patientId - Patient ID to search for.
 * @returns {Object|undefined} Patient object or undefined if not found.
 * @throws {Error} If patient retrieval fails.
 */
const getPatientById = (patientId) => {
  try {
    const patients = getPatients();
    return patients.find(p => p.id === patientId);
  } catch (error) {
    console.error('Error getting patient by ID:', error);
    return undefined;
  }
};

/**
 * @deprecated Use JSON database or Medplum API instead.
 * Updates patient data in localStorage.
 *
 * @param {string} patientId - ID of patient to update.
 * @param {Object} updatedData - Updated patient data object.
 * @returns {Object|null} Updated patient object or null if not found.
 * @throws {Error} If patient update operation fails.
 */
const updatePatient = (patientId, updatedData) => {
  try {
    const patients = getPatients();
    const index = patients.findIndex(p => p.id === patientId);
    if (index !== -1) {
      patients[index] = { ...patients[index], ...updatedData };
      localStorage.setItem(STORAGE_KEYS.PATIENTS, JSON.stringify(patients));
      return patients[index];
    }
    return null;
  } catch (error) {
    console.error('Error updating patient:', error);
    return null;
  }
};

/**
 * @deprecated Use JSON database or Medplum API instead.
 * Adds a note to a patient in localStorage.
 *
 * @param {string} patientId - ID of patient to add note to.
 * @param {Object} note - Note object containing text and metadata.
 * @returns {Object|null} Created note object or null if patient not found.
 * @throws {Error} If note creation fails.
 */
const addNoteToPatient = (patientId, note) => {
  try {
    const patients = getPatients();
    const index = patients.findIndex(p => p.id === patientId);
    if (index !== -1) {
      if (!patients[index].notes) {
        patients[index].notes = [];
      }
      const newNote = {
        id: `n${Date.now()}`,
        date: new Date().toISOString(),
        ...note
      };
      patients[index].notes.unshift(newNote);
      localStorage.setItem(STORAGE_KEYS.PATIENTS, JSON.stringify(patients));
      return newNote;
    }
    return null;
  } catch (error) {
    console.error('Error adding note to patient:', error);
    return null;
  }
};

/**
 * @deprecated Use JSON database or Medplum API instead.
 * Gets all practitioners from localStorage.
 *
 * @returns {Array} Array of practitioner objects.
 * @throws {Error} If localStorage access or JSON parsing fails.
 */
const getPractitioners = () => {
  try {
    const practitioners = localStorage.getItem(STORAGE_KEYS.PRACTITIONERS);
    return practitioners ? JSON.parse(practitioners) : [];
  } catch (error) {
    console.error('Error getting practitioners:', error);
    return [];
  }
};

/**
 * @deprecated Use JSON database or Medplum API instead.
 * Gets evaluations for a specific patient.
 *
 * @param {string} patientId - Patient ID to get evaluations for.
 * @returns {Array} Array of evaluation objects for the patient.
 * @throws {Error} If evaluation retrieval fails.
 */
const getEvaluations = (patientId) => {
  try {
    const evaluations = localStorage.getItem(STORAGE_KEYS.EVALUATIONS);
    const allEvaluations = evaluations ? JSON.parse(evaluations) : {};
    return allEvaluations[patientId] || [];
  } catch (error) {
    console.error('Error getting evaluations:', error);
    return [];
  }
};

/**
 * @deprecated Use JSON database or Medplum API instead.
 * Adds an evaluation for a specific patient.
 *
 * @param {string} patientId - Patient ID to add evaluation to.
 * @param {Object} evaluation - Evaluation object to add.
 * @returns {Object} The added evaluation object.
 * @throws {Error} If evaluation creation fails.
 */
const addEvaluation = (patientId, evaluation) => {
  try {
    const evaluations = localStorage.getItem(STORAGE_KEYS.EVALUATIONS);
    const allEvaluations = evaluations ? JSON.parse(evaluations) : {};

    if (!allEvaluations[patientId]) {
      allEvaluations[patientId] = [];
    }

    // Add new evaluation at the beginning of the array
    allEvaluations[patientId] = [evaluation, ...allEvaluations[patientId]];
    localStorage.setItem(STORAGE_KEYS.EVALUATIONS, JSON.stringify(allEvaluations));
    return evaluation;
  } catch (error) {
    console.error('Error adding evaluation:', error);
    return evaluation;
  }
};

/**
 * @deprecated Use JSON database or Medplum API instead.
 * Gets all appointments from localStorage.
 *
 * @returns {Array} Array of appointment objects.
 * @throws {Error} If localStorage access or JSON parsing fails.
 */
const getAppointments = () => {
  try {
    const appointments = localStorage.getItem(STORAGE_KEYS.APPOINTMENTS);
    return appointments ? JSON.parse(appointments) : [];
  } catch (error) {
    console.error('Error getting appointments:', error);
    return [];
  }
};

/**
 * @deprecated Use JSON database or Medplum API instead.
 * Gets appointments for a specific patient.
 *
 * @param {string} patientId - Patient ID to get appointments for.
 * @returns {Array} Array of appointment objects for the patient.
 * @throws {Error} If appointment retrieval fails.
 */
const getPatientAppointments = (patientId) => {
  try {
    const appointments = getAppointments();
    return appointments.filter(a => a.patientId === patientId);
  } catch (error) {
    console.error('Error getting patient appointments:', error);
    return [];
  }
};

/**
 * @deprecated Use JSON database or Medplum API instead.
 * Adds a new appointment.
 *
 * @param {Object} appointment - Appointment object to add.
 * @returns {Object|null} The created appointment object with generated ID or null if failed.
 * @throws {Error} If appointment creation fails.
 */
const addAppointment = (appointment) => {
  try {
    const appointments = getAppointments();
    const newAppointment = {
      ...appointment,
      id: `a${Date.now()}`,
      status: 'scheduled'
    };
    appointments.push(newAppointment);
    localStorage.setItem(STORAGE_KEYS.APPOINTMENTS, JSON.stringify(appointments));

    // Update patient's next appointment if this is a future appointment
    const appointmentDate = new Date(newAppointment.start);
    const now = new Date();
    if (appointmentDate > now) {
      const patient = getPatientById(newAppointment.patientId);
      if (patient) {
        const currentNextAppointment = patient.nextAppointment ? new Date(patient.nextAppointment) : null;
        if (!currentNextAppointment || appointmentDate < currentNextAppointment) {
          updatePatient(newAppointment.patientId, { nextAppointment: newAppointment.start });
        }
      }
    }

    return newAppointment;
  } catch (error) {
    console.error('Error adding appointment:', error);
    return null;
  }
};

/**
 * @deprecated Use JSON database or Medplum API instead.
 * Updates an existing appointment.
 *
 * @param {string} appointmentId - ID of appointment to update.
 * @param {Object} updatedData - Updated appointment data.
 * @returns {Object|null} Updated appointment object or null if not found.
 * @throws {Error} If appointment update fails.
 */
const updateAppointment = (appointmentId, updatedData) => {
  try {
    const appointments = getAppointments();
    const index = appointments.findIndex(a => a.id === appointmentId);
    if (index !== -1) {
      appointments[index] = { ...appointments[index], ...updatedData };
      localStorage.setItem(STORAGE_KEYS.APPOINTMENTS, JSON.stringify(appointments));
      return appointments[index];
    }
    return null;
  } catch (error) {
    console.error('Error updating appointment:', error);
    return null;
  }
};

/**
 * @deprecated Use JSON database or Medplum API instead.
 * Gets all sessions from localStorage.
 *
 * @returns {Array} Array of session objects.
 * @throws {Error} If localStorage access or JSON parsing fails.
 */
const getSessions = () => {
  try {
    const sessions = localStorage.getItem(STORAGE_KEYS.SESSIONS);
    return sessions ? JSON.parse(sessions) : [];
  } catch (error) {
    console.error('Error getting sessions:', error);
    return [];
  }
};

/**
 * @deprecated Use JSON database or Medplum API instead.
 * Adds a new session.
 *
 * @param {Object} session - Session object to add.
 * @returns {Object|null} The added session object or null if failed.
 * @throws {Error} If session creation fails.
 */
const addSession = (session) => {
  try {
    const sessions = getSessions();
    sessions.push(session);
    localStorage.setItem(STORAGE_KEYS.SESSIONS, JSON.stringify(sessions));
    return session;
  } catch (error) {
    console.error('Error adding session:', error);
    return null;
  }
};

// Initialize storage when the service is first imported
if (!localStorage.getItem(STORAGE_KEYS.PATIENTS)) {
  initializeStorage();
}



export const localStorageService = {
  // User operations
  getCurrentUser,
  setCurrentUser,
  getUserRole,
  setUserRole,
  clearAuthData,

  // Patient operations
  getPatients,
  getPatientById,
  updatePatient,
  addNoteToPatient,

  // Practitioner operations
  getPractitioners,

  // Evaluation operations
  getEvaluations,
  addEvaluation,

  // Appointment operations
  getAppointments,
  getPatientAppointments,
  addAppointment,
  updateAppointment,

  // Session operations
  getSessions,
  addSession,

  // Storage keys (for reference)
  STORAGE_KEYS
};
