import { z } from 'zod';

/**
 * @file Defines Zod schemas and TypeScript types for authentication-related data.
 */

// Zod schema for login request validation.
export const LoginSchema = z.object({
  email: z.string().email({ message: "Invalid email address" }),
  password: z.string().min(6, { message: "Password must be at least 6 characters long" }),
});

/**
 * TypeScript type inferred from LoginSchema.
 * Represents the structure of a login request payload.
 */
export type LoginRequest = z.infer<typeof LoginSchema>;

/**
 * Interface for a User object, reflecting a simplified Medplum "Profile" or "User" resource.
 * This is what we expect to get back as the authenticated user's details.
 */
export interface User {
  email: string;
  firstName?: string; // Optional
  lastName?: string;  // Optional
  role?: 'admin' | 'doctor' | 'nurse';
}

/**
 * Interface for a Login Response from AuthService.
 * This is what we expect to get back as the authenticated user's details.
 */
export interface LoginResponse {
  token: string;
  user: User;
}