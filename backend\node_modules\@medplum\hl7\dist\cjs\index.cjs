"use strict";var L=Object.create;var p=Object.defineProperty;var C=Object.getOwnPropertyDescriptor;var A=Object.getOwnPropertyNames;var O=Object.getPrototypeOf,B=Object.prototype.hasOwnProperty;var T=(n,t)=>{for(var e in t)p(n,e,{get:t[e],enumerable:!0})},f=(n,t,e,s)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of A(t))!B.call(n,r)&&r!==e&&p(n,r,{get:()=>t[r],enumerable:!(s=C(t,r))||s.enumerable});return n};var g=(n,t,e)=>(e=n!=null?L(O(n)):{},f(t||!n||!n.__esModule?p(e,"default",{value:n,enumerable:!0}):e,n)),S=n=>f(p({},"__esModule",{value:!0}),n);var P={};T(P,{CR:()=>E,FS:()=>k,Hl7Base:()=>a,Hl7Client:()=>m,Hl7CloseEvent:()=>l,Hl7Connection:()=>d,Hl7ErrorEvent:()=>c,Hl7MessageEvent:()=>h,Hl7Server:()=>u,VT:()=>I});module.exports=S(P);var a=class extends EventTarget{addEventListener(t,e,s){super.addEventListener(t,e,s)}removeEventListener(t,e,s){super.removeEventListener(t,e,s)}};var x=require("node:net");var H=require("@medplum/core"),v=g(require("iconv-lite"));var I=11,E=13,k=28;var h=class extends Event{constructor(t,e){super("message"),this.connection=t,this.message=e}},c=class extends Event{constructor(t){super("error"),this.error=t}},l=class extends Event{constructor(){super("close")}};var d=class extends a{constructor(e,s="utf-8",r=!1){super();this.chunks=[];this.messageQueue=[];this.socket=e,this.encoding=s,this.enhancedMode=r,e.on("data",o=>{try{if(this.appendData(o),o.at(-2)===28&&o.at(-1)===13){let i=Buffer.concat(this.chunks),M=i.subarray(1,i.length-2),w=v.default.decode(M,this.encoding),b=H.Hl7Message.parse(w);this.dispatchEvent(new h(this,b)),this.resetBuffer()}}catch(i){this.dispatchEvent(new c(i))}}),e.on("error",o=>{this.resetBuffer(),this.dispatchEvent(new c(o))}),e.on("end",()=>{this.close()}),this.addEventListener("message",o=>{r&&this.send(o.message.buildAck({ackCode:"CA"}));let i=this.messageQueue.shift();if(!i){this.dispatchEvent(new c(new Error(`Received a message when no pending messages were in the queue. Message: ${o.message}`)));return}i.resolve?.(o.message)})}sendImpl(e,s){this.messageQueue.push(s);let r=e.toString(),o=v.default.encode(r,this.encoding),i=Buffer.alloc(o.length+3);i.writeInt8(11,0),o.copy(i,1),i.writeInt8(28,o.length+1),i.writeInt8(13,o.length+2),this.socket.write(i)}send(e){this.sendImpl(e,{message:e})}async sendAndWait(e){return new Promise((s,r)=>{let o={message:e,resolve:s,reject:r};this.sendImpl(e,o)})}close(){this.socket.end(),this.socket.destroy(),this.dispatchEvent(new l)}appendData(e){this.chunks.push(e)}resetBuffer(){this.chunks=[]}};var m=class extends a{constructor(t){super(),this.options=t,this.host=this.options.host,this.port=this.options.port,this.encoding=this.options.encoding,this.keepAlive=this.options.keepAlive??!1,this.connectTimeout=this.options.connectTimeout??3e4}connect(){return this.connection?Promise.resolve(this.connection):(this.socket&&(this.socket.removeAllListeners(),this.socket.destroy(),this.socket=void 0),new Promise((t,e)=>{this.socket=(0,x.connect)({host:this.host,port:this.port,keepAlive:this.keepAlive}),this.connectTimeout>0&&(this.socket.setTimeout(this.connectTimeout),this.socket.on("timeout",()=>{let s=new Error(`Connection timeout after ${this.connectTimeout}ms`);this.socket&&(this.socket.destroy(),this.socket=void 0),e(s)})),this.socket.on("connect",()=>{if(!this.socket)return;let s;this.connection=s=new d(this.socket,this.encoding),this.socket.setTimeout(0),s.addEventListener("close",()=>{this.socket=void 0,this.dispatchEvent(new l)}),s.addEventListener("error",r=>{this.dispatchEvent(new c(r.error))}),t(this.connection)}),this.socket.on("error",s=>{this.socket&&(this.socket.destroy(),this.socket=void 0),e(s)})}))}async send(t){return(await this.connect()).send(t)}async sendAndWait(t){return(await this.connect()).sendAndWait(t)}close(){this.socket&&(this.socket.removeAllListeners(),this.socket.destroy(),this.socket=void 0),this.connection&&(this.connection.close(),delete this.connection)}};var y=g(require("node:net"));var u=class{constructor(t){this.handler=t}start(t,e,s=!1){let r=y.default.createServer(o=>{let i=new d(o,e,s);this.handler(i)});r.listen(t),this.server=r}async stop(){return new Promise((t,e)=>{if(!this.server){e(new Error("Stop was called but there is no server running"));return}this.server.close(s=>{if(s){e(s);return}t()}),this.server=void 0})}};0&&(module.exports={CR,FS,Hl7Base,Hl7Client,Hl7CloseEvent,Hl7Connection,Hl7ErrorEvent,Hl7MessageEvent,Hl7Server,VT});
//# sourceMappingURL=index.cjs.map
