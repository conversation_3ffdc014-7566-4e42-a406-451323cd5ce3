import React from 'react';
import { TrendingUp, AlertCircle, Info } from 'lucide-react';

const BiomarkersTab = ({ biomarkers, onChange }) => {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-6">Biomarkers Analysis</h3>
      
      <div className="space-y-6">
        {/* Cardiac Biomarkers */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Cardiac Biomarkers</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-xs text-gray-500 mb-1">BNP (pg/mL)</label>
              <input
                type="text"
                value={biomarkers.bnp}
                onChange={(e) => onChange('bnp', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Enter BNP value"
              />
              <div className="text-xs text-gray-500 mt-1">Normal range: &lt; 100 pg/mL</div>
            </div>
            <div>
              <label className="block text-xs text-gray-500 mb-1">Troponin (ng/L)</label>
              <input
                type="text"
                value={biomarkers.troponin}
                onChange={(e) => onChange('troponin', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Enter Troponin value"
              />
              <div className="text-xs text-gray-500 mt-1">Normal range: &lt; 14 ng/L</div>
            </div>
          </div>
          
          <div className="mt-4 p-2 bg-green-50 rounded border border-green-200">
            <div className="flex items-center">
              <TrendingUp size={16} className="text-green-600 mr-1" />
              <span className="text-sm text-green-800">
                BNP decreased from 450 to 85 pg/mL
              </span>
            </div>
          </div>
        </div>

        {/* Inflammatory Markers */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Inflammatory Markers</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-xs text-gray-500 mb-1">CRP (mg/L)</label>
              <input
                type="text"
                value={biomarkers.crp}
                onChange={(e) => onChange('crp', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Enter CRP value"
              />
              <div className="text-xs text-gray-500 mt-1">Normal range: &lt; 3 mg/L</div>
            </div>
            <div>
              <label className="block text-xs text-gray-500 mb-1">ESR (mm/hr)</label>
              <input
                type="text"
                value={biomarkers.esr}
                onChange={(e) => onChange('esr', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Enter ESR value"
              />
              <div className="text-xs text-gray-500 mt-1">Normal range: &lt; 20 mm/hr</div>
            </div>
          </div>
          
          <div className="mt-4 p-2 bg-green-50 rounded border border-green-200">
            <div className="flex items-center">
              <Info size={16} className="text-green-600 mr-1" />
              <span className="text-sm text-green-800">
                Inflammatory markers within normal range
              </span>
            </div>
          </div>
        </div>

        {/* Lipid Profile */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Lipid Profile</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-xs text-gray-500 mb-1">Total Cholesterol (mg/dL)</label>
              <input
                type="text"
                value={biomarkers.totalCholesterol}
                onChange={(e) => onChange('totalCholesterol', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Enter value"
              />
              <div className="text-xs text-gray-500 mt-1">Target: &lt; 200 mg/dL</div>
            </div>
            <div>
              <label className="block text-xs text-gray-500 mb-1">LDL (mg/dL)</label>
              <input
                type="text"
                value={biomarkers.ldl}
                onChange={(e) => onChange('ldl', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Enter value"
              />
              <div className="text-xs text-gray-500 mt-1">Target: &lt; 100 mg/dL</div>
            </div>
            <div>
              <label className="block text-xs text-gray-500 mb-1">HDL (mg/dL)</label>
              <input
                type="text"
                value={biomarkers.hdl}
                onChange={(e) => onChange('hdl', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Enter value"
              />
              <div className="text-xs text-gray-500 mt-1">Target: &gt; 40 mg/dL</div>
            </div>
            <div>
              <label className="block text-xs text-gray-500 mb-1">Triglycerides (mg/dL)</label>
              <input
                type="text"
                value={biomarkers.triglycerides}
                onChange={(e) => onChange('triglycerides', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Enter value"
              />
              <div className="text-xs text-gray-500 mt-1">Target: &lt; 150 mg/dL</div>
            </div>
          </div>
          
          <div className="mt-4 p-2 bg-yellow-50 rounded border border-yellow-200">
            <div className="flex items-center">
              <AlertCircle size={16} className="text-yellow-600 mr-1" />
              <span className="text-sm text-yellow-800">
                LDL slightly elevated, consider medication adjustment
              </span>
            </div>
          </div>
        </div>

        {/* Biomarkers Summary */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-blue-700 mb-2">Biomarkers Summary</h4>
          <div className="flex items-center justify-between">
            <div className="text-2xl font-bold text-blue-800">Overall Improvement</div>
            <div className="flex items-center text-green-600 text-sm">
              <span className="mr-1">+65%</span>
              <TrendingUp size={14} />
            </div>
          </div>
          <div className="text-xs text-gray-500 mt-1">Most markers improved or within normal range</div>
        </div>

        {/* Assessment Notes */}
        <div className="bg-blue-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-700 mb-2">Biomarkers Assessment Notes</h4>
          <textarea
            value={biomarkers.notes}
            onChange={(e) => onChange('notes', e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md h-24"
            placeholder="Enter notes about biomarkers assessment..."
          ></textarea>
        </div>
      </div>
    </div>
  );
};

export default BiomarkersTab; 