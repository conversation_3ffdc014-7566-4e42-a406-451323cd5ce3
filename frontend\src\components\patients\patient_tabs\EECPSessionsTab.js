import React, { useState, useMemo } from 'react';
import { 
  Heart, Activity, Calendar, Clock, User, AlertCircle, CheckCircle, 
  TrendingUp, TrendingDown, Filter, Search, Eye, BarChart3, 
  Thermometer, Droplets, Wind, Zap, PlayCircle, Users, FileText,
  ChevronDown, ChevronRight, Star, Target, Award
} from 'lucide-react';

const EECPSessionsTimeline = ({ patientData }) => {
  console.log(patientData, "THIS IS THE PATIENT DATA");

  const [filter, setFilter] = useState('all');
  const [viewMode, setViewMode] = useState('timeline'); // timeline, grid, chart
  const [expandedSession, setExpandedSession] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Process and filter sessions
  const processedSessions = useMemo(() => {
    if (!patientData?.sessions) return [];
    
    return patientData.sessions
      .map((session, index) => ({
        ...session,
        sessionId: `${session.sessionNumber}-${session.date}-${index}`,
        hasAdverseEvents: session.adverseEvents && session.adverseEvents.length > 0,
        hasInterruptions: session.interruptions && session.interruptions.length > 0,
        duration: session.duration || 0,
        completionRate: session.status === 'completed' ? 100 : 0
      }))
      .filter(session => {
        if (filter === 'adverse') return session.hasAdverseEvents;
        if (filter === 'interrupted') return session.hasInterruptions;
        if (filter === 'completed') return session.status === 'completed';
        if (searchTerm) {
          return session.notes?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                 session.personnel?.therapist?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                 session.personnel?.physician?.toLowerCase().includes(searchTerm.toLowerCase());
        }
        return true;
      })
      .sort((a, b) => {
        const dateA = new Date(`${a.date}T${a.startTime}`);
        const dateB = new Date(`${b.date}T${b.startTime}`);
        return dateA - dateB;
      });
  }, [patientData?.sessions, filter, searchTerm]);

  // Calculate timeline statistics
  const timelineStats = useMemo(() => {
    const total = processedSessions.length;
    const completed = processedSessions.filter(s => s.status === 'completed').length;
    const withAdverseEvents = processedSessions.filter(s => s.hasAdverseEvents).length;
    const avgDuration = total > 0 ? processedSessions.reduce((sum, s) => sum + s.duration, 0) / total : 0;
    
    return { total, completed, withAdverseEvents, avgDuration };
  }, [processedSessions]);

  const getSessionStatusColor = (session) => {
    if (session.hasAdverseEvents) return 'from-red-500 to-rose-600';
    if (session.hasInterruptions) return 'from-orange-500 to-amber-600';
    if (session.status === 'completed') return 'from-green-500 to-emerald-600';
    return 'from-blue-500 to-indigo-600';
  };

  const getMeasurementValue = (measurement, field) => {
    if (!measurement) return '—';
    
    if (field === 'bloodPressure') {
      return measurement.bloodPressure 
        ? `${measurement.bloodPressure.systolic}/${measurement.bloodPressure.diastolic}`
        : '—';
    }
    
    return measurement[field] || '—';
  };

  const SessionCard = ({ session, index }) => {
    const isExpanded = expandedSession === session.sessionId;
    const statusColor = getSessionStatusColor(session);
    
    return (
      <div className="relative group">
        {/* Timeline connector line */}
        {index < processedSessions.length - 1 && (
          <div className="absolute left-8 top-20 w-0.5 h-full bg-gradient-to-b from-blue-200 to-purple-200 z-0"></div>
        )}
        
        <div className="relative flex items-start space-x-6 pb-8">
          {/* Timeline node */}
          <div className={`relative z-10 w-16 h-16 bg-gradient-to-r ${statusColor} rounded-2xl flex items-center justify-center text-white font-bold shadow-xl group-hover:scale-110 transition-transform duration-300`}>
            <div className="text-center">
              <div className="text-lg">{session.sessionNumber}</div>
              {session.hasAdverseEvents && (
                <AlertCircle className="w-4 h-4 absolute -top-1 -right-1 text-red-300" />
              )}
            </div>
          </div>
          
          {/* Session details card */}
          <div className="flex-1 bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300">
            <div className="p-6">
              {/* Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <h3 className="text-xl font-bold text-gray-900">
                    Session {session.sessionNumber}
                  </h3>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    session.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {session.status}
                  </span>
                  {session.hasAdverseEvents && (
                    <span className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium">
                      Adverse Event
                    </span>
                  )}
                </div>
                
                <button
                  onClick={() => setExpandedSession(isExpanded ? null : session.sessionId)}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
                </button>
              </div>

              {/* Session metadata */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Calendar className="w-4 h-4" />
                  <span>{new Date(session.date).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Clock className="w-4 h-4" />
                  <span>{session.startTime} - {session.endTime}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Activity className="w-4 h-4" />
                  <span>{session.duration} min</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Users className="w-4 h-4" />
                  <span>{session.personnel?.therapist || 'N/A'}</span>
                </div>
              </div>

              {/* Quick vital signs overview */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                <div className="bg-blue-50 rounded-xl p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Heart className="w-4 h-4 text-blue-600" />
                    <span className="text-xs font-medium text-blue-600">Pre-EECP BP</span>
                  </div>
                  <div className="text-lg font-bold text-blue-900">
                    {getMeasurementValue(session.measurements?.preEECP, 'bloodPressure')}
                  </div>
                </div>
                
                <div className="bg-green-50 rounded-xl p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Heart className="w-4 h-4 text-green-600" />
                    <span className="text-xs font-medium text-green-600">Post-EECP BP</span>
                  </div>
                  <div className="text-lg font-bold text-green-900">
                    {getMeasurementValue(session.measurements?.postEECP, 'bloodPressure')}
                  </div>
                </div>
                
                <div className="bg-purple-50 rounded-xl p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Droplets className="w-4 h-4 text-purple-600" />
                    <span className="text-xs font-medium text-purple-600">O2 Saturation</span>
                  </div>
                  <div className="text-lg font-bold text-purple-900">
                    {getMeasurementValue(session.measurements?.preEECP, 'spo2')}%
                  </div>
                </div>
              </div>

              {/* Notes preview */}
              {session.notes && (
                <div className="bg-gray-50 rounded-xl p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <FileText className="w-4 h-4 text-gray-600" />
                    <span className="text-xs font-medium text-gray-600">Session Notes</span>
                  </div>
                  <p className="text-sm text-gray-700 line-clamp-2">{session.notes}</p>
                </div>
              )}

              {/* Expanded details */}
              {isExpanded && (
                <div className="mt-6 pt-6 border-t border-gray-200 space-y-6 animate-in slide-in-from-top duration-300">
                  {/* Detailed measurements */}
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">Detailed Measurements</h4>
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                      {/* Pre-EECP */}
                      <div className="bg-blue-50 rounded-xl p-4">
                        <h5 className="font-semibold text-blue-900 mb-3 flex items-center">
                          <PlayCircle className="w-4 h-4 mr-2" />
                          Pre-EECP
                        </h5>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-blue-700">Blood Pressure:</span>
                            <span className="font-medium">{getMeasurementValue(session.measurements?.preEECP, 'bloodPressure')}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-blue-700">Pulse Rate:</span>
                            <span className="font-medium">{session.measurements?.preEECP?.pulseRate || '—'} bpm</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-blue-700">Weight:</span>
                            <span className="font-medium">{session.measurements?.preEECP?.weight || '—'} {session.measurements?.preEECP?.weightUnit || ''}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-blue-700">SpO2:</span>
                            <span className="font-medium">{session.measurements?.preEECP?.spo2 || '—'}%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-blue-700">O2 Liters:</span>
                            <span className="font-medium">{session.measurements?.preEECP?.o2Liters || '—'}</span>
                          </div>
                        </div>
                      </div>

                      {/* During EECP */}
                      <div className="bg-amber-50 rounded-xl p-4">
                        <h5 className="font-semibold text-amber-900 mb-3 flex items-center">
                          <Zap className="w-4 h-4 mr-2" />
                          During EECP
                        </h5>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-amber-700">Applied Pressure:</span>
                            <span className="font-medium">{session.measurements?.during15Min?.appliedPressure || session.measurements?.during45Min?.appliedPressure || '—'} mmHg</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-amber-700">DS Peak:</span>
                            <span className="font-medium">{session.measurements?.during45Min?.dsPeak || '—'}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-amber-700">DS Area:</span>
                            <span className="font-medium">{session.measurements?.during45Min?.dsArea || '—'}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-amber-700">Pulse Rate:</span>
                            <span className="font-medium">{session.measurements?.during45Min?.pulseRate || '—'} bpm</span>
                          </div>
                        </div>
                      </div>

                      {/* Post-EECP */}
                      <div className="bg-green-50 rounded-xl p-4">
                        <h5 className="font-semibold text-green-900 mb-3 flex items-center">
                          <CheckCircle className="w-4 h-4 mr-2" />
                          Post-EECP
                        </h5>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-green-700">Blood Pressure:</span>
                            <span className="font-medium">{getMeasurementValue(session.measurements?.postEECP, 'bloodPressure')}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-green-700">Pulse Rate:</span>
                            <span className="font-medium">{session.measurements?.postEECP?.pulseRate || '—'} bpm</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-green-700">SpO2:</span>
                            <span className="font-medium">{session.measurements?.postEECP?.spo2 || '—'}%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-green-700">Skin Condition:</span>
                            <span className="font-medium">{session.measurements?.postEECP?.skinCondition || '—'}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-green-700">Electrode Artifact:</span>
                            <span className="font-medium">{session.measurements?.postEECP?.electrodeInducedArtefact ? 'Yes' : 'No'}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Personnel */}
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">Personnel</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-gray-50 rounded-xl p-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <User className="w-4 h-4 text-gray-600" />
                          <span className="text-sm font-medium text-gray-600">Therapist</span>
                        </div>
                        <div className="font-semibold text-gray-900">{session.personnel?.therapist || 'Not specified'}</div>
                      </div>
                      <div className="bg-gray-50 rounded-xl p-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <User className="w-4 h-4 text-gray-600" />
                          <span className="text-sm font-medium text-gray-600">Physician</span>
                        </div>
                        <div className="font-semibold text-gray-900">{session.personnel?.physician || 'Not specified'}</div>
                      </div>
                    </div>
                  </div>

                  {/* Adverse Events & Interruptions */}
                  {(session.hasAdverseEvents || session.hasInterruptions) && (
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-4">Events & Interruptions</h4>
                      {session.hasAdverseEvents && (
                        <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-4">
                          <div className="flex items-center space-x-2 mb-2">
                            <AlertCircle className="w-4 h-4 text-red-600" />
                            <span className="text-sm font-medium text-red-600">Adverse Events</span>
                          </div>
                          <div className="space-y-2">
                            {session.adverseEvents.map((event, idx) => (
                              <div key={idx} className="text-sm text-red-700">{event}</div>
                            ))}
                          </div>
                        </div>
                      )}
                      {session.hasInterruptions && (
                        <div className="bg-orange-50 border border-orange-200 rounded-xl p-4">
                          <div className="flex items-center space-x-2 mb-2">
                            <Clock className="w-4 h-4 text-orange-600" />
                            <span className="text-sm font-medium text-orange-600">Interruptions</span>
                          </div>
                          <div className="space-y-2">
                            {session.interruptions.map((interruption, idx) => (
                              <div key={idx} className="text-sm text-orange-700">{interruption}</div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const GridView = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {processedSessions.map((session, index) => (
        <div key={session.sessionId} className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all">
          <div className="flex items-center justify-between mb-4">
            <div className={`w-12 h-12 bg-gradient-to-r ${getSessionStatusColor(session)} rounded-xl flex items-center justify-center text-white font-bold`}>
              {session.sessionNumber}
            </div>
            <span className={`px-3 py-1 rounded-full text-xs font-medium ${
              session.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
            }`}>
              {session.status}
            </span>
          </div>
          
          <div className="space-y-3">
            <div className="text-sm text-gray-600">
              {new Date(session.date).toLocaleDateString()} • {session.duration} min
            </div>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>
                <span className="text-gray-500">Pre BP:</span>
                <div className="font-medium">{getMeasurementValue(session.measurements?.preEECP, 'bloodPressure')}</div>
              </div>
              <div>
                <span className="text-gray-500">Post BP:</span>
                <div className="font-medium">{getMeasurementValue(session.measurements?.postEECP, 'bloodPressure')}</div>
              </div>
            </div>
            {session.notes && (
              <p className="text-sm text-gray-600 line-clamp-2">{session.notes}</p>
            )}
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="space-y-8">
      {/* Header with stats and controls */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white shadow-2xl">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-3xl font-bold">EECP Sessions</h2>
          <div className="flex space-x-2">
            {['timeline', 'grid'].map(mode => (
              <button
                key={mode}
                onClick={() => setViewMode(mode)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                  viewMode === mode 
                    ? 'bg-white text-blue-600' 
                    : 'bg-white/20 text-white hover:bg-white/30'
                }`}
              >
                {mode.charAt(0).toUpperCase() + mode.slice(1)}
              </button>
            ))}
          </div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div className="bg-white/20 backdrop-blur-sm rounded-xl p-4">
            <div className="text-2xl font-bold">{timelineStats.total}</div>
            <div className="text-blue-100 text-sm">Total Sessions</div>
          </div>
          <div className="bg-white/20 backdrop-blur-sm rounded-xl p-4">
            <div className="text-2xl font-bold">{timelineStats.completed}</div>
            <div className="text-blue-100 text-sm">Completed</div>
          </div>
          <div className="bg-white/20 backdrop-blur-sm rounded-xl p-4">
            <div className="text-2xl font-bold">{timelineStats.withAdverseEvents}</div>
            <div className="text-blue-100 text-sm">Adverse Events</div>
          </div>
          <div className="bg-white/20 backdrop-blur-sm rounded-xl p-4">
            <div className="text-2xl font-bold">{Math.round(timelineStats.avgDuration)}</div>
            <div className="text-blue-100 text-sm">Avg Duration (min)</div>
          </div>
        </div>
      </div>

      {/* Filters and search */}
      <div className="flex flex-wrap items-center justify-between gap-4">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search sessions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div className="flex space-x-2">
            {['all', 'completed', 'adverse', 'interrupted'].map(filterType => (
              <button
                key={filterType}
                onClick={() => setFilter(filterType)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  filter === filterType 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
              </button>
            ))}
          </div>
        </div>
        
        <div className="text-sm text-gray-600">
          Showing {processedSessions.length} of {patientData?.sessions?.length || 0} sessions
        </div>
      </div>

      {/* Sessions display */}
      {processedSessions.length === 0 ? (
        <div className="text-center py-16 bg-white rounded-2xl shadow-lg">
          <Heart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-700 mb-2">No sessions found</h3>
          <p className="text-gray-500">Try adjusting your filters or search terms</p>
        </div>
      ) : viewMode === 'timeline' ? (
        <div className="relative">
          {processedSessions.map((session, index) => (
            <SessionCard key={session.sessionId} session={session} index={index} />
          ))}
        </div>
      ) : (
        <GridView />
      )}
    </div>
  );
};

export default EECPSessionsTimeline;