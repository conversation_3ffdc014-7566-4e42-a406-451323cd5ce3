/**
 * @file debug-project-connection.ts
 * @description Debug script to check which Medplum project your API is connected to.
 */

import dotenv from 'dotenv';
import { getMedplumClient } from '../helpers/medplumHelper.js';

dotenv.config();

/**
 * Debugs the current Medplum project connection and displays project info.
 * 
 * @returns {Promise<void>} Promise that resolves when debug is complete
 */
async function debugProjectConnection(): Promise<void> {
    console.log('🔍 Debugging Medplum project connection...\n');

    try {
        // Get client and basic info
        console.log('📋 Environment Configuration:');
        console.log(`   CLIENT_ID: ${process.env.CLIENT_ID ? '✅ Set' : '❌ Missing'}`);
        console.log(`   CLIENT_SECRET: ${process.env.CLIENT_SECRET ? '✅ Set' : '❌ Missing'}`);
        console.log(`   MEDPLUM_BASE_URL: ${process.env.MEDPLUM_BASE_URL || 'Using default'}\n`);

        // Get authenticated client
        const client = await getMedplumClient();
        console.log('✅ Successfully authenticated with Medplum\n');

        // Try to get project information
        console.log('🏗️ Project Information:');

        try {
            // Search for any resource to see what project we're in
            const testSearch = await client.search('Patient', { _count: '1' });

            if (testSearch.entry && testSearch.entry.length > 0) {
                const patient = testSearch.entry[0].resource;
                const meta = patient?.meta;

                if (meta) {
                    console.log(`   Project ID: ${meta.project || 'Unknown'}`);
                    console.log(`   Author: ${meta.author?.display || 'Unknown'}`);
                    console.log(`   Last Updated: ${meta.lastUpdated || 'Unknown'}`);

                    if (meta.compartment) {
                        console.log('   Compartments:');
                        meta.compartment.forEach((comp: any, index: number) => {
                            console.log(`     ${index + 1}. ${comp.reference}`);
                        });
                    }
                } else {
                    console.log('   ⚠️  No metadata available');
                }
            } else {
                console.log('   ⚠️  No patients found in current project');
            }

        } catch (searchError: any) {
            console.log(`   ❌ Failed to get project info: ${searchError.message}`);
        }

        // List all patients in current project
        console.log('\n📊 Patients in Current Project:');

        try {
            const allPatients = await client.search('Patient', { _count: '10' });

            if (allPatients.entry && allPatients.entry.length > 0) {
                console.log(`   Found ${allPatients.total || allPatients.entry.length} patients:`);

                allPatients.entry.forEach((entry: any, index: number) => {
                    const patient = entry.resource;
                    const name = patient.name?.[0];
                    const displayName = name ?
                        `${name.given?.join(' ') || ''} ${name.family || ''}`.trim() :
                        'No name';

                    console.log(`     ${index + 1}. ${displayName} (${patient.gender || 'unknown'}) - ${patient.id}`);
                });
            } else {
                console.log('   No patients found');
            }

        } catch (patientsError: any) {
            console.log(`   ❌ Failed to list patients: ${patientsError.message}`);
        }

        // Expected vs Actual comparison
        console.log('\n🎯 Expected vs Actual:');
        console.log('   Expected Project: 0197a386-5f0b-7312-85e3-8e776f338f8a (Syncore)');
        console.log('   Expected Patients: Test1-Test5 Patient');
        console.log('   Actual Project: See above');
        console.log('   Actual Patients: See above');

        console.log('\n💡 Next Steps:');
        console.log('   1. If project IDs don\'t match, update your .env credentials');
        console.log('   2. Get correct CLIENT_ID/CLIENT_SECRET for Syncore project');
        console.log('   3. Or use current project and ignore web interface data');

    } catch (error: any) {
        console.error('❌ Debug failed:', error.message);
        console.error('\n🔧 Check:');
        console.error('   1. .env file exists and has correct values');
        console.error('   2. Medplum credentials are valid');
        console.error('   3. Network connection is working');
    }
}

// Run debug
if (import.meta.url === `file://${process.argv[1]}`) {
    debugProjectConnection().catch(console.error);
}

export { debugProjectConnection };