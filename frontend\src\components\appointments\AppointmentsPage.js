import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { FaCalendarPlus, FaCalendarAlt, FaUserCircle, FaMapMarkerAlt, FaClock, FaFilter, FaHeartbeat, FaList, FaCalendarDay } from 'react-icons/fa';
import AppointmentCalendar from './AppointmentCalendar';
import { 
  APPOINTMENTS, 
  PATIENTS,
  PRACTITIONERS,
  getAppointmentsByDate,
  getAppointments 
} from '../../data/mockData';

const AppointmentsPage = () => {
  const { userRole } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  
  const [appointments, setAppointments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [filterStatus, setFilterStatus] = useState('all');
  const [showNewAppointmentModal, setShowNewAppointmentModal] = useState(false);
  const [viewMode, setViewMode] = useState('list');
  const [newAppointment, setNewAppointment] = useState({
    patientId: '',
    patientName: '',
    practitionerId: '',
    practitionerName: '',
    appointmentType: 'EECP Session',
    date: new Date().toISOString().split('T')[0],
    startTime: '09:00',
    endTime: '10:00',
    location: 'Room 101',
    notes: ''
  });
  
  useEffect(() => {
    const fetchAppointments = async () => {
      try {
        setLoading(true);
        
        // Get all appointments first
        let allAppointments = getAppointments();
        
        // Filter by selected date
        let filteredAppointments = allAppointments.filter(appt => {
          const apptDate = new Date(appt.start).toISOString().split('T')[0];
          return apptDate === selectedDate;
        });
        
        // Apply status filter if not 'all'
        if (filterStatus !== 'all') {
          filteredAppointments = filteredAppointments.filter(appt => appt.status === filterStatus);
        }
        
        // Sort by time
        filteredAppointments.sort((a, b) => new Date(a.start) - new Date(b.start));
        
        setAppointments(filteredAppointments);
      } catch (error) {
        console.error('Error fetching appointments:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchAppointments();
  }, [selectedDate, filterStatus]);
  
  // Check URL for /new and show modal if present
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const patientId = searchParams.get('patientId');
    
    if (location.pathname.includes('/new')) {
      setShowNewAppointmentModal(true);
      if (patientId) {
        const patient = PATIENTS.find(p => p.id === patientId);
        if (patient) {
          setNewAppointment(prev => ({
            ...prev,
            patientId,
            patientName: patient.name
          }));
        }
      }
    }
  }, [location]);
  
  const handleNewAppointmentChange = (e) => {
    const { name, value } = e.target;
    setNewAppointment({
      ...newAppointment,
      [name]: value
    });
  };
  
  const handlePatientSelect = (e) => {
    const patientId = e.target.value;
    const patient = PATIENTS.find(p => p.id === patientId);
    
    setNewAppointment({
      ...newAppointment,
      patientId,
      patientName: patient ? patient.name : ''
    });
  };
  
  const handlePractitionerSelect = (e) => {
    const practitionerId = e.target.value;
    const practitioner = PRACTITIONERS.find(p => p.id === practitionerId);
    
    setNewAppointment({
      ...newAppointment,
      practitionerId,
      practitionerName: practitioner ? practitioner.name : ''
    });
  };
  
  const handleCreateAppointment = async () => {
    try {
      // Create appointment
      const appointmentData = {
        id: `a${Date.now()}`,
        patientId: newAppointment.patientId,
        patientName: newAppointment.patientName,
        practitionerId: newAppointment.practitionerId,
        practitionerName: newAppointment.practitionerName,
        appointmentType: newAppointment.appointmentType,
        start: `${newAppointment.date}T${newAppointment.startTime}`,
        end: `${newAppointment.date}T${newAppointment.endTime}`,
        location: newAppointment.location,
        status: 'scheduled',
        notes: newAppointment.notes
      };
      
      // Add to appointments if date matches selected date
      if (newAppointment.date === selectedDate) {
        setAppointments(prev => [...prev, appointmentData].sort((a, b) => new Date(a.start) - new Date(b.start)));
      }
      
      // Reset form and close modal
      setNewAppointment({
        patientId: '',
        patientName: '',
        practitionerId: '',
        practitionerName: '',
        appointmentType: 'EECP Session',
        date: new Date().toISOString().split('T')[0],
        startTime: '09:00',
        endTime: '10:00',
        location: 'Room 101',
        notes: ''
      });
      setShowNewAppointmentModal(false);
      
      // Navigate back to appointments list
      navigate('/appointments');
    } catch (error) {
      console.error('Error creating appointment:', error);
    }
  };
  
  const formatTime = (isoString) => {
    const date = new Date(isoString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'arrived':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }
  
  return (
    <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Appointments</h1>
        <button
          onClick={() => setShowNewAppointmentModal(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
        >
          <FaCalendarPlus className="mr-2 -ml-1" />
          New Appointment
        </button>
      </div>
      
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div className="flex items-center space-x-4">
          <div>
            <label htmlFor="date-select" className="block text-sm font-medium text-gray-700">Date</label>
            <input
              type="date"
              id="date-select"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
            />
          </div>
          
          <div>
            <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700">Status</label>
            <select
              id="status-filter"
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
            >
              <option value="all">All Statuses</option>
              <option value="scheduled">Scheduled</option>
              <option value="arrived">Arrived</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="text-sm text-gray-500 flex items-center mr-4">
            <FaFilter className="mr-2" />
            Showing {appointments.length} appointments
          </div>
          
          <div className="inline-flex rounded-md shadow-sm">
            <button
              type="button"
              onClick={() => setViewMode('list')}
              className={`relative inline-flex items-center px-4 py-2 rounded-l-md border border-gray-300 text-sm font-medium ${
                viewMode === 'list' 
                  ? 'bg-primary text-white' 
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              } focus:z-10 focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary`}
            >
              <FaList className="mr-2 -ml-1 h-4 w-4" />
              List
            </button>
            <button
              type="button"
              onClick={() => setViewMode('calendar')}
              className={`relative inline-flex items-center px-4 py-2 rounded-r-md border border-gray-300 text-sm font-medium ${
                viewMode === 'calendar' 
                  ? 'bg-primary text-white' 
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              } focus:z-10 focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary`}
            >
              <FaCalendarDay className="mr-2 -ml-1 h-4 w-4" />
              Calendar
            </button>
          </div>
        </div>
      </div>
      
      {viewMode === 'list' ? (
        appointments.length > 0 ? (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {appointments.map((appointment) => (
                <li key={appointment.id}>
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="flex items-center">
                          <div className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(appointment.status)}`}>
                            {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                          </div>
                          <div className="ml-2 text-sm font-medium text-primary">{appointment.appointmentType}</div>
                        </div>
                        <div className="mt-2 flex items-center text-sm text-gray-500">
                          <FaUserCircle className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" />
                          <div>
                            <span className="font-medium">{appointment.patientName}</span> with {appointment.practitionerName}
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col items-end">
                        <div className="flex items-center text-sm text-gray-500">
                          <FaClock className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" />
                          <div>
                            {formatTime(appointment.start)} - {formatTime(appointment.end)}
                          </div>
                        </div>
                        <div className="mt-2 flex items-center text-sm text-gray-500">
                          <FaMapMarkerAlt className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" />
                          <div>
                            {appointment.location}
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {appointment.notes && (
                      <div className="mt-2 text-sm text-gray-700">
                        <div className="font-medium">Notes:</div>
                        <div className="mt-1">{appointment.notes}</div>
                      </div>
                    )}
                    
                    <div className="mt-4 flex justify-end space-x-2">
                      {userRole === 'nurse' && appointment.status === 'scheduled' && (
                        <Link
                          to={`/sessions/${appointment.id}`}
                          className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                        >
                          <FaHeartbeat className="mr-1" />
                          Start Session
                        </Link>
                      )}
                      <Link
                        to={`/patients/${appointment.patientId}`}
                        className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                      >
                        <FaUserCircle className="mr-1" />
                        View Patient
                      </Link>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <div className="bg-white shadow overflow-hidden sm:rounded-md p-6 text-center text-gray-500">
            <FaCalendarAlt className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p>No appointments found for the selected date and filters.</p>
          </div>
        )
      ) : (
        <AppointmentCalendar appointments={appointments} userRole={userRole} />
      )}
      
      {/* New Appointment Modal */}
      {showNewAppointmentModal && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 flex items-center">
                      <FaCalendarPlus className="mr-2 text-primary" />
                      Schedule New Appointment
                    </h3>
                    <div className="mt-4 space-y-4">
                      <div>
                        <label htmlFor="patientId" className="block text-sm font-medium text-gray-700">Patient</label>
                        <select
                          id="patientId"
                          name="patientId"
                          value={newAppointment.patientId}
                          onChange={handlePatientSelect}
                          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                          required
                        >
                          <option value="">Select Patient</option>
                          {PATIENTS.map(patient => (
                            <option key={patient.id} value={patient.id}>{patient.name}</option>
                          ))}
                        </select>
                      </div>
                      
                      <div>
                        <label htmlFor="practitionerId" className="block text-sm font-medium text-gray-700">Provider</label>
                        <select
                          id="practitionerId"
                          name="practitionerId"
                          value={newAppointment.practitionerId}
                          onChange={handlePractitionerSelect}
                          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                          required
                        >
                          <option value="">Select Provider</option>
                          {PRACTITIONERS.map(practitioner => (
                            <option key={practitioner.id} value={practitioner.id}>{practitioner.name}</option>
                          ))}
                        </select>
                      </div>
                      
                      <div>
                        <label htmlFor="appointmentType" className="block text-sm font-medium text-gray-700">Appointment Type</label>
                        <select
                          id="appointmentType"
                          name="appointmentType"
                          value={newAppointment.appointmentType}
                          onChange={handleNewAppointmentChange}
                          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                          required
                        >
                          <option value="EECP Session">EECP Session</option>
                          <option value="Initial Evaluation">Initial Evaluation</option>
                          <option value="Follow-up Evaluation">Follow-up Evaluation</option>
                          <option value="Consultation">Consultation</option>
                        </select>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label htmlFor="date" className="block text-sm font-medium text-gray-700">Date</label>
                          <input
                            type="date"
                            id="date"
                            name="date"
                            value={newAppointment.date}
                            onChange={handleNewAppointmentChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                            required
                          />
                        </div>
                        
                        <div>
                          <label htmlFor="location" className="block text-sm font-medium text-gray-700">Location</label>
                          <select
                            id="location"
                            name="location"
                            value={newAppointment.location}
                            onChange={handleNewAppointmentChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                            required
                          >
                            <option value="Room 101">Room 101</option>
                            <option value="Room 102">Room 102</option>
                            <option value="Room 103">Room 103</option>
                            <option value="Room 104">Room 104</option>
                            <option value="Room 105">Room 105</option>
                          </select>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label htmlFor="startTime" className="block text-sm font-medium text-gray-700">Start Time</label>
                          <input
                            type="time"
                            id="startTime"
                            name="startTime"
                            value={newAppointment.startTime}
                            onChange={handleNewAppointmentChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                            required
                          />
                        </div>
                        
                        <div>
                          <label htmlFor="endTime" className="block text-sm font-medium text-gray-700">End Time</label>
                          <input
                            type="time"
                            id="endTime"
                            name="endTime"
                            value={newAppointment.endTime}
                            onChange={handleNewAppointmentChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                            required
                          />
                        </div>
                      </div>
                      
                      <div>
                        <label htmlFor="notes" className="block text-sm font-medium text-gray-700">Notes</label>
                        <textarea
                          id="notes"
                          name="notes"
                          rows="3"
                          value={newAppointment.notes}
                          onChange={handleNewAppointmentChange}
                          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                          placeholder="Optional notes about this appointment"
                        ></textarea>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={handleCreateAppointment}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Schedule Appointment
                </button>
                <button
                  type="button"
                  onClick={() => setShowNewAppointmentModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AppointmentsPage;
