/**
 * @file patientSchemas.ts
 * @description This file contains the JSON schemas for patient-related data.
 *
 */
/**
 * Interface for creating a new patient
 * Matches the simple structure you specified in your requirements
 */
export interface PatientCreate {
    family: string;
    given: string[];
    gender?: 'male' | 'female' | 'other' | 'unknown';
    birthDate?: string;
    intakeReason?: string;
}
/**
 * Interface for patient data returned from API
 * Includes FHIR metadata that Medplum adds
 */
export interface PatientRead extends PatientCreate {
    id: string;
    resourceType: 'Patient';
    meta?: {
        versionId?: string;
        lastUpdated?: string;
        source?: string;
        profile?: string[];
        tag?: Array<{
            system?: string;
            code?: string;
            display?: string;
        }>;
    };
    identifier?: Array<{
        use?: string;
        type?: {
            coding?: Array<{
                system?: string;
                code?: string;
                display?: string;
            }>;
        };
        value?: string;
    }>;
    active?: boolean;
    name?: Array<{
        use?: string;
        family?: string;
        given?: string[];
        prefix?: string[];
        suffix?: string[];
    }>;
    telecom?: Array<{
        system?: 'phone' | 'email' | 'fax' | 'pager' | 'url' | 'sms' | 'other';
        value?: string;
        use?: 'home' | 'work' | 'temp' | 'old' | 'mobile';
    }>;
    address?: Array<{
        use?: 'home' | 'work' | 'temp' | 'old' | 'billing';
        type?: 'postal' | 'physical' | 'both';
        line?: string[];
        city?: string;
        district?: string;
        state?: string;
        postalCode?: string;
        country?: string;
    }>;
}
/**
 * Interface for updating an existing patient
 * All fields are optional since this is for PATCH operations
 */
export interface PatientUpdate {
    gender?: 'male' | 'female' | 'other' | 'unknown';
    birthDate?: string;
    intakeReason?: string;
}
/**
 * Interface for patient search/filter parameters
 */
export interface PatientSearchParams {
    name?: string;
    family?: string;
    given?: string;
    gender?: 'male' | 'female' | 'other' | 'unknown';
    birthdate?: string;
    active?: boolean;
    _count?: number;
    _offset?: number;
}
/**
 * Interface for paginated patient list response
 */
export interface PatientListResponse {
    success: boolean;
    data: PatientRead[];
    pagination: {
        total: number;
        limit: number;
        offset: number;
        hasMore: boolean;
    };
}
/**
 * Interface for single patient response
 */
export interface PatientResponse {
    success: boolean;
    message?: string;
    data: PatientRead;
}
/**
 * Validation helper: Check if required fields are present
 */
export declare function validatePatientCreate(patient: Partial<PatientCreate>): string[];
//# sourceMappingURL=patientSchemas.d.ts.map