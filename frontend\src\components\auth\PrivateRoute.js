import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

/**
 * PrivateRoute component that provides authentication and role-based access control
 * Protects routes by ensuring users are logged in and have the required role
 * 
 * Usage:
 * - Wrap protected routes with this component
 * - Optionally specify a required role for additional access control
 * - Users without proper authentication/authorization are redirected appropriately
 * 
 * @param {Object} props - Component props
 * @param {string} [props.requiredRole] - Optional role required to access the route (doctor, nurse, admin)
 * @returns {JSX.Element} Either the protected route content or a redirect component
 */
const PrivateRoute = ({ requiredRole }) => {
  const { currentUser, userRole } = useAuth();

  /**
   * Check if user is authenticated
   * If not logged in, redirect to login page
   */
  if (!currentUser) {
    return <Navigate to="/login" replace />;
  }

  /**
   * Check role-based access control
   * If a specific role is required and user doesn't have it,
   * redirect to their appropriate dashboard based on their actual role
   */
  if (requiredRole && userRole !== requiredRole) {
    // Determine redirect destination based on user's actual role
    const redirectPath = getRoleBasedRedirectPath(userRole);
    return <Navigate to={redirectPath} replace />;
  }

  /**
   * If user is authenticated and has the required role (or no specific role is required),
   * render the protected route content using Outlet
   */
  return <Outlet />;
};

/**
 * Helper function to determine redirect path based on user role
 * Provides consistent navigation logic for unauthorized access attempts
 * 
 * @param {string} userRole - The user's current role
 * @returns {string} The appropriate dashboard path for the user's role
 */
const getRoleBasedRedirectPath = (userRole) => {
  switch (userRole) {
    case 'doctor':
      return '/doctor-dashboard';
    case 'nurse':
      return '/nurse-dashboard';
    case 'admin':
      return '/admin-dashboard';
    default:
      // Fallback for users with undefined or unrecognized roles
      return '/';
  }
};

export default PrivateRoute;