"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const medplum_client_1 = require("./medplum_client");
async function main() {
    // Instantiate the MedplumService class
    const medplum = new medplum_client_1.MedplumService();
    // Create a new Patient resource
    const patient = await medplum.create('Patient', {
        resourceType: 'Patient',
        name: [{ given: ['<PERSON>'], family: '<PERSON>' }],
        gender: 'female',
    });
    console.log('Created Patient:', patient);
    // Read the created Patient resource
    const readBack = await medplum.read('Patient', patient.id);
    console.log('Read Back:', readBack);
    // Update the Patient resource
    const updatedPatient = await medplum.update('Patient', patient.id, {
        ...patient,
        name: [{ given: ['<PERSON>'], family: '<PERSON><PERSON>' }],
    });
    console.log('Updated Patient:', updatedPatient);
    // Delete the Patient resource
    const deletePatient = await medplum.delete('Patient', patient.id);
    console.log('Deleted Patient:', deletePatient);
}
main().catch(console.error);
//# sourceMappingURL=test.js.map