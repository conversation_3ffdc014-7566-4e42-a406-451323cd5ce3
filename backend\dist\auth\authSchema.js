"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoginSchema = void 0;
const zod_1 = require("zod");
/**
 * @file Defines Zod schemas and TypeScript types for authentication-related data.
 */
// Zod schema for login request validation.
exports.LoginSchema = zod_1.z.object({
    email: zod_1.z.string().email({ message: "Invalid email address" }),
    password: zod_1.z.string().min(6, { message: "Password must be at least 6 characters long" }),
});
//# sourceMappingURL=authSchema.js.map