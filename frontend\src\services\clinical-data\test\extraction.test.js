// src/services/clinical-data/test/extraction.test.js
const { ClinicalResponseParser } = require('../extraction/responseParser');

describe('Clinical Data Extraction', () => {
  let parser;

  beforeEach(() => {
    parser = new ClinicalResponseParser();
  });

  test('extracts basic clinical data', async () => {
    const mockResponse = {
      choices: [{
        message: {
          content: JSON.stringify({
            chiefComplaint: "chest pain",
            currentSymptoms: [{
              symptom: "chest pain",
              severity: "6"
            }]
          })
        }
      }]
    };

    const result = await parser.parseResponse(mockResponse, 'symptoms');
    expect(result.data).toHaveProperty('chiefComplaint');
    expect(result.data).toHaveProperty('currentSymptoms');
    expect(result.data.currentSymptoms[0].symptom).toBe('chest pain');
  });

  test('handles invalid data', async () => {
    const invalidResponse = {
      choices: [{
        message: {
          content: 'invalid json'
        }
      }]
    };

    await expect(parser.parseResponse(invalidResponse, 'symptoms'))
      .rejects
      .toThrow('Failed to parse clinical data');
  });
});

