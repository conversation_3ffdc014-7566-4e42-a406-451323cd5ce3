{"version": 3, "sources": ["../../node_modules/semver/internal/constants.js", "../../node_modules/semver/internal/debug.js", "../../node_modules/semver/internal/re.js", "../../node_modules/semver/internal/parse-options.js", "../../node_modules/semver/internal/identifiers.js", "../../node_modules/semver/classes/semver.js", "../../node_modules/semver/functions/parse.js", "../../node_modules/semver/functions/valid.js", "../../node_modules/semver/functions/clean.js", "../../node_modules/semver/functions/inc.js", "../../node_modules/semver/functions/diff.js", "../../node_modules/semver/functions/major.js", "../../node_modules/semver/functions/minor.js", "../../node_modules/semver/functions/patch.js", "../../node_modules/semver/functions/prerelease.js", "../../node_modules/semver/functions/compare.js", "../../node_modules/semver/functions/rcompare.js", "../../node_modules/semver/functions/compare-loose.js", "../../node_modules/semver/functions/compare-build.js", "../../node_modules/semver/functions/sort.js", "../../node_modules/semver/functions/rsort.js", "../../node_modules/semver/functions/gt.js", "../../node_modules/semver/functions/lt.js", "../../node_modules/semver/functions/eq.js", "../../node_modules/semver/functions/neq.js", "../../node_modules/semver/functions/gte.js", "../../node_modules/semver/functions/lte.js", "../../node_modules/semver/functions/cmp.js", "../../node_modules/semver/functions/coerce.js", "../../node_modules/semver/internal/lrucache.js", "../../node_modules/semver/classes/range.js", "../../node_modules/semver/classes/comparator.js", "../../node_modules/semver/functions/satisfies.js", "../../node_modules/semver/ranges/to-comparators.js", "../../node_modules/semver/ranges/max-satisfying.js", "../../node_modules/semver/ranges/min-satisfying.js", "../../node_modules/semver/ranges/min-version.js", "../../node_modules/semver/ranges/valid.js", "../../node_modules/semver/ranges/outside.js", "../../node_modules/semver/ranges/gtr.js", "../../node_modules/semver/ranges/ltr.js", "../../node_modules/semver/ranges/intersects.js", "../../node_modules/semver/ranges/simplify.js", "../../node_modules/semver/ranges/subset.js", "../../node_modules/semver/index.js", "../../src/index.ts", "../../src/agent.ts", "../../src/util/client.ts", "../../src/storage.ts", "../../src/utils.ts", "../../../../node_modules/jose/dist/node/esm/runtime/base64url.js", "../../../../node_modules/jose/dist/node/esm/lib/buffer_utils.js", "../../../../node_modules/jose/dist/node/esm/util/errors.js", "../../../../node_modules/jose/dist/node/esm/runtime/is_key_object.js", "../../../../node_modules/jose/dist/node/esm/runtime/webcrypto.js", "../../../../node_modules/jose/dist/node/esm/lib/crypto_key.js", "../../../../node_modules/jose/dist/node/esm/lib/invalid_key_input.js", "../../../../node_modules/jose/dist/node/esm/runtime/is_key_like.js", "../../../../node_modules/jose/dist/node/esm/lib/is_disjoint.js", "../../../../node_modules/jose/dist/node/esm/lib/is_object.js", "../../../../node_modules/jose/dist/node/esm/runtime/get_named_curve.js", "../../../../node_modules/jose/dist/node/esm/lib/is_jwk.js", "../../../../node_modules/jose/dist/node/esm/runtime/check_key_length.js", "../../../../node_modules/jose/dist/node/esm/lib/check_key_type.js", "../../../../node_modules/jose/dist/node/esm/lib/validate_crit.js", "../../../../node_modules/jose/dist/node/esm/runtime/dsa_digest.js", "../../../../node_modules/jose/dist/node/esm/runtime/node_key.js", "../../../../node_modules/jose/dist/node/esm/runtime/sign.js", "../../../../node_modules/jose/dist/node/esm/runtime/hmac_digest.js", "../../../../node_modules/jose/dist/node/esm/runtime/get_sign_verify_key.js", "../../../../node_modules/jose/dist/node/esm/lib/epoch.js", "../../../../node_modules/jose/dist/node/esm/lib/secs.js", "../../../../node_modules/jose/dist/node/esm/jws/flattened/sign.js", "../../../../node_modules/jose/dist/node/esm/jws/compact/sign.js", "../../../../node_modules/jose/dist/node/esm/jwt/produce.js", "../../../../node_modules/jose/dist/node/esm/jwt/sign.js", "../../src/auth.ts", "../../src/util/color.ts", "../../src/aws/utils.ts", "../../src/aws/terminal.ts", "../../src/aws/describe.ts", "../../src/aws/init.ts", "../../src/aws/list.ts", "../../src/aws/update-app.ts", "../../src/aws/update-bucket-policies.ts", "../../src/aws/update-config.ts", "../../src/aws/update-server.ts", "../../src/aws/index.ts", "../../src/bots.ts", "../../src/bulk.ts", "../../src/hl7.ts", "../../../hl7/src/base.ts", "../../../hl7/src/client.ts", "../../../hl7/src/connection.ts", "../../../hl7/src/constants.ts", "../../../hl7/src/events.ts", "../../../hl7/src/server.ts", "../../src/profiles.ts", "../../src/project.ts", "../../src/rest.ts"], "sourcesContent": ["'use strict'\n\n// Note: this is the semver.org version of the spec that it implements\n// Not necessarily the package version of this code.\nconst SEMVER_SPEC_VERSION = '2.0.0'\n\nconst MAX_LENGTH = 256\nconst MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER ||\n/* istanbul ignore next */ 9007199254740991\n\n// Max safe segment length for coercion.\nconst MAX_SAFE_COMPONENT_LENGTH = 16\n\n// Max safe length for a build identifier. The max length minus 6 characters for\n// the shortest version with a build 0.0.0+BUILD.\nconst MAX_SAFE_BUILD_LENGTH = MAX_LENGTH - 6\n\nconst RELEASE_TYPES = [\n  'major',\n  'premajor',\n  'minor',\n  'preminor',\n  'patch',\n  'prepatch',\n  'prerelease',\n]\n\nmodule.exports = {\n  MAX_LENGTH,\n  MAX_SAFE_COMPONENT_LENGTH,\n  MAX_SAFE_BUILD_LENGTH,\n  MAX_SAFE_INTEGER,\n  RELEASE_TYPES,\n  SEMVER_SPEC_VERSION,\n  FLAG_INCLUDE_PRERELEASE: 0b001,\n  FLAG_LOOSE: 0b010,\n}\n", "'use strict'\n\nconst debug = (\n  typeof process === 'object' &&\n  process.env &&\n  process.env.NODE_DEBUG &&\n  /\\bsemver\\b/i.test(process.env.NODE_DEBUG)\n) ? (...args) => console.error('SEMVER', ...args)\n  : () => {}\n\nmodule.exports = debug\n", "'use strict'\n\nconst {\n  MAX_SAFE_COMPONENT_LENGTH,\n  MAX_SAFE_BUILD_LENGTH,\n  MAX_LENGTH,\n} = require('./constants')\nconst debug = require('./debug')\nexports = module.exports = {}\n\n// The actual regexps go on exports.re\nconst re = exports.re = []\nconst safeRe = exports.safeRe = []\nconst src = exports.src = []\nconst safeSrc = exports.safeSrc = []\nconst t = exports.t = {}\nlet R = 0\n\nconst LETTERDASHNUMBER = '[a-zA-Z0-9-]'\n\n// Replace some greedy regex tokens to prevent regex dos issues. These regex are\n// used internally via the safeRe object since all inputs in this library get\n// normalized first to trim and collapse all extra whitespace. The original\n// regexes are exported for userland consumption and lower level usage. A\n// future breaking change could export the safer regex only with a note that\n// all input should have extra whitespace removed.\nconst safeRegexReplacements = [\n  ['\\\\s', 1],\n  ['\\\\d', MAX_LENGTH],\n  [LETTERDASHNUMBER, MAX_SAFE_BUILD_LENGTH],\n]\n\nconst makeSafeRegex = (value) => {\n  for (const [token, max] of safeRegexReplacements) {\n    value = value\n      .split(`${token}*`).join(`${token}{0,${max}}`)\n      .split(`${token}+`).join(`${token}{1,${max}}`)\n  }\n  return value\n}\n\nconst createToken = (name, value, isGlobal) => {\n  const safe = makeSafeRegex(value)\n  const index = R++\n  debug(name, index, value)\n  t[name] = index\n  src[index] = value\n  safeSrc[index] = safe\n  re[index] = new RegExp(value, isGlobal ? 'g' : undefined)\n  safeRe[index] = new RegExp(safe, isGlobal ? 'g' : undefined)\n}\n\n// The following Regular Expressions can be used for tokenizing,\n// validating, and parsing SemVer version strings.\n\n// ## Numeric Identifier\n// A single `0`, or a non-zero digit followed by zero or more digits.\n\ncreateToken('NUMERICIDENTIFIER', '0|[1-9]\\\\d*')\ncreateToken('NUMERICIDENTIFIERLOOSE', '\\\\d+')\n\n// ## Non-numeric Identifier\n// Zero or more digits, followed by a letter or hyphen, and then zero or\n// more letters, digits, or hyphens.\n\ncreateToken('NONNUMERICIDENTIFIER', `\\\\d*[a-zA-Z-]${LETTERDASHNUMBER}*`)\n\n// ## Main Version\n// Three dot-separated numeric identifiers.\n\ncreateToken('MAINVERSION', `(${src[t.NUMERICIDENTIFIER]})\\\\.` +\n                   `(${src[t.NUMERICIDENTIFIER]})\\\\.` +\n                   `(${src[t.NUMERICIDENTIFIER]})`)\n\ncreateToken('MAINVERSIONLOOSE', `(${src[t.NUMERICIDENTIFIERLOOSE]})\\\\.` +\n                        `(${src[t.NUMERICIDENTIFIERLOOSE]})\\\\.` +\n                        `(${src[t.NUMERICIDENTIFIERLOOSE]})`)\n\n// ## Pre-release Version Identifier\n// A numeric identifier, or a non-numeric identifier.\n// Non-numberic identifiers include numberic identifiers but can be longer.\n// Therefore non-numberic identifiers must go first.\n\ncreateToken('PRERELEASEIDENTIFIER', `(?:${src[t.NONNUMERICIDENTIFIER]\n}|${src[t.NUMERICIDENTIFIER]})`)\n\ncreateToken('PRERELEASEIDENTIFIERLOOSE', `(?:${src[t.NONNUMERICIDENTIFIER]\n}|${src[t.NUMERICIDENTIFIERLOOSE]})`)\n\n// ## Pre-release Version\n// Hyphen, followed by one or more dot-separated pre-release version\n// identifiers.\n\ncreateToken('PRERELEASE', `(?:-(${src[t.PRERELEASEIDENTIFIER]\n}(?:\\\\.${src[t.PRERELEASEIDENTIFIER]})*))`)\n\ncreateToken('PRERELEASELOOSE', `(?:-?(${src[t.PRERELEASEIDENTIFIERLOOSE]\n}(?:\\\\.${src[t.PRERELEASEIDENTIFIERLOOSE]})*))`)\n\n// ## Build Metadata Identifier\n// Any combination of digits, letters, or hyphens.\n\ncreateToken('BUILDIDENTIFIER', `${LETTERDASHNUMBER}+`)\n\n// ## Build Metadata\n// Plus sign, followed by one or more period-separated build metadata\n// identifiers.\n\ncreateToken('BUILD', `(?:\\\\+(${src[t.BUILDIDENTIFIER]\n}(?:\\\\.${src[t.BUILDIDENTIFIER]})*))`)\n\n// ## Full Version String\n// A main version, followed optionally by a pre-release version and\n// build metadata.\n\n// Note that the only major, minor, patch, and pre-release sections of\n// the version string are capturing groups.  The build metadata is not a\n// capturing group, because it should not ever be used in version\n// comparison.\n\ncreateToken('FULLPLAIN', `v?${src[t.MAINVERSION]\n}${src[t.PRERELEASE]}?${\n  src[t.BUILD]}?`)\n\ncreateToken('FULL', `^${src[t.FULLPLAIN]}$`)\n\n// like full, but allows v1.2.3 and =1.2.3, which people do sometimes.\n// also, 1.0.0alpha1 (prerelease without the hyphen) which is pretty\n// common in the npm registry.\ncreateToken('LOOSEPLAIN', `[v=\\\\s]*${src[t.MAINVERSIONLOOSE]\n}${src[t.PRERELEASELOOSE]}?${\n  src[t.BUILD]}?`)\n\ncreateToken('LOOSE', `^${src[t.LOOSEPLAIN]}$`)\n\ncreateToken('GTLT', '((?:<|>)?=?)')\n\n// Something like \"2.*\" or \"1.2.x\".\n// Note that \"x.x\" is a valid xRange identifer, meaning \"any version\"\n// Only the first item is strictly required.\ncreateToken('XRANGEIDENTIFIERLOOSE', `${src[t.NUMERICIDENTIFIERLOOSE]}|x|X|\\\\*`)\ncreateToken('XRANGEIDENTIFIER', `${src[t.NUMERICIDENTIFIER]}|x|X|\\\\*`)\n\ncreateToken('XRANGEPLAIN', `[v=\\\\s]*(${src[t.XRANGEIDENTIFIER]})` +\n                   `(?:\\\\.(${src[t.XRANGEIDENTIFIER]})` +\n                   `(?:\\\\.(${src[t.XRANGEIDENTIFIER]})` +\n                   `(?:${src[t.PRERELEASE]})?${\n                     src[t.BUILD]}?` +\n                   `)?)?`)\n\ncreateToken('XRANGEPLAINLOOSE', `[v=\\\\s]*(${src[t.XRANGEIDENTIFIERLOOSE]})` +\n                        `(?:\\\\.(${src[t.XRANGEIDENTIFIERLOOSE]})` +\n                        `(?:\\\\.(${src[t.XRANGEIDENTIFIERLOOSE]})` +\n                        `(?:${src[t.PRERELEASELOOSE]})?${\n                          src[t.BUILD]}?` +\n                        `)?)?`)\n\ncreateToken('XRANGE', `^${src[t.GTLT]}\\\\s*${src[t.XRANGEPLAIN]}$`)\ncreateToken('XRANGELOOSE', `^${src[t.GTLT]}\\\\s*${src[t.XRANGEPLAINLOOSE]}$`)\n\n// Coercion.\n// Extract anything that could conceivably be a part of a valid semver\ncreateToken('COERCEPLAIN', `${'(^|[^\\\\d])' +\n              '(\\\\d{1,'}${MAX_SAFE_COMPONENT_LENGTH}})` +\n              `(?:\\\\.(\\\\d{1,${MAX_SAFE_COMPONENT_LENGTH}}))?` +\n              `(?:\\\\.(\\\\d{1,${MAX_SAFE_COMPONENT_LENGTH}}))?`)\ncreateToken('COERCE', `${src[t.COERCEPLAIN]}(?:$|[^\\\\d])`)\ncreateToken('COERCEFULL', src[t.COERCEPLAIN] +\n              `(?:${src[t.PRERELEASE]})?` +\n              `(?:${src[t.BUILD]})?` +\n              `(?:$|[^\\\\d])`)\ncreateToken('COERCERTL', src[t.COERCE], true)\ncreateToken('COERCERTLFULL', src[t.COERCEFULL], true)\n\n// Tilde ranges.\n// Meaning is \"reasonably at or greater than\"\ncreateToken('LONETILDE', '(?:~>?)')\n\ncreateToken('TILDETRIM', `(\\\\s*)${src[t.LONETILDE]}\\\\s+`, true)\nexports.tildeTrimReplace = '$1~'\n\ncreateToken('TILDE', `^${src[t.LONETILDE]}${src[t.XRANGEPLAIN]}$`)\ncreateToken('TILDELOOSE', `^${src[t.LONETILDE]}${src[t.XRANGEPLAINLOOSE]}$`)\n\n// Caret ranges.\n// Meaning is \"at least and backwards compatible with\"\ncreateToken('LONECARET', '(?:\\\\^)')\n\ncreateToken('CARETTRIM', `(\\\\s*)${src[t.LONECARET]}\\\\s+`, true)\nexports.caretTrimReplace = '$1^'\n\ncreateToken('CARET', `^${src[t.LONECARET]}${src[t.XRANGEPLAIN]}$`)\ncreateToken('CARETLOOSE', `^${src[t.LONECARET]}${src[t.XRANGEPLAINLOOSE]}$`)\n\n// A simple gt/lt/eq thing, or just \"\" to indicate \"any version\"\ncreateToken('COMPARATORLOOSE', `^${src[t.GTLT]}\\\\s*(${src[t.LOOSEPLAIN]})$|^$`)\ncreateToken('COMPARATOR', `^${src[t.GTLT]}\\\\s*(${src[t.FULLPLAIN]})$|^$`)\n\n// An expression to strip any whitespace between the gtlt and the thing\n// it modifies, so that `> 1.2.3` ==> `>1.2.3`\ncreateToken('COMPARATORTRIM', `(\\\\s*)${src[t.GTLT]\n}\\\\s*(${src[t.LOOSEPLAIN]}|${src[t.XRANGEPLAIN]})`, true)\nexports.comparatorTrimReplace = '$1$2$3'\n\n// Something like `1.2.3 - 1.2.4`\n// Note that these all use the loose form, because they'll be\n// checked against either the strict or loose comparator form\n// later.\ncreateToken('HYPHENRANGE', `^\\\\s*(${src[t.XRANGEPLAIN]})` +\n                   `\\\\s+-\\\\s+` +\n                   `(${src[t.XRANGEPLAIN]})` +\n                   `\\\\s*$`)\n\ncreateToken('HYPHENRANGELOOSE', `^\\\\s*(${src[t.XRANGEPLAINLOOSE]})` +\n                        `\\\\s+-\\\\s+` +\n                        `(${src[t.XRANGEPLAINLOOSE]})` +\n                        `\\\\s*$`)\n\n// Star ranges basically just allow anything at all.\ncreateToken('STAR', '(<|>)?=?\\\\s*\\\\*')\n// >=0.0.0 is like a star\ncreateToken('GTE0', '^\\\\s*>=\\\\s*0\\\\.0\\\\.0\\\\s*$')\ncreateToken('GTE0PRE', '^\\\\s*>=\\\\s*0\\\\.0\\\\.0-0\\\\s*$')\n", "'use strict'\n\n// parse out just the options we care about\nconst looseOption = Object.freeze({ loose: true })\nconst emptyOpts = Object.freeze({ })\nconst parseOptions = options => {\n  if (!options) {\n    return emptyOpts\n  }\n\n  if (typeof options !== 'object') {\n    return looseOption\n  }\n\n  return options\n}\nmodule.exports = parseOptions\n", "'use strict'\n\nconst numeric = /^[0-9]+$/\nconst compareIdentifiers = (a, b) => {\n  const anum = numeric.test(a)\n  const bnum = numeric.test(b)\n\n  if (anum && bnum) {\n    a = +a\n    b = +b\n  }\n\n  return a === b ? 0\n    : (anum && !bnum) ? -1\n    : (bnum && !anum) ? 1\n    : a < b ? -1\n    : 1\n}\n\nconst rcompareIdentifiers = (a, b) => compareIdentifiers(b, a)\n\nmodule.exports = {\n  compareIdentifiers,\n  rcompareIdentifiers,\n}\n", "'use strict'\n\nconst debug = require('../internal/debug')\nconst { MAX_LENGTH, MAX_SAFE_INTEGER } = require('../internal/constants')\nconst { safeRe: re, t } = require('../internal/re')\n\nconst parseOptions = require('../internal/parse-options')\nconst { compareIdentifiers } = require('../internal/identifiers')\nclass SemVer {\n  constructor (version, options) {\n    options = parseOptions(options)\n\n    if (version instanceof SemVer) {\n      if (version.loose === !!options.loose &&\n        version.includePrerelease === !!options.includePrerelease) {\n        return version\n      } else {\n        version = version.version\n      }\n    } else if (typeof version !== 'string') {\n      throw new TypeError(`Invalid version. Must be a string. Got type \"${typeof version}\".`)\n    }\n\n    if (version.length > MAX_LENGTH) {\n      throw new TypeError(\n        `version is longer than ${MAX_LENGTH} characters`\n      )\n    }\n\n    debug('SemVer', version, options)\n    this.options = options\n    this.loose = !!options.loose\n    // this isn't actually relevant for versions, but keep it so that we\n    // don't run into trouble passing this.options around.\n    this.includePrerelease = !!options.includePrerelease\n\n    const m = version.trim().match(options.loose ? re[t.LOOSE] : re[t.FULL])\n\n    if (!m) {\n      throw new TypeError(`Invalid Version: ${version}`)\n    }\n\n    this.raw = version\n\n    // these are actually numbers\n    this.major = +m[1]\n    this.minor = +m[2]\n    this.patch = +m[3]\n\n    if (this.major > MAX_SAFE_INTEGER || this.major < 0) {\n      throw new TypeError('Invalid major version')\n    }\n\n    if (this.minor > MAX_SAFE_INTEGER || this.minor < 0) {\n      throw new TypeError('Invalid minor version')\n    }\n\n    if (this.patch > MAX_SAFE_INTEGER || this.patch < 0) {\n      throw new TypeError('Invalid patch version')\n    }\n\n    // numberify any prerelease numeric ids\n    if (!m[4]) {\n      this.prerelease = []\n    } else {\n      this.prerelease = m[4].split('.').map((id) => {\n        if (/^[0-9]+$/.test(id)) {\n          const num = +id\n          if (num >= 0 && num < MAX_SAFE_INTEGER) {\n            return num\n          }\n        }\n        return id\n      })\n    }\n\n    this.build = m[5] ? m[5].split('.') : []\n    this.format()\n  }\n\n  format () {\n    this.version = `${this.major}.${this.minor}.${this.patch}`\n    if (this.prerelease.length) {\n      this.version += `-${this.prerelease.join('.')}`\n    }\n    return this.version\n  }\n\n  toString () {\n    return this.version\n  }\n\n  compare (other) {\n    debug('SemVer.compare', this.version, this.options, other)\n    if (!(other instanceof SemVer)) {\n      if (typeof other === 'string' && other === this.version) {\n        return 0\n      }\n      other = new SemVer(other, this.options)\n    }\n\n    if (other.version === this.version) {\n      return 0\n    }\n\n    return this.compareMain(other) || this.comparePre(other)\n  }\n\n  compareMain (other) {\n    if (!(other instanceof SemVer)) {\n      other = new SemVer(other, this.options)\n    }\n\n    return (\n      compareIdentifiers(this.major, other.major) ||\n      compareIdentifiers(this.minor, other.minor) ||\n      compareIdentifiers(this.patch, other.patch)\n    )\n  }\n\n  comparePre (other) {\n    if (!(other instanceof SemVer)) {\n      other = new SemVer(other, this.options)\n    }\n\n    // NOT having a prerelease is > having one\n    if (this.prerelease.length && !other.prerelease.length) {\n      return -1\n    } else if (!this.prerelease.length && other.prerelease.length) {\n      return 1\n    } else if (!this.prerelease.length && !other.prerelease.length) {\n      return 0\n    }\n\n    let i = 0\n    do {\n      const a = this.prerelease[i]\n      const b = other.prerelease[i]\n      debug('prerelease compare', i, a, b)\n      if (a === undefined && b === undefined) {\n        return 0\n      } else if (b === undefined) {\n        return 1\n      } else if (a === undefined) {\n        return -1\n      } else if (a === b) {\n        continue\n      } else {\n        return compareIdentifiers(a, b)\n      }\n    } while (++i)\n  }\n\n  compareBuild (other) {\n    if (!(other instanceof SemVer)) {\n      other = new SemVer(other, this.options)\n    }\n\n    let i = 0\n    do {\n      const a = this.build[i]\n      const b = other.build[i]\n      debug('build compare', i, a, b)\n      if (a === undefined && b === undefined) {\n        return 0\n      } else if (b === undefined) {\n        return 1\n      } else if (a === undefined) {\n        return -1\n      } else if (a === b) {\n        continue\n      } else {\n        return compareIdentifiers(a, b)\n      }\n    } while (++i)\n  }\n\n  // preminor will bump the version up to the next minor release, and immediately\n  // down to pre-release. premajor and prepatch work the same way.\n  inc (release, identifier, identifierBase) {\n    if (release.startsWith('pre')) {\n      if (!identifier && identifierBase === false) {\n        throw new Error('invalid increment argument: identifier is empty')\n      }\n      // Avoid an invalid semver results\n      if (identifier) {\n        const match = `-${identifier}`.match(this.options.loose ? re[t.PRERELEASELOOSE] : re[t.PRERELEASE])\n        if (!match || match[1] !== identifier) {\n          throw new Error(`invalid identifier: ${identifier}`)\n        }\n      }\n    }\n\n    switch (release) {\n      case 'premajor':\n        this.prerelease.length = 0\n        this.patch = 0\n        this.minor = 0\n        this.major++\n        this.inc('pre', identifier, identifierBase)\n        break\n      case 'preminor':\n        this.prerelease.length = 0\n        this.patch = 0\n        this.minor++\n        this.inc('pre', identifier, identifierBase)\n        break\n      case 'prepatch':\n        // If this is already a prerelease, it will bump to the next version\n        // drop any prereleases that might already exist, since they are not\n        // relevant at this point.\n        this.prerelease.length = 0\n        this.inc('patch', identifier, identifierBase)\n        this.inc('pre', identifier, identifierBase)\n        break\n      // If the input is a non-prerelease version, this acts the same as\n      // prepatch.\n      case 'prerelease':\n        if (this.prerelease.length === 0) {\n          this.inc('patch', identifier, identifierBase)\n        }\n        this.inc('pre', identifier, identifierBase)\n        break\n      case 'release':\n        if (this.prerelease.length === 0) {\n          throw new Error(`version ${this.raw} is not a prerelease`)\n        }\n        this.prerelease.length = 0\n        break\n\n      case 'major':\n        // If this is a pre-major version, bump up to the same major version.\n        // Otherwise increment major.\n        // 1.0.0-5 bumps to 1.0.0\n        // 1.1.0 bumps to 2.0.0\n        if (\n          this.minor !== 0 ||\n          this.patch !== 0 ||\n          this.prerelease.length === 0\n        ) {\n          this.major++\n        }\n        this.minor = 0\n        this.patch = 0\n        this.prerelease = []\n        break\n      case 'minor':\n        // If this is a pre-minor version, bump up to the same minor version.\n        // Otherwise increment minor.\n        // 1.2.0-5 bumps to 1.2.0\n        // 1.2.1 bumps to 1.3.0\n        if (this.patch !== 0 || this.prerelease.length === 0) {\n          this.minor++\n        }\n        this.patch = 0\n        this.prerelease = []\n        break\n      case 'patch':\n        // If this is not a pre-release version, it will increment the patch.\n        // If it is a pre-release it will bump up to the same patch version.\n        // 1.2.0-5 patches to 1.2.0\n        // 1.2.0 patches to 1.2.1\n        if (this.prerelease.length === 0) {\n          this.patch++\n        }\n        this.prerelease = []\n        break\n      // This probably shouldn't be used publicly.\n      // 1.0.0 'pre' would become 1.0.0-0 which is the wrong direction.\n      case 'pre': {\n        const base = Number(identifierBase) ? 1 : 0\n\n        if (this.prerelease.length === 0) {\n          this.prerelease = [base]\n        } else {\n          let i = this.prerelease.length\n          while (--i >= 0) {\n            if (typeof this.prerelease[i] === 'number') {\n              this.prerelease[i]++\n              i = -2\n            }\n          }\n          if (i === -1) {\n            // didn't increment anything\n            if (identifier === this.prerelease.join('.') && identifierBase === false) {\n              throw new Error('invalid increment argument: identifier already exists')\n            }\n            this.prerelease.push(base)\n          }\n        }\n        if (identifier) {\n          // 1.2.0-beta.1 bumps to 1.2.0-beta.2,\n          // 1.2.0-beta.fooblz or 1.2.0-beta bumps to 1.2.0-beta.0\n          let prerelease = [identifier, base]\n          if (identifierBase === false) {\n            prerelease = [identifier]\n          }\n          if (compareIdentifiers(this.prerelease[0], identifier) === 0) {\n            if (isNaN(this.prerelease[1])) {\n              this.prerelease = prerelease\n            }\n          } else {\n            this.prerelease = prerelease\n          }\n        }\n        break\n      }\n      default:\n        throw new Error(`invalid increment argument: ${release}`)\n    }\n    this.raw = this.format()\n    if (this.build.length) {\n      this.raw += `+${this.build.join('.')}`\n    }\n    return this\n  }\n}\n\nmodule.exports = SemVer\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst parse = (version, options, throwErrors = false) => {\n  if (version instanceof SemVer) {\n    return version\n  }\n  try {\n    return new SemVer(version, options)\n  } catch (er) {\n    if (!throwErrors) {\n      return null\n    }\n    throw er\n  }\n}\n\nmodule.exports = parse\n", "'use strict'\n\nconst parse = require('./parse')\nconst valid = (version, options) => {\n  const v = parse(version, options)\n  return v ? v.version : null\n}\nmodule.exports = valid\n", "'use strict'\n\nconst parse = require('./parse')\nconst clean = (version, options) => {\n  const s = parse(version.trim().replace(/^[=v]+/, ''), options)\n  return s ? s.version : null\n}\nmodule.exports = clean\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\n\nconst inc = (version, release, options, identifier, identifierBase) => {\n  if (typeof (options) === 'string') {\n    identifierBase = identifier\n    identifier = options\n    options = undefined\n  }\n\n  try {\n    return new SemVer(\n      version instanceof SemVer ? version.version : version,\n      options\n    ).inc(release, identifier, identifierBase).version\n  } catch (er) {\n    return null\n  }\n}\nmodule.exports = inc\n", "'use strict'\n\nconst parse = require('./parse.js')\n\nconst diff = (version1, version2) => {\n  const v1 = parse(version1, null, true)\n  const v2 = parse(version2, null, true)\n  const comparison = v1.compare(v2)\n\n  if (comparison === 0) {\n    return null\n  }\n\n  const v1Higher = comparison > 0\n  const highVersion = v1Higher ? v1 : v2\n  const lowVersion = v1Higher ? v2 : v1\n  const highHasPre = !!highVersion.prerelease.length\n  const lowHasPre = !!lowVersion.prerelease.length\n\n  if (lowHasPre && !highHasPre) {\n    // Going from prerelease -> no prerelease requires some special casing\n\n    // If the low version has only a major, then it will always be a major\n    // Some examples:\n    // 1.0.0-1 -> 1.0.0\n    // 1.0.0-1 -> 1.1.1\n    // 1.0.0-1 -> 2.0.0\n    if (!lowVersion.patch && !lowVersion.minor) {\n      return 'major'\n    }\n\n    // If the main part has no difference\n    if (lowVersion.compareMain(highVersion) === 0) {\n      if (lowVersion.minor && !lowVersion.patch) {\n        return 'minor'\n      }\n      return 'patch'\n    }\n  }\n\n  // add the `pre` prefix if we are going to a prerelease version\n  const prefix = highHasPre ? 'pre' : ''\n\n  if (v1.major !== v2.major) {\n    return prefix + 'major'\n  }\n\n  if (v1.minor !== v2.minor) {\n    return prefix + 'minor'\n  }\n\n  if (v1.patch !== v2.patch) {\n    return prefix + 'patch'\n  }\n\n  // high and low are preleases\n  return 'prerelease'\n}\n\nmodule.exports = diff\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst major = (a, loose) => new SemVer(a, loose).major\nmodule.exports = major\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst minor = (a, loose) => new SemVer(a, loose).minor\nmodule.exports = minor\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst patch = (a, loose) => new SemVer(a, loose).patch\nmodule.exports = patch\n", "'use strict'\n\nconst parse = require('./parse')\nconst prerelease = (version, options) => {\n  const parsed = parse(version, options)\n  return (parsed && parsed.prerelease.length) ? parsed.prerelease : null\n}\nmodule.exports = prerelease\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst compare = (a, b, loose) =>\n  new SemVer(a, loose).compare(new SemVer(b, loose))\n\nmodule.exports = compare\n", "'use strict'\n\nconst compare = require('./compare')\nconst rcompare = (a, b, loose) => compare(b, a, loose)\nmodule.exports = rcompare\n", "'use strict'\n\nconst compare = require('./compare')\nconst compareLoose = (a, b) => compare(a, b, true)\nmodule.exports = compareLoose\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst compareBuild = (a, b, loose) => {\n  const versionA = new SemVer(a, loose)\n  const versionB = new SemVer(b, loose)\n  return versionA.compare(versionB) || versionA.compareBuild(versionB)\n}\nmodule.exports = compareBuild\n", "'use strict'\n\nconst compareBuild = require('./compare-build')\nconst sort = (list, loose) => list.sort((a, b) => compareBuild(a, b, loose))\nmodule.exports = sort\n", "'use strict'\n\nconst compareBuild = require('./compare-build')\nconst rsort = (list, loose) => list.sort((a, b) => compareBuild(b, a, loose))\nmodule.exports = rsort\n", "'use strict'\n\nconst compare = require('./compare')\nconst gt = (a, b, loose) => compare(a, b, loose) > 0\nmodule.exports = gt\n", "'use strict'\n\nconst compare = require('./compare')\nconst lt = (a, b, loose) => compare(a, b, loose) < 0\nmodule.exports = lt\n", "'use strict'\n\nconst compare = require('./compare')\nconst eq = (a, b, loose) => compare(a, b, loose) === 0\nmodule.exports = eq\n", "'use strict'\n\nconst compare = require('./compare')\nconst neq = (a, b, loose) => compare(a, b, loose) !== 0\nmodule.exports = neq\n", "'use strict'\n\nconst compare = require('./compare')\nconst gte = (a, b, loose) => compare(a, b, loose) >= 0\nmodule.exports = gte\n", "'use strict'\n\nconst compare = require('./compare')\nconst lte = (a, b, loose) => compare(a, b, loose) <= 0\nmodule.exports = lte\n", "'use strict'\n\nconst eq = require('./eq')\nconst neq = require('./neq')\nconst gt = require('./gt')\nconst gte = require('./gte')\nconst lt = require('./lt')\nconst lte = require('./lte')\n\nconst cmp = (a, op, b, loose) => {\n  switch (op) {\n    case '===':\n      if (typeof a === 'object') {\n        a = a.version\n      }\n      if (typeof b === 'object') {\n        b = b.version\n      }\n      return a === b\n\n    case '!==':\n      if (typeof a === 'object') {\n        a = a.version\n      }\n      if (typeof b === 'object') {\n        b = b.version\n      }\n      return a !== b\n\n    case '':\n    case '=':\n    case '==':\n      return eq(a, b, loose)\n\n    case '!=':\n      return neq(a, b, loose)\n\n    case '>':\n      return gt(a, b, loose)\n\n    case '>=':\n      return gte(a, b, loose)\n\n    case '<':\n      return lt(a, b, loose)\n\n    case '<=':\n      return lte(a, b, loose)\n\n    default:\n      throw new TypeError(`Invalid operator: ${op}`)\n  }\n}\nmodule.exports = cmp\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst parse = require('./parse')\nconst { safeRe: re, t } = require('../internal/re')\n\nconst coerce = (version, options) => {\n  if (version instanceof SemVer) {\n    return version\n  }\n\n  if (typeof version === 'number') {\n    version = String(version)\n  }\n\n  if (typeof version !== 'string') {\n    return null\n  }\n\n  options = options || {}\n\n  let match = null\n  if (!options.rtl) {\n    match = version.match(options.includePrerelease ? re[t.COERCEFULL] : re[t.COERCE])\n  } else {\n    // Find the right-most coercible string that does not share\n    // a terminus with a more left-ward coercible string.\n    // Eg, '1.2.3.4' wants to coerce '2.3.4', not '3.4' or '4'\n    // With includePrerelease option set, '1.2.3.4-rc' wants to coerce '2.3.4-rc', not '2.3.4'\n    //\n    // Walk through the string checking with a /g regexp\n    // Manually set the index so as to pick up overlapping matches.\n    // Stop when we get a match that ends at the string end, since no\n    // coercible string can be more right-ward without the same terminus.\n    const coerceRtlRegex = options.includePrerelease ? re[t.COERCERTLFULL] : re[t.COERCERTL]\n    let next\n    while ((next = coerceRtlRegex.exec(version)) &&\n        (!match || match.index + match[0].length !== version.length)\n    ) {\n      if (!match ||\n            next.index + next[0].length !== match.index + match[0].length) {\n        match = next\n      }\n      coerceRtlRegex.lastIndex = next.index + next[1].length + next[2].length\n    }\n    // leave it in a clean state\n    coerceRtlRegex.lastIndex = -1\n  }\n\n  if (match === null) {\n    return null\n  }\n\n  const major = match[2]\n  const minor = match[3] || '0'\n  const patch = match[4] || '0'\n  const prerelease = options.includePrerelease && match[5] ? `-${match[5]}` : ''\n  const build = options.includePrerelease && match[6] ? `+${match[6]}` : ''\n\n  return parse(`${major}.${minor}.${patch}${prerelease}${build}`, options)\n}\nmodule.exports = coerce\n", "'use strict'\n\nclass LRUCache {\n  constructor () {\n    this.max = 1000\n    this.map = new Map()\n  }\n\n  get (key) {\n    const value = this.map.get(key)\n    if (value === undefined) {\n      return undefined\n    } else {\n      // Remove the key from the map and add it to the end\n      this.map.delete(key)\n      this.map.set(key, value)\n      return value\n    }\n  }\n\n  delete (key) {\n    return this.map.delete(key)\n  }\n\n  set (key, value) {\n    const deleted = this.delete(key)\n\n    if (!deleted && value !== undefined) {\n      // If cache is full, delete the least recently used item\n      if (this.map.size >= this.max) {\n        const firstKey = this.map.keys().next().value\n        this.delete(firstKey)\n      }\n\n      this.map.set(key, value)\n    }\n\n    return this\n  }\n}\n\nmodule.exports = LRUCache\n", "'use strict'\n\nconst SPACE_CHARACTERS = /\\s+/g\n\n// hoisted class for cyclic dependency\nclass Range {\n  constructor (range, options) {\n    options = parseOptions(options)\n\n    if (range instanceof Range) {\n      if (\n        range.loose === !!options.loose &&\n        range.includePrerelease === !!options.includePrerelease\n      ) {\n        return range\n      } else {\n        return new Range(range.raw, options)\n      }\n    }\n\n    if (range instanceof Comparator) {\n      // just put it in the set and return\n      this.raw = range.value\n      this.set = [[range]]\n      this.formatted = undefined\n      return this\n    }\n\n    this.options = options\n    this.loose = !!options.loose\n    this.includePrerelease = !!options.includePrerelease\n\n    // First reduce all whitespace as much as possible so we do not have to rely\n    // on potentially slow regexes like \\s*. This is then stored and used for\n    // future error messages as well.\n    this.raw = range.trim().replace(SPACE_CHARACTERS, ' ')\n\n    // First, split on ||\n    this.set = this.raw\n      .split('||')\n      // map the range to a 2d array of comparators\n      .map(r => this.parseRange(r.trim()))\n      // throw out any comparator lists that are empty\n      // this generally means that it was not a valid range, which is allowed\n      // in loose mode, but will still throw if the WHOLE range is invalid.\n      .filter(c => c.length)\n\n    if (!this.set.length) {\n      throw new TypeError(`Invalid SemVer Range: ${this.raw}`)\n    }\n\n    // if we have any that are not the null set, throw out null sets.\n    if (this.set.length > 1) {\n      // keep the first one, in case they're all null sets\n      const first = this.set[0]\n      this.set = this.set.filter(c => !isNullSet(c[0]))\n      if (this.set.length === 0) {\n        this.set = [first]\n      } else if (this.set.length > 1) {\n        // if we have any that are *, then the range is just *\n        for (const c of this.set) {\n          if (c.length === 1 && isAny(c[0])) {\n            this.set = [c]\n            break\n          }\n        }\n      }\n    }\n\n    this.formatted = undefined\n  }\n\n  get range () {\n    if (this.formatted === undefined) {\n      this.formatted = ''\n      for (let i = 0; i < this.set.length; i++) {\n        if (i > 0) {\n          this.formatted += '||'\n        }\n        const comps = this.set[i]\n        for (let k = 0; k < comps.length; k++) {\n          if (k > 0) {\n            this.formatted += ' '\n          }\n          this.formatted += comps[k].toString().trim()\n        }\n      }\n    }\n    return this.formatted\n  }\n\n  format () {\n    return this.range\n  }\n\n  toString () {\n    return this.range\n  }\n\n  parseRange (range) {\n    // memoize range parsing for performance.\n    // this is a very hot path, and fully deterministic.\n    const memoOpts =\n      (this.options.includePrerelease && FLAG_INCLUDE_PRERELEASE) |\n      (this.options.loose && FLAG_LOOSE)\n    const memoKey = memoOpts + ':' + range\n    const cached = cache.get(memoKey)\n    if (cached) {\n      return cached\n    }\n\n    const loose = this.options.loose\n    // `1.2.3 - 1.2.4` => `>=1.2.3 <=1.2.4`\n    const hr = loose ? re[t.HYPHENRANGELOOSE] : re[t.HYPHENRANGE]\n    range = range.replace(hr, hyphenReplace(this.options.includePrerelease))\n    debug('hyphen replace', range)\n\n    // `> 1.2.3 < 1.2.5` => `>1.2.3 <1.2.5`\n    range = range.replace(re[t.COMPARATORTRIM], comparatorTrimReplace)\n    debug('comparator trim', range)\n\n    // `~ 1.2.3` => `~1.2.3`\n    range = range.replace(re[t.TILDETRIM], tildeTrimReplace)\n    debug('tilde trim', range)\n\n    // `^ 1.2.3` => `^1.2.3`\n    range = range.replace(re[t.CARETTRIM], caretTrimReplace)\n    debug('caret trim', range)\n\n    // At this point, the range is completely trimmed and\n    // ready to be split into comparators.\n\n    let rangeList = range\n      .split(' ')\n      .map(comp => parseComparator(comp, this.options))\n      .join(' ')\n      .split(/\\s+/)\n      // >=0.0.0 is equivalent to *\n      .map(comp => replaceGTE0(comp, this.options))\n\n    if (loose) {\n      // in loose mode, throw out any that are not valid comparators\n      rangeList = rangeList.filter(comp => {\n        debug('loose invalid filter', comp, this.options)\n        return !!comp.match(re[t.COMPARATORLOOSE])\n      })\n    }\n    debug('range list', rangeList)\n\n    // if any comparators are the null set, then replace with JUST null set\n    // if more than one comparator, remove any * comparators\n    // also, don't include the same comparator more than once\n    const rangeMap = new Map()\n    const comparators = rangeList.map(comp => new Comparator(comp, this.options))\n    for (const comp of comparators) {\n      if (isNullSet(comp)) {\n        return [comp]\n      }\n      rangeMap.set(comp.value, comp)\n    }\n    if (rangeMap.size > 1 && rangeMap.has('')) {\n      rangeMap.delete('')\n    }\n\n    const result = [...rangeMap.values()]\n    cache.set(memoKey, result)\n    return result\n  }\n\n  intersects (range, options) {\n    if (!(range instanceof Range)) {\n      throw new TypeError('a Range is required')\n    }\n\n    return this.set.some((thisComparators) => {\n      return (\n        isSatisfiable(thisComparators, options) &&\n        range.set.some((rangeComparators) => {\n          return (\n            isSatisfiable(rangeComparators, options) &&\n            thisComparators.every((thisComparator) => {\n              return rangeComparators.every((rangeComparator) => {\n                return thisComparator.intersects(rangeComparator, options)\n              })\n            })\n          )\n        })\n      )\n    })\n  }\n\n  // if ANY of the sets match ALL of its comparators, then pass\n  test (version) {\n    if (!version) {\n      return false\n    }\n\n    if (typeof version === 'string') {\n      try {\n        version = new SemVer(version, this.options)\n      } catch (er) {\n        return false\n      }\n    }\n\n    for (let i = 0; i < this.set.length; i++) {\n      if (testSet(this.set[i], version, this.options)) {\n        return true\n      }\n    }\n    return false\n  }\n}\n\nmodule.exports = Range\n\nconst LRU = require('../internal/lrucache')\nconst cache = new LRU()\n\nconst parseOptions = require('../internal/parse-options')\nconst Comparator = require('./comparator')\nconst debug = require('../internal/debug')\nconst SemVer = require('./semver')\nconst {\n  safeRe: re,\n  t,\n  comparatorTrimReplace,\n  tildeTrimReplace,\n  caretTrimReplace,\n} = require('../internal/re')\nconst { FLAG_INCLUDE_PRERELEASE, FLAG_LOOSE } = require('../internal/constants')\n\nconst isNullSet = c => c.value === '<0.0.0-0'\nconst isAny = c => c.value === ''\n\n// take a set of comparators and determine whether there\n// exists a version which can satisfy it\nconst isSatisfiable = (comparators, options) => {\n  let result = true\n  const remainingComparators = comparators.slice()\n  let testComparator = remainingComparators.pop()\n\n  while (result && remainingComparators.length) {\n    result = remainingComparators.every((otherComparator) => {\n      return testComparator.intersects(otherComparator, options)\n    })\n\n    testComparator = remainingComparators.pop()\n  }\n\n  return result\n}\n\n// comprised of xranges, tildes, stars, and gtlt's at this point.\n// already replaced the hyphen ranges\n// turn into a set of JUST comparators.\nconst parseComparator = (comp, options) => {\n  debug('comp', comp, options)\n  comp = replaceCarets(comp, options)\n  debug('caret', comp)\n  comp = replaceTildes(comp, options)\n  debug('tildes', comp)\n  comp = replaceXRanges(comp, options)\n  debug('xrange', comp)\n  comp = replaceStars(comp, options)\n  debug('stars', comp)\n  return comp\n}\n\nconst isX = id => !id || id.toLowerCase() === 'x' || id === '*'\n\n// ~, ~> --> * (any, kinda silly)\n// ~2, ~2.x, ~2.x.x, ~>2, ~>2.x ~>2.x.x --> >=2.0.0 <3.0.0-0\n// ~2.0, ~2.0.x, ~>2.0, ~>2.0.x --> >=2.0.0 <2.1.0-0\n// ~1.2, ~1.2.x, ~>1.2, ~>1.2.x --> >=1.2.0 <1.3.0-0\n// ~1.2.3, ~>1.2.3 --> >=1.2.3 <1.3.0-0\n// ~1.2.0, ~>1.2.0 --> >=1.2.0 <1.3.0-0\n// ~0.0.1 --> >=0.0.1 <0.1.0-0\nconst replaceTildes = (comp, options) => {\n  return comp\n    .trim()\n    .split(/\\s+/)\n    .map((c) => replaceTilde(c, options))\n    .join(' ')\n}\n\nconst replaceTilde = (comp, options) => {\n  const r = options.loose ? re[t.TILDELOOSE] : re[t.TILDE]\n  return comp.replace(r, (_, M, m, p, pr) => {\n    debug('tilde', comp, _, M, m, p, pr)\n    let ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = `>=${M}.0.0 <${+M + 1}.0.0-0`\n    } else if (isX(p)) {\n      // ~1.2 == >=1.2.0 <1.3.0-0\n      ret = `>=${M}.${m}.0 <${M}.${+m + 1}.0-0`\n    } else if (pr) {\n      debug('replaceTilde pr', pr)\n      ret = `>=${M}.${m}.${p}-${pr\n      } <${M}.${+m + 1}.0-0`\n    } else {\n      // ~1.2.3 == >=1.2.3 <1.3.0-0\n      ret = `>=${M}.${m}.${p\n      } <${M}.${+m + 1}.0-0`\n    }\n\n    debug('tilde return', ret)\n    return ret\n  })\n}\n\n// ^ --> * (any, kinda silly)\n// ^2, ^2.x, ^2.x.x --> >=2.0.0 <3.0.0-0\n// ^2.0, ^2.0.x --> >=2.0.0 <3.0.0-0\n// ^1.2, ^1.2.x --> >=1.2.0 <2.0.0-0\n// ^1.2.3 --> >=1.2.3 <2.0.0-0\n// ^1.2.0 --> >=1.2.0 <2.0.0-0\n// ^0.0.1 --> >=0.0.1 <0.0.2-0\n// ^0.1.0 --> >=0.1.0 <0.2.0-0\nconst replaceCarets = (comp, options) => {\n  return comp\n    .trim()\n    .split(/\\s+/)\n    .map((c) => replaceCaret(c, options))\n    .join(' ')\n}\n\nconst replaceCaret = (comp, options) => {\n  debug('caret', comp, options)\n  const r = options.loose ? re[t.CARETLOOSE] : re[t.CARET]\n  const z = options.includePrerelease ? '-0' : ''\n  return comp.replace(r, (_, M, m, p, pr) => {\n    debug('caret', comp, _, M, m, p, pr)\n    let ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = `>=${M}.0.0${z} <${+M + 1}.0.0-0`\n    } else if (isX(p)) {\n      if (M === '0') {\n        ret = `>=${M}.${m}.0${z} <${M}.${+m + 1}.0-0`\n      } else {\n        ret = `>=${M}.${m}.0${z} <${+M + 1}.0.0-0`\n      }\n    } else if (pr) {\n      debug('replaceCaret pr', pr)\n      if (M === '0') {\n        if (m === '0') {\n          ret = `>=${M}.${m}.${p}-${pr\n          } <${M}.${m}.${+p + 1}-0`\n        } else {\n          ret = `>=${M}.${m}.${p}-${pr\n          } <${M}.${+m + 1}.0-0`\n        }\n      } else {\n        ret = `>=${M}.${m}.${p}-${pr\n        } <${+M + 1}.0.0-0`\n      }\n    } else {\n      debug('no pr')\n      if (M === '0') {\n        if (m === '0') {\n          ret = `>=${M}.${m}.${p\n          }${z} <${M}.${m}.${+p + 1}-0`\n        } else {\n          ret = `>=${M}.${m}.${p\n          }${z} <${M}.${+m + 1}.0-0`\n        }\n      } else {\n        ret = `>=${M}.${m}.${p\n        } <${+M + 1}.0.0-0`\n      }\n    }\n\n    debug('caret return', ret)\n    return ret\n  })\n}\n\nconst replaceXRanges = (comp, options) => {\n  debug('replaceXRanges', comp, options)\n  return comp\n    .split(/\\s+/)\n    .map((c) => replaceXRange(c, options))\n    .join(' ')\n}\n\nconst replaceXRange = (comp, options) => {\n  comp = comp.trim()\n  const r = options.loose ? re[t.XRANGELOOSE] : re[t.XRANGE]\n  return comp.replace(r, (ret, gtlt, M, m, p, pr) => {\n    debug('xRange', comp, ret, gtlt, M, m, p, pr)\n    const xM = isX(M)\n    const xm = xM || isX(m)\n    const xp = xm || isX(p)\n    const anyX = xp\n\n    if (gtlt === '=' && anyX) {\n      gtlt = ''\n    }\n\n    // if we're including prereleases in the match, then we need\n    // to fix this to -0, the lowest possible prerelease value\n    pr = options.includePrerelease ? '-0' : ''\n\n    if (xM) {\n      if (gtlt === '>' || gtlt === '<') {\n        // nothing is allowed\n        ret = '<0.0.0-0'\n      } else {\n        // nothing is forbidden\n        ret = '*'\n      }\n    } else if (gtlt && anyX) {\n      // we know patch is an x, because we have any x at all.\n      // replace X with 0\n      if (xm) {\n        m = 0\n      }\n      p = 0\n\n      if (gtlt === '>') {\n        // >1 => >=2.0.0\n        // >1.2 => >=1.3.0\n        gtlt = '>='\n        if (xm) {\n          M = +M + 1\n          m = 0\n          p = 0\n        } else {\n          m = +m + 1\n          p = 0\n        }\n      } else if (gtlt === '<=') {\n        // <=0.7.x is actually <0.8.0, since any 0.7.x should\n        // pass.  Similarly, <=7.x is actually <8.0.0, etc.\n        gtlt = '<'\n        if (xm) {\n          M = +M + 1\n        } else {\n          m = +m + 1\n        }\n      }\n\n      if (gtlt === '<') {\n        pr = '-0'\n      }\n\n      ret = `${gtlt + M}.${m}.${p}${pr}`\n    } else if (xm) {\n      ret = `>=${M}.0.0${pr} <${+M + 1}.0.0-0`\n    } else if (xp) {\n      ret = `>=${M}.${m}.0${pr\n      } <${M}.${+m + 1}.0-0`\n    }\n\n    debug('xRange return', ret)\n\n    return ret\n  })\n}\n\n// Because * is AND-ed with everything else in the comparator,\n// and '' means \"any version\", just remove the *s entirely.\nconst replaceStars = (comp, options) => {\n  debug('replaceStars', comp, options)\n  // Looseness is ignored here.  star is always as loose as it gets!\n  return comp\n    .trim()\n    .replace(re[t.STAR], '')\n}\n\nconst replaceGTE0 = (comp, options) => {\n  debug('replaceGTE0', comp, options)\n  return comp\n    .trim()\n    .replace(re[options.includePrerelease ? t.GTE0PRE : t.GTE0], '')\n}\n\n// This function is passed to string.replace(re[t.HYPHENRANGE])\n// M, m, patch, prerelease, build\n// 1.2 - 3.4.5 => >=1.2.0 <=3.4.5\n// 1.2.3 - 3.4 => >=1.2.0 <3.5.0-0 Any 3.4.x will do\n// 1.2 - 3.4 => >=1.2.0 <3.5.0-0\n// TODO build?\nconst hyphenReplace = incPr => ($0,\n  from, fM, fm, fp, fpr, fb,\n  to, tM, tm, tp, tpr) => {\n  if (isX(fM)) {\n    from = ''\n  } else if (isX(fm)) {\n    from = `>=${fM}.0.0${incPr ? '-0' : ''}`\n  } else if (isX(fp)) {\n    from = `>=${fM}.${fm}.0${incPr ? '-0' : ''}`\n  } else if (fpr) {\n    from = `>=${from}`\n  } else {\n    from = `>=${from}${incPr ? '-0' : ''}`\n  }\n\n  if (isX(tM)) {\n    to = ''\n  } else if (isX(tm)) {\n    to = `<${+tM + 1}.0.0-0`\n  } else if (isX(tp)) {\n    to = `<${tM}.${+tm + 1}.0-0`\n  } else if (tpr) {\n    to = `<=${tM}.${tm}.${tp}-${tpr}`\n  } else if (incPr) {\n    to = `<${tM}.${tm}.${+tp + 1}-0`\n  } else {\n    to = `<=${to}`\n  }\n\n  return `${from} ${to}`.trim()\n}\n\nconst testSet = (set, version, options) => {\n  for (let i = 0; i < set.length; i++) {\n    if (!set[i].test(version)) {\n      return false\n    }\n  }\n\n  if (version.prerelease.length && !options.includePrerelease) {\n    // Find the set of versions that are allowed to have prereleases\n    // For example, ^1.2.3-pr.1 desugars to >=1.2.3-pr.1 <2.0.0\n    // That should allow `1.2.3-pr.2` to pass.\n    // However, `1.2.4-alpha.notready` should NOT be allowed,\n    // even though it's within the range set by the comparators.\n    for (let i = 0; i < set.length; i++) {\n      debug(set[i].semver)\n      if (set[i].semver === Comparator.ANY) {\n        continue\n      }\n\n      if (set[i].semver.prerelease.length > 0) {\n        const allowed = set[i].semver\n        if (allowed.major === version.major &&\n            allowed.minor === version.minor &&\n            allowed.patch === version.patch) {\n          return true\n        }\n      }\n    }\n\n    // Version has a -pre, but it's not one of the ones we like.\n    return false\n  }\n\n  return true\n}\n", "'use strict'\n\nconst ANY = Symbol('SemVer ANY')\n// hoisted class for cyclic dependency\nclass Comparator {\n  static get ANY () {\n    return ANY\n  }\n\n  constructor (comp, options) {\n    options = parseOptions(options)\n\n    if (comp instanceof Comparator) {\n      if (comp.loose === !!options.loose) {\n        return comp\n      } else {\n        comp = comp.value\n      }\n    }\n\n    comp = comp.trim().split(/\\s+/).join(' ')\n    debug('comparator', comp, options)\n    this.options = options\n    this.loose = !!options.loose\n    this.parse(comp)\n\n    if (this.semver === ANY) {\n      this.value = ''\n    } else {\n      this.value = this.operator + this.semver.version\n    }\n\n    debug('comp', this)\n  }\n\n  parse (comp) {\n    const r = this.options.loose ? re[t.COMPARATORLOOSE] : re[t.COMPARATOR]\n    const m = comp.match(r)\n\n    if (!m) {\n      throw new TypeError(`Invalid comparator: ${comp}`)\n    }\n\n    this.operator = m[1] !== undefined ? m[1] : ''\n    if (this.operator === '=') {\n      this.operator = ''\n    }\n\n    // if it literally is just '>' or '' then allow anything.\n    if (!m[2]) {\n      this.semver = ANY\n    } else {\n      this.semver = new SemVer(m[2], this.options.loose)\n    }\n  }\n\n  toString () {\n    return this.value\n  }\n\n  test (version) {\n    debug('Comparator.test', version, this.options.loose)\n\n    if (this.semver === ANY || version === ANY) {\n      return true\n    }\n\n    if (typeof version === 'string') {\n      try {\n        version = new SemVer(version, this.options)\n      } catch (er) {\n        return false\n      }\n    }\n\n    return cmp(version, this.operator, this.semver, this.options)\n  }\n\n  intersects (comp, options) {\n    if (!(comp instanceof Comparator)) {\n      throw new TypeError('a Comparator is required')\n    }\n\n    if (this.operator === '') {\n      if (this.value === '') {\n        return true\n      }\n      return new Range(comp.value, options).test(this.value)\n    } else if (comp.operator === '') {\n      if (comp.value === '') {\n        return true\n      }\n      return new Range(this.value, options).test(comp.semver)\n    }\n\n    options = parseOptions(options)\n\n    // Special cases where nothing can possibly be lower\n    if (options.includePrerelease &&\n      (this.value === '<0.0.0-0' || comp.value === '<0.0.0-0')) {\n      return false\n    }\n    if (!options.includePrerelease &&\n      (this.value.startsWith('<0.0.0') || comp.value.startsWith('<0.0.0'))) {\n      return false\n    }\n\n    // Same direction increasing (> or >=)\n    if (this.operator.startsWith('>') && comp.operator.startsWith('>')) {\n      return true\n    }\n    // Same direction decreasing (< or <=)\n    if (this.operator.startsWith('<') && comp.operator.startsWith('<')) {\n      return true\n    }\n    // same SemVer and both sides are inclusive (<= or >=)\n    if (\n      (this.semver.version === comp.semver.version) &&\n      this.operator.includes('=') && comp.operator.includes('=')) {\n      return true\n    }\n    // opposite directions less than\n    if (cmp(this.semver, '<', comp.semver, options) &&\n      this.operator.startsWith('>') && comp.operator.startsWith('<')) {\n      return true\n    }\n    // opposite directions greater than\n    if (cmp(this.semver, '>', comp.semver, options) &&\n      this.operator.startsWith('<') && comp.operator.startsWith('>')) {\n      return true\n    }\n    return false\n  }\n}\n\nmodule.exports = Comparator\n\nconst parseOptions = require('../internal/parse-options')\nconst { safeRe: re, t } = require('../internal/re')\nconst cmp = require('../functions/cmp')\nconst debug = require('../internal/debug')\nconst SemVer = require('./semver')\nconst Range = require('./range')\n", "'use strict'\n\nconst Range = require('../classes/range')\nconst satisfies = (version, range, options) => {\n  try {\n    range = new Range(range, options)\n  } catch (er) {\n    return false\n  }\n  return range.test(version)\n}\nmodule.exports = satisfies\n", "'use strict'\n\nconst Range = require('../classes/range')\n\n// Mostly just for testing and legacy API reasons\nconst toComparators = (range, options) =>\n  new Range(range, options).set\n    .map(comp => comp.map(c => c.value).join(' ').trim().split(' '))\n\nmodule.exports = toComparators\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst Range = require('../classes/range')\n\nconst maxSatisfying = (versions, range, options) => {\n  let max = null\n  let maxSV = null\n  let rangeObj = null\n  try {\n    rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach((v) => {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!max || maxSV.compare(v) === -1) {\n        // compare(max, v, true)\n        max = v\n        maxSV = new SemVer(max, options)\n      }\n    }\n  })\n  return max\n}\nmodule.exports = maxSatisfying\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst Range = require('../classes/range')\nconst minSatisfying = (versions, range, options) => {\n  let min = null\n  let minSV = null\n  let rangeObj = null\n  try {\n    rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach((v) => {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!min || minSV.compare(v) === 1) {\n        // compare(min, v, true)\n        min = v\n        minSV = new SemVer(min, options)\n      }\n    }\n  })\n  return min\n}\nmodule.exports = minSatisfying\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst Range = require('../classes/range')\nconst gt = require('../functions/gt')\n\nconst minVersion = (range, loose) => {\n  range = new Range(range, loose)\n\n  let minver = new SemVer('0.0.0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = new SemVer('0.0.0-0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = null\n  for (let i = 0; i < range.set.length; ++i) {\n    const comparators = range.set[i]\n\n    let setMin = null\n    comparators.forEach((comparator) => {\n      // Clone to avoid manipulating the comparator's semver object.\n      const compver = new SemVer(comparator.semver.version)\n      switch (comparator.operator) {\n        case '>':\n          if (compver.prerelease.length === 0) {\n            compver.patch++\n          } else {\n            compver.prerelease.push(0)\n          }\n          compver.raw = compver.format()\n          /* fallthrough */\n        case '':\n        case '>=':\n          if (!setMin || gt(compver, setMin)) {\n            setMin = compver\n          }\n          break\n        case '<':\n        case '<=':\n          /* Ignore maximum versions */\n          break\n        /* istanbul ignore next */\n        default:\n          throw new Error(`Unexpected operation: ${comparator.operator}`)\n      }\n    })\n    if (setMin && (!minver || gt(minver, setMin))) {\n      minver = setMin\n    }\n  }\n\n  if (minver && range.test(minver)) {\n    return minver\n  }\n\n  return null\n}\nmodule.exports = minVersion\n", "'use strict'\n\nconst Range = require('../classes/range')\nconst validRange = (range, options) => {\n  try {\n    // Return '*' instead of '' so that truthiness works.\n    // This will throw if it's invalid anyway\n    return new Range(range, options).range || '*'\n  } catch (er) {\n    return null\n  }\n}\nmodule.exports = validRange\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst Comparator = require('../classes/comparator')\nconst { ANY } = Comparator\nconst Range = require('../classes/range')\nconst satisfies = require('../functions/satisfies')\nconst gt = require('../functions/gt')\nconst lt = require('../functions/lt')\nconst lte = require('../functions/lte')\nconst gte = require('../functions/gte')\n\nconst outside = (version, range, hilo, options) => {\n  version = new SemVer(version, options)\n  range = new Range(range, options)\n\n  let gtfn, ltefn, ltfn, comp, ecomp\n  switch (hilo) {\n    case '>':\n      gtfn = gt\n      ltefn = lte\n      ltfn = lt\n      comp = '>'\n      ecomp = '>='\n      break\n    case '<':\n      gtfn = lt\n      ltefn = gte\n      ltfn = gt\n      comp = '<'\n      ecomp = '<='\n      break\n    default:\n      throw new TypeError('Must provide a hilo val of \"<\" or \">\"')\n  }\n\n  // If it satisfies the range it is not outside\n  if (satisfies(version, range, options)) {\n    return false\n  }\n\n  // From now on, variable terms are as if we're in \"gtr\" mode.\n  // but note that everything is flipped for the \"ltr\" function.\n\n  for (let i = 0; i < range.set.length; ++i) {\n    const comparators = range.set[i]\n\n    let high = null\n    let low = null\n\n    comparators.forEach((comparator) => {\n      if (comparator.semver === ANY) {\n        comparator = new Comparator('>=0.0.0')\n      }\n      high = high || comparator\n      low = low || comparator\n      if (gtfn(comparator.semver, high.semver, options)) {\n        high = comparator\n      } else if (ltfn(comparator.semver, low.semver, options)) {\n        low = comparator\n      }\n    })\n\n    // If the edge version comparator has a operator then our version\n    // isn't outside it\n    if (high.operator === comp || high.operator === ecomp) {\n      return false\n    }\n\n    // If the lowest version comparator has an operator and our version\n    // is less than it then it isn't higher than the range\n    if ((!low.operator || low.operator === comp) &&\n        ltefn(version, low.semver)) {\n      return false\n    } else if (low.operator === ecomp && ltfn(version, low.semver)) {\n      return false\n    }\n  }\n  return true\n}\n\nmodule.exports = outside\n", "'use strict'\n\n// Determine if version is greater than all the versions possible in the range.\nconst outside = require('./outside')\nconst gtr = (version, range, options) => outside(version, range, '>', options)\nmodule.exports = gtr\n", "'use strict'\n\nconst outside = require('./outside')\n// Determine if version is less than all the versions possible in the range\nconst ltr = (version, range, options) => outside(version, range, '<', options)\nmodule.exports = ltr\n", "'use strict'\n\nconst Range = require('../classes/range')\nconst intersects = (r1, r2, options) => {\n  r1 = new Range(r1, options)\n  r2 = new Range(r2, options)\n  return r1.intersects(r2, options)\n}\nmodule.exports = intersects\n", "'use strict'\n\n// given a set of versions and a range, create a \"simplified\" range\n// that includes the same versions that the original range does\n// If the original range is shorter than the simplified one, return that.\nconst satisfies = require('../functions/satisfies.js')\nconst compare = require('../functions/compare.js')\nmodule.exports = (versions, range, options) => {\n  const set = []\n  let first = null\n  let prev = null\n  const v = versions.sort((a, b) => compare(a, b, options))\n  for (const version of v) {\n    const included = satisfies(version, range, options)\n    if (included) {\n      prev = version\n      if (!first) {\n        first = version\n      }\n    } else {\n      if (prev) {\n        set.push([first, prev])\n      }\n      prev = null\n      first = null\n    }\n  }\n  if (first) {\n    set.push([first, null])\n  }\n\n  const ranges = []\n  for (const [min, max] of set) {\n    if (min === max) {\n      ranges.push(min)\n    } else if (!max && min === v[0]) {\n      ranges.push('*')\n    } else if (!max) {\n      ranges.push(`>=${min}`)\n    } else if (min === v[0]) {\n      ranges.push(`<=${max}`)\n    } else {\n      ranges.push(`${min} - ${max}`)\n    }\n  }\n  const simplified = ranges.join(' || ')\n  const original = typeof range.raw === 'string' ? range.raw : String(range)\n  return simplified.length < original.length ? simplified : range\n}\n", "'use strict'\n\nconst Range = require('../classes/range.js')\nconst Comparator = require('../classes/comparator.js')\nconst { ANY } = Comparator\nconst satisfies = require('../functions/satisfies.js')\nconst compare = require('../functions/compare.js')\n\n// Complex range `r1 || r2 || ...` is a subset of `R1 || R2 || ...` iff:\n// - Every simple range `r1, r2, ...` is a null set, OR\n// - Every simple range `r1, r2, ...` which is not a null set is a subset of\n//   some `R1, R2, ...`\n//\n// Simple range `c1 c2 ...` is a subset of simple range `C1 C2 ...` iff:\n// - If c is only the ANY comparator\n//   - If C is only the ANY comparator, return true\n//   - Else if in prerelease mode, return false\n//   - else replace c with `[>=0.0.0]`\n// - If C is only the ANY comparator\n//   - if in prerelease mode, return true\n//   - else replace C with `[>=0.0.0]`\n// - Let EQ be the set of = comparators in c\n// - If EQ is more than one, return true (null set)\n// - Let GT be the highest > or >= comparator in c\n// - Let LT be the lowest < or <= comparator in c\n// - If GT and LT, and GT.semver > LT.semver, return true (null set)\n// - If any C is a = range, and GT or LT are set, return false\n// - If EQ\n//   - If GT, and EQ does not satisfy GT, return true (null set)\n//   - If LT, and EQ does not satisfy LT, return true (null set)\n//   - If EQ satisfies every C, return true\n//   - Else return false\n// - If GT\n//   - If GT.semver is lower than any > or >= comp in C, return false\n//   - If GT is >=, and GT.semver does not satisfy every C, return false\n//   - If GT.semver has a prerelease, and not in prerelease mode\n//     - If no C has a prerelease and the GT.semver tuple, return false\n// - If LT\n//   - If LT.semver is greater than any < or <= comp in C, return false\n//   - If LT is <=, and LT.semver does not satisfy every C, return false\n//   - If GT.semver has a prerelease, and not in prerelease mode\n//     - If no C has a prerelease and the LT.semver tuple, return false\n// - Else return true\n\nconst subset = (sub, dom, options = {}) => {\n  if (sub === dom) {\n    return true\n  }\n\n  sub = new Range(sub, options)\n  dom = new Range(dom, options)\n  let sawNonNull = false\n\n  OUTER: for (const simpleSub of sub.set) {\n    for (const simpleDom of dom.set) {\n      const isSub = simpleSubset(simpleSub, simpleDom, options)\n      sawNonNull = sawNonNull || isSub !== null\n      if (isSub) {\n        continue OUTER\n      }\n    }\n    // the null set is a subset of everything, but null simple ranges in\n    // a complex range should be ignored.  so if we saw a non-null range,\n    // then we know this isn't a subset, but if EVERY simple range was null,\n    // then it is a subset.\n    if (sawNonNull) {\n      return false\n    }\n  }\n  return true\n}\n\nconst minimumVersionWithPreRelease = [new Comparator('>=0.0.0-0')]\nconst minimumVersion = [new Comparator('>=0.0.0')]\n\nconst simpleSubset = (sub, dom, options) => {\n  if (sub === dom) {\n    return true\n  }\n\n  if (sub.length === 1 && sub[0].semver === ANY) {\n    if (dom.length === 1 && dom[0].semver === ANY) {\n      return true\n    } else if (options.includePrerelease) {\n      sub = minimumVersionWithPreRelease\n    } else {\n      sub = minimumVersion\n    }\n  }\n\n  if (dom.length === 1 && dom[0].semver === ANY) {\n    if (options.includePrerelease) {\n      return true\n    } else {\n      dom = minimumVersion\n    }\n  }\n\n  const eqSet = new Set()\n  let gt, lt\n  for (const c of sub) {\n    if (c.operator === '>' || c.operator === '>=') {\n      gt = higherGT(gt, c, options)\n    } else if (c.operator === '<' || c.operator === '<=') {\n      lt = lowerLT(lt, c, options)\n    } else {\n      eqSet.add(c.semver)\n    }\n  }\n\n  if (eqSet.size > 1) {\n    return null\n  }\n\n  let gtltComp\n  if (gt && lt) {\n    gtltComp = compare(gt.semver, lt.semver, options)\n    if (gtltComp > 0) {\n      return null\n    } else if (gtltComp === 0 && (gt.operator !== '>=' || lt.operator !== '<=')) {\n      return null\n    }\n  }\n\n  // will iterate one or zero times\n  for (const eq of eqSet) {\n    if (gt && !satisfies(eq, String(gt), options)) {\n      return null\n    }\n\n    if (lt && !satisfies(eq, String(lt), options)) {\n      return null\n    }\n\n    for (const c of dom) {\n      if (!satisfies(eq, String(c), options)) {\n        return false\n      }\n    }\n\n    return true\n  }\n\n  let higher, lower\n  let hasDomLT, hasDomGT\n  // if the subset has a prerelease, we need a comparator in the superset\n  // with the same tuple and a prerelease, or it's not a subset\n  let needDomLTPre = lt &&\n    !options.includePrerelease &&\n    lt.semver.prerelease.length ? lt.semver : false\n  let needDomGTPre = gt &&\n    !options.includePrerelease &&\n    gt.semver.prerelease.length ? gt.semver : false\n  // exception: <1.2.3-0 is the same as <1.2.3\n  if (needDomLTPre && needDomLTPre.prerelease.length === 1 &&\n      lt.operator === '<' && needDomLTPre.prerelease[0] === 0) {\n    needDomLTPre = false\n  }\n\n  for (const c of dom) {\n    hasDomGT = hasDomGT || c.operator === '>' || c.operator === '>='\n    hasDomLT = hasDomLT || c.operator === '<' || c.operator === '<='\n    if (gt) {\n      if (needDomGTPre) {\n        if (c.semver.prerelease && c.semver.prerelease.length &&\n            c.semver.major === needDomGTPre.major &&\n            c.semver.minor === needDomGTPre.minor &&\n            c.semver.patch === needDomGTPre.patch) {\n          needDomGTPre = false\n        }\n      }\n      if (c.operator === '>' || c.operator === '>=') {\n        higher = higherGT(gt, c, options)\n        if (higher === c && higher !== gt) {\n          return false\n        }\n      } else if (gt.operator === '>=' && !satisfies(gt.semver, String(c), options)) {\n        return false\n      }\n    }\n    if (lt) {\n      if (needDomLTPre) {\n        if (c.semver.prerelease && c.semver.prerelease.length &&\n            c.semver.major === needDomLTPre.major &&\n            c.semver.minor === needDomLTPre.minor &&\n            c.semver.patch === needDomLTPre.patch) {\n          needDomLTPre = false\n        }\n      }\n      if (c.operator === '<' || c.operator === '<=') {\n        lower = lowerLT(lt, c, options)\n        if (lower === c && lower !== lt) {\n          return false\n        }\n      } else if (lt.operator === '<=' && !satisfies(lt.semver, String(c), options)) {\n        return false\n      }\n    }\n    if (!c.operator && (lt || gt) && gtltComp !== 0) {\n      return false\n    }\n  }\n\n  // if there was a < or >, and nothing in the dom, then must be false\n  // UNLESS it was limited by another range in the other direction.\n  // Eg, >1.0.0 <1.0.1 is still a subset of <2.0.0\n  if (gt && hasDomLT && !lt && gtltComp !== 0) {\n    return false\n  }\n\n  if (lt && hasDomGT && !gt && gtltComp !== 0) {\n    return false\n  }\n\n  // we needed a prerelease range in a specific tuple, but didn't get one\n  // then this isn't a subset.  eg >=1.2.3-pre is not a subset of >=1.0.0,\n  // because it includes prereleases in the 1.2.3 tuple\n  if (needDomGTPre || needDomLTPre) {\n    return false\n  }\n\n  return true\n}\n\n// >=1.2.3 is lower than >1.2.3\nconst higherGT = (a, b, options) => {\n  if (!a) {\n    return b\n  }\n  const comp = compare(a.semver, b.semver, options)\n  return comp > 0 ? a\n    : comp < 0 ? b\n    : b.operator === '>' && a.operator === '>=' ? b\n    : a\n}\n\n// <=1.2.3 is higher than <1.2.3\nconst lowerLT = (a, b, options) => {\n  if (!a) {\n    return b\n  }\n  const comp = compare(a.semver, b.semver, options)\n  return comp < 0 ? a\n    : comp > 0 ? b\n    : b.operator === '<' && a.operator === '<=' ? b\n    : a\n}\n\nmodule.exports = subset\n", "'use strict'\n\n// just pre-load all the stuff that index.js lazily exports\nconst internalRe = require('./internal/re')\nconst constants = require('./internal/constants')\nconst SemVer = require('./classes/semver')\nconst identifiers = require('./internal/identifiers')\nconst parse = require('./functions/parse')\nconst valid = require('./functions/valid')\nconst clean = require('./functions/clean')\nconst inc = require('./functions/inc')\nconst diff = require('./functions/diff')\nconst major = require('./functions/major')\nconst minor = require('./functions/minor')\nconst patch = require('./functions/patch')\nconst prerelease = require('./functions/prerelease')\nconst compare = require('./functions/compare')\nconst rcompare = require('./functions/rcompare')\nconst compareLoose = require('./functions/compare-loose')\nconst compareBuild = require('./functions/compare-build')\nconst sort = require('./functions/sort')\nconst rsort = require('./functions/rsort')\nconst gt = require('./functions/gt')\nconst lt = require('./functions/lt')\nconst eq = require('./functions/eq')\nconst neq = require('./functions/neq')\nconst gte = require('./functions/gte')\nconst lte = require('./functions/lte')\nconst cmp = require('./functions/cmp')\nconst coerce = require('./functions/coerce')\nconst Comparator = require('./classes/comparator')\nconst Range = require('./classes/range')\nconst satisfies = require('./functions/satisfies')\nconst toComparators = require('./ranges/to-comparators')\nconst maxSatisfying = require('./ranges/max-satisfying')\nconst minSatisfying = require('./ranges/min-satisfying')\nconst minVersion = require('./ranges/min-version')\nconst validRange = require('./ranges/valid')\nconst outside = require('./ranges/outside')\nconst gtr = require('./ranges/gtr')\nconst ltr = require('./ranges/ltr')\nconst intersects = require('./ranges/intersects')\nconst simplifyRange = require('./ranges/simplify')\nconst subset = require('./ranges/subset')\nmodule.exports = {\n  parse,\n  valid,\n  clean,\n  inc,\n  diff,\n  major,\n  minor,\n  patch,\n  prerelease,\n  compare,\n  rcompare,\n  compareLoose,\n  compareBuild,\n  sort,\n  rsort,\n  gt,\n  lt,\n  eq,\n  neq,\n  gte,\n  lte,\n  cmp,\n  coerce,\n  Comparator,\n  Range,\n  satisfies,\n  toComparators,\n  maxSatisfying,\n  minSatisfying,\n  minVersion,\n  validRange,\n  outside,\n  gtr,\n  ltr,\n  intersects,\n  simplifyRange,\n  subset,\n  SemVer,\n  re: internalRe.re,\n  src: internalRe.src,\n  tokens: internalRe.t,\n  SEMVER_SPEC_VERSION: constants.SEMVER_SPEC_VERSION,\n  RELEASE_TYPES: constants.RELEASE_TYPES,\n  compareIdentifiers: identifiers.compareIdentifiers,\n  rcompareIdentifiers: identifiers.rcompareIdentifiers,\n}\n", "import { MEDPLUM_VERSION, normalizeErrorString } from '@medplum/core';\nimport { CommanderError, Option } from 'commander';\nimport dotenv from 'dotenv';\nimport { agent } from './agent';\nimport { login, token, whoami } from './auth';\nimport { buildAwsCommand } from './aws/index';\nimport { bot, createBotDeprecate, deployBotDeprecate, saveBotDeprecate } from './bots';\nimport { bulk } from './bulk';\nimport { hl7 } from './hl7';\nimport { profile } from './profiles';\nimport { project } from './project';\nimport { deleteObject, get, patch, post, put } from './rest';\nimport { MedplumCommand, addSubcommand } from './utils';\n\nexport async function main(argv: string[]): Promise<void> {\n  const index = new MedplumCommand('medplum')\n    .description('Command to access Medplum CLI')\n    .option('--client-id <clientId>', 'FHIR server client id')\n    .option('--client-secret <clientSecret>', 'FHIR server client secret')\n    .option('--base-url <baseUrl>', 'FHIR server base URL, must be absolute')\n    .option('--token-url <tokenUrl>', 'FHIR server token URL, absolute or relative to base URL')\n    .option('--authorize-url <authorizeUrl>', 'FHIR server authorize URL, absolute or relative to base URL')\n    .option('--fhir-url, --fhir-url-path <fhirUrlPath>', 'FHIR server URL, absolute or relative to base URL')\n    .option('--scope <scope>', 'JWT scope')\n    .option('--access-token <accessToken>', 'Access token for token exchange authentication')\n    .option('--callback-url <callbackUrl>', 'Callback URL for authorization code flow')\n    .option('--subject <subject>', 'Subject for JWT authentication')\n    .option('--audience <audience>', 'Audience for JWT authentication')\n    .option('--issuer <issuer>', 'Issuer for JWT authentication')\n    .option('--private-key-path <privateKeyPath>', 'Private key path for JWT assertion')\n    .option('-p, --profile <profile>', 'Profile name')\n    .option('-v --verbose', 'Verbose output')\n    .addOption(\n      new Option('--auth-type <authType>', 'Type of authentication').choices([\n        'basic',\n        'client-credentials',\n        'authorization-code',\n        'jwt-bearer',\n        'token-exchange',\n        'jwt-assertion',\n      ])\n    )\n    .on('option:verbose', () => {\n      process.env.VERBOSE = '1';\n    });\n\n  // Configure CLI\n  index.exitOverride();\n  index.version(MEDPLUM_VERSION);\n  index.configureHelp({ showGlobalOptions: true });\n\n  // Auth commands\n  addSubcommand(index, login);\n  addSubcommand(index, whoami);\n  addSubcommand(index, token);\n\n  // REST commands\n  addSubcommand(index, get);\n  addSubcommand(index, post);\n  addSubcommand(index, patch);\n  addSubcommand(index, put);\n  addSubcommand(index, deleteObject);\n\n  // Project\n  addSubcommand(index, project);\n\n  // Bulk Commands\n  addSubcommand(index, bulk);\n\n  // Bot Commands\n  addSubcommand(index, bot);\n\n  // Agent Commands\n  addSubcommand(index, agent);\n\n  // Deprecated Bot Commands\n  addSubcommand(index, saveBotDeprecate);\n  addSubcommand(index, deployBotDeprecate);\n  addSubcommand(index, createBotDeprecate);\n\n  // Profile Commands\n  addSubcommand(index, profile);\n\n  // AWS commands\n  addSubcommand(index, buildAwsCommand());\n\n  // HL7 commands\n  addSubcommand(index, hl7);\n\n  try {\n    await index.parseAsync(argv);\n  } catch (err) {\n    handleError(err as Error);\n  }\n}\n\nexport function handleError(err: Error | CommanderError): void {\n  let exitCode = 1;\n  let shouldPrint = true;\n  if (err instanceof CommanderError) {\n    // We return if not in verbose mode for CommanderErrors\n    // Since commander.js will already log the error to console for us\n    // Previously we didn't have this guard here and it would always double print errors\n    if (!process.env.VERBOSE) {\n      shouldPrint = false;\n    }\n    exitCode = err.exitCode;\n  }\n  if (exitCode !== 0 && shouldPrint) {\n    writeErrorToStderr(err, !!process.env.VERBOSE);\n    const cause = err.cause;\n    if (process.env.VERBOSE) {\n      if (Array.isArray(cause)) {\n        for (const err of cause as Error[]) {\n          writeErrorToStderr(err, true);\n        }\n      } else if (cause instanceof Error) {\n        writeErrorToStderr(cause, true);\n      }\n    }\n  }\n  process.exit(exitCode);\n}\n\nfunction writeErrorToStderr(err: unknown, verbose = false): void {\n  if (verbose) {\n    console.error(err);\n    return;\n  }\n  if (err instanceof CommanderError) {\n    process.stderr.write(`${normalizeErrorString(err)}\\n`);\n  } else {\n    process.stderr.write(`Error: ${normalizeErrorString(err)}\\n`);\n  }\n}\n\nexport async function run(): Promise<void> {\n  dotenv.config();\n  await main(process.argv);\n}\n\nif (require.main === module) {\n  run().catch((err) => {\n    console.error('Unhandled error:', normalizeErrorString(err));\n    process.exit(1);\n  });\n}\n", "import { ContentType, IssueSeverity, MedplumClient, MedplumClientOptions, WithId, isOk, isUUID } from '@medplum/core';\nimport { Agent, Bundle, OperationOutcome, Parameters, ParametersParameter, Reference } from '@medplum/fhirtypes';\nimport { Option } from 'commander';\nimport { createMedplumClient } from './util/client';\nimport { MedplumCommand, addSubcommand } from './utils';\n\nexport type ValidIdsOrCriteria = { type: 'ids'; ids: string[] } | { type: 'criteria'; criteria: string };\n\nexport type ParsedParametersMap<R extends string[], O extends string[]> = Record<R[number], string> &\n  Record<O[number], string | undefined>;\n\nexport type ParamNames<R extends string[], O extends string[] = []> = {\n  required: R;\n  optional?: O;\n};\n\nexport type AgentBulkOpResponse<T extends Parameters | OperationOutcome = Parameters | OperationOutcome> = {\n  agent: WithId<Agent>;\n  result: T;\n};\n\nexport type CallAgentBulkOperationArgs<T extends Record<string, string>, R extends Parameters | OperationOutcome> = {\n  operation: string;\n  agentIds: string[];\n  options: MedplumClientOptions & { criteria: string; output?: 'json' };\n  parseSuccessfulResponse: (response: AgentBulkOpResponse<R>) => T;\n};\n\nexport type FailedRow = {\n  id: string;\n  name: string;\n  severity: IssueSeverity;\n  code: string;\n  details: string;\n};\n\nexport type StatusRow = {\n  id: string;\n  name: string;\n  enabledStatus: string;\n  connectionStatus: string;\n  version: string;\n  statusLastUpdated: string;\n};\n\nconst agentStatusCommand = new MedplumCommand('status').aliases(['info', 'list', 'ls']);\nconst agentPingCommand = new MedplumCommand('ping');\nconst agentPushCommand = new MedplumCommand('push');\nconst agentReloadConfigCommand = new MedplumCommand('reload-config');\nconst agentUpgradeCommand = new MedplumCommand('upgrade');\n\nexport const agent = new MedplumCommand('agent');\naddSubcommand(agent, agentStatusCommand);\naddSubcommand(agent, agentPingCommand);\naddSubcommand(agent, agentPushCommand);\naddSubcommand(agent, agentReloadConfigCommand);\naddSubcommand(agent, agentUpgradeCommand);\n\nagentStatusCommand\n  .description('Get the status of a specified agent')\n  .argument('[agentIds...]', 'The ID(s) of the agent(s) to get the status of')\n  .option(\n    '--criteria <criteria>',\n    'An optional FHIR search criteria to resolve the agent to get the status of. Mutually exclusive with [agentIds...] arg'\n  )\n  .addOption(\n    new Option('--output <format>', 'An optional output format, defaults to table')\n      .choices(['table', 'json'])\n      .default('table')\n  )\n  .action(async (agentIds, options) => {\n    await callAgentBulkOperation({\n      operation: '$bulk-status',\n      agentIds,\n      options,\n      parseSuccessfulResponse: (response: AgentBulkOpResponse<Parameters>) => {\n        const statusEntry = parseParameterValues(response.result, {\n          required: ['status', 'version'],\n          optional: ['lastUpdated'],\n        });\n\n        return {\n          id: response.agent.id,\n          name: response.agent.name,\n          enabledStatus: response.agent.status,\n          version: statusEntry.version,\n          connectionStatus: statusEntry.status,\n          statusLastUpdated: statusEntry.lastUpdated ?? 'N/A',\n        } satisfies StatusRow;\n      },\n    });\n  });\n\nagentPingCommand\n  .description('Ping a host from a specified agent')\n  .argument('<ipOrDomain>', 'The IPv4 address or domain name to ping')\n  .argument(\n    '[agentId]',\n    'Conditionally optional ID of the agent to ping from. Mutually exclusive with --criteria <criteria> option'\n  )\n  .option('--count <count>', 'An optional amount of pings to issue before returning results', '1')\n  .option(\n    '--criteria <criteria>',\n    'An optional FHIR search criteria to resolve the agent to ping from. Mutually exclusive with [agentId] arg'\n  )\n  .action(async (ipOrDomain, agentId, options) => {\n    const medplum = await createMedplumClient(options);\n    const agentRef = await resolveAgentReference(medplum, agentId, options);\n\n    const count = Number.parseInt(options.count, 10);\n    if (Number.isNaN(count)) {\n      throw new Error('--count <count> must be an integer if specified');\n    }\n\n    try {\n      const pingResult = (await medplum.pushToAgent(agentRef, ipOrDomain, `PING ${count}`, ContentType.PING, true, {\n        maxRetries: 0,\n      })) as string;\n      console.info(pingResult);\n    } catch (err) {\n      throw new Error('Unexpected response from agent while pinging', { cause: err });\n    }\n  });\n\nagentPushCommand\n  .description('Push a message to a target device via a specified agent')\n  .argument('<deviceId>', 'The ID of the device to push the message to')\n  .argument('<message>', 'The message to send to the destination device')\n  .argument(\n    '[agentId]',\n    'Conditionally optional ID of the agent to send the message from. Mutually exclusive with --criteria <criteria> option'\n  )\n  .option('--content-type <contentType>', 'The content type of the message', ContentType.HL7_V2)\n  .option('--no-wait', 'Tells the server not to wait for a response from the destination device')\n  .option(\n    '--criteria <criteria>',\n    'An optional FHIR search criteria to resolve the agent to ping from. Mutually exclusive with [agentId] arg'\n  )\n  .action(async (deviceId, message, agentId, options) => {\n    const medplum = await createMedplumClient(options);\n    const agentRef = await resolveAgentReference(medplum, agentId, options);\n\n    let pushResult: string;\n    try {\n      pushResult = (await medplum.pushToAgent(\n        agentRef,\n        { reference: `Device/${deviceId}` },\n        message,\n        options.contentType,\n        options.wait !== false,\n        { maxRetries: 0 }\n      )) as string;\n    } catch (err) {\n      throw new Error('Unexpected response from agent while pushing message to agent', { cause: err });\n    }\n\n    console.info(pushResult);\n  });\n\nagentReloadConfigCommand\n  .description('Reload the config for the specified agent(s)')\n  .argument(\n    '[agentIds...]',\n    'The ID(s) of the agent(s) for which the config should be reloaded. Mutually exclusive with --criteria <criteria> flag'\n  )\n  .option(\n    '--criteria <criteria>',\n    'An optional FHIR search criteria to resolve the agent(s) for which to notify to reload their config. Mutually exclusive with [agentIds...] arg'\n  )\n  .addOption(\n    new Option('--output <format>', 'An optional output format, defaults to table')\n      .choices(['table', 'json'])\n      .default('table')\n  )\n  .action(async (agentIds, options) => {\n    await callAgentBulkOperation({\n      operation: '$reload-config',\n      agentIds,\n      options,\n      parseSuccessfulResponse: (response: AgentBulkOpResponse<OperationOutcome>) => {\n        return {\n          id: response.agent.id,\n          name: response.agent.name,\n        };\n      },\n    });\n  });\n\nagentUpgradeCommand\n  .description('Upgrade the version for the specified agent(s)')\n  .argument(\n    '[agentIds...]',\n    'The ID(s) of the agent(s) that should be upgraded. Mutually exclusive with --criteria <criteria> flag'\n  )\n  .option(\n    '--criteria <criteria>',\n    'An optional FHIR search criteria to resolve the agent(s) to upgrade. Mutually exclusive with [agentIds...] arg'\n  )\n  .option(\n    '--version <version>',\n    'An optional version to upgrade to. Defaults to the latest version if flag not included'\n  )\n  .addOption(\n    new Option('--output <format>', 'An optional output format, defaults to table')\n      .choices(['table', 'json'])\n      .default('table')\n  )\n  .action(async (agentIds, options) => {\n    await callAgentBulkOperation({\n      operation: '$upgrade',\n      agentIds,\n      options,\n      parseSuccessfulResponse: (response: AgentBulkOpResponse<OperationOutcome>) => {\n        return {\n          id: response.agent.id,\n          name: response.agent.name,\n          version: options.version ?? 'latest',\n        };\n      },\n    });\n  });\n\nexport async function callAgentBulkOperation<\n  T extends Record<string, string>,\n  R extends Parameters | OperationOutcome,\n>({ operation, agentIds, options, parseSuccessfulResponse }: CallAgentBulkOperationArgs<T, R>): Promise<void> {\n  const normalized = parseEitherIdsOrCriteria(agentIds, options);\n  const medplum = await createMedplumClient(options);\n  const usedCriteria = normalized.type === 'criteria' ? normalized.criteria : `Agent?_id=${normalized.ids.join(',')}`;\n  const searchParams = new URLSearchParams(usedCriteria.split('?')[1]);\n\n  let result: Bundle<Parameters> | Parameters | OperationOutcome;\n  try {\n    const url = medplum.fhirUrl('Agent', operation);\n    url.search = searchParams.toString();\n    result = await medplum.get(url, {\n      cache: 'reload',\n    });\n  } catch (err) {\n    throw new Error(`Operation '${operation}' failed`, { cause: err });\n  }\n\n  if (options.output === 'json') {\n    console.info(JSON.stringify(result, null, 2));\n    return;\n  }\n\n  const successfulResponses = [] as AgentBulkOpResponse<R>[];\n  const failedResponses = [] as AgentBulkOpResponse<OperationOutcome>[];\n\n  switch (result.resourceType) {\n    case 'Bundle': {\n      const responses = parseAgentBulkOpBundle(result);\n      for (const response of responses) {\n        if (response.result.resourceType === 'Parameters' || isOk(response.result)) {\n          successfulResponses.push(response as AgentBulkOpResponse<R>);\n        } else {\n          failedResponses.push(response as AgentBulkOpResponse<OperationOutcome>);\n        }\n      }\n      break;\n    }\n    case 'Parameters':\n    case 'OperationOutcome': {\n      const agent = await medplum.searchOne('Agent', searchParams, { cache: 'reload' });\n      if (!agent) {\n        throw new Error('Agent not found');\n      }\n      if (result.resourceType === 'Parameters') {\n        successfulResponses.push({ agent, result } as AgentBulkOpResponse<R>);\n      } else {\n        failedResponses.push({ agent, result });\n      }\n      break;\n    }\n    default:\n      throw new Error(`Invalid result received for '${operation}' operation: ${JSON.stringify(result)}`);\n  }\n\n  const successfulRows = [] as T[];\n  for (const response of successfulResponses) {\n    const row = parseSuccessfulResponse(response);\n    successfulRows.push(row);\n  }\n\n  const failedRows = [] as FailedRow[];\n  for (const response of failedResponses) {\n    const outcome = response.result;\n    const issue = outcome.issue?.[0];\n    const row = {\n      id: response.agent.id,\n      name: response.agent.name,\n      severity: issue.severity,\n      code: issue.code,\n      details: issue.details?.text ?? 'No details to show',\n    } satisfies FailedRow;\n    failedRows.push(row);\n  }\n\n  console.info(`\\n${successfulRows.length} successful response(s):\\n`);\n  console.table(successfulRows.length ? successfulRows : 'No successful responses received');\n  console.info();\n\n  if (failedRows.length) {\n    console.info(`${failedRows.length} failed response(s):`);\n    console.info();\n    console.table(failedRows);\n  }\n}\n\nexport async function resolveAgentReference(\n  medplum: MedplumClient,\n  agentId: string | undefined,\n  options: Record<string, string>\n): Promise<Reference<Agent>> {\n  if (!(agentId || options.criteria)) {\n    throw new Error('This command requires either an [agentId] or a --criteria <criteria> flag');\n  }\n  if (agentId && options.criteria) {\n    throw new Error(\n      'Ambiguous arguments and options combination; [agentId] arg and --criteria <criteria> flag are mutually exclusive'\n    );\n  }\n\n  let usedId: string;\n  if (agentId) {\n    usedId = agentId;\n  } else {\n    assertValidAgentCriteria(options.criteria);\n    const result = await medplum.search('Agent', `${options.criteria.split('?')[1]}&_count=2`);\n    if (!result?.entry?.length) {\n      throw new Error('Could not find an agent matching the provided criteria');\n    }\n    if (result.entry.length !== 1) {\n      throw new Error(\n        'Found more than one agent matching this criteria. This operation requires the criteria to resolve to exactly one agent'\n      );\n    }\n    usedId = result.entry[0].resource?.id as string;\n  }\n\n  return { reference: `Agent/${usedId}` };\n}\n\nexport function parseAgentBulkOpBundle(bundle: Bundle<Parameters>): AgentBulkOpResponse[] {\n  const responses = [];\n  for (const entry of bundle.entry ?? []) {\n    if (!entry.resource) {\n      throw new Error('No Parameter resource found in entry');\n    }\n    responses.push(parseAgentBulkOpParameters(entry.resource));\n  }\n  return responses;\n}\n\nexport function parseAgentBulkOpParameters(params: Parameters): AgentBulkOpResponse {\n  const agent = params.parameter?.find((p) => p.name === 'agent')?.resource as WithId<Agent>;\n  if (!agent) {\n    throw new Error(\"Agent bulk operation response missing 'agent'\");\n  }\n  if (agent.resourceType !== 'Agent') {\n    throw new Error(`Agent bulk operation returned 'agent' with type '${agent.resourceType}'`);\n  }\n  const result = params.parameter?.find((p) => p.name === 'result')?.resource;\n  if (!result) {\n    throw new Error(\"Agent bulk operation response missing result'\");\n  }\n  if (!(result.resourceType === 'Parameters' || result.resourceType === 'OperationOutcome')) {\n    throw new Error(`Agent bulk operation returned 'result' with type '${result.resourceType}'`);\n  }\n  return { agent, result };\n}\n\nexport function parseParameterValues<const R extends string[], const O extends string[] = []>(\n  params: Parameters,\n  paramNames: ParamNames<R, O>\n): ParsedParametersMap<R, O> {\n  const map = {} as ParsedParametersMap<R, O>;\n  const requiredParams = paramNames.required;\n  const optionalParams = paramNames.optional;\n\n  for (const paramName of requiredParams) {\n    const paramsParam = params.parameter?.find((p) => p.name === paramName);\n    if (!paramsParam) {\n      throw new Error(`Failed to find parameter '${paramName}'`);\n    }\n    let valueProp: string | undefined;\n    for (const prop in paramsParam) {\n      // This technically could lead to parsing invalid values (ie. valueAbc123) but for now we can pretend this always works\n      if (prop.startsWith('value')) {\n        if (valueProp) {\n          throw new Error(`Found multiple values for parameter '${paramName}'`);\n        }\n        valueProp = prop;\n      }\n    }\n    if (!valueProp) {\n      throw new Error(`Failed to find a value for parameter '${paramName}'`);\n    }\n\n    // @ts-expect-error ParsedParameterMap expects key to be T[number], which it is, but unable to be inferred in for-of loop\n    map[paramName] = paramsParam[valueProp] as string;\n  }\n\n  if (optionalParams?.length) {\n    for (const paramName of optionalParams) {\n      const paramsParam = params.parameter?.find((p) => p.name === paramName);\n      if (!paramsParam) {\n        continue;\n      }\n      const value = extractValueFromParametersParameter(paramName, paramsParam);\n      // @ts-expect-error ParsedParameterMap expects key to be T[number], which it is, but unable to be inferred in for-of loop\n      map[paramName] = value;\n    }\n  }\n\n  return map;\n}\n\nexport function extractValueFromParametersParameter(paramName: string, paramsParam: ParametersParameter): string {\n  let valueProp: string | undefined;\n  for (const prop in paramsParam) {\n    // This technically could lead to parsing invalid values (ie. valueAbc123) but for now we can pretend this always works\n    if (prop.startsWith('value')) {\n      if (valueProp) {\n        throw new Error(`Found multiple values for parameter '${paramName}'`);\n      }\n      valueProp = prop;\n    }\n  }\n  if (!valueProp) {\n    throw new Error(`Failed to find a value for parameter '${paramName}'`);\n  }\n  // @ts-expect-error valueProp is any string but it should only be choice-of-type `value[x]`\n  return paramsParam[valueProp] as string;\n}\n\nexport function parseEitherIdsOrCriteria(agentIds: string[], options: { criteria: string }): ValidIdsOrCriteria {\n  if (!Array.isArray(agentIds)) {\n    throw new Error('Invalid agent IDs array');\n  }\n  if (agentIds.length) {\n    // Check that options.criteria is not defined\n    if (options.criteria) {\n      throw new Error(\n        'Ambiguous arguments and options combination; [agentIds...] arg and --criteria <criteria> flag are mutually exclusive'\n      );\n    }\n    for (const id of agentIds) {\n      if (!isUUID(id)) {\n        throw new Error(`Input '${id}' is not a valid agentId`);\n      }\n    }\n    return { type: 'ids', ids: agentIds };\n  }\n  if (options.criteria) {\n    assertValidAgentCriteria(options.criteria);\n    return { type: 'criteria', criteria: options.criteria };\n  }\n\n  throw new Error('Either an [agentId...] arg or a --criteria <criteria> flag is required');\n}\n\nfunction assertValidAgentCriteria(criteria: string): void {\n  const invalidCriteriaMsg =\n    \"Criteria must be formatted as a string containing the resource type (Agent) followed by a '?' and valid URL search query params, eg. `Agent?name=Test Agent`\";\n  if (typeof criteria !== 'string') {\n    throw new Error(invalidCriteriaMsg);\n  }\n  const [resourceType, queryStr] = criteria.split('?');\n  if (resourceType !== 'Agent' || !queryStr) {\n    throw new Error(invalidCriteriaMsg);\n  }\n  try {\n    // eslint-disable-next-line no-new\n    new URLSearchParams(queryStr);\n  } catch (err) {\n    throw new Error(invalidCriteriaMsg, { cause: err });\n  }\n  if (!queryStr.includes('=')) {\n    throw new Error(invalidCriteriaMsg, { cause: new Error('Query string lacks at least one `=`') });\n  }\n}\n", "import { MedplumClient, MedplumClientOptions } from '@medplum/core';\nimport { FileSystemStorage } from '../storage';\nimport { Profile } from '../utils';\n\nexport async function createMedplumClient(\n  options: MedplumClientOptions & { profile?: string },\n  setupCredentials = true\n): Promise<MedplumClient> {\n  const profileName = options.profile ?? 'default';\n\n  const storage = new FileSystemStorage(profileName);\n  const profile = storage.getObject('options') as Profile;\n  if (profileName !== 'default' && !profile) {\n    throw new Error(`Profile \"${profileName}\" does not exist`);\n  }\n\n  const { baseUrl, fhirUrlPath, accessToken, tokenUrl, authorizeUrl, clientId, clientSecret } = getClientValues(\n    options,\n    storage\n  );\n  const fetchApi = options.fetch ?? fetch;\n  const medplumClient = new MedplumClient({\n    fetch: fetchApi,\n    baseUrl,\n    tokenUrl,\n    fhirUrlPath,\n    authorizeUrl,\n    storage,\n    onUnauthenticated,\n    verbose: options.verbose,\n  });\n\n  // In most commands, we want to automatically set up credentials.\n  // However, in some cases such as \"login\", we don't want to do that.\n  // Setup credentials if the user does not explicitly disable it.\n  if (setupCredentials) {\n    if (accessToken) {\n      // If the access token is provided, use it.\n      medplumClient.setAccessToken(accessToken);\n    } else if (clientId && clientSecret) {\n      // If the client ID and secret are provided, use them.\n      medplumClient.setBasicAuth(clientId as string, clientSecret as string);\n      if (profile?.authType !== 'basic') {\n        // Unless the user explicitly specified basic auth, start the client login.\n        await medplumClient.startClientLogin(clientId as string, clientSecret as string);\n      }\n    }\n  }\n\n  return medplumClient;\n}\n\nfunction getClientValues(options: MedplumClientOptions, storage: FileSystemStorage): MedplumClientOptions {\n  const storageOptions = storage.getObject('options') as MedplumClientOptions;\n  const baseUrl =\n    options.baseUrl ?? storageOptions?.baseUrl ?? process.env['MEDPLUM_BASE_URL'] ?? 'https://api.medplum.com/';\n  const fhirUrlPath = options.fhirUrlPath ?? storageOptions?.fhirUrlPath ?? process.env['MEDPLUM_FHIR_URL_PATH'];\n  const accessToken = options.accessToken ?? storageOptions?.accessToken ?? process.env['MEDPLUM_CLIENT_ACCESS_TOKEN'];\n  const tokenUrl = options.tokenUrl ?? storageOptions?.tokenUrl ?? process.env['MEDPLUM_TOKEN_URL'];\n  const authorizeUrl = options.authorizeUrl ?? storageOptions?.authorizeUrl ?? process.env['MEDPLUM_AUTHORIZE_URL'];\n\n  const clientId = options.clientId ?? storageOptions?.clientId ?? process.env['MEDPLUM_CLIENT_ID'];\n  const clientSecret = options.clientSecret ?? storageOptions?.clientSecret ?? process.env['MEDPLUM_CLIENT_SECRET'];\n\n  return { baseUrl, fhirUrlPath, accessToken, tokenUrl, authorizeUrl, clientId, clientSecret };\n}\n\nexport function onUnauthenticated(): void {\n  console.log('Unauthenticated: run `npx medplum login` to sign in');\n}\n", "import { ClientStorage } from '@medplum/core';\nimport { existsSync, mkdirSync, readFileSync, writeFileSync } from 'node:fs';\nimport { homedir } from 'node:os';\nimport { resolve } from 'node:path';\n\nexport class FileSystemStorage extends ClientStorage {\n  private readonly dirName: string;\n  private readonly fileName: string;\n\n  constructor(profile: string) {\n    super();\n    this.dirName = resolve(homedir(), '.medplum');\n    this.fileName = resolve(this.dirName, profile + '.json');\n  }\n\n  clear(): void {\n    this.writeFile({});\n  }\n\n  getString(key: string): string | undefined {\n    return this.readFile()?.[key];\n  }\n\n  setString(key: string, value: string | undefined): void {\n    const data = this.readFile() ?? {};\n    if (value) {\n      data[key] = value;\n    } else {\n      delete data[key];\n    }\n    this.writeFile(data);\n  }\n\n  getObject<T>(key: string): T | undefined {\n    const str = this.getString(key);\n    return str ? (JSON.parse(str) as T) : undefined;\n  }\n\n  setObject<T>(key: string, value: T): void {\n    this.setString(key, value ? JSON.stringify(value) : undefined);\n  }\n\n  private readFile(): Record<string, string> | undefined {\n    if (existsSync(this.fileName)) {\n      return JSON.parse(readFileSync(this.fileName, 'utf8'));\n    }\n    return undefined;\n  }\n\n  private writeFile(data: Record<string, string>): void {\n    if (!existsSync(this.dirName)) {\n      mkdirSync(this.dirName);\n    }\n    writeFileSync(this.fileName, JSON.stringify(data, null, 2), 'utf8');\n  }\n}\n", "import { ContentType, encodeBase64, MedplumClient, WithId } from '@medplum/core';\nimport { Bot, Extension, OperationOutcome } from '@medplum/fhirtypes';\nimport { Command } from 'commander';\nimport { SignJWT } from 'jose';\nimport { createHmac, createPrivateKey, randomBytes } from 'node:crypto';\nimport { existsSync, readFileSync, writeFileSync } from 'node:fs';\nimport { basename, extname, resolve } from 'node:path';\nimport { isPromise } from 'node:util/types';\nimport { extract } from 'tar';\nimport { FileSystemStorage } from './storage';\n\nexport interface MedplumConfig {\n  baseUrl?: string;\n  clientId?: string;\n  googleClientId?: string;\n  recaptchaSiteKey?: string;\n  registerEnabled?: boolean;\n  bots?: MedplumBotConfig[];\n}\n\nexport interface MedplumBotConfig {\n  readonly name: string;\n  readonly id: string;\n  readonly source: string;\n  readonly dist?: string;\n}\n\nexport interface Profile {\n  readonly name?: string;\n  readonly authType?: string;\n  readonly baseUrl?: string;\n  readonly clientId?: string;\n  readonly clientSecret?: string;\n  readonly tokenUrl?: string;\n  readonly authorizeUrl?: string;\n  readonly fhirUrlPath?: string;\n  readonly scope?: string;\n  readonly accessToken?: string;\n  readonly callbackUrl?: string;\n  readonly subject?: string;\n  readonly audience?: string;\n  readonly issuer?: string;\n  readonly privateKeyPath?: string;\n}\n\nexport function prettyPrint(input: unknown): void {\n  console.log(JSON.stringify(input, null, 2));\n}\n\nexport async function saveBot(medplum: MedplumClient, botConfig: MedplumBotConfig, bot: Bot): Promise<void> {\n  const codePath = botConfig.source;\n  const code = readFileContents(codePath);\n  if (!code) {\n    return;\n  }\n\n  console.log('Saving source code...');\n  const sourceCode = await medplum.createAttachment({\n    data: code,\n    filename: basename(codePath),\n    contentType: getCodeContentType(codePath),\n  });\n\n  console.log('Updating bot...');\n  const updateResult = await medplum.updateResource({\n    ...bot,\n    sourceCode,\n  });\n  console.log('Success! New bot version: ' + updateResult.meta?.versionId);\n}\n\nexport async function deployBot(medplum: MedplumClient, botConfig: MedplumBotConfig, bot: WithId<Bot>): Promise<void> {\n  const codePath = botConfig.dist ?? botConfig.source;\n  const code = readFileContents(codePath);\n  if (!code) {\n    return;\n  }\n\n  console.log('Deploying bot...');\n  const deployResult = (await medplum.post(medplum.fhirUrl('Bot', bot.id, '$deploy'), {\n    code,\n    filename: basename(codePath),\n  })) as OperationOutcome;\n  console.log('Deploy result: ' + deployResult.issue?.[0]?.details?.text);\n}\n\nexport async function createBot(\n  medplum: MedplumClient,\n  botName: string,\n  projectId: string,\n  sourceFile: string,\n  distFile: string,\n  runtimeVersion?: string,\n  writeConfig?: boolean\n): Promise<void> {\n  const body = {\n    name: botName,\n    description: '',\n    runtimeVersion,\n  };\n  const newBot = await medplum.post('admin/projects/' + projectId + '/bot', body);\n  const bot = await medplum.readResource('Bot', newBot.id);\n\n  const botConfig = {\n    name: botName,\n    id: newBot.id,\n    source: sourceFile,\n    dist: distFile,\n  };\n\n  await saveBot(medplum, botConfig as MedplumBotConfig, bot);\n  await deployBot(medplum, botConfig as MedplumBotConfig, bot);\n  console.log(`Success! Bot created: ${bot.id}`);\n\n  if (writeConfig) {\n    addBotToConfig(botConfig);\n  }\n}\n\nexport function readBotConfigs(botName: string): MedplumBotConfig[] {\n  const regExBotName = new RegExp('^' + escapeRegex(botName).replace(/\\\\\\*/g, '.*') + '$');\n  const botConfigs = readConfig()?.bots?.filter((b) => regExBotName.test(b.name));\n  if (!botConfigs) {\n    return [];\n  }\n  return botConfigs;\n}\n\n/**\n * Returns the config file name.\n * @param tagName - Optional environment tag name.\n * @param options - Optional command line options.\n * @returns The config file name.\n */\nexport function getConfigFileName(tagName?: string, options?: Record<string, any>): string {\n  if (options?.file) {\n    return options.file;\n  }\n  const parts = ['medplum'];\n  if (tagName) {\n    parts.push(tagName);\n  }\n  parts.push('config');\n  if (options?.server) {\n    parts.push('server');\n  }\n  parts.push('json');\n  return parts.join('.');\n}\n\n/**\n * Writes a config file to disk.\n * @param configFileName - The config file name.\n * @param config - The config file contents.\n */\nexport function writeConfig(configFileName: string, config: Record<string, any>): void {\n  writeFileSync(resolve(configFileName), JSON.stringify(config, undefined, 2), 'utf-8');\n}\n\nexport function readConfig(tagName?: string, options?: { file?: string }): MedplumConfig | undefined {\n  const fileName = getConfigFileName(tagName, options);\n  const content = readFileContents(fileName);\n  if (!content) {\n    return undefined;\n  }\n  return JSON.parse(content);\n}\n\nexport function readServerConfig(tagName?: string): Record<string, string | number> | undefined {\n  const content = readFileContents(getConfigFileName(tagName, { server: true }));\n  if (!content) {\n    return undefined;\n  }\n  return JSON.parse(content);\n}\n\nfunction readFileContents(fileName: string): string {\n  const path = resolve(fileName);\n  if (!existsSync(path)) {\n    return '';\n  }\n  return readFileSync(path, 'utf8');\n}\n\nfunction addBotToConfig(botConfig: MedplumBotConfig): void {\n  const config = readConfig() ?? {};\n  if (!config.bots) {\n    config.bots = [];\n  }\n  config.bots.push(botConfig);\n  writeFileSync('medplum.config.json', JSON.stringify(config, null, 2), 'utf8');\n  console.log(`Bot added to config: ${botConfig.id}`);\n}\n\nfunction escapeRegex(str: string): string {\n  return str.replace(/[/\\-\\\\^$*+?.()|[\\]{}]/g, '\\\\$&');\n}\n\n/**\n * Creates a safe tar extractor that limits the number of files and total size.\n *\n * Expanding archive files without controlling resource consumption is security-sensitive\n *\n * See: https://sonarcloud.io/organizations/medplum/rules?open=typescript%3AS5042&rule_key=typescript%3AS5042\n * @param destinationDir - The destination directory where all files will be extracted.\n * @returns A tar file extractor.\n */\nexport function safeTarExtractor(destinationDir: string): NodeJS.WritableStream {\n  const MAX_FILES = 100;\n  const MAX_SIZE = 10 * 1024 * 1024; // 10 MB\n\n  let fileCount = 0;\n  let totalSize = 0;\n\n  return extract({\n    cwd: destinationDir,\n    filter: (_path, entry) => {\n      fileCount++;\n      if (fileCount > MAX_FILES) {\n        throw new Error('Tar extractor reached max number of files');\n      }\n\n      totalSize += entry.size;\n      if (totalSize > MAX_SIZE) {\n        throw new Error('Tar extractor reached max size');\n      }\n\n      return true;\n    },\n  });\n}\n\nexport function getUnsupportedExtension(): Extension {\n  return {\n    url: 'http://hl7.org/fhir/StructureDefinition/data-absent-reason',\n    valueCode: 'unsupported',\n  };\n}\n\nexport function getCodeContentType(filename: string): string {\n  const ext = extname(filename).toLowerCase();\n  if (['.cjs', '.mjs', '.js'].includes(ext)) {\n    return ContentType.JAVASCRIPT;\n  }\n  if (['.cts', '.mts', '.ts'].includes(ext)) {\n    return ContentType.TYPESCRIPT;\n  }\n  return ContentType.TEXT;\n}\n\nexport function saveProfile(profileName: string, options: Profile): Profile {\n  const storage = new FileSystemStorage(profileName);\n  const optionsObject = { name: profileName, ...options };\n  storage.setObject('options', optionsObject);\n  return optionsObject;\n}\n\nexport function loadProfile(profileName: string): Profile {\n  const storage = new FileSystemStorage(profileName);\n  return storage.getObject('options') as Profile;\n}\n\nexport function profileExists(storage: FileSystemStorage, profile: string): boolean {\n  if (profile === 'default') {\n    return true;\n  }\n  const optionsObject = storage.getObject('options');\n  if (!optionsObject) {\n    return false;\n  }\n  return true;\n}\n\nexport async function jwtBearerLogin(medplum: MedplumClient, profile: Profile): Promise<void> {\n  const header = {\n    typ: 'JWT',\n    alg: 'HS256',\n  };\n\n  const currentTimestamp = Math.floor(Date.now() / 1000);\n  const data = {\n    aud: `${profile.baseUrl}${profile.audience}`,\n    iss: profile.issuer,\n    sub: profile.subject,\n    nbf: currentTimestamp,\n    iat: currentTimestamp,\n    exp: currentTimestamp + 604800, // expiry time is 7 days from time of creation\n  };\n  const encodedHeader = encodeBase64(JSON.stringify(header));\n  const encodedData = encodeBase64(JSON.stringify(data));\n  const token = `${encodedHeader}.${encodedData}`;\n  const signature = createHmac('sha256', profile.clientSecret as string)\n    .update(token)\n    .digest('base64url');\n  const signedToken = `${token}.${signature}`;\n  await medplum.startJwtBearerLogin(profile.clientId as string, signedToken, profile.scope ?? '');\n}\n\nexport async function jwtAssertionLogin(medplum: MedplumClient, profile: Profile): Promise<void> {\n  const privateKey = createPrivateKey(readFileSync(resolve(profile.privateKeyPath as string)));\n  const jwt = await new SignJWT({})\n    .setProtectedHeader({ alg: 'RS384', typ: 'JWT' })\n    .setIssuer(profile.clientId as string)\n    .setSubject(profile.clientId as string)\n    .setAudience(`${profile.baseUrl}${profile.audience}`)\n    .setJti(randomBytes(16).toString('hex'))\n    .setIssuedAt()\n    .setExpirationTime('5m')\n    .sign(privateKey);\n  await medplum.startJwtAssertionLogin(jwt);\n}\n\n/**\n * Attaches the provided subcommand to the provided parent command.\n *\n * We use this rather than directly calling the `addCommand` method on the parent command because we need\n * to modify some additional settings on each command before adding them to the parent.\n *\n * @param command - The parent command.\n * @param subcommand - The command to attach to the provided parent command.\n */\nexport function addSubcommand(command: Command, subcommand: Command): void {\n  subcommand.configureHelp({ showGlobalOptions: true });\n  command.addCommand(subcommand);\n}\n\nexport class MedplumCommand extends Command {\n  action(fn: (...args: any[]) => void | Promise<void>): this {\n    // This is the only way to get both global and local options propagated to all subcommands automatically\n    // Otherwise you have to call `command.optsWithGlobals()` within every function to get merged global and local options\n    const wrappedFn = withMergedOptions(this, fn);\n    // @ts-expect-error Access to hidden member\n    // This is the function that gets called when a command is executed\n    // We overwrite it with the wrapped version\n    super._actionHandler = wrappedFn;\n    return this;\n  }\n\n  /**\n   * We use this method to reset the option state\n   * Which is not cleared between executions of the main function during tests\n   *\n   * This is because all of our subcommands are declared in the global scope\n   *\n   * Rather than re-architect the entire CLI package, I added this to make sure all options are reset between executions of main\n   */\n  resetOptionDefaults(): void {\n    // @ts-expect-error Overriding private field\n    this._optionValues = {};\n    for (const option of this.options) {\n      // So we also set options that default to false\n      // We explicitly check strict equality to undefined\n      if (option.defaultValue !== undefined) {\n        // We use the attributeName since that's the camelCase'd name that is used to access options\n        // @ts-expect-error Overriding private field\n        this._optionValues[option.attributeName()] = option.defaultValue;\n      }\n    }\n  }\n}\n\nexport function withMergedOptions(\n  command: MedplumCommand,\n  fn: ((...args: any[]) => Promise<void>) | ((...args: any[]) => void)\n): (args: any[]) => Promise<void> {\n  // The .action callback takes an extra parameter which is the command or options.\n  return async (args: any[]): Promise<void> => {\n    const expectedArgsCount = command.registeredArguments.length;\n    const actionArgs = args.slice(0, expectedArgsCount);\n    actionArgs[expectedArgsCount] = command.optsWithGlobals();\n    try {\n      const result: Promise<void> | void = fn(...actionArgs);\n      if (isPromise(result)) {\n        await result;\n      }\n    } finally {\n      // We want to always make sure to reset the options to default at the end of each execution,\n      // We do it in a finally block in case the command errors\n      command.resetOptionDefaults();\n    }\n  };\n}\n", "import { <PERSON><PERSON><PERSON> } from 'node:buffer';\nimport { decoder } from '../lib/buffer_utils.js';\nfunction normalize(input) {\n    let encoded = input;\n    if (encoded instanceof Uint8Array) {\n        encoded = decoder.decode(encoded);\n    }\n    return encoded;\n}\nconst encode = (input) => Buffer.from(input).toString('base64url');\nexport const decodeBase64 = (input) => new Uint8Array(Buffer.from(input, 'base64'));\nexport const encodeBase64 = (input) => Buffer.from(input).toString('base64');\nexport { encode };\nexport const decode = (input) => new Uint8Array(Buffer.from(normalize(input), 'base64url'));\n", "import digest from '../runtime/digest.js';\nexport const encoder = new TextEncoder();\nexport const decoder = new TextDecoder();\nconst MAX_INT32 = 2 ** 32;\nexport function concat(...buffers) {\n    const size = buffers.reduce((acc, { length }) => acc + length, 0);\n    const buf = new Uint8Array(size);\n    let i = 0;\n    for (const buffer of buffers) {\n        buf.set(buffer, i);\n        i += buffer.length;\n    }\n    return buf;\n}\nexport function p2s(alg, p2sInput) {\n    return concat(encoder.encode(alg), new Uint8Array([0]), p2sInput);\n}\nfunction writeUInt32BE(buf, value, offset) {\n    if (value < 0 || value >= MAX_INT32) {\n        throw new RangeError(`value must be >= 0 and <= ${MAX_INT32 - 1}. Received ${value}`);\n    }\n    buf.set([value >>> 24, value >>> 16, value >>> 8, value & 0xff], offset);\n}\nexport function uint64be(value) {\n    const high = Math.floor(value / MAX_INT32);\n    const low = value % MAX_INT32;\n    const buf = new Uint8Array(8);\n    writeUInt32BE(buf, high, 0);\n    writeUInt32BE(buf, low, 4);\n    return buf;\n}\nexport function uint32be(value) {\n    const buf = new Uint8Array(4);\n    writeUInt32BE(buf, value);\n    return buf;\n}\nexport function lengthAndInput(input) {\n    return concat(uint32be(input.length), input);\n}\nexport async function concatKdf(secret, bits, value) {\n    const iterations = Math.ceil((bits >> 3) / 32);\n    const res = new Uint8Array(iterations * 32);\n    for (let iter = 0; iter < iterations; iter++) {\n        const buf = new Uint8Array(4 + secret.length + value.length);\n        buf.set(uint32be(iter + 1));\n        buf.set(secret, 4);\n        buf.set(value, 4 + secret.length);\n        res.set(await digest('sha256', buf), iter * 32);\n    }\n    return res.slice(0, bits >> 3);\n}\n", "export class JOSEError extends Error {\n    static code = 'ERR_JOSE_GENERIC';\n    code = 'ERR_JOSE_GENERIC';\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nexport class JWTClaimValidationFailed extends JOSEError {\n    static code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nexport class JWTExpired extends JOSEError {\n    static code = 'ERR_JWT_EXPIRED';\n    code = 'ERR_JWT_EXPIRED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nexport class JOSEAlgNotAllowed extends J<PERSON><PERSON><PERSON>r {\n    static code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n    code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n}\nexport class JOSENotSupported extends JOSEError {\n    static code = 'ERR_JOSE_NOT_SUPPORTED';\n    code = 'ERR_JOSE_NOT_SUPPORTED';\n}\nexport class JWEDecryptionFailed extends JOSEError {\n    static code = 'ERR_JWE_DECRYPTION_FAILED';\n    code = 'ERR_JWE_DECRYPTION_FAILED';\n    constructor(message = 'decryption operation failed', options) {\n        super(message, options);\n    }\n}\nexport class JWEInvalid extends JOSEError {\n    static code = 'ERR_JWE_INVALID';\n    code = 'ERR_JWE_INVALID';\n}\nexport class JWSInvalid extends JOSEError {\n    static code = 'ERR_JWS_INVALID';\n    code = 'ERR_JWS_INVALID';\n}\nexport class JWTInvalid extends JOSEError {\n    static code = 'ERR_JWT_INVALID';\n    code = 'ERR_JWT_INVALID';\n}\nexport class JWKInvalid extends JOSEError {\n    static code = 'ERR_JWK_INVALID';\n    code = 'ERR_JWK_INVALID';\n}\nexport class JWKSInvalid extends JOSEError {\n    static code = 'ERR_JWKS_INVALID';\n    code = 'ERR_JWKS_INVALID';\n}\nexport class JWKSNoMatchingKey extends JOSEError {\n    static code = 'ERR_JWKS_NO_MATCHING_KEY';\n    code = 'ERR_JWKS_NO_MATCHING_KEY';\n    constructor(message = 'no applicable key found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nexport class JWKSMultipleMatchingKeys extends JOSEError {\n    [Symbol.asyncIterator];\n    static code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    constructor(message = 'multiple matching keys found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nexport class JWKSTimeout extends JOSEError {\n    static code = 'ERR_JWKS_TIMEOUT';\n    code = 'ERR_JWKS_TIMEOUT';\n    constructor(message = 'request timed out', options) {\n        super(message, options);\n    }\n}\nexport class JWSSignatureVerificationFailed extends JOSEError {\n    static code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    constructor(message = 'signature verification failed', options) {\n        super(message, options);\n    }\n}\n", "import * as util from 'node:util';\nexport default (obj) => util.types.isKeyObject(obj);\n", "import * as crypto from 'node:crypto';\nimport * as util from 'node:util';\nconst webcrypto = crypto.webcrypto;\nexport default webcrypto;\nexport const isCryptoKey = (key) => util.types.isCryptoKey(key);\n", "function unusable(name, prop = 'algorithm.name') {\n    return new TypeError(`CryptoKey does not support this operation, its ${prop} must be ${name}`);\n}\nfunction isAlgorithm(algorithm, name) {\n    return algorithm.name === name;\n}\nfunction getHashLength(hash) {\n    return parseInt(hash.name.slice(4), 10);\n}\nfunction getNamedCurve(alg) {\n    switch (alg) {\n        case 'ES256':\n            return 'P-256';\n        case 'ES384':\n            return 'P-384';\n        case 'ES512':\n            return 'P-521';\n        default:\n            throw new Error('unreachable');\n    }\n}\nfunction checkUsage(key, usages) {\n    if (usages.length && !usages.some((expected) => key.usages.includes(expected))) {\n        let msg = 'CryptoKey does not support this operation, its usages must include ';\n        if (usages.length > 2) {\n            const last = usages.pop();\n            msg += `one of ${usages.join(', ')}, or ${last}.`;\n        }\n        else if (usages.length === 2) {\n            msg += `one of ${usages[0]} or ${usages[1]}.`;\n        }\n        else {\n            msg += `${usages[0]}.`;\n        }\n        throw new TypeError(msg);\n    }\n}\nexport function checkSigCryptoKey(key, alg, ...usages) {\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512': {\n            if (!isAlgorithm(key.algorithm, 'HMAC'))\n                throw unusable('HMAC');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'RS256':\n        case 'RS384':\n        case 'RS512': {\n            if (!isAlgorithm(key.algorithm, 'RSASSA-PKCS1-v1_5'))\n                throw unusable('RSASSA-PKCS1-v1_5');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'PS256':\n        case 'PS384':\n        case 'PS512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-PSS'))\n                throw unusable('RSA-PSS');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'EdDSA': {\n            if (key.algorithm.name !== 'Ed25519' && key.algorithm.name !== 'Ed448') {\n                throw unusable('Ed25519 or Ed448');\n            }\n            break;\n        }\n        case 'Ed25519': {\n            if (!isAlgorithm(key.algorithm, 'Ed25519'))\n                throw unusable('Ed25519');\n            break;\n        }\n        case 'ES256':\n        case 'ES384':\n        case 'ES512': {\n            if (!isAlgorithm(key.algorithm, 'ECDSA'))\n                throw unusable('ECDSA');\n            const expected = getNamedCurve(alg);\n            const actual = key.algorithm.namedCurve;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.namedCurve');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usages);\n}\nexport function checkEncCryptoKey(key, alg, ...usages) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM': {\n            if (!isAlgorithm(key.algorithm, 'AES-GCM'))\n                throw unusable('AES-GCM');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (!isAlgorithm(key.algorithm, 'AES-KW'))\n                throw unusable('AES-KW');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'ECDH': {\n            switch (key.algorithm.name) {\n                case 'ECDH':\n                case 'X25519':\n                case 'X448':\n                    break;\n                default:\n                    throw unusable('ECDH, X25519, or X448');\n            }\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW':\n            if (!isAlgorithm(key.algorithm, 'PBKDF2'))\n                throw unusable('PBKDF2');\n            break;\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-OAEP'))\n                throw unusable('RSA-OAEP');\n            const expected = parseInt(alg.slice(9), 10) || 1;\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usages);\n}\n", "function message(msg, actual, ...types) {\n    types = types.filter(<PERSON><PERSON><PERSON>);\n    if (types.length > 2) {\n        const last = types.pop();\n        msg += `one of type ${types.join(', ')}, or ${last}.`;\n    }\n    else if (types.length === 2) {\n        msg += `one of type ${types[0]} or ${types[1]}.`;\n    }\n    else {\n        msg += `of type ${types[0]}.`;\n    }\n    if (actual == null) {\n        msg += ` Received ${actual}`;\n    }\n    else if (typeof actual === 'function' && actual.name) {\n        msg += ` Received function ${actual.name}`;\n    }\n    else if (typeof actual === 'object' && actual != null) {\n        if (actual.constructor?.name) {\n            msg += ` Received an instance of ${actual.constructor.name}`;\n        }\n    }\n    return msg;\n}\nexport default (actual, ...types) => {\n    return message('Key must be ', actual, ...types);\n};\nexport function withAlg(alg, actual, ...types) {\n    return message(`Key for the ${alg} algorithm must be `, actual, ...types);\n}\n", "import webcrypto, { isCryptoKey } from './webcrypto.js';\nimport isKeyObject from './is_key_object.js';\nexport default (key) => isKeyObject(key) || isCryptoKey(key);\nconst types = ['KeyObject'];\nif (globalThis.CryptoKey || webcrypto?.CryptoKey) {\n    types.push('CryptoKey');\n}\nexport { types };\n", "const isDisjoint = (...headers) => {\n    const sources = headers.filter(Boolean);\n    if (sources.length === 0 || sources.length === 1) {\n        return true;\n    }\n    let acc;\n    for (const header of sources) {\n        const parameters = Object.keys(header);\n        if (!acc || acc.size === 0) {\n            acc = new Set(parameters);\n            continue;\n        }\n        for (const parameter of parameters) {\n            if (acc.has(parameter)) {\n                return false;\n            }\n            acc.add(parameter);\n        }\n    }\n    return true;\n};\nexport default isDisjoint;\n", "function isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\nexport default function isObject(input) {\n    if (!isObjectLike(input) || Object.prototype.toString.call(input) !== '[object Object]') {\n        return false;\n    }\n    if (Object.getPrototypeOf(input) === null) {\n        return true;\n    }\n    let proto = input;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(input) === proto;\n}\n", "import { KeyObject } from 'node:crypto';\nimport { JOSENotSupported } from '../util/errors.js';\nimport { isCryptoKey } from './webcrypto.js';\nimport isKeyObject from './is_key_object.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport { types } from './is_key_like.js';\nimport { isJWK } from '../lib/is_jwk.js';\nexport const weakMap = new WeakMap();\nconst namedCurveToJOSE = (namedCurve) => {\n    switch (namedCurve) {\n        case 'prime256v1':\n            return 'P-256';\n        case 'secp384r1':\n            return 'P-384';\n        case 'secp521r1':\n            return 'P-521';\n        case 'secp256k1':\n            return 'secp256k1';\n        default:\n            throw new JOSENotSupported('Unsupported key curve for this operation');\n    }\n};\nconst getNamedCurve = (kee, raw) => {\n    let key;\n    if (isCryptoKey(kee)) {\n        key = KeyObject.from(kee);\n    }\n    else if (isKeyObject(kee)) {\n        key = kee;\n    }\n    else if (isJWK(kee)) {\n        return kee.crv;\n    }\n    else {\n        throw new TypeError(invalidKeyInput(kee, ...types));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError('only \"private\" or \"public\" type keys can be used for this operation');\n    }\n    switch (key.asymmetricKeyType) {\n        case 'ed25519':\n        case 'ed448':\n            return `Ed${key.asymmetricKeyType.slice(2)}`;\n        case 'x25519':\n        case 'x448':\n            return `X${key.asymmetricKeyType.slice(1)}`;\n        case 'ec': {\n            const namedCurve = key.asymmetricKeyDetails.namedCurve;\n            if (raw) {\n                return namedCurve;\n            }\n            return namedCurveToJOSE(namedCurve);\n        }\n        default:\n            throw new TypeError('Invalid asymmetric key type for this operation');\n    }\n};\nexport default getNamedCurve;\n", "import isObject from './is_object.js';\nexport function isJWK(key) {\n    return isObject(key) && typeof key.kty === 'string';\n}\nexport function isPrivateJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'string';\n}\nexport function isPublicJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'undefined';\n}\nexport function isSecretJWK(key) {\n    return isJWK(key) && key.kty === 'oct' && typeof key.k === 'string';\n}\n", "import { KeyObject } from 'node:crypto';\nexport default (key, alg) => {\n    let modulusLength;\n    try {\n        if (key instanceof KeyObject) {\n            modulusLength = key.asymmetricKeyDetails?.modulusLength;\n        }\n        else {\n            modulusLength = Buffer.from(key.n, 'base64url').byteLength << 3;\n        }\n    }\n    catch { }\n    if (typeof modulusLength !== 'number' || modulusLength < 2048) {\n        throw new TypeError(`${alg} requires key modulusLength to be 2048 bits or larger`);\n    }\n};\n", "import { withAlg as invalidKeyInput } from './invalid_key_input.js';\nimport isKeyLike, { types } from '../runtime/is_key_like.js';\nimport * as jwk from './is_jwk.js';\nconst tag = (key) => key?.[Symbol.toStringTag];\nconst jwkMatchesOp = (alg, key, usage) => {\n    if (key.use !== undefined && key.use !== 'sig') {\n        throw new TypeError('Invalid key for this operation, when present its use must be sig');\n    }\n    if (key.key_ops !== undefined && key.key_ops.includes?.(usage) !== true) {\n        throw new TypeError(`Invalid key for this operation, when present its key_ops must include ${usage}`);\n    }\n    if (key.alg !== undefined && key.alg !== alg) {\n        throw new TypeError(`Invalid key for this operation, when present its alg must be ${alg}`);\n    }\n    return true;\n};\nconst symmetricTypeCheck = (alg, key, usage, allowJwk) => {\n    if (key instanceof Uint8Array)\n        return;\n    if (allowJwk && jwk.isJWK(key)) {\n        if (jwk.isSecretJWK(key) && jwkMatchesOp(alg, key, usage))\n            return;\n        throw new TypeError(`JSON Web Key for symmetric algorithms must have JWK \"kty\" (Key Type) equal to \"oct\" and the JWK \"k\" (Key Value) present`);\n    }\n    if (!isKeyLike(key)) {\n        throw new TypeError(invalidKeyInput(alg, key, ...types, 'Uint8Array', allowJwk ? 'JSON Web Key' : null));\n    }\n    if (key.type !== 'secret') {\n        throw new TypeError(`${tag(key)} instances for symmetric algorithms must be of type \"secret\"`);\n    }\n};\nconst asymmetricTypeCheck = (alg, key, usage, allowJwk) => {\n    if (allowJwk && jwk.isJWK(key)) {\n        switch (usage) {\n            case 'sign':\n                if (jwk.isPrivateJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a private JWK`);\n            case 'verify':\n                if (jwk.isPublicJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a public JWK`);\n        }\n    }\n    if (!isKeyLike(key)) {\n        throw new TypeError(invalidKeyInput(alg, key, ...types, allowJwk ? 'JSON Web Key' : null));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithms must not be of type \"secret\"`);\n    }\n    if (usage === 'sign' && key.type === 'public') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm signing must be of type \"private\"`);\n    }\n    if (usage === 'decrypt' && key.type === 'public') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm decryption must be of type \"private\"`);\n    }\n    if (key.algorithm && usage === 'verify' && key.type === 'private') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm verifying must be of type \"public\"`);\n    }\n    if (key.algorithm && usage === 'encrypt' && key.type === 'private') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm encryption must be of type \"public\"`);\n    }\n};\nfunction checkKeyType(allowJwk, alg, key, usage) {\n    const symmetric = alg.startsWith('HS') ||\n        alg === 'dir' ||\n        alg.startsWith('PBES2') ||\n        /^A\\d{3}(?:GCM)?KW$/.test(alg);\n    if (symmetric) {\n        symmetricTypeCheck(alg, key, usage, allowJwk);\n    }\n    else {\n        asymmetricTypeCheck(alg, key, usage, allowJwk);\n    }\n}\nexport default checkKeyType.bind(undefined, false);\nexport const checkKeyTypeWithJwk = checkKeyType.bind(undefined, true);\n", "import { JOSENotSupported } from '../util/errors.js';\nfunction validateCrit(Err, recognizedDefault, recognizedOption, protectedHeader, joseHeader) {\n    if (joseHeader.crit !== undefined && protectedHeader?.crit === undefined) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be integrity protected');\n    }\n    if (!protectedHeader || protectedHeader.crit === undefined) {\n        return new Set();\n    }\n    if (!Array.isArray(protectedHeader.crit) ||\n        protectedHeader.crit.length === 0 ||\n        protectedHeader.crit.some((input) => typeof input !== 'string' || input.length === 0)) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be an array of non-empty strings when present');\n    }\n    let recognized;\n    if (recognizedOption !== undefined) {\n        recognized = new Map([...Object.entries(recognizedOption), ...recognizedDefault.entries()]);\n    }\n    else {\n        recognized = recognizedDefault;\n    }\n    for (const parameter of protectedHeader.crit) {\n        if (!recognized.has(parameter)) {\n            throw new JOSENotSupported(`Extension Header Parameter \"${parameter}\" is not recognized`);\n        }\n        if (joseHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" is missing`);\n        }\n        if (recognized.get(parameter) && protectedHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" MUST be integrity protected`);\n        }\n    }\n    return new Set(protectedHeader.crit);\n}\nexport default validateCrit;\n", "import { JOSENotSupported } from '../util/errors.js';\nexport default function dsaDigest(alg) {\n    switch (alg) {\n        case 'PS256':\n        case 'RS256':\n        case 'ES256':\n        case 'ES256K':\n            return 'sha256';\n        case 'PS384':\n        case 'RS384':\n        case 'ES384':\n            return 'sha384';\n        case 'PS512':\n        case 'RS512':\n        case 'ES512':\n            return 'sha512';\n        case 'Ed25519':\n        case 'EdDSA':\n            return undefined;\n        default:\n            throw new JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n}\n", "import { constants, KeyObject } from 'node:crypto';\nimport getNamedCurve from './get_named_curve.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport checkKeyLength from './check_key_length.js';\nconst ecCurveAlgMap = new Map([\n    ['ES256', 'P-256'],\n    ['ES256K', 'secp256k1'],\n    ['ES384', 'P-384'],\n    ['ES512', 'P-521'],\n]);\nexport default function keyForCrypto(alg, key) {\n    let asymmetricKeyType;\n    let asymmetricKeyDetails;\n    let isJWK;\n    if (key instanceof KeyObject) {\n        asymmetricKeyType = key.asymmetricKeyType;\n        asymmetricKeyDetails = key.asymmetricKeyDetails;\n    }\n    else {\n        isJWK = true;\n        switch (key.kty) {\n            case 'RSA':\n                asymmetricKeyType = 'rsa';\n                break;\n            case 'EC':\n                asymmetricKeyType = 'ec';\n                break;\n            case 'OKP': {\n                if (key.crv === 'Ed25519') {\n                    asymmetricKeyType = 'ed25519';\n                    break;\n                }\n                if (key.crv === 'Ed448') {\n                    asymmetricKeyType = 'ed448';\n                    break;\n                }\n                throw new TypeError('Invalid key for this operation, its crv must be Ed25519 or Ed448');\n            }\n            default:\n                throw new TypeError('Invalid key for this operation, its kty must be RSA, OKP, or EC');\n        }\n    }\n    let options;\n    switch (alg) {\n        case 'Ed25519':\n            if (asymmetricKeyType !== 'ed25519') {\n                throw new TypeError(`Invalid key for this operation, its asymmetricKeyType must be ed25519`);\n            }\n            break;\n        case 'EdDSA':\n            if (!['ed25519', 'ed448'].includes(asymmetricKeyType)) {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be ed25519 or ed448');\n            }\n            break;\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            if (asymmetricKeyType !== 'rsa') {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be rsa');\n            }\n            checkKeyLength(key, alg);\n            break;\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            if (asymmetricKeyType === 'rsa-pss') {\n                const { hashAlgorithm, mgf1HashAlgorithm, saltLength } = asymmetricKeyDetails;\n                const length = parseInt(alg.slice(-3), 10);\n                if (hashAlgorithm !== undefined &&\n                    (hashAlgorithm !== `sha${length}` || mgf1HashAlgorithm !== hashAlgorithm)) {\n                    throw new TypeError(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of \"alg\" ${alg}`);\n                }\n                if (saltLength !== undefined && saltLength > length >> 3) {\n                    throw new TypeError(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of \"alg\" ${alg}`);\n                }\n            }\n            else if (asymmetricKeyType !== 'rsa') {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be rsa or rsa-pss');\n            }\n            checkKeyLength(key, alg);\n            options = {\n                padding: constants.RSA_PKCS1_PSS_PADDING,\n                saltLength: constants.RSA_PSS_SALTLEN_DIGEST,\n            };\n            break;\n        case 'ES256':\n        case 'ES256K':\n        case 'ES384':\n        case 'ES512': {\n            if (asymmetricKeyType !== 'ec') {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be ec');\n            }\n            const actual = getNamedCurve(key);\n            const expected = ecCurveAlgMap.get(alg);\n            if (actual !== expected) {\n                throw new TypeError(`Invalid key curve for the algorithm, its curve must be ${expected}, got ${actual}`);\n            }\n            options = { dsaEncoding: 'ieee-p1363' };\n            break;\n        }\n        default:\n            throw new JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n    if (isJWK) {\n        return { format: 'jwk', key, ...options };\n    }\n    return options ? { ...options, key } : key;\n}\n", "import * as crypto from 'node:crypto';\nimport { promisify } from 'node:util';\nimport nodeDigest from './dsa_digest.js';\nimport hmacDigest from './hmac_digest.js';\nimport nodeKey from './node_key.js';\nimport getSignKey from './get_sign_verify_key.js';\nconst oneShotSign = promisify(crypto.sign);\nconst sign = async (alg, key, data) => {\n    const k = getSignKey(alg, key, 'sign');\n    if (alg.startsWith('HS')) {\n        const hmac = crypto.createHmac(hmacDigest(alg), k);\n        hmac.update(data);\n        return hmac.digest();\n    }\n    return oneShotSign(nodeDigest(alg), data, nodeKey(alg, k));\n};\nexport default sign;\n", "import { JOSENotSupported } from '../util/errors.js';\nexport default function hmacDigest(alg) {\n    switch (alg) {\n        case 'HS256':\n            return 'sha256';\n        case 'HS384':\n            return 'sha384';\n        case 'HS512':\n            return 'sha512';\n        default:\n            throw new JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n}\n", "import { KeyObject, createS<PERSON>ret<PERSON><PERSON> } from 'node:crypto';\nimport { isCrypto<PERSON>ey } from './webcrypto.js';\nimport { checkSigCrypto<PERSON>ey } from '../lib/crypto_key.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nimport { types } from './is_key_like.js';\nimport * as jwk from '../lib/is_jwk.js';\nexport default function getSignVerifyKey(alg, key, usage) {\n    if (key instanceof Uint8Array) {\n        if (!alg.startsWith('HS')) {\n            throw new TypeError(invalidKeyInput(key, ...types));\n        }\n        return createSecretKey(key);\n    }\n    if (key instanceof KeyObject) {\n        return key;\n    }\n    if (isCryptoKey(key)) {\n        checkSigCryptoKey(key, alg, usage);\n        return KeyObject.from(key);\n    }\n    if (jwk.isJWK(key)) {\n        if (alg.startsWith('HS')) {\n            return createS<PERSON><PERSON><PERSON><PERSON>(Buffer.from(key.k, 'base64url'));\n        }\n        return key;\n    }\n    throw new TypeError(invalidKeyInput(key, ...types, 'Uint8Array', 'JSON Web Key'));\n}\n", "export default (date) => Math.floor(date.getTime() / 1000);\n", "const minute = 60;\nconst hour = minute * 60;\nconst day = hour * 24;\nconst week = day * 7;\nconst year = day * 365.25;\nconst REGEX = /^(\\+|\\-)? ?(\\d+|\\d+\\.\\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i;\nexport default (str) => {\n    const matched = REGEX.exec(str);\n    if (!matched || (matched[4] && matched[1])) {\n        throw new TypeError('Invalid time period format');\n    }\n    const value = parseFloat(matched[2]);\n    const unit = matched[3].toLowerCase();\n    let numericDate;\n    switch (unit) {\n        case 'sec':\n        case 'secs':\n        case 'second':\n        case 'seconds':\n        case 's':\n            numericDate = Math.round(value);\n            break;\n        case 'minute':\n        case 'minutes':\n        case 'min':\n        case 'mins':\n        case 'm':\n            numericDate = Math.round(value * minute);\n            break;\n        case 'hour':\n        case 'hours':\n        case 'hr':\n        case 'hrs':\n        case 'h':\n            numericDate = Math.round(value * hour);\n            break;\n        case 'day':\n        case 'days':\n        case 'd':\n            numericDate = Math.round(value * day);\n            break;\n        case 'week':\n        case 'weeks':\n        case 'w':\n            numericDate = Math.round(value * week);\n            break;\n        default:\n            numericDate = Math.round(value * year);\n            break;\n    }\n    if (matched[1] === '-' || matched[4] === 'ago') {\n        return -numericDate;\n    }\n    return numericDate;\n};\n", "import { encode as base64url } from '../../runtime/base64url.js';\nimport sign from '../../runtime/sign.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport { JWSInvalid } from '../../util/errors.js';\nimport { encoder, decoder, concat } from '../../lib/buffer_utils.js';\nimport { checkKeyTypeWithJwk } from '../../lib/check_key_type.js';\nimport validateCrit from '../../lib/validate_crit.js';\nexport class FlattenedSign {\n    _payload;\n    _protectedHeader;\n    _unprotectedHeader;\n    constructor(payload) {\n        if (!(payload instanceof Uint8Array)) {\n            throw new TypeError('payload must be an instance of Uint8Array');\n        }\n        this._payload = payload;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this._protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this._unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this._unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    async sign(key, options) {\n        if (!this._protectedHeader && !this._unprotectedHeader) {\n            throw new JWSInvalid('either setProtectedHeader or setUnprotectedHeader must be called before #sign()');\n        }\n        if (!isDisjoint(this._protectedHeader, this._unprotectedHeader)) {\n            throw new JWSInvalid('JWS Protected and JWS Unprotected Header Parameter names must be disjoint');\n        }\n        const joseHeader = {\n            ...this._protectedHeader,\n            ...this._unprotectedHeader,\n        };\n        const extensions = validateCrit(JWSInvalid, new Map([['b64', true]]), options?.crit, this._protectedHeader, joseHeader);\n        let b64 = true;\n        if (extensions.has('b64')) {\n            b64 = this._protectedHeader.b64;\n            if (typeof b64 !== 'boolean') {\n                throw new JWSInvalid('The \"b64\" (base64url-encode payload) Header Parameter must be a boolean');\n            }\n        }\n        const { alg } = joseHeader;\n        if (typeof alg !== 'string' || !alg) {\n            throw new JWSInvalid('JWS \"alg\" (Algorithm) Header Parameter missing or invalid');\n        }\n        checkKeyTypeWithJwk(alg, key, 'sign');\n        let payload = this._payload;\n        if (b64) {\n            payload = encoder.encode(base64url(payload));\n        }\n        let protectedHeader;\n        if (this._protectedHeader) {\n            protectedHeader = encoder.encode(base64url(JSON.stringify(this._protectedHeader)));\n        }\n        else {\n            protectedHeader = encoder.encode('');\n        }\n        const data = concat(protectedHeader, encoder.encode('.'), payload);\n        const signature = await sign(alg, key, data);\n        const jws = {\n            signature: base64url(signature),\n            payload: '',\n        };\n        if (b64) {\n            jws.payload = decoder.decode(payload);\n        }\n        if (this._unprotectedHeader) {\n            jws.header = this._unprotectedHeader;\n        }\n        if (this._protectedHeader) {\n            jws.protected = decoder.decode(protectedHeader);\n        }\n        return jws;\n    }\n}\n", "import { FlattenedSign } from '../flattened/sign.js';\nexport class CompactSign {\n    _flattened;\n    constructor(payload) {\n        this._flattened = new FlattenedSign(payload);\n    }\n    setProtectedHeader(protectedHeader) {\n        this._flattened.setProtectedHeader(protectedHeader);\n        return this;\n    }\n    async sign(key, options) {\n        const jws = await this._flattened.sign(key, options);\n        if (jws.payload === undefined) {\n            throw new TypeError('use the flattened module for creating JWS with b64: false');\n        }\n        return `${jws.protected}.${jws.payload}.${jws.signature}`;\n    }\n}\n", "import epoch from '../lib/epoch.js';\nimport isObject from '../lib/is_object.js';\nimport secs from '../lib/secs.js';\nfunction validateInput(label, input) {\n    if (!Number.isFinite(input)) {\n        throw new TypeError(`Invalid ${label} input`);\n    }\n    return input;\n}\nexport class ProduceJWT {\n    _payload;\n    constructor(payload = {}) {\n        if (!isObject(payload)) {\n            throw new TypeError('JWT Claims Set MUST be an object');\n        }\n        this._payload = payload;\n    }\n    setIssuer(issuer) {\n        this._payload = { ...this._payload, iss: issuer };\n        return this;\n    }\n    setSubject(subject) {\n        this._payload = { ...this._payload, sub: subject };\n        return this;\n    }\n    setAudience(audience) {\n        this._payload = { ...this._payload, aud: audience };\n        return this;\n    }\n    setJti(jwtId) {\n        this._payload = { ...this._payload, jti: jwtId };\n        return this;\n    }\n    setNotBefore(input) {\n        if (typeof input === 'number') {\n            this._payload = { ...this._payload, nbf: validateInput('setNotBefore', input) };\n        }\n        else if (input instanceof Date) {\n            this._payload = { ...this._payload, nbf: validateInput('setNotBefore', epoch(input)) };\n        }\n        else {\n            this._payload = { ...this._payload, nbf: epoch(new Date()) + secs(input) };\n        }\n        return this;\n    }\n    setExpirationTime(input) {\n        if (typeof input === 'number') {\n            this._payload = { ...this._payload, exp: validateInput('setExpirationTime', input) };\n        }\n        else if (input instanceof Date) {\n            this._payload = { ...this._payload, exp: validateInput('setExpirationTime', epoch(input)) };\n        }\n        else {\n            this._payload = { ...this._payload, exp: epoch(new Date()) + secs(input) };\n        }\n        return this;\n    }\n    setIssuedAt(input) {\n        if (typeof input === 'undefined') {\n            this._payload = { ...this._payload, iat: epoch(new Date()) };\n        }\n        else if (input instanceof Date) {\n            this._payload = { ...this._payload, iat: validateInput('setIssuedAt', epoch(input)) };\n        }\n        else if (typeof input === 'string') {\n            this._payload = {\n                ...this._payload,\n                iat: validateInput('setIssuedAt', epoch(new Date()) + secs(input)),\n            };\n        }\n        else {\n            this._payload = { ...this._payload, iat: validateInput('setIssuedAt', input) };\n        }\n        return this;\n    }\n}\n", "import { CompactSign } from '../jws/compact/sign.js';\nimport { JWTInvalid } from '../util/errors.js';\nimport { encoder } from '../lib/buffer_utils.js';\nimport { ProduceJWT } from './produce.js';\nexport class SignJWT extends ProduceJWT {\n    _protectedHeader;\n    setProtectedHeader(protectedHeader) {\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    async sign(key, options) {\n        const sig = new CompactSign(encoder.encode(JSON.stringify(this._payload)));\n        sig.setProtectedHeader(this._protectedHeader);\n        if (Array.isArray(this._protectedHeader?.crit) &&\n            this._protectedHeader.crit.includes('b64') &&\n            this._protectedHeader.b64 === false) {\n            throw new JWTInvalid('JWTs MUST NOT use unencoded payload');\n        }\n        return sig.sign(key, options);\n    }\n}\n", "import {\n  ContentType,\n  getDisplayString,\n  MEDPLUM_CLI_CLIENT_ID,\n  MedplumClient,\n  normalizeErrorString,\n} from '@medplum/core';\nimport { exec } from 'node:child_process';\nimport { createServer } from 'node:http';\nimport { platform } from 'node:os';\nimport { createMedplumClient } from './util/client';\nimport { jwtAssertionLogin, jwtBearerLogin, MedplumCommand, Profile, saveProfile } from './utils';\n\nconst clientId = MEDPLUM_CLI_CLIENT_ID;\nconst redirectUri = 'http://localhost:9615';\n\nexport const login = new MedplumCommand('login');\nexport const whoami = new MedplumCommand('whoami');\nexport const token = new MedplumCommand('token');\n\nlogin.action(async (options) => {\n  const profileName = options.profile ?? 'default';\n\n  // Always save the profile to update settings\n  const profile = saveProfile(profileName, options);\n\n  const medplum = await createMedplumClient(options, false);\n  await startLogin(medplum, profile);\n});\n\nwhoami.action(async (options) => {\n  const medplum = await createMedplumClient(options);\n  printMe(medplum);\n});\n\ntoken.action(async (options) => {\n  const medplum = await createMedplumClient(options);\n  await medplum.getProfileAsync();\n  const token = medplum.getAccessToken();\n  if (!token) {\n    throw new Error('Not logged in');\n  }\n  console.log(token);\n});\n\nasync function startLogin(medplum: MedplumClient, profile: Profile): Promise<void> {\n  const authType = profile?.authType ?? 'authorization-code';\n  switch (authType) {\n    case 'authorization-code':\n      await medplumAuthorizationCodeLogin(medplum);\n      break;\n    case 'basic':\n      medplum.setBasicAuth(profile.clientId as string, profile.clientSecret as string);\n      break;\n    case 'client-credentials':\n      medplum.setBasicAuth(profile.clientId as string, profile.clientSecret as string);\n      await medplum.startClientLogin(profile.clientId as string, profile.clientSecret as string);\n      break;\n    case 'jwt-bearer':\n      await jwtBearerLogin(medplum, profile);\n      break;\n    case 'jwt-assertion':\n      await jwtAssertionLogin(medplum, profile);\n      break;\n  }\n}\n\nasync function startWebServer(medplum: MedplumClient): Promise<void> {\n  const server = createServer(async (req, res) => {\n    const url = new URL(req.url as string, 'http://localhost:9615');\n    const code = url.searchParams.get('code');\n    if (req.method === 'OPTIONS') {\n      res.writeHead(200, {\n        Allow: 'GET, POST',\n        'Content-Type': ContentType.TEXT,\n      });\n      res.end('OK');\n      return;\n    }\n    if (url.pathname === '/' && code) {\n      try {\n        const profile = await medplum.processCode(code, { clientId, redirectUri });\n        res.writeHead(200, { 'Content-Type': ContentType.TEXT });\n        res.end(`Signed in as ${getDisplayString(profile)}. You may close this window.`);\n      } catch (err) {\n        res.writeHead(400, { 'Content-Type': ContentType.TEXT });\n        res.end(`Error: ${normalizeErrorString(err)}`);\n      } finally {\n        server.close();\n      }\n    } else {\n      res.writeHead(404, { 'Content-Type': ContentType.TEXT });\n      res.end('Not found');\n    }\n  }).listen(9615);\n}\n\n/**\n * Opens a web browser to the specified URL.\n * See: https://hasinthaindrajee.medium.com/browser-sso-for-cli-applications-b0be743fa656\n * @param url - The URL to open.\n */\nasync function openBrowser(url: string): Promise<void> {\n  const os = platform();\n  let cmd = undefined;\n  switch (os) {\n    case 'openbsd':\n    case 'linux':\n      cmd = `xdg-open '${url}'`;\n      break;\n    case 'darwin':\n      cmd = `open '${url}'`;\n      break;\n    case 'win32':\n      cmd = `cmd /c start \"\" \"${url}\"`;\n      break;\n    default:\n      throw new Error('Unsupported platform: ' + os);\n  }\n  return new Promise((resolve, reject) => {\n    exec(cmd, (error, _, stderr) => {\n      if (error) {\n        reject(error);\n        return;\n      }\n      if (stderr) {\n        reject(new Error('Could not open browser: ' + stderr));\n        return;\n      }\n      resolve();\n    });\n  });\n}\n\n/**\n * Prints the current user and project.\n * @param medplum - The Medplum client.\n */\nfunction printMe(medplum: MedplumClient): void {\n  const loginState = medplum.getActiveLogin();\n  if (loginState) {\n    console.log(`Server:  ${medplum.getBaseUrl()}`);\n    console.log(`Profile: ${loginState.profile.display} (${loginState.profile.reference})`);\n    console.log(`Project: ${loginState.project.display} (${loginState.project.reference})`);\n  } else {\n    console.log('Not logged in');\n  }\n}\n\nasync function medplumAuthorizationCodeLogin(medplum: MedplumClient): Promise<void> {\n  await startWebServer(medplum);\n  const loginUrl = new URL(medplum.getAuthorizeUrl());\n  loginUrl.searchParams.set('client_id', clientId);\n  loginUrl.searchParams.set('redirect_uri', redirectUri);\n  loginUrl.searchParams.set('scope', 'openid');\n  loginUrl.searchParams.set('response_type', 'code');\n  loginUrl.searchParams.set('prompt', 'login');\n  await openBrowser(loginUrl.toString());\n}\n", "// CLI color highlighting\nconst RESET = '\\x1b[0m';\nconst BOLD = '\\x1b[1m';\nconst RED = '\\x1b[31m';\nconst GREEN = '\\x1b[32m';\nconst YELLOW = '\\x1b[33m';\nconst BLUE = '\\x1b[34m';\n\nexport const color = {\n  red: (text: string) => `${RED}${text}${RESET}`,\n  green: (text: string) => `${GREEN}${text}${RESET}`,\n  yellow: (text: string) => `${YELLOW}${text}${RESET}`,\n  blue: (text: string) => `${BLUE}${text}${RESET}`,\n  bold: (text: string) => `${BOLD}${text}${RESET}`,\n};\n\n// Bold text wrapped in ** **\nexport const processDescription = (desc: string): string => {\n  return desc.replace(/\\*\\*(.*?)\\*\\*/g, (_, text) => color.bold(text));\n};\n", "import {\n  CloudFormationClient,\n  DescribeStackResourcesCommand,\n  DescribeStacksCommand,\n  paginateListStacks,\n  Stack,\n  StackResource,\n  StackSummary,\n} from '@aws-sdk/client-cloudformation';\nimport { CloudFrontClient, CreateInvalidationCommand } from '@aws-sdk/client-cloudfront';\nimport { ECSClient } from '@aws-sdk/client-ecs';\nimport { S3Client } from '@aws-sdk/client-s3';\nimport { GetParameterCommand, PutParameterCommand, SSMClient } from '@aws-sdk/client-ssm';\nimport { GetCallerIdentityCommand, STSClient } from '@aws-sdk/client-sts';\nimport { normalizeErrorString } from '@medplum/core';\nimport fetch from 'node-fetch';\nimport { readdirSync } from 'node:fs';\nimport * as semver from 'semver';\nimport { getConfigFileName } from '../utils';\nimport { checkOk, print } from './terminal';\n\nexport interface MedplumStackDetails {\n  stack: Stack;\n  tag: string;\n  ecsCluster?: StackResource;\n  ecsService?: StackResource;\n  appBucket?: StackResource;\n  appDistribution?: StackResource;\n  appOriginAccessIdentity?: StackResource;\n  storageBucket?: StackResource;\n  storageDistribution?: StackResource;\n  storageOriginAccessIdentity?: StackResource;\n}\n\nexport const cloudFormationClient = new CloudFormationClient({});\nexport const cloudFrontClient = new CloudFrontClient({ region: 'us-east-1' });\nexport const ecsClient = new ECSClient({});\nexport const s3Client = new S3Client({});\nexport const tagKey = 'medplum:environment';\n\n/**\n * Returns a list of all AWS CloudFormation stacks (both Medplum and non-Medplum).\n * @returns List of AWS CloudFormation stacks.\n */\nexport async function getAllStacks(): Promise<(StackSummary & { StackName: string })[]> {\n  const listResult = [] as StackSummary[];\n  const paginator = paginateListStacks(\n    { client: cloudFormationClient },\n    {\n      StackStatusFilter: [\n        'CREATE_COMPLETE',\n        'CREATE_FAILED',\n        'CREATE_IN_PROGRESS',\n        'DELETE_FAILED',\n        'DELETE_IN_PROGRESS',\n        'IMPORT_COMPLETE',\n        'IMPORT_IN_PROGRESS',\n        'IMPORT_ROLLBACK_COMPLETE',\n        'IMPORT_ROLLBACK_FAILED',\n        'IMPORT_ROLLBACK_IN_PROGRESS',\n        'REVIEW_IN_PROGRESS',\n        'ROLLBACK_COMPLETE',\n        'ROLLBACK_FAILED',\n        'ROLLBACK_IN_PROGRESS',\n        'UPDATE_COMPLETE',\n        'UPDATE_COMPLETE_CLEANUP_IN_PROGRESS',\n        'UPDATE_FAILED',\n        'UPDATE_IN_PROGRESS',\n        'UPDATE_ROLLBACK_COMPLETE',\n        'UPDATE_ROLLBACK_COMPLETE_CLEANUP_IN_PROGRESS',\n        'UPDATE_ROLLBACK_FAILED',\n        'UPDATE_ROLLBACK_IN_PROGRESS',\n      ],\n    }\n  );\n\n  for await (const page of paginator) {\n    if (page.StackSummaries) {\n      for (const stack of page.StackSummaries) {\n        listResult.push(stack);\n      }\n    }\n  }\n\n  return listResult as (StackSummary & { StackName: string })[];\n}\n\n/**\n * Returns Medplum stack details for the given tag.\n * @param tag - The Medplum stack tag.\n * @returns The Medplum stack details.\n */\nexport async function getStackByTag(tag: string): Promise<MedplumStackDetails | undefined> {\n  const stackSummaries = await getAllStacks();\n  for (const stackSummary of stackSummaries) {\n    const stackName = stackSummary.StackName;\n    const details = await getStackDetails(stackName);\n    if (details?.tag === tag) {\n      return details;\n    }\n  }\n  return undefined;\n}\n\n/**\n * Returns Medplum stack details for the given stack name.\n * @param stackName - The CloudFormation stack name.\n * @returns The Medplum stack details.\n */\nexport async function getStackDetails(stackName: string): Promise<MedplumStackDetails | undefined> {\n  const result = {} as Partial<MedplumStackDetails>;\n  await buildStackDetails(cloudFormationClient, stackName, result);\n  if ((await cloudFormationClient.config.region()) !== 'us-east-1') {\n    try {\n      await buildStackDetails(new CloudFormationClient({ region: 'us-east-1' }), stackName + '-us-east-1', result);\n    } catch {\n      // Fail gracefully\n    }\n  }\n  return result as MedplumStackDetails;\n}\n\n/**\n * Builds the Medplum stack details for the given stack name and region.\n * @param client - The CloudFormation client.\n * @param stackName - The CloudFormation stack name.\n * @param result - The Medplum stack details builder.\n */\nasync function buildStackDetails(\n  client: CloudFormationClient,\n  stackName: string,\n  result: Partial<MedplumStackDetails>\n): Promise<void> {\n  const describeStacksCommand = new DescribeStacksCommand({ StackName: stackName });\n  const stackDetails = await client.send(describeStacksCommand);\n  const stack = stackDetails?.Stacks?.[0];\n  const medplumTag = stack?.Tags?.find((tag) => tag.Key === tagKey);\n  if (!medplumTag) {\n    return;\n  }\n\n  const stackResources = await client.send(new DescribeStackResourcesCommand({ StackName: stackName }));\n  if (!stackResources.StackResources) {\n    return;\n  }\n\n  if (client === cloudFormationClient) {\n    result.stack = stack;\n    result.tag = medplumTag.Value as string;\n  }\n\n  for (const resource of stackResources.StackResources) {\n    assignStackDetails(resource, result);\n  }\n}\n\nfunction assignStackDetails(resource: StackResource, result: Partial<MedplumStackDetails>): void {\n  if (resource.ResourceType === 'AWS::ECS::Cluster') {\n    result.ecsCluster = resource;\n  } else if (resource.ResourceType === 'AWS::ECS::Service') {\n    result.ecsService = resource;\n  } else if (\n    resource.ResourceType === 'AWS::S3::Bucket' &&\n    resource.LogicalResourceId?.startsWith('FrontEndAppBucket')\n  ) {\n    result.appBucket = resource;\n  } else if (\n    resource.ResourceType === 'AWS::CloudFront::Distribution' &&\n    resource.LogicalResourceId?.startsWith('FrontEndAppDistribution')\n  ) {\n    result.appDistribution = resource;\n  } else if (\n    resource.ResourceType === 'AWS::CloudFront::CloudFrontOriginAccessIdentity' &&\n    resource.LogicalResourceId?.startsWith('FrontEndOriginAccessIdentity')\n  ) {\n    result.appOriginAccessIdentity = resource;\n  } else if (\n    resource.ResourceType === 'AWS::S3::Bucket' &&\n    resource.LogicalResourceId?.startsWith('StorageStorageBucket')\n  ) {\n    result.storageBucket = resource;\n  } else if (\n    resource.ResourceType === 'AWS::CloudFront::Distribution' &&\n    resource.LogicalResourceId?.startsWith('StorageStorageDistribution')\n  ) {\n    result.storageDistribution = resource;\n  } else if (\n    resource.ResourceType === 'AWS::CloudFront::CloudFrontOriginAccessIdentity' &&\n    resource.LogicalResourceId?.startsWith('StorageOriginAccessIdentity')\n  ) {\n    result.storageOriginAccessIdentity = resource;\n  }\n}\n\n/**\n * Prints the given Medplum stack details to stdout.\n * @param details - The Medplum stack details.\n */\nexport function printStackDetails(details: MedplumStackDetails): void {\n  console.log(`Medplum Tag:           ${details.tag}`);\n  console.log(`Stack Name:            ${details.stack?.StackName}`);\n  console.log(`Stack ID:              ${details.stack?.StackId}`);\n  console.log(`Status:                ${details.stack?.StackStatus}`);\n  console.log(`ECS Cluster:           ${details.ecsCluster?.PhysicalResourceId}`);\n  console.log(`ECS Service:           ${getEcsServiceName(details.ecsService)}`);\n  console.log(`App Bucket:            ${details.appBucket?.PhysicalResourceId}`);\n  console.log(`App Distribution:      ${details.appDistribution?.PhysicalResourceId}`);\n  console.log(`App OAI:               ${details.appOriginAccessIdentity?.PhysicalResourceId}`);\n  console.log(`Storage Bucket:        ${details.storageBucket?.PhysicalResourceId}`);\n  console.log(`Storage Distribution:  ${details.storageDistribution?.PhysicalResourceId}`);\n  console.log(`Storage OAI:           ${details.storageOriginAccessIdentity?.PhysicalResourceId}`);\n}\n\n/**\n * Parses the ECS service name from the given AWS ECS service resource.\n * @param resource - The AWS ECS service resource.\n * @returns The ECS service name.\n */\nexport function getEcsServiceName(resource: StackResource | undefined): string | undefined {\n  return resource?.PhysicalResourceId?.split('/')?.pop() || '';\n}\n\n/**\n * Creates a CloudFront invalidation to clear the cache for all files.\n * This is not strictly necessary, but it helps to ensure that the latest version of the app is served.\n * In a perfect world, every deploy is clean, and hashed resources should be cached forever.\n * However, we do not recalculate hashes after variable replacements.\n * So if variables change, we need to invalidate the cache.\n * @param distributionId - The CloudFront distribution ID.\n */\nexport async function createInvalidation(distributionId: string): Promise<void> {\n  const response = await cloudFrontClient.send(\n    new CreateInvalidationCommand({\n      DistributionId: distributionId,\n      InvalidationBatch: {\n        CallerReference: `invalidate-all-${Date.now()}`,\n        Paths: {\n          Quantity: 1,\n          Items: ['/*'],\n        },\n      },\n    })\n  );\n  console.log(`Created invalidation with ID: ${response.Invalidation?.Id}`);\n}\n\nexport async function getServerVersions(from?: string): Promise<string[]> {\n  const response = await fetch('https://api.github.com/repos/medplum/medplum/releases?per_page=100', {\n    headers: {\n      Accept: 'application/vnd.github+json',\n      'X-GitHub-Api-Version': '2022-11-28',\n    },\n  });\n\n  const json = (await response.json()) as { tag_name: string }[];\n  const versions = json.map((release) =>\n    release.tag_name.startsWith('v') ? release.tag_name.slice(1) : release.tag_name\n  );\n\n  // Sort in descending order\n  versions.sort((a, b) => semver.compare(b, a));\n\n  return from ? versions.slice(0, versions.indexOf(from)) : versions;\n}\n\n/**\n * Writes a collection of parameters to AWS Parameter Store.\n * @param region - The AWS region.\n * @param prefix - The AWS Parameter Store prefix.\n * @param params - The parameters to write.\n */\nexport async function writeParameters(\n  region: string,\n  prefix: string,\n  params: Record<string, string | number>\n): Promise<void> {\n  const client = new SSMClient({ region });\n  for (const [key, value] of Object.entries(params)) {\n    const name = prefix + key;\n    const valueStr = value.toString();\n    const existingValue = await readParameter(client, name);\n\n    if (existingValue !== undefined && existingValue !== valueStr) {\n      print(`Parameter \"${name}\" exists with different value.`);\n      await checkOk(`Do you want to overwrite \"${name}\"?`);\n    }\n\n    await writeParameter(client, name, valueStr);\n  }\n}\n\n/**\n * Reads a parameter from AWS Parameter Store.\n * @param client - The AWS SSM client.\n * @param name - The parameter name.\n * @returns The parameter value, or undefined if not found.\n */\nasync function readParameter(client: SSMClient, name: string): Promise<string | undefined> {\n  const command = new GetParameterCommand({\n    Name: name,\n    WithDecryption: true,\n  });\n  try {\n    const result = await client.send(command);\n    return result.Parameter?.Value;\n  } catch (err: any) {\n    if (err.name === 'ParameterNotFound') {\n      return undefined;\n    }\n    throw err;\n  }\n}\n\n/**\n * Writes a parameter to AWS Parameter Store.\n * @param client - The AWS SSM client.\n * @param name - The parameter name.\n * @param value - The parameter value.\n */\nasync function writeParameter(client: SSMClient, name: string, value: string): Promise<void> {\n  const command = new PutParameterCommand({\n    Name: name,\n    Value: value,\n    Type: 'SecureString',\n    Overwrite: true,\n  });\n  await client.send(command);\n}\n\n/**\n * Prints a \"config not found\" message to stdout.\n * Includes helpful debugging information such as available configs.\n * @param tagName - Medplum stack tag name.\n * @param options - Additional command line options.\n */\nexport function printConfigNotFound(tagName: string, options?: Record<string, any>): void {\n  console.log(`Config not found: ${tagName} (${getConfigFileName(tagName, options)})`);\n\n  if (options) {\n    const entries = Object.entries(options);\n    if (entries.length > 0) {\n      console.log('Additional options:');\n      for (const [key, value] of entries) {\n        console.log(`  ${key}: ${value}`);\n      }\n    }\n  }\n\n  console.log();\n\n  let files: any[] = readdirSync('.', { withFileTypes: true });\n  files = files\n    .filter((f) => f.isFile() && f.name.startsWith('medplum.') && f.name.endsWith('.json'))\n    .map((f) => f.name);\n\n  if (files.length === 0) {\n    console.log('No configs found');\n  } else {\n    console.log('Available configs:');\n    for (const file of files) {\n      console.log(\n        `  ${file\n          .replaceAll('medplum.', '')\n          .replaceAll('.config', '')\n          .replaceAll('.server', '')\n          .replaceAll('.json', '')\n          .padEnd(40, ' ')} (${file})`\n      );\n    }\n  }\n}\n\n/**\n * Prints a \"stack not found\" message to stdout.\n * Includes helpful debugging information such as AWS account ID and region.\n * @param tagName - Medplum stack tag name.\n */\nexport async function printStackNotFound(tagName: string): Promise<void> {\n  console.log(`Stack not found: ${tagName}`);\n  console.log();\n\n  try {\n    const client = new STSClient();\n    const command = new GetCallerIdentityCommand({});\n    const response = await client.send(command);\n    const region = await client.config.region();\n    console.log('AWS Region:        ', region);\n    console.log('AWS Account ID:    ', response.Account);\n    console.log('AWS Account ARN:   ', response.Arn);\n    console.log('AWS User ID:       ', response.UserId);\n  } catch (err) {\n    console.log('Warning: Unable to get AWS account ID', normalizeErrorString(err));\n  }\n}\n", "import readline from 'node:readline';\n\nlet terminal: readline.Interface;\n\nexport function initTerminal(): void {\n  terminal = readline.createInterface({ input: process.stdin, output: process.stdout });\n}\n\nexport function closeTerminal(): void {\n  terminal.close();\n}\n\n/**\n * Prints to stdout.\n * @param text - The text to print.\n */\nexport function print(text: string): void {\n  terminal.write(text + '\\n');\n}\n\n/**\n * Prints a header with extra line spacing.\n * @param text - The text to print.\n */\nexport function header(text: string): void {\n  print('\\n' + text + '\\n');\n}\n\n/**\n * Prints a question and waits for user input.\n * @param text - The question text to print.\n * @param defaultValue - Optional default value.\n * @returns The selected value, or default value on empty selection.\n */\nexport function ask(text: string, defaultValue: string | number = ''): Promise<string> {\n  return new Promise((resolve) => {\n    terminal.question(text + (defaultValue ? ' (' + defaultValue + ')' : '') + ' ', (answer: string) => {\n      resolve(answer || defaultValue.toString());\n    });\n  });\n}\n\n/**\n * Prints a question and waits for user to choose one of the provided options.\n * @param text - The prompt text to print.\n * @param options - The list of options that the user can select.\n * @param defaultValue - Optional default value.\n * @returns The selected value, or default value on empty selection.\n */\nexport async function choose(text: string, options: (string | number)[], defaultValue = ''): Promise<string> {\n  const str = text + ' [' + options.map((o) => (o === defaultValue ? '(' + o + ')' : o)).join('|') + ']';\n  // eslint-disable-next-line no-constant-condition\n  while (true) {\n    const answer = (await ask(str)) || defaultValue;\n    if (options.includes(answer)) {\n      return answer;\n    }\n    print('Please choose one of the following options: ' + options.join(', '));\n  }\n}\n\n/**\n * Prints a question and waits for the user to choose a valid integer option.\n * @param text - The prompt text to print.\n * @param options - The list of options that the user can select.\n * @param defaultValue - Default value.\n * @returns The selected value, or default value on empty selection.\n */\nexport async function chooseInt(text: string, options: number[], defaultValue: number): Promise<number> {\n  return parseInt(\n    await choose(\n      text,\n      options.map((o) => o.toString()),\n      defaultValue.toString()\n    ),\n    10\n  );\n}\n\n/**\n * Prints a question and waits for the user to choose yes or no.\n * @param text - The question to print.\n * @returns true on accept or false on reject.\n */\nexport async function yesOrNo(text: string): Promise<boolean> {\n  return (await choose(text, ['y', 'n'])).toLowerCase() === 'y';\n}\n\n/**\n * Prints a question and waits for the user to confirm yes. Throws error on no, and exits the program.\n * @param text - The prompt text to print.\n */\nexport async function checkOk(text: string): Promise<void> {\n  if (!(await yesOrNo(text))) {\n    print('Exiting...');\n    throw new Error('User cancelled');\n  }\n}\n", "import { getStackByTag, printStackDetails, printStackNotFound } from './utils';\n\n/**\n * The AWS \"describe\" command prints details about a Medplum CloudFormation stack.\n * @param tag - The Medplum stack tag.\n */\nexport async function describeStacksCommand(tag: string): Promise<void> {\n  const details = await getStackByTag(tag);\n  if (!details) {\n    await printStackNotFound(tag);\n    throw new Error(`Stack not found: ${tag}`);\n  }\n  printStackDetails(details);\n}\n", "import {\n  AC<PERSON>lient,\n  CertificateSummary,\n  ListCertificatesCommand,\n  RequestCertificateCommand,\n  ValidationMethod,\n} from '@aws-sdk/client-acm';\nimport { CloudFrontClient, CreatePublicKeyCommand } from '@aws-sdk/client-cloudfront';\nimport { GetCallerIdentityCommand, STSClient } from '@aws-sdk/client-sts';\nimport { MedplumInfraConfig, normalizeErrorString } from '@medplum/core';\nimport { generateKeyPairSync, randomUUID } from 'node:crypto';\nimport { existsSync } from 'node:fs';\nimport { getConfigFileName, writeConfig } from '../utils';\nimport { ask, checkOk, choose, chooseInt, closeTerminal, header, initTerminal, print, yesOrNo } from './terminal';\nimport { getServerVersions, writeParameters } from './utils';\n\ntype MedplumDomainType = 'api' | 'app' | 'storage';\ntype MedplumDomainSetting = `${MedplumDomainType}DomainName`;\ntype MedplumDomainCertSetting = `${MedplumDomainType}SslCertArn`;\n\nconst getDomainSetting = (domain: MedplumDomainType): MedplumDomainSetting => `${domain}DomainName`;\nconst getDomainCertSetting = (domain: MedplumDomainType): MedplumDomainCertSetting => `${domain}SslCertArn`;\n\nexport async function initStackCommand(): Promise<void> {\n  const config = { apiPort: 8103, region: 'us-east-1' } as MedplumInfraConfig;\n  initTerminal();\n  header('MEDPLUM');\n  print('This tool prepares the necessary prerequisites for deploying Medplum in your AWS account.');\n  print('');\n  print('Most Medplum infrastructure is deployed using the AWS CDK.');\n  print('However, some AWS resources must be created manually, such as email addresses and SSL certificates.');\n  print('This tool will help you create those resources.');\n  print('');\n  print('Upon completion, this tool will:');\n  print('  1. Generate a Medplum CDK config file (i.e., medplum.demo.config.json)');\n  print('  2. Optionally generate an AWS CloudFront signing key');\n  print('  3. Optionally request SSL certificates from AWS Certificate Manager');\n  print('  4. Optionally write server config settings to AWS Parameter Store');\n  print('');\n  print('The Medplum infra config file is an input to the Medplum CDK.');\n  print('The Medplum CDK will create and manage the necessary AWS resources.');\n  print('');\n  print('We will ask a series of questions to generate your infra config file.');\n  print('Some questions have predefined options in [square brackets].');\n  print('Some questions have default values in (parentheses), which you can accept by pressing Enter.');\n  print('Press Ctrl+C at any time to exit.');\n\n  const currentAccountId = await getAccountId(config.region);\n  if (!currentAccountId) {\n    print('It appears that you do not have AWS credentials configured.');\n    print('AWS credentials are not strictly required, but will enable some additional features.');\n    print('If you intend to use AWS credentials, please configure them now.');\n    await checkOk('Do you want to continue without AWS credentials?');\n  }\n\n  header('ENVIRONMENT NAME');\n  print('Medplum deployments have a short environment name such as \"prod\", \"staging\", \"alice\", or \"demo\".');\n  print('The environment name is used in multiple places:');\n  print('  1. As part of config file names (i.e., medplum.demo.config.json)');\n  print('  2. As the base of CloudFormation stack names (i.e., MedplumDemo)');\n  print('  3. AWS Parameter Store keys (i.e., /medplum/demo/...)');\n  config.name = await ask('What is your environment name?', 'demo');\n  print('Using environment name \"' + config.name + '\"...');\n\n  header('CONFIG FILE');\n  print('Medplum Infrastructure will create a config file in the current directory.');\n  const configFileName = await ask('What is the config file name?', `medplum.${config.name}.config.json`);\n  if (existsSync(configFileName)) {\n    print('Config file already exists.');\n    await checkOk('Do you want to overwrite the config file?');\n  }\n  print('Using config file \"' + configFileName + '\"...');\n  writeConfig(configFileName, config);\n\n  header('AWS REGION');\n  print('Most Medplum resources will be created in a single AWS region.');\n  config.region = await ask('Enter your AWS region:', 'us-east-1');\n  writeConfig(configFileName, config);\n\n  header('AWS ACCOUNT NUMBER');\n  print('Medplum Infrastructure will use your AWS account number to create AWS resources.');\n  if (currentAccountId) {\n    print('Using the AWS CLI, your current account ID is: ' + currentAccountId);\n  }\n  config.accountNumber = await ask('What is your AWS account number?', currentAccountId);\n  writeConfig(configFileName, config);\n\n  header('STACK NAME');\n  print('Medplum will create a CloudFormation stack to manage AWS resources.');\n  print('AWS CloudFormation stack names ');\n  const defaultStackName = 'Medplum' + config.name.charAt(0).toUpperCase() + config.name.slice(1);\n  config.stackName = await ask('Enter your CloudFormation stack name?', defaultStackName);\n  writeConfig(configFileName, config);\n\n  header('BASE DOMAIN NAME');\n  print('Please enter the base domain name for your Medplum deployment.');\n  print('');\n  print('Medplum deploys multiple subdomains for various services.');\n  print('');\n  print('For example, \"api.\" for the REST API and \"app.\" for the web application.');\n  print('The base domain name is the common suffix for all subdomains.');\n  print('');\n  print('For example, if your base domain name is \"example.com\",');\n  print('then the REST API will be \"api.example.com\".');\n  print('');\n  print('The base domain should include the TLD (i.e., \".com\", \".org\", \".net\").');\n  print('');\n  print('Note that you must own the base domain, and it must use Route53 DNS.');\n  while (!config.domainName) {\n    config.domainName = await ask('Enter your base domain name:');\n  }\n  writeConfig(configFileName, config);\n\n  header('SUPPORT EMAIL');\n  print('Medplum sends transactional emails to users.');\n  print('For example, emails to new users or for password reset.');\n  print('Medplum will use the support email address to send these emails.');\n  print('Note that you must verify the support email address in SES.');\n  const supportEmail = await ask('Enter your support email address:');\n\n  header('API DOMAIN NAME');\n  print('Medplum deploys a REST API for the backend services.');\n  config.apiDomainName = await ask('Enter your REST API domain name:', 'api.' + config.domainName);\n  config.baseUrl = `https://${config.apiDomainName}/`;\n  writeConfig(configFileName, config);\n\n  header('APP DOMAIN NAME');\n  print('Medplum deploys a web application for the user interface.');\n  config.appDomainName = await ask('Enter your web application domain name:', 'app.' + config.domainName);\n  writeConfig(configFileName, config);\n\n  header('STORAGE DOMAIN NAME');\n  print('Medplum deploys a storage service for file uploads.');\n  config.storageDomainName = await ask('Enter your storage domain name:', 'storage.' + config.domainName);\n  writeConfig(configFileName, config);\n\n  header('STORAGE BUCKET');\n  print('Medplum uses an S3 bucket to store binary content such as file uploads.');\n  print('Medplum will create a the S3 bucket as part of the CloudFormation stack.');\n  config.storageBucketName = await ask('Enter your storage bucket name:', config.storageDomainName);\n  writeConfig(configFileName, config);\n\n  header('MAX AVAILABILITY ZONES');\n  print('Medplum API servers can be deployed in multiple availability zones.');\n  print('This provides redundancy and high availability.');\n  print('However, it also increases the cost of the deployment.');\n  print('If you want to use all availability zones, choose a large number such as 99.');\n  print('If you want to restrict the number, for example to manage EIP limits,');\n  print('then choose a small number such as 2 or 3.');\n  config.maxAzs = await chooseInt('Enter the maximum number of availability zones:', [2, 3, 99], 2);\n\n  header('DATABASE INSTANCES');\n  print('Medplum uses a relational database to store data.');\n  print('Medplum can create a new RDS database as part of the CloudFormation stack,');\n  print('or can set up your own database and enter the database name, username, and password.');\n  if (await yesOrNo('Do you want to create a new RDS database as part of the CloudFormation stack?')) {\n    print('Medplum will create a new RDS database as part of the CloudFormation stack.');\n    print('');\n    print('If you need high availability, you can choose multiple instances.');\n    print('Use 1 for a single instance, or 2 for a primary and a standby.');\n    config.rdsInstances = await chooseInt('Enter the number of database instances:', [1, 2], 1);\n  } else {\n    print('Medplum will not create a new RDS database.');\n    print('Please create a new RDS database and enter the database name, username, and password.');\n    print('Set the AWS Secrets Manager secret ARN in the config file in the \"rdsSecretsArn\" setting.');\n    config.rdsSecretsArn = 'TODO';\n  }\n  writeConfig(configFileName, config);\n\n  header('SERVER INSTANCES');\n  print('Medplum uses AWS Fargate to run the API servers.');\n  print('Medplum will create a new Fargate cluster as part of the CloudFormation stack.');\n  print('Fargate will automatically scale the number of servers up and down.');\n  print('If you need high availability, you can choose multiple instances.');\n  config.desiredServerCount = await chooseInt('Enter the number of server instances:', [1, 2, 3, 4, 6, 8], 1);\n  writeConfig(configFileName, config);\n\n  header('SERVER MEMORY');\n  print('You can choose the amount of memory for each server instance.');\n  print('The default is 512 MB, which is sufficient for getting started.');\n  print('Note that only certain CPU units are compatible with memory units.');\n  print('Consult AWS Fargate \"Task Definition Parameters\" for more information.');\n  config.serverMemory = await chooseInt('Enter the server memory (MB):', [512, 1024, 2048, 4096, 8192, 16384], 512);\n  writeConfig(configFileName, config);\n\n  header('SERVER CPU');\n  print('You can choose the amount of CPU for each server instance.');\n  print('CPU is expressed as an integer using AWS CPU units');\n  print('The default is 256, which is sufficient for getting started.');\n  print('Note that only certain CPU units are compatible with memory units.');\n  print('Consult AWS Fargate \"Task Definition Parameters\" for more information.');\n  config.serverCpu = await chooseInt('Enter the server CPU:', [256, 512, 1024, 2048, 4096, 8192, 16384], 256);\n  writeConfig(configFileName, config);\n\n  header('SERVER IMAGE');\n  print('Medplum uses Docker images for the API servers.');\n  print('You can choose the image to use for the servers.');\n  print('Docker images can be loaded from either Docker Hub or AWS ECR.');\n  print('The default is the latest Medplum release.');\n  const latestVersion = (await getServerVersions())[0] ?? 'latest';\n  config.serverImage = await ask('Enter the server image:', `medplum/medplum-server:${latestVersion}`);\n  writeConfig(configFileName, config);\n\n  header('SIGNING KEY');\n  print('Medplum uses AWS CloudFront Presigned URLs for binary content such as file uploads.');\n  const signingKey = await generateSigningKey(config.region, config.stackName + 'SigningKey');\n  if (signingKey) {\n    config.signingKeyId = signingKey.keyId;\n    config.storagePublicKey = signingKey.publicKey;\n    writeConfig(configFileName, config);\n  } else {\n    print('Unable to generate signing key.');\n    print('Please manually create a signing key and enter the key ID and public key in the config file.');\n    print('You must set the \"signingKeyId\", \"signingKey\", and \"signingKeyPassphrase\" settings.');\n  }\n\n  header('SSL CERTIFICATES');\n  print(`Medplum will now check for existing SSL certificates for the subdomains.`);\n  const allCerts = await listAllCertificates(config.region);\n  print('Found ' + allCerts.length + ' certificate(s).');\n\n  // Process certificates for each subdomain\n  // Note: The \"api\" certificate must be created in the same region as the API\n  // Note: The \"app\" and \"storage\" certificates must be created in us-east-1\n  for (const { region, certName } of [\n    { region: config.region, certName: 'api' },\n    { region: 'us-east-1', certName: 'app' },\n    { region: 'us-east-1', certName: 'storage' },\n  ] as const) {\n    print('');\n    const arn = await processCert(config, allCerts, region, certName);\n    config[getDomainCertSetting(certName)] = arn;\n    writeConfig(configFileName, config);\n  }\n\n  header('AWS PARAMETER STORE');\n  print('Medplum uses AWS Parameter Store to store sensitive configuration values.');\n  print('These values will be encrypted at rest.');\n  print(`The values will be stored in the \"/medplum/${config.name}\" path.`);\n\n  const serverParams: Record<string, string | number> = {\n    port: config.apiPort,\n    baseUrl: config.baseUrl,\n    appBaseUrl: `https://${config.appDomainName}/`,\n    storageBaseUrl: `https://${config.storageDomainName}/binary/`,\n    binaryStorage: `s3:${config.storageBucketName}`,\n    supportEmail: supportEmail,\n  };\n\n  if (signingKey) {\n    serverParams.signingKeyId = signingKey.keyId;\n    serverParams.signingKey = signingKey.privateKey;\n    serverParams.signingKeyPassphrase = signingKey.passphrase;\n  }\n\n  print(\n    JSON.stringify(\n      {\n        ...serverParams,\n        signingKey: '****',\n        signingKeyPassphrase: '****',\n      },\n      null,\n      2\n    )\n  );\n\n  if (await yesOrNo('Do you want to store these values in AWS Parameter Store?')) {\n    await writeParameters(config.region, `/medplum/${config.name}/`, serverParams);\n  } else {\n    const serverConfigFileName = getConfigFileName(config.name, { server: true });\n    writeConfig(serverConfigFileName, serverParams);\n    print('Skipping AWS Parameter Store.');\n    print(`Writing values to local config file: ${serverConfigFileName}`);\n    print('Please add these values to AWS Parameter Store manually.');\n  }\n\n  header('DONE!');\n  print('Medplum configuration complete.');\n  print('You can now proceed to deploying the Medplum infrastructure with CDK.');\n  print('Run:');\n  print('');\n  print(`    npx cdk bootstrap -c config=${configFileName}`);\n  print(`    npx cdk synth -c config=${configFileName}`);\n  if (config.region === 'us-east-1') {\n    print(`    npx cdk deploy -c config=${configFileName}`);\n  } else {\n    print(`    npx cdk deploy -c config=${configFileName} --all`);\n  }\n  print('');\n  print('See Medplum documentation for more information:');\n  print('');\n  print('    https://www.medplum.com/docs/self-hosting/install-on-aws');\n  print('');\n  closeTerminal();\n}\n\n/**\n * Returns the current AWS account ID.\n * This is used as the default value for the \"accountNumber\" config setting.\n * @param region - The AWS region.\n * @returns The AWS account ID.\n */\nasync function getAccountId(region: string): Promise<string | undefined> {\n  try {\n    const client = new STSClient({ region });\n    const command = new GetCallerIdentityCommand({});\n    const response = await client.send(command);\n    return response.Account as string;\n  } catch (err) {\n    console.log('Warning: Unable to get AWS account ID', (err as Error).message);\n    return undefined;\n  }\n}\n\n/**\n * Returns a list of all AWS certificates.\n * This is used to find existing certificates for the subdomains.\n * If the primary region is not us-east-1, then certificates in us-east-1 will also be returned.\n * @param region - The AWS region.\n * @returns The list of AWS Certificates.\n */\nasync function listAllCertificates(region: string): Promise<CertificateSummary[]> {\n  const result = await listCertificates(region);\n  if (region !== 'us-east-1') {\n    const usEast1Result = await listCertificates('us-east-1');\n    result.push(...usEast1Result);\n  }\n  return result;\n}\n\n/**\n * Returns a list of AWS Certificates.\n * This is used to find existing certificates for the subdomains.\n * @param region - The AWS region.\n * @returns The list of AWS Certificates.\n */\nasync function listCertificates(region: string): Promise<CertificateSummary[]> {\n  try {\n    const client = new ACMClient({ region });\n    const command = new ListCertificatesCommand({ MaxItems: 1000 });\n    const response = await client.send(command);\n    return response.CertificateSummaryList as CertificateSummary[];\n  } catch (err) {\n    console.log('Warning: Unable to list certificates', (err as Error).message);\n    return [];\n  }\n}\n\n/**\n * Processes a required certificate.\n *\n * 1. If the certificate already exists, return the ARN.\n * 2. If the certificate does not exist, and the user wants to create a new certificate, create it and return the ARN.\n * 3. If the certificate does not exist, and the user does not want to create a new certificate, return a placeholder.\n * @param config - In-progress config settings.\n * @param allCerts - List of all existing certificates.\n * @param region - The AWS region where the certificate is needed.\n * @param certName - The name of the certificate (api, app, or storage).\n * @returns The ARN of the certificate or placeholder if a new certificate is needed.\n */\nasync function processCert(\n  config: MedplumInfraConfig,\n  allCerts: CertificateSummary[],\n  region: string,\n  certName: 'api' | 'app' | 'storage'\n): Promise<string> {\n  const domainName = config[getDomainSetting(certName)];\n  const existingCert = allCerts.find((cert) => cert.CertificateArn?.includes(region) && cert.DomainName === domainName);\n  if (existingCert) {\n    print(`Found existing certificate for \"${domainName}\" in \"${region}.`);\n    return existingCert.CertificateArn as string;\n  }\n\n  print(`No existing certificate found for \"${domainName}\" in \"${region}.`);\n  if (!(await yesOrNo('Do you want to request a new certificate?'))) {\n    print(`Please add your certificate ARN to the config file in the \"${getDomainCertSetting(certName)}\" setting.`);\n    return 'TODO';\n  }\n\n  const arn = await requestCert(region, domainName);\n  print('Certificate ARN: ' + arn);\n  return arn;\n}\n\n/**\n * Requests an AWS Certificate.\n * @param region - The AWS region.\n * @param domain - The domain name.\n * @returns The AWS Certificate ARN on success, or undefined on failure.\n */\nasync function requestCert(region: string, domain: string): Promise<string> {\n  try {\n    const validationMethod = await choose(\n      'Validate certificate using DNS or email validation?',\n      ['dns', 'email'],\n      'dns'\n    );\n    const client = new ACMClient({ region });\n    const command = new RequestCertificateCommand({\n      DomainName: domain,\n      ValidationMethod: validationMethod.toUpperCase() as ValidationMethod,\n    });\n    const response = await client.send(command);\n    return response.CertificateArn as string;\n  } catch (err) {\n    console.log('Error: Unable to request certificate', (err as Error).message);\n    return 'TODO';\n  }\n}\n\n/**\n * Generates an AWS CloudFront signing key.\n *\n * Requirements:\n *\n *   1. It must be an SSH-2 RSA key pair.\n *   2. It must be in base64-encoded PEM format.\n *   3. It must be a 2048-bit key pair.\n *\n * See: https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/private-content-trusted-signers.html#private-content-creating-cloudfront-key-pairs\n *\n * @param region - The AWS region.\n * @param keyName - The key name.\n * @returns A new signing key.\n */\nasync function generateSigningKey(\n  region: string,\n  keyName: string\n): Promise<\n  | {\n      keyId: string;\n      publicKey: string;\n      privateKey: string;\n      passphrase: string;\n    }\n  | undefined\n> {\n  const passphrase = randomUUID();\n  const signingKey = generateKeyPairSync('rsa', {\n    modulusLength: 2048,\n    publicKeyEncoding: {\n      type: 'spki',\n      format: 'pem',\n    },\n    privateKeyEncoding: {\n      type: 'pkcs1',\n      format: 'pem',\n      cipher: 'aes-256-cbc',\n      passphrase,\n    },\n  });\n\n  try {\n    const response = await new CloudFrontClient({ region }).send(\n      new CreatePublicKeyCommand({\n        PublicKeyConfig: {\n          Name: keyName,\n          CallerReference: randomUUID(),\n          EncodedKey: signingKey.publicKey,\n        },\n      })\n    );\n\n    return {\n      keyId: response.PublicKey?.Id as string,\n      publicKey: signingKey.publicKey,\n      privateKey: signingKey.privateKey,\n      passphrase,\n    };\n  } catch (err) {\n    console.log('Error: Unable to create signing key: ', normalizeErrorString(err));\n    return undefined;\n  }\n}\n", "import { getAllStacks, getStackDetails, printStackDetails } from './utils';\n\n/**\n * The AWS \"list\" command prints summary details about all Medplum CloudFormation stacks.\n */\nexport async function listStacksCommand(): Promise<void> {\n  const stackSummaries = await getAllStacks();\n  for (const stackSummary of stackSummaries) {\n    const stackName = stackSummary.StackName;\n    const details = await getStackDetails(stackName);\n    if (!details) {\n      continue;\n    }\n    printStackDetails(details);\n    console.log('');\n  }\n}\n", "import { PutObjectCommand } from '@aws-sdk/client-s3';\nimport { ContentType } from '@medplum/core';\nimport fastGlob from 'fast-glob';\nimport fetch from 'node-fetch';\nimport { createReadStream, mkdtempSync, readdirSync, readFileSync, rmSync, writeFileSync } from 'node:fs';\nimport { tmpdir } from 'node:os';\nimport { join, sep } from 'node:path';\nimport { pipeline } from 'node:stream/promises';\nimport { readConfig, safeTarExtractor } from '../utils';\nimport { createInvalidation, getStackByTag, printConfigNotFound, printStackNotFound, s3Client } from './utils';\n\nexport interface UpdateAppOptions {\n  file?: string;\n  toVersion?: string;\n  dryrun?: boolean;\n  tarPath?: string;\n}\n\n/**\n * The AWS \"update-app\" command updates the Medplum app in a Medplum CloudFormation stack to the latest version.\n * @param tag - The Medplum stack tag.\n * @param options - The update options.\n */\nexport async function updateAppCommand(tag: string, options: UpdateAppOptions): Promise<void> {\n  const config = readConfig(tag, options);\n  if (!config) {\n    printConfigNotFound(tag, options);\n    throw new Error(`Config not found: ${tag}`);\n  }\n  const details = await getStackByTag(tag);\n  if (!details) {\n    await printStackNotFound(tag);\n    throw new Error(`Stack not found: ${tag}`);\n  }\n  const appBucket = details.appBucket;\n  if (!appBucket) {\n    throw new Error(`App bucket not found for stack ${tag}`);\n  }\n\n  let tmpDir: string;\n\n  if (options.tarPath) {\n    tmpDir = options.tarPath;\n  } else {\n    const version = options?.toVersion ?? 'latest';\n    tmpDir = await downloadNpmPackage('@medplum/app', version);\n  }\n\n  // Replace variables in the app\n  replaceVariables(tmpDir, {\n    MEDPLUM_BASE_URL: config.baseUrl as string,\n    MEDPLUM_CLIENT_ID: config.clientId ?? '',\n    GOOGLE_CLIENT_ID: config.googleClientId ?? '',\n    RECAPTCHA_SITE_KEY: config.recaptchaSiteKey ?? '',\n    MEDPLUM_REGISTER_ENABLED: config.registerEnabled ? 'true' : 'false',\n  });\n\n  // Upload the app to S3 with correct content-type and cache-control\n  await uploadAppToS3(tmpDir, appBucket.PhysicalResourceId as string, options);\n\n  // Create a CloudFront invalidation to clear any cached resources\n  if (details.appDistribution?.PhysicalResourceId && !options.dryrun) {\n    await createInvalidation(details.appDistribution.PhysicalResourceId);\n  }\n\n  console.log('Done');\n}\n\n/**\n * Returns NPM package metadata for a given package name.\n * See: https://github.com/npm/registry/blob/master/docs/REGISTRY-API.md#getpackageversion\n * @param packageName - The npm package name.\n * @param version - The npm package version string.\n * @returns The package.json metadata content.\n */\nasync function getNpmPackageMetadata(packageName: string, version: string): Promise<any> {\n  const url = `https://registry.npmjs.org/${packageName}/${version}`;\n  const response = await fetch(url);\n  return response.json();\n}\n\n/**\n * Downloads and extracts an NPM package.\n * @param packageName - The NPM package name.\n * @param version - The NPM package version or \"latest\".\n * @returns Path to temporary directory where the package was downloaded and extracted.\n */\nasync function downloadNpmPackage(packageName: string, version: string): Promise<string> {\n  const packageMetadata = await getNpmPackageMetadata(packageName, version);\n  const tarballUrl = packageMetadata.dist.tarball as string;\n  const tmpDir = mkdtempSync(join(tmpdir(), 'tarball-'));\n  try {\n    const response = await fetch(tarballUrl);\n    const extractor = safeTarExtractor(tmpDir);\n    await pipeline(response.body, extractor);\n    return join(tmpDir, 'package', 'dist');\n  } catch (error) {\n    rmSync(tmpDir, { recursive: true, force: true });\n    throw error;\n  }\n}\n\n/**\n * Replaces variables in all JS files in the given folder.\n * @param folderName - The folder name of the files.\n * @param replacements - The collection of variable placeholders and replacements.\n */\nfunction replaceVariables(folderName: string, replacements: Record<string, string>): void {\n  for (const item of readdirSync(folderName, { withFileTypes: true })) {\n    const itemPath = join(folderName, item.name);\n    if (item.isDirectory()) {\n      replaceVariables(itemPath, replacements);\n    } else if (item.isFile() && itemPath.endsWith('.js')) {\n      replaceVariablesInFile(itemPath, replacements);\n    }\n  }\n}\n\n/**\n * Replaces variables in the JS file.\n * @param fileName - The file name.\n * @param replacements - The collection of variable placeholders and replacements.\n */\nfunction replaceVariablesInFile(fileName: string, replacements: Record<string, string>): void {\n  let contents = readFileSync(fileName, 'utf-8');\n  for (const [placeholder, replacement] of Object.entries(replacements)) {\n    contents = contents.replaceAll(`__${placeholder}__`, replacement);\n  }\n  writeFileSync(fileName, contents);\n}\n\n/**\n * Uploads the app to S3.\n * Ensures correct content-type and cache-control for each file.\n * @param tmpDir - The temporary directory where the app is located.\n * @param bucketName - The destination S3 bucket name.\n * @param options - The update options.\n */\nasync function uploadAppToS3(tmpDir: string, bucketName: string, options: UpdateAppOptions): Promise<void> {\n  // Manually iterate and upload files\n  // Automatic content-type detection is not reliable on Microsoft Windows\n  // So we explicitly set content-type\n  const uploadPatterns: [string, string, boolean][] = [\n    // Cached\n    // These files generally have a hash, so they can be cached forever\n    // It is important to upload them first to avoid broken references from index.html\n    ['assets/**/*.css', ContentType.CSS, true],\n    ['assets/**/*.css.map', ContentType.JSON, true],\n    ['assets/**/*.js', ContentType.JAVASCRIPT, true],\n    ['assets/**/*.js.map', ContentType.JSON, true],\n    ['assets/**/*.txt', ContentType.TEXT, true],\n    ['assets/**/*.ico', ContentType.FAVICON, true],\n    ['img/**/*.png', ContentType.PNG, true],\n    ['img/**/*.svg', ContentType.SVG, true],\n    ['robots.txt', ContentType.TEXT, true],\n\n    // Not cached\n    ['index.html', ContentType.HTML, false],\n  ];\n  for (const uploadPattern of uploadPatterns) {\n    await uploadFolderToS3({\n      rootDir: tmpDir,\n      bucketName,\n      fileNamePattern: uploadPattern[0],\n      contentType: uploadPattern[1],\n      cached: uploadPattern[2],\n      dryrun: options.dryrun,\n    });\n  }\n}\n\n/**\n * Uploads a directory of files to S3.\n * @param options - The upload options such as bucket name, content type, and cache control.\n * @param options.rootDir - The root directory of the upload.\n * @param options.bucketName - The destination bucket name.\n * @param options.fileNamePattern - The glob file pattern to upload.\n * @param options.contentType - The content type MIME type.\n * @param options.cached - True to mark as public and cached forever.\n * @param options.dryrun - True to skip the upload.\n */\nasync function uploadFolderToS3(options: {\n  rootDir: string;\n  bucketName: string;\n  fileNamePattern: string;\n  contentType: string;\n  cached: boolean;\n  dryrun?: boolean;\n}): Promise<void> {\n  const items = fastGlob.sync(options.fileNamePattern, { cwd: options.rootDir });\n  for (const item of items) {\n    await uploadFileToS3(join(options.rootDir, item), options);\n  }\n}\n\n/**\n * Uploads a file to S3.\n * @param filePath - The file path.\n * @param options - The upload options such as bucket name, content type, and cache control.\n * @param options.rootDir - The root directory of the upload.\n * @param options.bucketName - The destination bucket name.\n * @param options.contentType - The content type MIME type.\n * @param options.cached - True to mark as public and cached forever.\n * @param options.dryrun - True to skip the upload.\n */\nasync function uploadFileToS3(\n  filePath: string,\n  options: {\n    rootDir: string;\n    bucketName: string;\n    contentType: string;\n    cached: boolean;\n    dryrun?: boolean;\n  }\n): Promise<void> {\n  const fileStream = createReadStream(filePath);\n  const s3Key = filePath\n    .substring(options.rootDir.length + 1)\n    .split(sep)\n    .join('/');\n\n  const putObjectParams = {\n    Bucket: options.bucketName,\n    Key: s3Key,\n    Body: fileStream,\n    ContentType: options.contentType,\n    CacheControl: options.cached ? 'public, max-age=31536000' : 'no-cache, no-store, must-revalidate',\n  };\n\n  console.log(`Uploading ${s3Key} to ${options.bucketName}...`);\n  if (!options.dryrun) {\n    await s3Client.send(new PutObjectCommand(putObjectParams));\n  }\n}\n", "import { StackResource } from '@aws-sdk/client-cloudformation';\nimport { GetBucketPolicyCommand, PutBucketPolicyCommand } from '@aws-sdk/client-s3';\nimport { readConfig } from '../utils';\nimport { createInvalidation, getStackByTag, printConfigNotFound, printStackNotFound, s3Client } from './utils';\n\nexport interface UpdateBucketPoliciesOptions {\n  file?: string;\n  dryrun?: boolean;\n}\n\ninterface Policy {\n  Version?: string;\n  Statement?: PolicyStatement[];\n}\n\ninterface PolicyStatement {\n  Effect?: string;\n  Principal?: { AWS: string };\n  Action?: string | string[];\n  Resource?: string | string[];\n}\n\n/**\n * The AWS \"update-bucket-policies\" command adds necessary policy statements to S3 bucket policy documents.\n *\n * This is necessary for Medplum deployments outside of the us-east-1 region.\n *\n * @param tag - The Medplum stack tag.\n * @param options - The update options.\n */\nexport async function updateBucketPoliciesCommand(tag: string, options: UpdateBucketPoliciesOptions): Promise<void> {\n  const config = readConfig(tag, options);\n  if (!config) {\n    printConfigNotFound(tag, options);\n    throw new Error(`Config not found: ${tag}`);\n  }\n\n  const details = await getStackByTag(tag);\n  if (!details) {\n    await printStackNotFound(tag);\n    throw new Error(`Stack not found: ${tag}`);\n  }\n\n  await updateBucketPolicy('App', details.appBucket, details.appDistribution, details.appOriginAccessIdentity, options);\n\n  await updateBucketPolicy(\n    'Storage',\n    details.storageBucket,\n    details.storageDistribution,\n    details.storageOriginAccessIdentity,\n    options\n  );\n\n  console.log('Done');\n}\n\nexport async function updateBucketPolicy(\n  friendlyName: string,\n  bucketResource: StackResource | undefined,\n  distributionResource: StackResource | undefined,\n  oaiResource: StackResource | undefined,\n  options: UpdateBucketPoliciesOptions\n): Promise<void> {\n  if (!bucketResource?.PhysicalResourceId) {\n    throw new Error(`${friendlyName} bucket not found`);\n  }\n\n  if (!distributionResource?.PhysicalResourceId) {\n    throw new Error(`${friendlyName} distribution not found`);\n  }\n\n  if (!oaiResource?.PhysicalResourceId) {\n    throw new Error(`${friendlyName} OAI not found`);\n  }\n\n  const bucketName = bucketResource.PhysicalResourceId;\n  const oaiId = oaiResource.PhysicalResourceId;\n  const bucketPolicy = await getPolicy(bucketName);\n  if (policyHasStatement(bucketPolicy, bucketName, oaiId)) {\n    throw new Error(`${friendlyName} bucket already has policy statement`);\n  }\n\n  addPolicyStatement(bucketPolicy, bucketName, oaiId);\n  console.log(`${friendlyName} bucket policy:`);\n  console.log(JSON.stringify(bucketPolicy, undefined, 2));\n\n  if (options.dryrun) {\n    console.log('Dry run - skipping updates');\n  } else {\n    // Apply the updated policy\n    console.log('Updating bucket policy...');\n    await setPolicy(bucketName, bucketPolicy);\n    console.log('Bucket policy updated');\n\n    // Create a CloudFront invalidation to clear any cached responses\n    console.log('Creating CloudFront invalidation...');\n    await createInvalidation(distributionResource.PhysicalResourceId);\n    console.log('CloudFront invalidation created');\n\n    console.log(`${friendlyName} bucket policy updated`);\n  }\n}\n\nasync function getPolicy(bucketName: string): Promise<Policy> {\n  const policyResponse = await s3Client.send(\n    new GetBucketPolicyCommand({\n      Bucket: bucketName,\n    })\n  );\n  return JSON.parse(policyResponse.Policy ?? '{}') as Policy;\n}\n\nasync function setPolicy(bucketName: string, policy: Policy): Promise<void> {\n  await s3Client.send(\n    new PutBucketPolicyCommand({\n      Bucket: bucketName,\n      Policy: JSON.stringify(policy),\n    })\n  );\n}\n\nfunction policyHasStatement(policy: Policy, bucketName: string, oaiId: string): boolean {\n  return !!policy?.Statement?.some((s: PolicyStatement) => {\n    return (\n      s?.Effect === 'Allow' &&\n      s?.Principal?.AWS === `arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity ${oaiId}` &&\n      Array.isArray(s?.Action) &&\n      s?.Action?.includes('s3:GetObject*') &&\n      s?.Action?.includes('s3:GetBucket*') &&\n      s?.Action?.includes('s3:List*') &&\n      Array.isArray(s?.Resource) &&\n      s?.Resource?.includes(`arn:aws:s3:::${bucketName}`) &&\n      s?.Resource?.includes(`arn:aws:s3:::${bucketName}/*`)\n    );\n  });\n}\n\nfunction addPolicyStatement(policy: Policy, bucketName: string, oaiId: string): void {\n  if (!policy.Version) {\n    policy.Version = '2012-10-17';\n  }\n\n  if (!policy.Statement) {\n    policy.Statement = [];\n  }\n\n  policy.Statement.push({\n    Effect: 'Allow',\n    Principal: {\n      AWS: `arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity ${oaiId}`,\n    },\n    Action: ['s3:GetObject*', 's3:GetBucket*', 's3:List*'],\n    Resource: [`arn:aws:s3:::${bucketName}`, `arn:aws:s3:::${bucketName}/*`],\n  });\n}\n", "import { MedplumInfraConfig } from '@medplum/core';\nimport { color } from '../util/color';\nimport { getConfigFileName, readConfig, readServerConfig } from '../utils';\nimport { closeTerminal, initTerminal, print, yesOrNo } from './terminal';\nimport { printConfigNotFound, writeParameters } from './utils';\n\nexport interface UpdateConfigOptions {\n  file?: string;\n  dryrun?: boolean;\n  yes?: boolean;\n}\n\n/**\n * The AWS \"update-config\" command updates AWS Parameter Store values with values from the local config file.\n * @param tag - The Medplum stack tag.\n * @param options - Additional command line options.\n */\nexport async function updateConfigCommand(tag: string, options: UpdateConfigOptions): Promise<void> {\n  try {\n    initTerminal();\n\n    const infraConfig = readConfig(tag, options) as MedplumInfraConfig;\n    if (!infraConfig) {\n      printConfigNotFound(tag, options);\n      throw new Error(`Config not found: ${tag}`);\n    }\n\n    const serverConfig = readServerConfig(tag) ?? {};\n\n    // If the server config is empty, prompt the user to proceed\n    if (!options.yes && Object.keys(serverConfig).length === 0) {\n      const serverConfigFileName = getConfigFileName(tag, { server: true });\n      console.log(color.yellow(`Config file ${serverConfigFileName} not found!`));\n      if (!(await yesOrNo('Do you want to proceed?'))) {\n        console.log(color.red(`Run Aborted, please ensure ${serverConfigFileName} is present and try again.`));\n        return;\n      }\n    }\n\n    checkConfigConflicts(infraConfig, serverConfig);\n    mergeConfigs(infraConfig, serverConfig);\n\n    print('Medplum uses AWS Parameter Store to store sensitive configuration values.');\n    print('These values will be encrypted at rest.');\n    print(`The values will be stored in the \"/medplum/${infraConfig.name}\" path.`);\n\n    print(\n      JSON.stringify(\n        {\n          ...serverConfig,\n          signingKey: '****',\n          signingKeyPassphrase: '****',\n        },\n        null,\n        2\n      )\n    );\n\n    if (options.dryrun) {\n      console.log(color.yellow('Dry run - skipping updates!'));\n    } else if (options.yes || (await yesOrNo('Do you want to store these values in AWS Parameter Store?'))) {\n      await writeParameters(infraConfig.region, `/medplum/${infraConfig.name}/`, serverConfig);\n    }\n  } finally {\n    closeTerminal();\n  }\n}\n\nexport function checkConfigConflicts(\n  infraConfig: MedplumInfraConfig,\n  serverConfig: Record<string, string | number>\n): void {\n  checkConflict(\n    infraConfig.apiPort,\n    serverConfig.port,\n    `Infra \"apiPort\" (${infraConfig.apiPort}) does not match server \"port\" (${serverConfig.port})`\n  );\n\n  checkConflict(\n    infraConfig.baseUrl,\n    serverConfig.baseUrl,\n    `Infra \"baseUrl\" (${infraConfig.baseUrl}) does not match server \"baseUrl\" (${serverConfig.baseUrl})`\n  );\n\n  checkConflict(\n    infraConfig.appDomainName && `https://${infraConfig.appDomainName}/`,\n    serverConfig.appBaseUrl,\n    `Infra \"appDomainName\" (${infraConfig.appDomainName}) does not match server \"appBaseUrl\" (${serverConfig.appBaseUrl})`\n  );\n\n  checkConflict(\n    infraConfig.storageDomainName && `https://${infraConfig.storageDomainName}/binary/`,\n    serverConfig.storageBaseUrl,\n    `Infra \"storageDomainName\" (${infraConfig.storageDomainName}) does not match server \"storageBaseUrl\" (${serverConfig.storageBaseUrl})`\n  );\n}\n\nfunction checkConflict<T>(a: T, b: T, message: string): void {\n  if (isConflict(a, b)) {\n    throw new Error(message);\n  }\n}\n\nfunction isConflict<T>(a: T, b: T): boolean {\n  return a !== undefined && b !== undefined && a !== b;\n}\n\nexport function mergeConfigs(infraConfig: MedplumInfraConfig, serverConfig: Record<string, string | number>): void {\n  if (infraConfig.apiPort) {\n    serverConfig.port = infraConfig.apiPort;\n  }\n  if (infraConfig.baseUrl) {\n    serverConfig.baseUrl = infraConfig.baseUrl;\n  }\n  if (infraConfig.appDomainName) {\n    serverConfig.appBaseUrl = `https://${infraConfig.appDomainName}/`;\n  }\n  if (infraConfig.storageDomainName) {\n    serverConfig.storageBaseUrl = `https://${infraConfig.storageDomainName}/`;\n  }\n}\n", "import { MedplumClient, MedplumClientOptions, MedplumInfraConfig } from '@medplum/core';\nimport { spawnSync } from 'node:child_process';\nimport * as semver from 'semver';\nimport { createMedplumClient } from '../util/client';\nimport { getConfigFileName, readConfig, writeConfig } from '../utils';\nimport { getServerVersions, printConfigNotFound } from './utils';\n\nexport interface UpdateServerOptions extends MedplumClientOptions {\n  file?: string;\n  toVersion?: string;\n}\n\n/**\n * The AWS \"update-server\" command updates the Medplum server in a Medplum CloudFormation stack.\n * @param tag - The Medplum stack tag.\n * @param options - Client options\n */\nexport async function updateServerCommand(tag: string, options: UpdateServerOptions): Promise<void> {\n  const client = await createMedplumClient(options);\n  const config = readConfig(tag, options) as MedplumInfraConfig;\n  if (!config) {\n    console.log(`Configuration file ${getConfigFileName(tag)} not found`);\n    printConfigNotFound(tag, options);\n    throw new Error(`Config not found: ${tag}`);\n  }\n\n  const separatorIndex = config.serverImage.lastIndexOf(':');\n  const serverImagePrefix = config.serverImage.slice(0, separatorIndex);\n\n  const initialVersion = await getCurrentVersion(client, config);\n\n  let updateVersion = await nextUpdateVersion(initialVersion);\n  while (updateVersion) {\n    if (options.toVersion && semver.gt(updateVersion, options.toVersion)) {\n      console.log(`Skipping update to v${updateVersion}`);\n      break;\n    }\n\n    console.log(`Performing update to v${updateVersion}`);\n    config.serverImage = `${serverImagePrefix}:${updateVersion}`;\n    deployServerUpdate(tag, config);\n\n    // Run data migrations\n    await client.startAsyncRequest('/admin/super/migrate');\n\n    updateVersion = await nextUpdateVersion(updateVersion);\n  }\n}\n\nasync function getCurrentVersion(medplum: MedplumClient, config: MedplumInfraConfig): Promise<string> {\n  const separatorIndex = config.serverImage.lastIndexOf(':');\n  let initialVersion = config.serverImage.slice(separatorIndex + 1);\n  if (initialVersion === 'latest') {\n    const serverInfo = await medplum.get('/healthcheck');\n    initialVersion = serverInfo.version as string;\n    const sep = initialVersion.indexOf('-');\n    if (sep > -1) {\n      initialVersion = initialVersion.slice(0, sep);\n    }\n  }\n  return initialVersion;\n}\n\nasync function nextUpdateVersion(currentVersion: string, targetVersion?: string): Promise<string | undefined> {\n  // The list of server versions is sorted in descending order\n  // The first entry is the latest version\n  // The last entry is the oldest version\n  // We want to find the \"next\" version after our current version\n  // Filter the list to only include versions that are greater than or equal to the current minor version\n  // Then pop the last entry from the list\n  const allVersions = await getServerVersions(currentVersion);\n  const latestVersion = allVersions[0];\n  return allVersions\n    .filter(\n      (v) => v === latestVersion || v === targetVersion || semver.gte(v, semver.inc(currentVersion, 'minor') as string)\n    )\n    .pop();\n}\n\nfunction deployServerUpdate(tag: string, config: MedplumInfraConfig): void {\n  const configFile = getConfigFileName(tag);\n  writeConfig(configFile, config);\n\n  const cmd = `npx cdk deploy -c config=${configFile}${config.region !== 'us-east-1' ? ' --all' : ''}`;\n  console.log('> ' + cmd);\n  const deploy = spawnSync(cmd, { stdio: 'inherit' });\n\n  if (deploy.status !== 0) {\n    throw new Error(`Deploy of ${config.serverImage} failed (exit code ${deploy.status}): ${deploy.stderr}`);\n  }\n  console.log(deploy.stdout);\n}\n", "import { color, processDescription } from '../util/color';\nimport { addSubcommand, MedplumCommand } from '../utils';\nimport { describeStacksCommand } from './describe';\nimport { initStackCommand } from './init';\nimport { listStacksCommand } from './list';\nimport { updateAppCommand } from './update-app';\nimport { updateBucketPoliciesCommand } from './update-bucket-policies';\nimport { updateConfigCommand } from './update-config';\nimport { updateServerCommand } from './update-server';\n\nexport function buildAwsCommand(): MedplumCommand {\n  const aws = new MedplumCommand('aws').description('Commands to manage AWS resources');\n\n  aws.command('init').description('Initialize a new Medplum AWS CloudFormation stacks').action(initStackCommand);\n\n  aws.command('list').description('List Medplum AWS CloudFormation stacks').action(listStacksCommand);\n\n  aws\n    .command('describe')\n    .description('Describe a Medplum AWS CloudFormation stack by tag')\n    .argument('<tag>', 'The Medplum stack tag')\n    .action(describeStacksCommand);\n\n  aws\n    .command('update-config')\n    .alias('deploy-config')\n    .summary('Update the AWS Parameter Store config values.')\n    .description(\n      processDescription(\n        'Update the AWS Parameter Store config values.\\n\\nConfiguration values come from a file named **medplum.<tag>.config.server.json** where **<tag>** is the Medplum stack tag.\\n\\n' +\n          color.yellow('**Services must be restarted to apply changes.**')\n      )\n    )\n    .argument('<tag>', 'The Medplum stack tag')\n    .option(\n      '--file [file]',\n      processDescription(\n        'File to provide overrides for **apiPort**, **baseUrl**, **appDomainName** and **storageDomainName** values that appear in the config file.'\n      )\n    )\n    .option(\n      '--dryrun',\n      'Displays the operations that would be performed using the specified command without actually running them.'\n    )\n    .option('--yes', 'Automatically confirm the update')\n    .action(updateConfigCommand);\n\n  addSubcommand(\n    aws,\n    new MedplumCommand('update-server')\n      .alias('deploy-server')\n      .description('Update the server image')\n      .argument('<tag>', 'The Medplum stack tag')\n      .option('--file [file]', 'Specifies the config file to use. If not specified, the file is based on the tag.')\n      .option(\n        '--to-version [version]',\n        'Specifies the version of the configuration to update. If not specified, the latest version is updated.'\n      )\n      .action(updateServerCommand)\n  );\n\n  aws\n    .command('update-app')\n    .alias('deploy-app')\n    .description('Update the app site')\n    .argument('<tag>', 'The Medplum stack tag')\n    .option('--file [file]', 'Specifies the config file to use. If not specified, the file is based on the tag.')\n    .option(\n      '--to-version [version]',\n      'Specifies the version of the configuration to update. If not specified, the latest version is updated.'\n    )\n    .option(\n      '--dryrun',\n      'Displays the operations that would be performed using the specified command without actually running them.'\n    )\n    .option('--tar-path [tarPath]', 'Specifies the path to the extracted tarball of the @medplum/app package.')\n    .action(updateAppCommand);\n\n  aws\n    .command('update-bucket-policies')\n    .description('Update S3 bucket policies')\n    .argument('<tag>', 'The Medplum stack tag')\n    .option('--file [file]', 'Specifies the config file to use. If not specified, the file is based on the tag.')\n    .option(\n      '--dryrun',\n      'Displays the operations that would be performed using the specified command without actually running them.'\n    )\n    .action(updateBucketPoliciesCommand);\n\n  return aws;\n}\n", "import { MedplumClient } from '@medplum/core';\nimport { createMedplumClient } from './util/client';\nimport { MedplumCommand, addSubcommand, createBot, deployBot, readBotConfigs, saveBot } from './utils';\n\nconst botSaveCommand = new MedplumCommand('save');\nconst botDeployCommand = new MedplumCommand('deploy');\nconst botCreateCommand = new MedplumCommand('create');\n\nexport const bot = new MedplumCommand('bot');\naddSubcommand(bot, botSaveCommand);\naddSubcommand(bot, botDeployCommand);\naddSubcommand(bot, botCreateCommand);\n\n// Commands to deprecate\nexport const saveBotDeprecate = new MedplumCommand('save-bot');\nexport const deployBotDeprecate = new MedplumCommand('deploy-bot');\nexport const createBotDeprecate = new MedplumCommand('create-bot');\n\nbotSaveCommand\n  .description('Saving the bot')\n  .argument('<botName>')\n  .action(async (botName, options) => {\n    const medplum = await createMedplumClient(options);\n\n    await botWrapper(medplum, botName);\n  });\n\nbotDeployCommand\n  .description('Deploy the app to AWS')\n  .argument('<botName>')\n  .action(async (botName, options) => {\n    const medplum = await createMedplumClient(options);\n\n    await botWrapper(medplum, botName, true);\n  });\n\nbotCreateCommand\n  .arguments('<botName> <projectId> <sourceFile> <distFile>')\n  .description('Creating a bot')\n  .option('--runtime-version <runtimeVersion>', 'Runtime version (awslambda, vmcontext)')\n  .option('--no-write-config', 'Do not write bot to config')\n  .action(async (botName, projectId, sourceFile, distFile, options) => {\n    const medplum = await createMedplumClient(options);\n\n    await createBot(medplum, botName, projectId, sourceFile, distFile, options.runtimeVersion, !!options.writeConfig);\n  });\n\nexport async function botWrapper(medplum: MedplumClient, botName: string, deploy = false): Promise<void> {\n  const botConfigs = readBotConfigs(botName);\n  const errors = [] as Error[];\n  const errored = [] as string[];\n  let saved = 0;\n  let deployed = 0;\n\n  for (const botConfig of botConfigs) {\n    try {\n      const bot = await medplum.readResource('Bot', botConfig.id);\n      await saveBot(medplum, botConfig, bot);\n      saved++;\n      if (deploy) {\n        await deployBot(medplum, botConfig, bot);\n        deployed++;\n      }\n    } catch (err: unknown) {\n      errors.push(err as Error);\n      errored.push(`${botConfig.name} [${botConfig.id}]`);\n    }\n  }\n\n  console.log(`Number of bots saved: ${saved}`);\n  console.log(`Number of bots deployed: ${deployed}`);\n  console.log(`Number of errors: ${errors.length}`);\n\n  if (errors.length) {\n    throw new Error(`${errors.length} bot(s) had failures. Bots with failures:\\n\\n    ${errored.join('\\n    ')}`, {\n      cause: errors,\n    });\n  }\n}\n\n// Deprecate bot commands\nsaveBotDeprecate\n  .description('Saves the bot')\n  .argument('<botName>')\n  .action(async (botName, options) => {\n    const medplum = await createMedplumClient(options);\n\n    await botWrapper(medplum, botName);\n  });\n\ndeployBotDeprecate\n  .description('Deploy the bot to AWS')\n  .argument('<botName>')\n  .action(async (botName, options) => {\n    const medplum = await createMedplumClient(options);\n\n    await botWrapper(medplum, botName, true);\n  });\n\ncreateBotDeprecate\n  .arguments('<botName> <projectId> <sourceFile> <distFile>')\n  .description('Creates and saves the bot')\n  .action(async (botName, projectId, sourceFile, distFile, options) => {\n    const medplum = await createMedplumClient(options);\n\n    await createBot(medplum, botName, projectId, sourceFile, distFile);\n  });\n", "import { MedplumClient } from '@medplum/core';\nimport { BundleEntry, ExplanationOfBenefit, ExplanationOfBenefitItem, Resource } from '@medplum/fhirtypes';\nimport { createReadStream, writeFile } from 'node:fs';\nimport { resolve } from 'node:path';\nimport { createInterface } from 'node:readline';\nimport { createMedplumClient } from './util/client';\nimport { MedplumCommand, addSubcommand, getUnsupportedExtension, prettyPrint } from './utils';\n\nconst bulkExportCommand = new MedplumCommand('export');\nconst bulkImportCommand = new MedplumCommand('import');\n\nexport const bulk = new MedplumCommand('bulk');\naddSubcommand(bulk, bulkExportCommand);\naddSubcommand(bulk, bulkImportCommand);\n\nbulkExportCommand\n  .option(\n    '-e, --export-level <exportLevel>',\n    'Optional export level. Defaults to system level export. \"Group/:id\" - Group of Patients, \"Patient\" - All Patients.'\n  )\n  .option('-t, --types <types>', 'optional resource types to export')\n  .option(\n    '-s, --since <since>',\n    'optional Resources will be included in the response if their state has changed after the supplied time (e.g. if Resource.meta.lastUpdated is later than the supplied _since time).'\n  )\n  .option(\n    '-d, --target-directory <targetDirectory>',\n    'optional target directory to save files from the bulk export operations.'\n  )\n  .action(async (options) => {\n    const { exportLevel, types, since, targetDirectory } = options;\n    const medplum = await createMedplumClient(options);\n    const response = await medplum.bulkExport(exportLevel, types, since, { pollStatusOnAccepted: true });\n\n    response.output?.forEach(async ({ type, url }) => {\n      const fileUrl = new URL(url as string);\n      const data = await medplum.download(url as string);\n      const fileName = `${type}_${fileUrl.pathname}`.replace(/[^a-zA-Z0-9]+/g, '_') + '.ndjson';\n      const path = resolve(targetDirectory ?? '', fileName);\n\n      writeFile(`${path}`, await data.text(), () => {\n        console.log(`${path} is created`);\n      });\n    });\n  });\n\nbulkImportCommand\n  .argument('<filename>', 'File Name')\n  .option(\n    '--num-resources-per-request <numResourcesPerRequest>',\n    'optional number of resources to import per batch request. Defaults to 25.',\n    '25'\n  )\n  .option(\n    '--add-extensions-for-missing-values',\n    'optional flag to add extensions for missing values in a resource',\n    false\n  )\n  .option('-d, --target-directory <targetDirectory>', 'optional target directory of file to be imported')\n  .action(async (fileName, options) => {\n    const { numResourcesPerRequest, addExtensionsForMissingValues, targetDirectory } = options;\n    const path = resolve(targetDirectory ?? process.cwd(), fileName);\n    const medplum = await createMedplumClient(options);\n\n    await importFile(path, Number.parseInt(numResourcesPerRequest, 10), medplum, addExtensionsForMissingValues);\n  });\n\nasync function importFile(\n  path: string,\n  numResourcesPerRequest: number,\n  medplum: MedplumClient,\n  addExtensionsForMissingValues: boolean\n): Promise<void> {\n  let entries: BundleEntry[] = [];\n  const fileStream = createReadStream(path);\n  const rl = createInterface({\n    input: fileStream,\n  });\n\n  for await (const line of rl) {\n    const resource = parseResource(line, addExtensionsForMissingValues);\n    entries.push({\n      resource: resource,\n      request: {\n        method: 'POST',\n        url: resource.resourceType,\n      },\n    });\n    if (entries.length % numResourcesPerRequest === 0) {\n      await sendBatchEntries(entries, medplum);\n      entries = [];\n    }\n  }\n  if (entries.length > 0) {\n    await sendBatchEntries(entries, medplum);\n  }\n}\n\nasync function sendBatchEntries(entries: BundleEntry[], medplum: MedplumClient): Promise<void> {\n  const result = await medplum.executeBatch({\n    resourceType: 'Bundle',\n    type: 'transaction',\n    entry: entries,\n  });\n\n  result.entry?.forEach((resultEntry) => {\n    prettyPrint(resultEntry.response);\n  });\n}\n\nfunction parseResource(jsonString: string, addExtensionsForMissingValues: boolean): Resource {\n  const resource = JSON.parse(jsonString);\n\n  if (addExtensionsForMissingValues) {\n    return addExtensionsForMissingValuesResource(resource);\n  }\n\n  return resource;\n}\n\nfunction addExtensionsForMissingValuesResource(resource: Resource): Resource {\n  if (resource.resourceType === 'ExplanationOfBenefit') {\n    return addExtensionsForMissingValuesExplanationOfBenefits(resource);\n  }\n  return resource;\n}\n\nfunction addExtensionsForMissingValuesExplanationOfBenefits(resource: ExplanationOfBenefit): ExplanationOfBenefit {\n  if (!resource.provider) {\n    resource.provider = getUnsupportedExtension();\n  }\n\n  resource.item?.forEach((item: ExplanationOfBenefitItem) => {\n    if (!item?.productOrService) {\n      item.productOrService = getUnsupportedExtension();\n    }\n  });\n\n  return resource;\n}\n", "import { formatHl7DateTime, Hl7Message } from '@medplum/core';\nimport { Hl7Client, Hl7Server } from '@medplum/hl7';\nimport { readFileSync } from 'node:fs';\nimport { addSubcommand, MedplumCommand } from './utils';\n\nconst send = new MedplumCommand('send')\n  .description('Send an HL7 v2 message via MLLP')\n  .argument('<host>', 'The destination host name or IP address')\n  .argument('<port>', 'The destination port number')\n  .argument('[body]', 'Optional HL7 message body')\n  .option('--generate-example', 'Generate a sample HL7 message')\n  .option('--file <file>', 'Read the HL7 message from a file')\n  .option('--encoding <encoding>', 'The encoding to use')\n  .action(async (host, port, body, options) => {\n    if (options.generateExample) {\n      body = generateSampleHl7Message();\n    } else if (options.file) {\n      body = readFileSync(options.file, 'utf8');\n    }\n\n    if (!body) {\n      throw new Error('Missing HL7 message body');\n    }\n\n    const client = new Hl7Client({\n      host,\n      port: Number.parseInt(port, 10),\n      encoding: options.encoding,\n    });\n\n    try {\n      const response = await client.sendAndWait(Hl7Message.parse(body));\n      console.log(response.toString().replaceAll('\\r', '\\n'));\n    } finally {\n      client.close();\n    }\n  });\n\nconst listen = new MedplumCommand('listen')\n  .description('Starts an HL7 v2 MLLP server')\n  .argument('<port>')\n  .option('--encoding <encoding>', 'The encoding to use')\n  .action(async (port, options) => {\n    const server = new Hl7Server((connection) => {\n      connection.addEventListener('message', ({ message }) => {\n        console.log(message.toString().replaceAll('\\r', '\\n'));\n        connection.send(message.buildAck());\n      });\n    });\n\n    server.start(Number.parseInt(port, 10), options.encoding);\n    console.log('Listening on port ' + port);\n  });\n\nexport const hl7 = new MedplumCommand('hl7');\naddSubcommand(hl7, send);\naddSubcommand(hl7, listen);\n\nexport function generateSampleHl7Message(): string {\n  const now = formatHl7DateTime(new Date());\n  const controlId = Date.now().toString();\n  return `MSH|^~\\\\&|ADTSYS|HOSPITAL|RECEIVER|DEST|${now}||ADT^A01|${controlId}|P|2.5|\nEVN|A01|${now}||\nPID|1|12345|12345^^^HOSP^MR|123456|DOE^JOHN^MIDDLE^SUFFIX|19800101|M|||123 STREET^APT 4B^CITY^ST^12345-6789||555-555-5555||S|\nPV1|1|I|2000^2012^01||||12345^DOCTOR^DOC||||||||||1234567^DOCTOR^DOC||AMB|||||||||||||||||||||||||202309280900|`;\n}\n", "import { Hl7CloseEvent, Hl7ErrorEvent, Hl7MessageEvent } from './events';\n\nexport interface Hl7EventMap {\n  message: Hl7MessageEvent;\n  error: Hl7ErrorEvent;\n  close: Hl7CloseEvent;\n}\n\nexport abstract class Hl7Base extends EventTarget {\n  addEventListener<K extends keyof Hl7EventMap>(\n    type: K,\n    listener: ((event: Hl7EventMap[K]) => void) | EventListenerObject | null,\n    options?: boolean | AddEventListenerOptions\n  ): void;\n\n  addEventListener(\n    type: string,\n    listener: EventListenerOrEventListenerObject | null,\n    options?: boolean | AddEventListenerOptions\n  ): void {\n    super.addEventListener(type, listener, options);\n  }\n  removeEventListener<K extends keyof Hl7EventMap>(\n    type: K,\n    listener: ((event: Hl7EventMap[K]) => void) | EventListenerObject | null,\n    options?: boolean | AddEventListenerOptions\n  ): void;\n\n  removeEventListener(\n    type: string,\n    listener: EventListenerOrEventListenerObject | null,\n    options?: boolean | AddEventListenerOptions\n  ): void {\n    super.removeEventListener(type, listener, options);\n  }\n}\n", "import { Hl7Message } from '@medplum/core';\nimport { connect, Socket } from 'node:net';\nimport { Hl7Base } from './base';\nimport { Hl7Connection } from './connection';\nimport { Hl7CloseEvent, Hl7ErrorEvent } from './events';\n\nexport interface Hl7ClientOptions {\n  host: string;\n  port: number;\n  encoding?: string;\n  keepAlive?: boolean;\n  connectTimeout?: number; // Add timeout option\n}\n\nexport class Hl7Client extends Hl7Base {\n  options: Hl7ClientOptions;\n  host: string;\n  port: number;\n  encoding?: string;\n  connection?: Hl7Connection;\n  keepAlive: boolean;\n  private socket?: Socket;\n  private connectTimeout: number;\n\n  constructor(options: Hl7ClientOptions) {\n    super();\n    this.options = options;\n    this.host = this.options.host;\n    this.port = this.options.port;\n    this.encoding = this.options.encoding;\n    this.keepAlive = this.options.keepAlive ?? false;\n    this.connectTimeout = this.options.connectTimeout ?? 30000; // Default 30 seconds\n  }\n\n  connect(): Promise<Hl7Connection> {\n    // If we already have a connection, use it\n    if (this.connection) {\n      return Promise.resolve(this.connection);\n    }\n\n    // If there's an ongoing connection attempt, destroy it\n    if (this.socket) {\n      this.socket.removeAllListeners();\n      this.socket.destroy();\n      this.socket = undefined;\n    }\n\n    return new Promise((resolve, reject) => {\n      // Create the socket\n      this.socket = connect({\n        host: this.host,\n        port: this.port,\n        keepAlive: this.keepAlive,\n      });\n\n      // Set timeout if specified\n      if (this.connectTimeout > 0) {\n        this.socket.setTimeout(this.connectTimeout);\n\n        // Handle timeout event\n        this.socket.on('timeout', () => {\n          const error = new Error(`Connection timeout after ${this.connectTimeout}ms`);\n          if (this.socket) {\n            this.socket.destroy();\n            this.socket = undefined;\n          }\n          reject(error);\n        });\n      }\n\n      // Handle successful connection\n      this.socket.on('connect', () => {\n        if (!this.socket) {\n          return; // Socket was already destroyed\n        }\n\n        // Create the HL7 connection\n        let connection: Hl7Connection;\n        this.connection = connection = new Hl7Connection(this.socket, this.encoding);\n\n        // Remove the timeout listener as we're now connected\n        this.socket.setTimeout(0);\n\n        // Set up event handlers\n        connection.addEventListener('close', () => {\n          this.socket = undefined;\n          this.dispatchEvent(new Hl7CloseEvent());\n        });\n\n        connection.addEventListener('error', (event) => {\n          this.dispatchEvent(new Hl7ErrorEvent(event.error));\n        });\n\n        resolve(this.connection);\n      });\n\n      // Handle connection errors\n      this.socket.on('error', (err) => {\n        if (this.socket) {\n          this.socket.destroy();\n          this.socket = undefined;\n        }\n        reject(err);\n      });\n    });\n  }\n\n  async send(msg: Hl7Message): Promise<void> {\n    return (await this.connect()).send(msg);\n  }\n\n  async sendAndWait(msg: Hl7Message): Promise<Hl7Message> {\n    return (await this.connect()).sendAndWait(msg);\n  }\n\n  close(): void {\n    // Close the socket if it exists\n    if (this.socket) {\n      this.socket.removeAllListeners();\n      this.socket.destroy();\n      this.socket = undefined;\n    }\n\n    // Close established connection if it exists\n    if (this.connection) {\n      this.connection.close();\n      delete this.connection;\n    }\n  }\n}\n", "import { Hl7Message } from '@medplum/core';\nimport iconv from 'iconv-lite';\nimport net from 'node:net';\nimport { Hl7Base } from './base';\nimport { CR, FS, VT } from './constants';\nimport { Hl7CloseEvent, Hl7ErrorEvent, Hl7MessageEvent } from './events';\n\n// iconv-lite docs have great examples and explanations for how to use Buffers with iconv-lite:\n// See: https://github.com/ashtuchkin/iconv-lite/wiki/Use-Buffers-when-decoding\n\nexport type Hl7MessageQueueItem = {\n  message: Hl7Message;\n  resolve?: (reply: Hl7Message) => void;\n  reject?: (err: Error) => void;\n};\n\nexport class Hl7Connection extends Hl7Base {\n  readonly socket: net.Socket;\n  readonly encoding: string;\n  readonly enhancedMode: boolean;\n  private chunks: Buffer[] = [];\n  private readonly messageQueue: Hl7MessageQueueItem[] = [];\n\n  constructor(socket: net.Socket, encoding: string = 'utf-8', enhancedMode = false) {\n    super();\n\n    this.socket = socket;\n    this.encoding = encoding;\n    this.enhancedMode = enhancedMode;\n\n    socket.on('data', (data: Buffer) => {\n      try {\n        this.appendData(data);\n        if (data.at(-2) === FS && data.at(-1) === CR) {\n          const buffer = Buffer.concat(this.chunks);\n          const contentBuffer = buffer.subarray(1, buffer.length - 2);\n          const contentString = iconv.decode(contentBuffer, this.encoding);\n          const message = Hl7Message.parse(contentString);\n          this.dispatchEvent(new Hl7MessageEvent(this, message));\n          this.resetBuffer();\n        }\n      } catch (err) {\n        this.dispatchEvent(new Hl7ErrorEvent(err as Error));\n      }\n    });\n\n    socket.on('error', (err) => {\n      this.resetBuffer();\n      this.dispatchEvent(new Hl7ErrorEvent(err));\n    });\n\n    socket.on('end', () => {\n      this.close();\n    });\n\n    this.addEventListener('message', (event) => {\n      if (enhancedMode) {\n        this.send(event.message.buildAck({ ackCode: 'CA' }));\n      }\n      // Get the queue item at the head of the queue\n      const next = this.messageQueue.shift();\n      // If there isn't an item, then throw an error\n      if (!next) {\n        this.dispatchEvent(\n          new Hl7ErrorEvent(\n            new Error(`Received a message when no pending messages were in the queue. Message: ${event.message}`)\n          )\n        );\n        return;\n      }\n      // Resolve the promise if there is one pending for this message\n      next.resolve?.(event.message);\n    });\n  }\n\n  private sendImpl(reply: Hl7Message, queueItem: Hl7MessageQueueItem): void {\n    this.messageQueue.push(queueItem);\n    const replyString = reply.toString();\n    const replyBuffer = iconv.encode(replyString, this.encoding);\n    const outputBuffer = Buffer.alloc(replyBuffer.length + 3);\n    outputBuffer.writeInt8(VT, 0);\n    replyBuffer.copy(outputBuffer, 1);\n    outputBuffer.writeInt8(FS, replyBuffer.length + 1);\n    outputBuffer.writeInt8(CR, replyBuffer.length + 2);\n    this.socket.write(outputBuffer);\n  }\n\n  send(reply: Hl7Message): void {\n    this.sendImpl(reply, { message: reply });\n  }\n\n  async sendAndWait(msg: Hl7Message): Promise<Hl7Message> {\n    return new Promise<Hl7Message>((resolve, reject) => {\n      const queueItem = { message: msg, resolve, reject };\n      this.sendImpl(msg, queueItem);\n    });\n  }\n\n  close(): void {\n    this.socket.end();\n    this.socket.destroy();\n    this.dispatchEvent(new Hl7CloseEvent());\n  }\n\n  private appendData(data: Buffer): void {\n    this.chunks.push(data);\n  }\n\n  private resetBuffer(): void {\n    this.chunks = [];\n  }\n}\n", "/**\n * VT (Vertical Tab) character.\n *\n * In HL7 messages, this character is used to indicate the start of a message.\n */\nexport const VT = 0x0b;\n\n/**\n * CR (Carriage Return) character.\n *\n * In HL7 messages, this character is used to indicate the end of a message.\n */\nexport const CR = 0x0d;\n\n/**\n * FS (File Separator) character.\n *\n * In HL7 messages, this character is used to separate fields.\n */\nexport const FS = 0x1c;\n", "import { Hl7Message } from '@medplum/core';\nimport { Hl7Connection } from './connection';\n\nexport class Hl7MessageEvent extends Event {\n  readonly connection: Hl7Connection;\n  readonly message: Hl7Message;\n\n  constructor(connection: Hl7Connection, message: Hl7Message) {\n    super('message');\n    this.connection = connection;\n    this.message = message;\n  }\n}\n\nexport class Hl7ErrorEvent extends Event {\n  readonly error: Error;\n\n  constructor(error: Error) {\n    super('error');\n    this.error = error;\n  }\n}\n\nexport class Hl7CloseEvent extends Event {\n  constructor() {\n    super('close');\n  }\n}\n", "import net from 'node:net';\nimport { Hl7Connection } from './connection';\n\nexport class Hl7Server {\n  readonly handler: (connection: Hl7Connection) => void;\n  server?: net.Server;\n\n  constructor(handler: (connection: Hl7Connection) => void) {\n    this.handler = handler;\n  }\n\n  start(port: number, encoding?: string, enhancedMode = false): void {\n    const server = net.createServer((socket) => {\n      const connection = new Hl7Connection(socket, encoding, enhancedMode);\n      this.handler(connection);\n    });\n\n    server.listen(port);\n    this.server = server;\n  }\n\n  async stop(): Promise<void> {\n    return new Promise<void>((resolve, reject) => {\n      if (!this.server) {\n        reject(new Error('Stop was called but there is no server running'));\n        return;\n      }\n      this.server.close((err) => {\n        if (err) {\n          reject(err);\n          return;\n        }\n        resolve();\n      });\n      this.server = undefined;\n    });\n  }\n}\n", "import { readdirSync } from 'node:fs';\nimport { homedir } from 'node:os';\nimport { resolve } from 'node:path';\nimport { FileSystemStorage } from './storage';\nimport { MedplumCommand, addSubcommand, loadProfile, saveProfile } from './utils';\n\nconst setProfile = new MedplumCommand('set');\nconst removeProfile = new MedplumCommand('remove');\nconst listProfiles = new MedplumCommand('list');\nconst describeProfile = new MedplumCommand('describe');\n\nexport const profile = new MedplumCommand('profile');\naddSubcommand(profile, setProfile);\naddSubcommand(profile, removeProfile);\naddSubcommand(profile, listProfiles);\naddSubcommand(profile, describeProfile);\n\nsetProfile\n  .argument('<profileName>', 'Name of the profile')\n  .description('Create a new profile or replace it with the given name and its associated properties')\n  .action(async (profileName, options) => {\n    saveProfile(profileName, options);\n  });\n\nremoveProfile\n  .argument('<profileName>', 'Name of the profile')\n  .description('Remove a profile by name')\n  .action(async (profileName) => {\n    const storage = new FileSystemStorage(profileName);\n    storage.setObject('options', undefined);\n    console.log(`${profileName} profile removed`);\n  });\n\nlistProfiles.description('List all profiles saved').action(async () => {\n  const dir = resolve(homedir(), '.medplum');\n  const files = readdirSync(dir);\n  const allProfiles: any[] = [];\n  files.forEach((file) => {\n    const fileName = file.split('.')[0];\n    const storage = new FileSystemStorage(fileName);\n    const profile = storage.getObject('options');\n    if (profile) {\n      allProfiles.push({ profileName: fileName, profile });\n    }\n  });\n  console.log(allProfiles);\n});\n\ndescribeProfile\n  .argument('<profileName>', 'Name of the profile')\n  .description('Describes a profile')\n  .action(async (profileName) => {\n    const profile = loadProfile(profileName);\n    console.log(profile);\n  });\n", "import { InviteRequest, LoginState, MedplumClient } from '@medplum/core';\nimport { Option } from 'commander';\nimport { createMedplumClient } from './util/client';\nimport { addSubcommand, MedplumCommand } from './utils';\n\nconst projectListCommand = new MedplumCommand('list');\nconst projectCurrentCommand = new MedplumCommand('current');\nconst projectSwitchCommand = new MedplumCommand('switch');\nconst projectInviteCommand = new MedplumCommand('invite');\n\nexport const project = new MedplumCommand('project');\naddSubcommand(project, projectListCommand);\naddSubcommand(project, projectCurrentCommand);\naddSubcommand(project, projectSwitchCommand);\naddSubcommand(project, projectInviteCommand);\n\nprojectListCommand.description('List of current projects').action(async (options) => {\n  const medplum = await createMedplumClient(options);\n  projectList(medplum);\n});\n\nfunction projectList(medplum: MedplumClient): void {\n  const logins = medplum.getLogins();\n\n  const projects = logins\n    .map((login: LoginState) => `${login.project.display} (${login.project.reference})`)\n    .join('\\n\\n');\n\n  console.log(projects);\n}\n\nprojectCurrentCommand.description('Project you are currently on').action(async (options) => {\n  const medplum = await createMedplumClient(options);\n  const login = medplum.getActiveLogin();\n  if (!login) {\n    throw new Error('Unauthenticated: run `npx medplum login` to login');\n  }\n  console.log(`${login.project.display} (${login.project.reference})`);\n});\n\nprojectSwitchCommand\n  .description('Switching to another project from the current one')\n  .argument('<projectId>')\n  .action(async (projectId, options) => {\n    const medplum = await createMedplumClient(options);\n    await switchProject(medplum, projectId);\n  });\n\nprojectInviteCommand\n  .description('Invite a member to your current project (run npx medplum project current to confirm)')\n  .arguments('<firstName> <lastName> <email>')\n  .option('--send-email', 'If you want to send the email when inviting the user')\n  .option('--admin', 'If the user you are inviting is an admin')\n  .addOption(\n    new Option('-r, --role <role>', 'Role of user')\n      .choices(['Practitioner', 'Patient', 'RelatedPerson'])\n      .default('Practitioner')\n  )\n  .action(async (firstName, lastName, email, options) => {\n    const medplum = await createMedplumClient(options);\n    const login = medplum.getActiveLogin();\n    if (!login) {\n      throw new Error('Unauthenticated: run `npx medplum login` to login');\n    }\n    if (!login?.project?.reference) {\n      throw new Error('No current project to invite user to');\n    }\n\n    const projectId = login.project.reference.split('/')[1];\n    const inviteBody: InviteRequest = {\n      resourceType: options.role,\n      firstName,\n      lastName,\n      email,\n      sendEmail: !!options.sendEmail,\n      admin: !!options.admin,\n    };\n    await inviteUser(projectId, inviteBody, medplum);\n  });\n\nasync function switchProject(medplum: MedplumClient, projectId: string): Promise<void> {\n  const logins = medplum.getLogins();\n  const login = logins.find((login: LoginState) => login.project.reference?.includes(projectId));\n  if (!login) {\n    throw new Error(`Project ${projectId} not found. Make sure you are added as a user to this project`);\n  }\n  await medplum.setActiveLogin(login);\n  console.log(`Switched to project ${projectId}\\n`);\n}\n\nasync function inviteUser(projectId: string, inviteBody: InviteRequest, medplum: MedplumClient): Promise<void> {\n  await medplum.invite(projectId, inviteBody);\n  if (inviteBody.sendEmail) {\n    console.log('Email sent');\n  }\n  console.log('See your users at https://app.medplum.com/admin/users');\n}\n", "import { convertToTransactionBundle, MedplumClient } from '@medplum/core';\nimport { createMedplumClient } from './util/client';\nimport { MedplumCommand, prettyPrint } from './utils';\n\nexport const deleteObject = new MedplumCommand('delete');\nexport const get = new MedplumCommand('get');\nexport const patch = new MedplumCommand('patch');\nexport const post = new MedplumCommand('post');\nexport const put = new MedplumCommand('put');\n\ndeleteObject.argument('<url>', 'Resource/$id').action(async (url, options) => {\n  const medplum = await createMedplumClient(options);\n  prettyPrint(await medplum.delete(cleanUrl(medplum, url)));\n});\n\nget\n  .argument('<url>', 'Resource/$id')\n  .option('--as-transaction', 'Print out the bundle as a transaction type')\n  .action(async (url, options) => {\n    const medplum = await createMedplumClient(options);\n    const response = await medplum.get(cleanUrl(medplum, url));\n    if (options.asTransaction) {\n      prettyPrint(convertToTransactionBundle(response));\n    } else {\n      prettyPrint(response);\n    }\n  });\n\npatch.arguments('<url> <body>').action(async (url, body, options) => {\n  const medplum = await createMedplumClient(options);\n\n  prettyPrint(await medplum.patch(cleanUrl(medplum, url), parseBody(body)));\n});\n\npost\n  .arguments('<url> <body>')\n  .option('--prefer-async', 'Sets the Prefer header to \"respond-async\"')\n  .action(async (url, body, options) => {\n    const medplum = await createMedplumClient(options);\n\n    const headers = options.preferAsync ? { Prefer: 'respond-async' } : undefined;\n    prettyPrint(await medplum.post(cleanUrl(medplum, url), parseBody(body), undefined, { headers }));\n  });\n\nput.arguments('<url> <body>').action(async (url, body, options) => {\n  const medplum = await createMedplumClient(options);\n\n  prettyPrint(await medplum.put(cleanUrl(medplum, url), parseBody(body)));\n});\n\nfunction parseBody(input: string | undefined): any {\n  if (!input) {\n    return undefined;\n  }\n  try {\n    return JSON.parse(input);\n  } catch (_err) {\n    return input;\n  }\n}\n\nexport function cleanUrl(medplum: MedplumClient, input: string): string {\n  const knownPrefixes = ['admin/', 'auth/', 'fhir/R4'];\n  if (knownPrefixes.some((p) => input.startsWith(p))) {\n    // If the URL starts with a known prefix, return it as-is\n    return input;\n  }\n\n  // Otherwise, default to FHIR\n  return medplum.fhirUrl(input).toString();\n}\n"], "mappings": ";85BAAA,IAAAA,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAIA,IAAMC,GAAsB,QAGtBC,GAAmB,OAAO,kBACL,iBAGrBC,GAA4B,GAI5BC,GAAwB,IAExBC,GAAgB,CACpB,QACA,WACA,QACA,WACA,QACA,WACA,YACF,EAEAL,GAAO,QAAU,CACf,eACA,0BAAAG,GACA,sBAAAC,GACA,iBAAAF,GACA,cAAAG,GACA,oBAAAJ,GACA,wBAAyB,EACzB,WAAY,CACd,ICpCA,IAAAK,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GACJ,OAAO,SAAY,UACnB,QAAQ,KACR,QAAQ,IAAI,YACZ,cAAc,KAAK,QAAQ,IAAI,UAAU,EACvC,IAAIC,IAAS,QAAQ,MAAM,SAAU,GAAGA,CAAI,EAC5C,IAAM,CAAC,EAEXF,GAAO,QAAUC,KCVjB,IAAAE,GAAAC,EAAA,CAAAC,EAAAC,KAAA,cAEA,GAAM,CACJ,0BAAAC,GACA,sBAAAC,GACA,WAAAC,EACF,EAAI,KACEC,GAAQ,KACdL,EAAUC,GAAO,QAAU,CAAC,EAG5B,IAAMK,GAAKN,EAAQ,GAAK,CAAC,EACnBO,GAASP,EAAQ,OAAS,CAAC,EAC3BQ,EAAMR,EAAQ,IAAM,CAAC,EACrBS,GAAUT,EAAQ,QAAU,CAAC,EAC7BU,EAAIV,EAAQ,EAAI,CAAC,EACnBW,GAAI,EAEFC,GAAmB,eAQnBC,GAAwB,CAC5B,CAAC,MAAO,CAAC,EACT,CAAC,MAAOT,EAAU,EAClB,CAACQ,GAAkBT,EAAqB,CAC1C,EAEMW,GAAiBC,GAAU,CAC/B,OAAW,CAACC,EAAOC,CAAG,IAAKJ,GACzBE,EAAQA,EACL,MAAM,GAAGC,CAAK,GAAG,EAAE,KAAK,GAAGA,CAAK,MAAMC,CAAG,GAAG,EAC5C,MAAM,GAAGD,CAAK,GAAG,EAAE,KAAK,GAAGA,CAAK,MAAMC,CAAG,GAAG,EAEjD,OAAOF,CACT,EAEMG,EAAc,CAACC,EAAMJ,EAAOK,IAAa,CAC7C,IAAMC,EAAOP,GAAcC,CAAK,EAC1BO,EAAQX,KACdN,GAAMc,EAAMG,EAAOP,CAAK,EACxBL,EAAES,CAAI,EAAIG,EACVd,EAAIc,CAAK,EAAIP,EACbN,GAAQa,CAAK,EAAID,EACjBf,GAAGgB,CAAK,EAAI,IAAI,OAAOP,EAAOK,EAAW,IAAM,MAAS,EACxDb,GAAOe,CAAK,EAAI,IAAI,OAAOD,EAAMD,EAAW,IAAM,MAAS,CAC7D,EAQAF,EAAY,oBAAqB,aAAa,EAC9CA,EAAY,yBAA0B,MAAM,EAM5CA,EAAY,uBAAwB,gBAAgBN,EAAgB,GAAG,EAKvEM,EAAY,cAAe,IAAIV,EAAIE,EAAE,iBAAiB,CAAC,QAChCF,EAAIE,EAAE,iBAAiB,CAAC,QACxBF,EAAIE,EAAE,iBAAiB,CAAC,GAAG,EAElDQ,EAAY,mBAAoB,IAAIV,EAAIE,EAAE,sBAAsB,CAAC,QACrCF,EAAIE,EAAE,sBAAsB,CAAC,QAC7BF,EAAIE,EAAE,sBAAsB,CAAC,GAAG,EAO5DQ,EAAY,uBAAwB,MAAMV,EAAIE,EAAE,oBAAoB,CACpE,IAAIF,EAAIE,EAAE,iBAAiB,CAAC,GAAG,EAE/BQ,EAAY,4BAA6B,MAAMV,EAAIE,EAAE,oBAAoB,CACzE,IAAIF,EAAIE,EAAE,sBAAsB,CAAC,GAAG,EAMpCQ,EAAY,aAAc,QAAQV,EAAIE,EAAE,oBAAoB,CAC5D,SAASF,EAAIE,EAAE,oBAAoB,CAAC,MAAM,EAE1CQ,EAAY,kBAAmB,SAASV,EAAIE,EAAE,yBAAyB,CACvE,SAASF,EAAIE,EAAE,yBAAyB,CAAC,MAAM,EAK/CQ,EAAY,kBAAmB,GAAGN,EAAgB,GAAG,EAMrDM,EAAY,QAAS,UAAUV,EAAIE,EAAE,eAAe,CACpD,SAASF,EAAIE,EAAE,eAAe,CAAC,MAAM,EAWrCQ,EAAY,YAAa,KAAKV,EAAIE,EAAE,WAAW,CAC/C,GAAGF,EAAIE,EAAE,UAAU,CAAC,IAClBF,EAAIE,EAAE,KAAK,CAAC,GAAG,EAEjBQ,EAAY,OAAQ,IAAIV,EAAIE,EAAE,SAAS,CAAC,GAAG,EAK3CQ,EAAY,aAAc,WAAWV,EAAIE,EAAE,gBAAgB,CAC3D,GAAGF,EAAIE,EAAE,eAAe,CAAC,IACvBF,EAAIE,EAAE,KAAK,CAAC,GAAG,EAEjBQ,EAAY,QAAS,IAAIV,EAAIE,EAAE,UAAU,CAAC,GAAG,EAE7CQ,EAAY,OAAQ,cAAc,EAKlCA,EAAY,wBAAyB,GAAGV,EAAIE,EAAE,sBAAsB,CAAC,UAAU,EAC/EQ,EAAY,mBAAoB,GAAGV,EAAIE,EAAE,iBAAiB,CAAC,UAAU,EAErEQ,EAAY,cAAe,YAAYV,EAAIE,EAAE,gBAAgB,CAAC,WACjCF,EAAIE,EAAE,gBAAgB,CAAC,WACvBF,EAAIE,EAAE,gBAAgB,CAAC,OAC3BF,EAAIE,EAAE,UAAU,CAAC,KACrBF,EAAIE,EAAE,KAAK,CAAC,OACR,EAEzBQ,EAAY,mBAAoB,YAAYV,EAAIE,EAAE,qBAAqB,CAAC,WACtCF,EAAIE,EAAE,qBAAqB,CAAC,WAC5BF,EAAIE,EAAE,qBAAqB,CAAC,OAChCF,EAAIE,EAAE,eAAe,CAAC,KAC1BF,EAAIE,EAAE,KAAK,CAAC,OACR,EAE9BQ,EAAY,SAAU,IAAIV,EAAIE,EAAE,IAAI,CAAC,OAAOF,EAAIE,EAAE,WAAW,CAAC,GAAG,EACjEQ,EAAY,cAAe,IAAIV,EAAIE,EAAE,IAAI,CAAC,OAAOF,EAAIE,EAAE,gBAAgB,CAAC,GAAG,EAI3EQ,EAAY,cAAe,oBACDhB,EAAyB,kBACrBA,EAAyB,oBACzBA,EAAyB,MAAM,EAC7DgB,EAAY,SAAU,GAAGV,EAAIE,EAAE,WAAW,CAAC,cAAc,EACzDQ,EAAY,aAAcV,EAAIE,EAAE,WAAW,EAC7B,MAAMF,EAAIE,EAAE,UAAU,CAAC,QACjBF,EAAIE,EAAE,KAAK,CAAC,gBACJ,EAC5BQ,EAAY,YAAaV,EAAIE,EAAE,MAAM,EAAG,EAAI,EAC5CQ,EAAY,gBAAiBV,EAAIE,EAAE,UAAU,EAAG,EAAI,EAIpDQ,EAAY,YAAa,SAAS,EAElCA,EAAY,YAAa,SAASV,EAAIE,EAAE,SAAS,CAAC,OAAQ,EAAI,EAC9DV,EAAQ,iBAAmB,MAE3BkB,EAAY,QAAS,IAAIV,EAAIE,EAAE,SAAS,CAAC,GAAGF,EAAIE,EAAE,WAAW,CAAC,GAAG,EACjEQ,EAAY,aAAc,IAAIV,EAAIE,EAAE,SAAS,CAAC,GAAGF,EAAIE,EAAE,gBAAgB,CAAC,GAAG,EAI3EQ,EAAY,YAAa,SAAS,EAElCA,EAAY,YAAa,SAASV,EAAIE,EAAE,SAAS,CAAC,OAAQ,EAAI,EAC9DV,EAAQ,iBAAmB,MAE3BkB,EAAY,QAAS,IAAIV,EAAIE,EAAE,SAAS,CAAC,GAAGF,EAAIE,EAAE,WAAW,CAAC,GAAG,EACjEQ,EAAY,aAAc,IAAIV,EAAIE,EAAE,SAAS,CAAC,GAAGF,EAAIE,EAAE,gBAAgB,CAAC,GAAG,EAG3EQ,EAAY,kBAAmB,IAAIV,EAAIE,EAAE,IAAI,CAAC,QAAQF,EAAIE,EAAE,UAAU,CAAC,OAAO,EAC9EQ,EAAY,aAAc,IAAIV,EAAIE,EAAE,IAAI,CAAC,QAAQF,EAAIE,EAAE,SAAS,CAAC,OAAO,EAIxEQ,EAAY,iBAAkB,SAASV,EAAIE,EAAE,IAAI,CACjD,QAAQF,EAAIE,EAAE,UAAU,CAAC,IAAIF,EAAIE,EAAE,WAAW,CAAC,IAAK,EAAI,EACxDV,EAAQ,sBAAwB,SAMhCkB,EAAY,cAAe,SAASV,EAAIE,EAAE,WAAW,CAAC,cAE/BF,EAAIE,EAAE,WAAW,CAAC,QACf,EAE1BQ,EAAY,mBAAoB,SAASV,EAAIE,EAAE,gBAAgB,CAAC,cAEpCF,EAAIE,EAAE,gBAAgB,CAAC,QACpB,EAG/BQ,EAAY,OAAQ,iBAAiB,EAErCA,EAAY,OAAQ,2BAA2B,EAC/CA,EAAY,UAAW,6BAA6B,IC9NpD,IAAAK,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAGA,IAAMC,GAAc,OAAO,OAAO,CAAE,MAAO,EAAK,CAAC,EAC3CC,GAAY,OAAO,OAAO,CAAE,CAAC,EAC7BC,GAAeC,GACdA,EAID,OAAOA,GAAY,SACdH,GAGFG,EAPEF,GASXF,GAAO,QAAUG,KChBjB,IAAAE,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAU,WACVC,GAAqB,CAACC,EAAGC,IAAM,CACnC,IAAMC,EAAOJ,GAAQ,KAAKE,CAAC,EACrBG,EAAOL,GAAQ,KAAKG,CAAC,EAE3B,OAAIC,GAAQC,IACVH,EAAI,CAACA,EACLC,EAAI,CAACA,GAGAD,IAAMC,EAAI,EACZC,GAAQ,CAACC,EAAQ,GACjBA,GAAQ,CAACD,EAAQ,EAClBF,EAAIC,EAAI,GACR,CACN,EAEMG,GAAsB,CAACJ,EAAGC,IAAMF,GAAmBE,EAAGD,CAAC,EAE7DH,GAAO,QAAU,CACf,mBAAAE,GACA,oBAAAK,EACF,ICxBA,IAAAC,EAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAQ,KACR,CAAE,WAAAC,GAAY,iBAAAC,EAAiB,EAAI,KACnC,CAAE,OAAQC,GAAI,EAAAC,EAAE,EAAI,KAEpBC,GAAe,KACf,CAAE,mBAAAC,EAAmB,EAAI,KACzBC,GAAN,MAAMC,CAAO,CACX,YAAaC,EAASC,EAAS,CAG7B,GAFAA,EAAUL,GAAaK,CAAO,EAE1BD,aAAmBD,EAAQ,CAC7B,GAAIC,EAAQ,QAAU,CAAC,CAACC,EAAQ,OAC9BD,EAAQ,oBAAsB,CAAC,CAACC,EAAQ,kBACxC,OAAOD,EAEPA,EAAUA,EAAQ,OAEtB,SAAW,OAAOA,GAAY,SAC5B,MAAM,IAAI,UAAU,gDAAgD,OAAOA,CAAO,IAAI,EAGxF,GAAIA,EAAQ,OAASR,GACnB,MAAM,IAAI,UACR,0BAA0BA,EAAU,aACtC,EAGFD,GAAM,SAAUS,EAASC,CAAO,EAChC,KAAK,QAAUA,EACf,KAAK,MAAQ,CAAC,CAACA,EAAQ,MAGvB,KAAK,kBAAoB,CAAC,CAACA,EAAQ,kBAEnC,IAAMC,EAAIF,EAAQ,KAAK,EAAE,MAAMC,EAAQ,MAAQP,GAAGC,GAAE,KAAK,EAAID,GAAGC,GAAE,IAAI,CAAC,EAEvE,GAAI,CAACO,EACH,MAAM,IAAI,UAAU,oBAAoBF,CAAO,EAAE,EAUnD,GAPA,KAAK,IAAMA,EAGX,KAAK,MAAQ,CAACE,EAAE,CAAC,EACjB,KAAK,MAAQ,CAACA,EAAE,CAAC,EACjB,KAAK,MAAQ,CAACA,EAAE,CAAC,EAEb,KAAK,MAAQT,IAAoB,KAAK,MAAQ,EAChD,MAAM,IAAI,UAAU,uBAAuB,EAG7C,GAAI,KAAK,MAAQA,IAAoB,KAAK,MAAQ,EAChD,MAAM,IAAI,UAAU,uBAAuB,EAG7C,GAAI,KAAK,MAAQA,IAAoB,KAAK,MAAQ,EAChD,MAAM,IAAI,UAAU,uBAAuB,EAIxCS,EAAE,CAAC,EAGN,KAAK,WAAaA,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,IAAKC,GAAO,CAC5C,GAAI,WAAW,KAAKA,CAAE,EAAG,CACvB,IAAMC,EAAM,CAACD,EACb,GAAIC,GAAO,GAAKA,EAAMX,GACpB,OAAOW,CAEX,CACA,OAAOD,CACT,CAAC,EAVD,KAAK,WAAa,CAAC,EAarB,KAAK,MAAQD,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAE,MAAM,GAAG,EAAI,CAAC,EACvC,KAAK,OAAO,CACd,CAEA,QAAU,CACR,YAAK,QAAU,GAAG,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,GACpD,KAAK,WAAW,SAClB,KAAK,SAAW,IAAI,KAAK,WAAW,KAAK,GAAG,CAAC,IAExC,KAAK,OACd,CAEA,UAAY,CACV,OAAO,KAAK,OACd,CAEA,QAASG,EAAO,CAEd,GADAd,GAAM,iBAAkB,KAAK,QAAS,KAAK,QAASc,CAAK,EACrD,EAAEA,aAAiBN,GAAS,CAC9B,GAAI,OAAOM,GAAU,UAAYA,IAAU,KAAK,QAC9C,MAAO,GAETA,EAAQ,IAAIN,EAAOM,EAAO,KAAK,OAAO,CACxC,CAEA,OAAIA,EAAM,UAAY,KAAK,QAClB,EAGF,KAAK,YAAYA,CAAK,GAAK,KAAK,WAAWA,CAAK,CACzD,CAEA,YAAaA,EAAO,CAClB,OAAMA,aAAiBN,IACrBM,EAAQ,IAAIN,EAAOM,EAAO,KAAK,OAAO,GAItCR,GAAmB,KAAK,MAAOQ,EAAM,KAAK,GAC1CR,GAAmB,KAAK,MAAOQ,EAAM,KAAK,GAC1CR,GAAmB,KAAK,MAAOQ,EAAM,KAAK,CAE9C,CAEA,WAAYA,EAAO,CAMjB,GALMA,aAAiBN,IACrBM,EAAQ,IAAIN,EAAOM,EAAO,KAAK,OAAO,GAIpC,KAAK,WAAW,QAAU,CAACA,EAAM,WAAW,OAC9C,MAAO,GACF,GAAI,CAAC,KAAK,WAAW,QAAUA,EAAM,WAAW,OACrD,MAAO,GACF,GAAI,CAAC,KAAK,WAAW,QAAU,CAACA,EAAM,WAAW,OACtD,MAAO,GAGT,IAAIC,EAAI,EACR,EAAG,CACD,IAAMC,EAAI,KAAK,WAAWD,CAAC,EACrBE,EAAIH,EAAM,WAAWC,CAAC,EAE5B,GADAf,GAAM,qBAAsBe,EAAGC,EAAGC,CAAC,EAC/BD,IAAM,QAAaC,IAAM,OAC3B,MAAO,GACF,GAAIA,IAAM,OACf,MAAO,GACF,GAAID,IAAM,OACf,MAAO,GACF,GAAIA,IAAMC,EACf,SAEA,OAAOX,GAAmBU,EAAGC,CAAC,CAElC,OAAS,EAAEF,EACb,CAEA,aAAcD,EAAO,CACbA,aAAiBN,IACrBM,EAAQ,IAAIN,EAAOM,EAAO,KAAK,OAAO,GAGxC,IAAIC,EAAI,EACR,EAAG,CACD,IAAMC,EAAI,KAAK,MAAMD,CAAC,EAChBE,EAAIH,EAAM,MAAMC,CAAC,EAEvB,GADAf,GAAM,gBAAiBe,EAAGC,EAAGC,CAAC,EAC1BD,IAAM,QAAaC,IAAM,OAC3B,MAAO,GACF,GAAIA,IAAM,OACf,MAAO,GACF,GAAID,IAAM,OACf,MAAO,GACF,GAAIA,IAAMC,EACf,SAEA,OAAOX,GAAmBU,EAAGC,CAAC,CAElC,OAAS,EAAEF,EACb,CAIA,IAAKG,EAASC,EAAYC,EAAgB,CACxC,GAAIF,EAAQ,WAAW,KAAK,EAAG,CAC7B,GAAI,CAACC,GAAcC,IAAmB,GACpC,MAAM,IAAI,MAAM,iDAAiD,EAGnE,GAAID,EAAY,CACd,IAAME,EAAQ,IAAIF,CAAU,GAAG,MAAM,KAAK,QAAQ,MAAQhB,GAAGC,GAAE,eAAe,EAAID,GAAGC,GAAE,UAAU,CAAC,EAClG,GAAI,CAACiB,GAASA,EAAM,CAAC,IAAMF,EACzB,MAAM,IAAI,MAAM,uBAAuBA,CAAU,EAAE,CAEvD,CACF,CAEA,OAAQD,EAAS,CACf,IAAK,WACH,KAAK,WAAW,OAAS,EACzB,KAAK,MAAQ,EACb,KAAK,MAAQ,EACb,KAAK,QACL,KAAK,IAAI,MAAOC,EAAYC,CAAc,EAC1C,MACF,IAAK,WACH,KAAK,WAAW,OAAS,EACzB,KAAK,MAAQ,EACb,KAAK,QACL,KAAK,IAAI,MAAOD,EAAYC,CAAc,EAC1C,MACF,IAAK,WAIH,KAAK,WAAW,OAAS,EACzB,KAAK,IAAI,QAASD,EAAYC,CAAc,EAC5C,KAAK,IAAI,MAAOD,EAAYC,CAAc,EAC1C,MAGF,IAAK,aACC,KAAK,WAAW,SAAW,GAC7B,KAAK,IAAI,QAASD,EAAYC,CAAc,EAE9C,KAAK,IAAI,MAAOD,EAAYC,CAAc,EAC1C,MACF,IAAK,UACH,GAAI,KAAK,WAAW,SAAW,EAC7B,MAAM,IAAI,MAAM,WAAW,KAAK,GAAG,sBAAsB,EAE3D,KAAK,WAAW,OAAS,EACzB,MAEF,IAAK,SAMD,KAAK,QAAU,GACf,KAAK,QAAU,GACf,KAAK,WAAW,SAAW,IAE3B,KAAK,QAEP,KAAK,MAAQ,EACb,KAAK,MAAQ,EACb,KAAK,WAAa,CAAC,EACnB,MACF,IAAK,SAKC,KAAK,QAAU,GAAK,KAAK,WAAW,SAAW,IACjD,KAAK,QAEP,KAAK,MAAQ,EACb,KAAK,WAAa,CAAC,EACnB,MACF,IAAK,QAKC,KAAK,WAAW,SAAW,GAC7B,KAAK,QAEP,KAAK,WAAa,CAAC,EACnB,MAGF,IAAK,MAAO,CACV,IAAME,EAAO,OAAOF,CAAc,EAAI,EAAI,EAE1C,GAAI,KAAK,WAAW,SAAW,EAC7B,KAAK,WAAa,CAACE,CAAI,MAClB,CACL,IAAIP,EAAI,KAAK,WAAW,OACxB,KAAO,EAAEA,GAAK,GACR,OAAO,KAAK,WAAWA,CAAC,GAAM,WAChC,KAAK,WAAWA,CAAC,IACjBA,EAAI,IAGR,GAAIA,IAAM,GAAI,CAEZ,GAAII,IAAe,KAAK,WAAW,KAAK,GAAG,GAAKC,IAAmB,GACjE,MAAM,IAAI,MAAM,uDAAuD,EAEzE,KAAK,WAAW,KAAKE,CAAI,CAC3B,CACF,CACA,GAAIH,EAAY,CAGd,IAAII,EAAa,CAACJ,EAAYG,CAAI,EAC9BF,IAAmB,KACrBG,EAAa,CAACJ,CAAU,GAEtBb,GAAmB,KAAK,WAAW,CAAC,EAAGa,CAAU,IAAM,EACrD,MAAM,KAAK,WAAW,CAAC,CAAC,IAC1B,KAAK,WAAaI,GAGpB,KAAK,WAAaA,CAEtB,CACA,KACF,CACA,QACE,MAAM,IAAI,MAAM,+BAA+BL,CAAO,EAAE,CAC5D,CACA,YAAK,IAAM,KAAK,OAAO,EACnB,KAAK,MAAM,SACb,KAAK,KAAO,IAAI,KAAK,MAAM,KAAK,GAAG,CAAC,IAE/B,IACT,CACF,EAEAnB,GAAO,QAAUQ,KC9TjB,IAAAiB,EAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAS,IACTC,GAAQ,CAACC,EAASC,EAASC,EAAc,KAAU,CACvD,GAAIF,aAAmBF,GACrB,OAAOE,EAET,GAAI,CACF,OAAO,IAAIF,GAAOE,EAASC,CAAO,CACpC,OAASE,EAAI,CACX,GAAI,CAACD,EACH,OAAO,KAET,MAAMC,CACR,CACF,EAEAN,GAAO,QAAUE,KCjBjB,IAAAK,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAQ,IACRC,GAAQ,CAACC,EAASC,IAAY,CAClC,IAAMC,EAAIJ,GAAME,EAASC,CAAO,EAChC,OAAOC,EAAIA,EAAE,QAAU,IACzB,EACAL,GAAO,QAAUE,KCPjB,IAAAI,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAQ,IACRC,GAAQ,CAACC,EAASC,IAAY,CAClC,IAAMC,EAAIJ,GAAME,EAAQ,KAAK,EAAE,QAAQ,SAAU,EAAE,EAAGC,CAAO,EAC7D,OAAOC,EAAIA,EAAE,QAAU,IACzB,EACAL,GAAO,QAAUE,KCPjB,IAAAI,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAS,IAETC,GAAM,CAACC,EAASC,EAASC,EAASC,EAAYC,IAAmB,CACjE,OAAQF,GAAa,WACvBE,EAAiBD,EACjBA,EAAaD,EACbA,EAAU,QAGZ,GAAI,CACF,OAAO,IAAIJ,GACTE,aAAmBF,GAASE,EAAQ,QAAUA,EAC9CE,CACF,EAAE,IAAID,EAASE,EAAYC,CAAc,EAAE,OAC7C,MAAa,CACX,OAAO,IACT,CACF,EACAP,GAAO,QAAUE,KCpBjB,IAAAM,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAQ,IAERC,GAAO,CAACC,EAAUC,IAAa,CACnC,IAAMC,EAAKJ,GAAME,EAAU,KAAM,EAAI,EAC/BG,EAAKL,GAAMG,EAAU,KAAM,EAAI,EAC/BG,EAAaF,EAAG,QAAQC,CAAE,EAEhC,GAAIC,IAAe,EACjB,OAAO,KAGT,IAAMC,EAAWD,EAAa,EACxBE,EAAcD,EAAWH,EAAKC,EAC9BI,EAAaF,EAAWF,EAAKD,EAC7BM,EAAa,CAAC,CAACF,EAAY,WAAW,OAG5C,GAFkB,CAAC,CAACC,EAAW,WAAW,QAEzB,CAACC,EAAY,CAQ5B,GAAI,CAACD,EAAW,OAAS,CAACA,EAAW,MACnC,MAAO,QAIT,GAAIA,EAAW,YAAYD,CAAW,IAAM,EAC1C,OAAIC,EAAW,OAAS,CAACA,EAAW,MAC3B,QAEF,OAEX,CAGA,IAAME,EAASD,EAAa,MAAQ,GAEpC,OAAIN,EAAG,QAAUC,EAAG,MACXM,EAAS,QAGdP,EAAG,QAAUC,EAAG,MACXM,EAAS,QAGdP,EAAG,QAAUC,EAAG,MACXM,EAAS,QAIX,YACT,EAEAZ,GAAO,QAAUE,KC3DjB,IAAAW,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAS,IACTC,GAAQ,CAACC,EAAGC,IAAU,IAAIH,GAAOE,EAAGC,CAAK,EAAE,MACjDJ,GAAO,QAAUE,KCJjB,IAAAG,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAS,IACTC,GAAQ,CAACC,EAAGC,IAAU,IAAIH,GAAOE,EAAGC,CAAK,EAAE,MACjDJ,GAAO,QAAUE,KCJjB,IAAAG,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAS,IACTC,GAAQ,CAACC,EAAGC,IAAU,IAAIH,GAAOE,EAAGC,CAAK,EAAE,MACjDJ,GAAO,QAAUE,KCJjB,IAAAG,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAQ,IACRC,GAAa,CAACC,EAASC,IAAY,CACvC,IAAMC,EAASJ,GAAME,EAASC,CAAO,EACrC,OAAQC,GAAUA,EAAO,WAAW,OAAUA,EAAO,WAAa,IACpE,EACAL,GAAO,QAAUE,KCPjB,IAAAI,EAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAS,IACTC,GAAU,CAACC,EAAGC,EAAGC,IACrB,IAAIJ,GAAOE,EAAGE,CAAK,EAAE,QAAQ,IAAIJ,GAAOG,EAAGC,CAAK,CAAC,EAEnDL,GAAO,QAAUE,KCNjB,IAAAI,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAU,IACVC,GAAW,CAACC,EAAGC,EAAGC,IAAUJ,GAAQG,EAAGD,EAAGE,CAAK,EACrDL,GAAO,QAAUE,KCJjB,IAAAI,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAU,IACVC,GAAe,CAACC,EAAGC,IAAMH,GAAQE,EAAGC,EAAG,EAAI,EACjDJ,GAAO,QAAUE,KCJjB,IAAAG,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAS,IACTC,GAAe,CAACC,EAAGC,EAAGC,IAAU,CACpC,IAAMC,EAAW,IAAIL,GAAOE,EAAGE,CAAK,EAC9BE,EAAW,IAAIN,GAAOG,EAAGC,CAAK,EACpC,OAAOC,EAAS,QAAQC,CAAQ,GAAKD,EAAS,aAAaC,CAAQ,CACrE,EACAP,GAAO,QAAUE,KCRjB,IAAAM,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAe,KACfC,GAAO,CAACC,EAAMC,IAAUD,EAAK,KAAK,CAACE,EAAGC,IAAML,GAAaI,EAAGC,EAAGF,CAAK,CAAC,EAC3EJ,GAAO,QAAUE,KCJjB,IAAAK,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAe,KACfC,GAAQ,CAACC,EAAMC,IAAUD,EAAK,KAAK,CAACE,EAAGC,IAAML,GAAaK,EAAGD,EAAGD,CAAK,CAAC,EAC5EJ,GAAO,QAAUE,KCJjB,IAAAK,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAU,IACVC,GAAK,CAACC,EAAGC,EAAGC,IAAUJ,GAAQE,EAAGC,EAAGC,CAAK,EAAI,EACnDL,GAAO,QAAUE,KCJjB,IAAAI,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAU,IACVC,GAAK,CAACC,EAAGC,EAAGC,IAAUJ,GAAQE,EAAGC,EAAGC,CAAK,EAAI,EACnDL,GAAO,QAAUE,KCJjB,IAAAI,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAU,IACVC,GAAK,CAACC,EAAGC,EAAGC,IAAUJ,GAAQE,EAAGC,EAAGC,CAAK,IAAM,EACrDL,GAAO,QAAUE,KCJjB,IAAAI,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAU,IACVC,GAAM,CAACC,EAAGC,EAAGC,IAAUJ,GAAQE,EAAGC,EAAGC,CAAK,IAAM,EACtDL,GAAO,QAAUE,KCJjB,IAAAI,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAU,IACVC,GAAM,CAACC,EAAGC,EAAGC,IAAUJ,GAAQE,EAAGC,EAAGC,CAAK,GAAK,EACrDL,GAAO,QAAUE,KCJjB,IAAAI,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAU,IACVC,GAAM,CAACC,EAAGC,EAAGC,IAAUJ,GAAQE,EAAGC,EAAGC,CAAK,GAAK,EACrDL,GAAO,QAAUE,KCJjB,IAAAI,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAK,KACLC,GAAM,KACNC,GAAK,KACLC,GAAM,KACNC,GAAK,KACLC,GAAM,KAENC,GAAM,CAACC,EAAGC,EAAIC,EAAGC,IAAU,CAC/B,OAAQF,EAAI,CACV,IAAK,MACH,OAAI,OAAOD,GAAM,WACfA,EAAIA,EAAE,SAEJ,OAAOE,GAAM,WACfA,EAAIA,EAAE,SAEDF,IAAME,EAEf,IAAK,MACH,OAAI,OAAOF,GAAM,WACfA,EAAIA,EAAE,SAEJ,OAAOE,GAAM,WACfA,EAAIA,EAAE,SAEDF,IAAME,EAEf,IAAK,GACL,IAAK,IACL,IAAK,KACH,OAAOT,GAAGO,EAAGE,EAAGC,CAAK,EAEvB,IAAK,KACH,OAAOT,GAAIM,EAAGE,EAAGC,CAAK,EAExB,IAAK,IACH,OAAOR,GAAGK,EAAGE,EAAGC,CAAK,EAEvB,IAAK,KACH,OAAOP,GAAII,EAAGE,EAAGC,CAAK,EAExB,IAAK,IACH,OAAON,GAAGG,EAAGE,EAAGC,CAAK,EAEvB,IAAK,KACH,OAAOL,GAAIE,EAAGE,EAAGC,CAAK,EAExB,QACE,MAAM,IAAI,UAAU,qBAAqBF,CAAE,EAAE,CACjD,CACF,EACAT,GAAO,QAAUO,KCrDjB,IAAAK,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAS,IACTC,GAAQ,IACR,CAAE,OAAQC,GAAI,EAAAC,EAAE,EAAI,KAEpBC,GAAS,CAACC,EAASC,IAAY,CACnC,GAAID,aAAmBL,GACrB,OAAOK,EAOT,GAJI,OAAOA,GAAY,WACrBA,EAAU,OAAOA,CAAO,GAGtB,OAAOA,GAAY,SACrB,OAAO,KAGTC,EAAUA,GAAW,CAAC,EAEtB,IAAIC,EAAQ,KACZ,GAAI,CAACD,EAAQ,IACXC,EAAQF,EAAQ,MAAMC,EAAQ,kBAAoBJ,GAAGC,GAAE,UAAU,EAAID,GAAGC,GAAE,MAAM,CAAC,MAC5E,CAUL,IAAMK,EAAiBF,EAAQ,kBAAoBJ,GAAGC,GAAE,aAAa,EAAID,GAAGC,GAAE,SAAS,EACnFM,EACJ,MAAQA,EAAOD,EAAe,KAAKH,CAAO,KACrC,CAACE,GAASA,EAAM,MAAQA,EAAM,CAAC,EAAE,SAAWF,EAAQ,UAEnD,CAACE,GACCE,EAAK,MAAQA,EAAK,CAAC,EAAE,SAAWF,EAAM,MAAQA,EAAM,CAAC,EAAE,UAC3DA,EAAQE,GAEVD,EAAe,UAAYC,EAAK,MAAQA,EAAK,CAAC,EAAE,OAASA,EAAK,CAAC,EAAE,OAGnED,EAAe,UAAY,EAC7B,CAEA,GAAID,IAAU,KACZ,OAAO,KAGT,IAAMG,EAAQH,EAAM,CAAC,EACfI,EAAQJ,EAAM,CAAC,GAAK,IACpBK,EAAQL,EAAM,CAAC,GAAK,IACpBM,EAAaP,EAAQ,mBAAqBC,EAAM,CAAC,EAAI,IAAIA,EAAM,CAAC,CAAC,GAAK,GACtEO,EAAQR,EAAQ,mBAAqBC,EAAM,CAAC,EAAI,IAAIA,EAAM,CAAC,CAAC,GAAK,GAEvE,OAAON,GAAM,GAAGS,CAAK,IAAIC,CAAK,IAAIC,CAAK,GAAGC,CAAU,GAAGC,CAAK,GAAIR,CAAO,CACzE,EACAP,GAAO,QAAUK,KC7DjB,IAAAW,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAN,KAAe,CACb,aAAe,CACb,KAAK,IAAM,IACX,KAAK,IAAM,IAAI,GACjB,CAEA,IAAKC,EAAK,CACR,IAAMC,EAAQ,KAAK,IAAI,IAAID,CAAG,EAC9B,GAAIC,IAAU,OAIZ,YAAK,IAAI,OAAOD,CAAG,EACnB,KAAK,IAAI,IAAIA,EAAKC,CAAK,EAChBA,CAEX,CAEA,OAAQD,EAAK,CACX,OAAO,KAAK,IAAI,OAAOA,CAAG,CAC5B,CAEA,IAAKA,EAAKC,EAAO,CAGf,GAAI,CAFY,KAAK,OAAOD,CAAG,GAEfC,IAAU,OAAW,CAEnC,GAAI,KAAK,IAAI,MAAQ,KAAK,IAAK,CAC7B,IAAMC,EAAW,KAAK,IAAI,KAAK,EAAE,KAAK,EAAE,MACxC,KAAK,OAAOA,CAAQ,CACtB,CAEA,KAAK,IAAI,IAAIF,EAAKC,CAAK,CACzB,CAEA,OAAO,IACT,CACF,EAEAH,GAAO,QAAUC,KCzCjB,IAAAI,EAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAmB,OAGnBC,GAAN,MAAMC,CAAM,CACV,YAAaC,EAAOC,EAAS,CAG3B,GAFAA,EAAUC,GAAaD,CAAO,EAE1BD,aAAiBD,EACnB,OACEC,EAAM,QAAU,CAAC,CAACC,EAAQ,OAC1BD,EAAM,oBAAsB,CAAC,CAACC,EAAQ,kBAE/BD,EAEA,IAAID,EAAMC,EAAM,IAAKC,CAAO,EAIvC,GAAID,aAAiBG,GAEnB,YAAK,IAAMH,EAAM,MACjB,KAAK,IAAM,CAAC,CAACA,CAAK,CAAC,EACnB,KAAK,UAAY,OACV,KAsBT,GAnBA,KAAK,QAAUC,EACf,KAAK,MAAQ,CAAC,CAACA,EAAQ,MACvB,KAAK,kBAAoB,CAAC,CAACA,EAAQ,kBAKnC,KAAK,IAAMD,EAAM,KAAK,EAAE,QAAQH,GAAkB,GAAG,EAGrD,KAAK,IAAM,KAAK,IACb,MAAM,IAAI,EAEV,IAAIO,GAAK,KAAK,WAAWA,EAAE,KAAK,CAAC,CAAC,EAIlC,OAAOC,GAAKA,EAAE,MAAM,EAEnB,CAAC,KAAK,IAAI,OACZ,MAAM,IAAI,UAAU,yBAAyB,KAAK,GAAG,EAAE,EAIzD,GAAI,KAAK,IAAI,OAAS,EAAG,CAEvB,IAAMC,EAAQ,KAAK,IAAI,CAAC,EAExB,GADA,KAAK,IAAM,KAAK,IAAI,OAAOD,GAAK,CAACE,GAAUF,EAAE,CAAC,CAAC,CAAC,EAC5C,KAAK,IAAI,SAAW,EACtB,KAAK,IAAM,CAACC,CAAK,UACR,KAAK,IAAI,OAAS,GAE3B,QAAWD,KAAK,KAAK,IACnB,GAAIA,EAAE,SAAW,GAAKG,GAAMH,EAAE,CAAC,CAAC,EAAG,CACjC,KAAK,IAAM,CAACA,CAAC,EACb,KACF,EAGN,CAEA,KAAK,UAAY,MACnB,CAEA,IAAI,OAAS,CACX,GAAI,KAAK,YAAc,OAAW,CAChC,KAAK,UAAY,GACjB,QAASI,EAAI,EAAGA,EAAI,KAAK,IAAI,OAAQA,IAAK,CACpCA,EAAI,IACN,KAAK,WAAa,MAEpB,IAAMC,EAAQ,KAAK,IAAID,CAAC,EACxB,QAASE,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAC5BA,EAAI,IACN,KAAK,WAAa,KAEpB,KAAK,WAAaD,EAAMC,CAAC,EAAE,SAAS,EAAE,KAAK,CAE/C,CACF,CACA,OAAO,KAAK,SACd,CAEA,QAAU,CACR,OAAO,KAAK,KACd,CAEA,UAAY,CACV,OAAO,KAAK,KACd,CAEA,WAAYX,EAAO,CAMjB,IAAMY,IAFH,KAAK,QAAQ,mBAAqBC,KAClC,KAAK,QAAQ,OAASC,KACE,IAAMd,EAC3Be,EAASC,GAAM,IAAIJ,CAAO,EAChC,GAAIG,EACF,OAAOA,EAGT,IAAME,EAAQ,KAAK,QAAQ,MAErBC,EAAKD,EAAQE,EAAGC,EAAE,gBAAgB,EAAID,EAAGC,EAAE,WAAW,EAC5DpB,EAAQA,EAAM,QAAQkB,EAAIG,GAAc,KAAK,QAAQ,iBAAiB,CAAC,EACvEC,EAAM,iBAAkBtB,CAAK,EAG7BA,EAAQA,EAAM,QAAQmB,EAAGC,EAAE,cAAc,EAAGG,EAAqB,EACjED,EAAM,kBAAmBtB,CAAK,EAG9BA,EAAQA,EAAM,QAAQmB,EAAGC,EAAE,SAAS,EAAGI,EAAgB,EACvDF,EAAM,aAActB,CAAK,EAGzBA,EAAQA,EAAM,QAAQmB,EAAGC,EAAE,SAAS,EAAGK,EAAgB,EACvDH,EAAM,aAActB,CAAK,EAKzB,IAAI0B,EAAY1B,EACb,MAAM,GAAG,EACT,IAAI2B,GAAQC,GAAgBD,EAAM,KAAK,OAAO,CAAC,EAC/C,KAAK,GAAG,EACR,MAAM,KAAK,EAEX,IAAIA,GAAQE,GAAYF,EAAM,KAAK,OAAO,CAAC,EAE1CV,IAEFS,EAAYA,EAAU,OAAOC,IAC3BL,EAAM,uBAAwBK,EAAM,KAAK,OAAO,EACzC,CAAC,CAACA,EAAK,MAAMR,EAAGC,EAAE,eAAe,CAAC,EAC1C,GAEHE,EAAM,aAAcI,CAAS,EAK7B,IAAMI,EAAW,IAAI,IACfC,EAAcL,EAAU,IAAIC,GAAQ,IAAIxB,GAAWwB,EAAM,KAAK,OAAO,CAAC,EAC5E,QAAWA,KAAQI,EAAa,CAC9B,GAAIxB,GAAUoB,CAAI,EAChB,MAAO,CAACA,CAAI,EAEdG,EAAS,IAAIH,EAAK,MAAOA,CAAI,CAC/B,CACIG,EAAS,KAAO,GAAKA,EAAS,IAAI,EAAE,GACtCA,EAAS,OAAO,EAAE,EAGpB,IAAME,EAAS,CAAC,GAAGF,EAAS,OAAO,CAAC,EACpC,OAAAd,GAAM,IAAIJ,EAASoB,CAAM,EAClBA,CACT,CAEA,WAAYhC,EAAOC,EAAS,CAC1B,GAAI,EAAED,aAAiBD,GACrB,MAAM,IAAI,UAAU,qBAAqB,EAG3C,OAAO,KAAK,IAAI,KAAMkC,GAElBC,GAAcD,EAAiBhC,CAAO,GACtCD,EAAM,IAAI,KAAMmC,GAEZD,GAAcC,EAAkBlC,CAAO,GACvCgC,EAAgB,MAAOG,GACdD,EAAiB,MAAOE,GACtBD,EAAe,WAAWC,EAAiBpC,CAAO,CAC1D,CACF,CAEJ,CAEJ,CACH,CAGA,KAAMqC,EAAS,CACb,GAAI,CAACA,EACH,MAAO,GAGT,GAAI,OAAOA,GAAY,SACrB,GAAI,CACFA,EAAU,IAAIC,GAAOD,EAAS,KAAK,OAAO,CAC5C,MAAa,CACX,MAAO,EACT,CAGF,QAAS7B,EAAI,EAAGA,EAAI,KAAK,IAAI,OAAQA,IACnC,GAAI+B,GAAQ,KAAK,IAAI/B,CAAC,EAAG6B,EAAS,KAAK,OAAO,EAC5C,MAAO,GAGX,MAAO,EACT,CACF,EAEA1C,GAAO,QAAUE,GAEjB,IAAM2C,GAAM,KACNzB,GAAQ,IAAIyB,GAEZvC,GAAe,KACfC,GAAa,KACbmB,EAAQ,KACRiB,GAAS,IACT,CACJ,OAAQpB,EACR,EAAAC,EACA,sBAAAG,GACA,iBAAAC,GACA,iBAAAC,EACF,EAAI,KACE,CAAE,wBAAAZ,GAAyB,WAAAC,EAAW,EAAI,KAE1CP,GAAYF,GAAKA,EAAE,QAAU,WAC7BG,GAAQH,GAAKA,EAAE,QAAU,GAIzB6B,GAAgB,CAACH,EAAa9B,IAAY,CAC9C,IAAI+B,EAAS,GACPU,EAAuBX,EAAY,MAAM,EAC3CY,EAAiBD,EAAqB,IAAI,EAE9C,KAAOV,GAAUU,EAAqB,QACpCV,EAASU,EAAqB,MAAOE,GAC5BD,EAAe,WAAWC,EAAiB3C,CAAO,CAC1D,EAED0C,EAAiBD,EAAqB,IAAI,EAG5C,OAAOV,CACT,EAKMJ,GAAkB,CAACD,EAAM1B,KAC7BqB,EAAM,OAAQK,EAAM1B,CAAO,EAC3B0B,EAAOkB,GAAclB,EAAM1B,CAAO,EAClCqB,EAAM,QAASK,CAAI,EACnBA,EAAOmB,GAAcnB,EAAM1B,CAAO,EAClCqB,EAAM,SAAUK,CAAI,EACpBA,EAAOoB,GAAepB,EAAM1B,CAAO,EACnCqB,EAAM,SAAUK,CAAI,EACpBA,EAAOqB,GAAarB,EAAM1B,CAAO,EACjCqB,EAAM,QAASK,CAAI,EACZA,GAGHsB,EAAMC,GAAM,CAACA,GAAMA,EAAG,YAAY,IAAM,KAAOA,IAAO,IAStDJ,GAAgB,CAACnB,EAAM1B,IACpB0B,EACJ,KAAK,EACL,MAAM,KAAK,EACX,IAAKtB,GAAM8C,GAAa9C,EAAGJ,CAAO,CAAC,EACnC,KAAK,GAAG,EAGPkD,GAAe,CAACxB,EAAM1B,IAAY,CACtC,IAAM,EAAIA,EAAQ,MAAQkB,EAAGC,EAAE,UAAU,EAAID,EAAGC,EAAE,KAAK,EACvD,OAAOO,EAAK,QAAQ,EAAG,CAACyB,EAAGC,EAAGC,EAAGC,EAAGC,IAAO,CACzClC,EAAM,QAASK,EAAMyB,EAAGC,EAAGC,EAAGC,EAAGC,CAAE,EACnC,IAAIC,EAEJ,OAAIR,EAAII,CAAC,EACPI,EAAM,GACGR,EAAIK,CAAC,EACdG,EAAM,KAAKJ,CAAC,SAAS,CAACA,EAAI,CAAC,SAClBJ,EAAIM,CAAC,EAEdE,EAAM,KAAKJ,CAAC,IAAIC,CAAC,OAAOD,CAAC,IAAI,CAACC,EAAI,CAAC,OAC1BE,GACTlC,EAAM,kBAAmBkC,CAAE,EAC3BC,EAAM,KAAKJ,CAAC,IAAIC,CAAC,IAAIC,CAAC,IAAIC,CAC1B,KAAKH,CAAC,IAAI,CAACC,EAAI,CAAC,QAGhBG,EAAM,KAAKJ,CAAC,IAAIC,CAAC,IAAIC,CACrB,KAAKF,CAAC,IAAI,CAACC,EAAI,CAAC,OAGlBhC,EAAM,eAAgBmC,CAAG,EAClBA,CACT,CAAC,CACH,EAUMZ,GAAgB,CAAClB,EAAM1B,IACpB0B,EACJ,KAAK,EACL,MAAM,KAAK,EACX,IAAKtB,GAAMqD,GAAarD,EAAGJ,CAAO,CAAC,EACnC,KAAK,GAAG,EAGPyD,GAAe,CAAC/B,EAAM1B,IAAY,CACtCqB,EAAM,QAASK,EAAM1B,CAAO,EAC5B,IAAM,EAAIA,EAAQ,MAAQkB,EAAGC,EAAE,UAAU,EAAID,EAAGC,EAAE,KAAK,EACjDuC,EAAI1D,EAAQ,kBAAoB,KAAO,GAC7C,OAAO0B,EAAK,QAAQ,EAAG,CAACyB,EAAGC,EAAGC,EAAGC,EAAGC,IAAO,CACzClC,EAAM,QAASK,EAAMyB,EAAGC,EAAGC,EAAGC,EAAGC,CAAE,EACnC,IAAIC,EAEJ,OAAIR,EAAII,CAAC,EACPI,EAAM,GACGR,EAAIK,CAAC,EACdG,EAAM,KAAKJ,CAAC,OAAOM,CAAC,KAAK,CAACN,EAAI,CAAC,SACtBJ,EAAIM,CAAC,EACVF,IAAM,IACRI,EAAM,KAAKJ,CAAC,IAAIC,CAAC,KAAKK,CAAC,KAAKN,CAAC,IAAI,CAACC,EAAI,CAAC,OAEvCG,EAAM,KAAKJ,CAAC,IAAIC,CAAC,KAAKK,CAAC,KAAK,CAACN,EAAI,CAAC,SAE3BG,GACTlC,EAAM,kBAAmBkC,CAAE,EACvBH,IAAM,IACJC,IAAM,IACRG,EAAM,KAAKJ,CAAC,IAAIC,CAAC,IAAIC,CAAC,IAAIC,CAC1B,KAAKH,CAAC,IAAIC,CAAC,IAAI,CAACC,EAAI,CAAC,KAErBE,EAAM,KAAKJ,CAAC,IAAIC,CAAC,IAAIC,CAAC,IAAIC,CAC1B,KAAKH,CAAC,IAAI,CAACC,EAAI,CAAC,OAGlBG,EAAM,KAAKJ,CAAC,IAAIC,CAAC,IAAIC,CAAC,IAAIC,CAC1B,KAAK,CAACH,EAAI,CAAC,WAGb/B,EAAM,OAAO,EACT+B,IAAM,IACJC,IAAM,IACRG,EAAM,KAAKJ,CAAC,IAAIC,CAAC,IAAIC,CACrB,GAAGI,CAAC,KAAKN,CAAC,IAAIC,CAAC,IAAI,CAACC,EAAI,CAAC,KAEzBE,EAAM,KAAKJ,CAAC,IAAIC,CAAC,IAAIC,CACrB,GAAGI,CAAC,KAAKN,CAAC,IAAI,CAACC,EAAI,CAAC,OAGtBG,EAAM,KAAKJ,CAAC,IAAIC,CAAC,IAAIC,CACrB,KAAK,CAACF,EAAI,CAAC,UAIf/B,EAAM,eAAgBmC,CAAG,EAClBA,CACT,CAAC,CACH,EAEMV,GAAiB,CAACpB,EAAM1B,KAC5BqB,EAAM,iBAAkBK,EAAM1B,CAAO,EAC9B0B,EACJ,MAAM,KAAK,EACX,IAAKtB,GAAMuD,GAAcvD,EAAGJ,CAAO,CAAC,EACpC,KAAK,GAAG,GAGP2D,GAAgB,CAACjC,EAAM1B,IAAY,CACvC0B,EAAOA,EAAK,KAAK,EACjB,IAAM,EAAI1B,EAAQ,MAAQkB,EAAGC,EAAE,WAAW,EAAID,EAAGC,EAAE,MAAM,EACzD,OAAOO,EAAK,QAAQ,EAAG,CAAC8B,EAAKI,EAAMR,EAAGC,EAAGC,EAAGC,IAAO,CACjDlC,EAAM,SAAUK,EAAM8B,EAAKI,EAAMR,EAAGC,EAAGC,EAAGC,CAAE,EAC5C,IAAMM,EAAKb,EAAII,CAAC,EACVU,EAAKD,GAAMb,EAAIK,CAAC,EAChBU,EAAKD,GAAMd,EAAIM,CAAC,EAChBU,EAAOD,EAEb,OAAIH,IAAS,KAAOI,IAClBJ,EAAO,IAKTL,EAAKvD,EAAQ,kBAAoB,KAAO,GAEpC6D,EACED,IAAS,KAAOA,IAAS,IAE3BJ,EAAM,WAGNA,EAAM,IAECI,GAAQI,GAGbF,IACFT,EAAI,GAENC,EAAI,EAEAM,IAAS,KAGXA,EAAO,KACHE,GACFV,EAAI,CAACA,EAAI,EACTC,EAAI,EACJC,EAAI,IAEJD,EAAI,CAACA,EAAI,EACTC,EAAI,IAEGM,IAAS,OAGlBA,EAAO,IACHE,EACFV,EAAI,CAACA,EAAI,EAETC,EAAI,CAACA,EAAI,GAITO,IAAS,MACXL,EAAK,MAGPC,EAAM,GAAGI,EAAOR,CAAC,IAAIC,CAAC,IAAIC,CAAC,GAAGC,CAAE,IACvBO,EACTN,EAAM,KAAKJ,CAAC,OAAOG,CAAE,KAAK,CAACH,EAAI,CAAC,SACvBW,IACTP,EAAM,KAAKJ,CAAC,IAAIC,CAAC,KAAKE,CACtB,KAAKH,CAAC,IAAI,CAACC,EAAI,CAAC,QAGlBhC,EAAM,gBAAiBmC,CAAG,EAEnBA,CACT,CAAC,CACH,EAIMT,GAAe,CAACrB,EAAM1B,KAC1BqB,EAAM,eAAgBK,EAAM1B,CAAO,EAE5B0B,EACJ,KAAK,EACL,QAAQR,EAAGC,EAAE,IAAI,EAAG,EAAE,GAGrBS,GAAc,CAACF,EAAM1B,KACzBqB,EAAM,cAAeK,EAAM1B,CAAO,EAC3B0B,EACJ,KAAK,EACL,QAAQR,EAAGlB,EAAQ,kBAAoBmB,EAAE,QAAUA,EAAE,IAAI,EAAG,EAAE,GAS7DC,GAAgB6C,GAAS,CAACC,EAC9BC,EAAMC,EAAIC,EAAIC,EAAIC,EAAKC,EACvBC,EAAIC,EAAIC,EAAIC,EAAIC,KACZ7B,EAAIoB,CAAE,EACRD,EAAO,GACEnB,EAAIqB,CAAE,EACfF,EAAO,KAAKC,CAAE,OAAOH,EAAQ,KAAO,EAAE,GAC7BjB,EAAIsB,CAAE,EACfH,EAAO,KAAKC,CAAE,IAAIC,CAAE,KAAKJ,EAAQ,KAAO,EAAE,GACjCM,EACTJ,EAAO,KAAKA,CAAI,GAEhBA,EAAO,KAAKA,CAAI,GAAGF,EAAQ,KAAO,EAAE,GAGlCjB,EAAI0B,CAAE,EACRD,EAAK,GACIzB,EAAI2B,CAAE,EACfF,EAAK,IAAI,CAACC,EAAK,CAAC,SACP1B,EAAI4B,CAAE,EACfH,EAAK,IAAIC,CAAE,IAAI,CAACC,EAAK,CAAC,OACbE,EACTJ,EAAK,KAAKC,CAAE,IAAIC,CAAE,IAAIC,CAAE,IAAIC,CAAG,GACtBZ,EACTQ,EAAK,IAAIC,CAAE,IAAIC,CAAE,IAAI,CAACC,EAAK,CAAC,KAE5BH,EAAK,KAAKA,CAAE,GAGP,GAAGN,CAAI,IAAIM,CAAE,GAAG,KAAK,GAGxBlC,GAAU,CAACuC,EAAKzC,EAASrC,IAAY,CACzC,QAASQ,EAAI,EAAGA,EAAIsE,EAAI,OAAQtE,IAC9B,GAAI,CAACsE,EAAItE,CAAC,EAAE,KAAK6B,CAAO,EACtB,MAAO,GAIX,GAAIA,EAAQ,WAAW,QAAU,CAACrC,EAAQ,kBAAmB,CAM3D,QAASQ,EAAI,EAAGA,EAAIsE,EAAI,OAAQtE,IAE9B,GADAa,EAAMyD,EAAItE,CAAC,EAAE,MAAM,EACfsE,EAAItE,CAAC,EAAE,SAAWN,GAAW,KAI7B4E,EAAItE,CAAC,EAAE,OAAO,WAAW,OAAS,EAAG,CACvC,IAAMuE,EAAUD,EAAItE,CAAC,EAAE,OACvB,GAAIuE,EAAQ,QAAU1C,EAAQ,OAC1B0C,EAAQ,QAAU1C,EAAQ,OAC1B0C,EAAQ,QAAU1C,EAAQ,MAC5B,MAAO,EAEX,CAIF,MAAO,EACT,CAEA,MAAO,EACT,IC3iBA,IAAA2C,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAM,OAAO,YAAY,EAEzBC,GAAN,MAAMC,CAAW,CACf,WAAW,KAAO,CAChB,OAAOF,EACT,CAEA,YAAaG,EAAMC,EAAS,CAG1B,GAFAA,EAAUC,GAAaD,CAAO,EAE1BD,aAAgBD,EAAY,CAC9B,GAAIC,EAAK,QAAU,CAAC,CAACC,EAAQ,MAC3B,OAAOD,EAEPA,EAAOA,EAAK,KAEhB,CAEAA,EAAOA,EAAK,KAAK,EAAE,MAAM,KAAK,EAAE,KAAK,GAAG,EACxCG,GAAM,aAAcH,EAAMC,CAAO,EACjC,KAAK,QAAUA,EACf,KAAK,MAAQ,CAAC,CAACA,EAAQ,MACvB,KAAK,MAAMD,CAAI,EAEX,KAAK,SAAWH,GAClB,KAAK,MAAQ,GAEb,KAAK,MAAQ,KAAK,SAAW,KAAK,OAAO,QAG3CM,GAAM,OAAQ,IAAI,CACpB,CAEA,MAAOH,EAAM,CACX,IAAM,EAAI,KAAK,QAAQ,MAAQI,GAAGC,GAAE,eAAe,EAAID,GAAGC,GAAE,UAAU,EAChEC,EAAIN,EAAK,MAAM,CAAC,EAEtB,GAAI,CAACM,EACH,MAAM,IAAI,UAAU,uBAAuBN,CAAI,EAAE,EAGnD,KAAK,SAAWM,EAAE,CAAC,IAAM,OAAYA,EAAE,CAAC,EAAI,GACxC,KAAK,WAAa,MACpB,KAAK,SAAW,IAIbA,EAAE,CAAC,EAGN,KAAK,OAAS,IAAIC,GAAOD,EAAE,CAAC,EAAG,KAAK,QAAQ,KAAK,EAFjD,KAAK,OAAST,EAIlB,CAEA,UAAY,CACV,OAAO,KAAK,KACd,CAEA,KAAMW,EAAS,CAGb,GAFAL,GAAM,kBAAmBK,EAAS,KAAK,QAAQ,KAAK,EAEhD,KAAK,SAAWX,IAAOW,IAAYX,GACrC,MAAO,GAGT,GAAI,OAAOW,GAAY,SACrB,GAAI,CACFA,EAAU,IAAID,GAAOC,EAAS,KAAK,OAAO,CAC5C,MAAa,CACX,MAAO,EACT,CAGF,OAAOC,GAAID,EAAS,KAAK,SAAU,KAAK,OAAQ,KAAK,OAAO,CAC9D,CAEA,WAAYR,EAAMC,EAAS,CACzB,GAAI,EAAED,aAAgBD,GACpB,MAAM,IAAI,UAAU,0BAA0B,EAGhD,OAAI,KAAK,WAAa,GAChB,KAAK,QAAU,GACV,GAEF,IAAIW,GAAMV,EAAK,MAAOC,CAAO,EAAE,KAAK,KAAK,KAAK,EAC5CD,EAAK,WAAa,GACvBA,EAAK,QAAU,GACV,GAEF,IAAIU,GAAM,KAAK,MAAOT,CAAO,EAAE,KAAKD,EAAK,MAAM,GAGxDC,EAAUC,GAAaD,CAAO,EAG1BA,EAAQ,oBACT,KAAK,QAAU,YAAcD,EAAK,QAAU,aAG3C,CAACC,EAAQ,oBACV,KAAK,MAAM,WAAW,QAAQ,GAAKD,EAAK,MAAM,WAAW,QAAQ,GAC3D,GAIL,QAAK,SAAS,WAAW,GAAG,GAAKA,EAAK,SAAS,WAAW,GAAG,GAI7D,KAAK,SAAS,WAAW,GAAG,GAAKA,EAAK,SAAS,WAAW,GAAG,GAK9D,KAAK,OAAO,UAAYA,EAAK,OAAO,SACrC,KAAK,SAAS,SAAS,GAAG,GAAKA,EAAK,SAAS,SAAS,GAAG,GAIvDS,GAAI,KAAK,OAAQ,IAAKT,EAAK,OAAQC,CAAO,GAC5C,KAAK,SAAS,WAAW,GAAG,GAAKD,EAAK,SAAS,WAAW,GAAG,GAI3DS,GAAI,KAAK,OAAQ,IAAKT,EAAK,OAAQC,CAAO,GAC5C,KAAK,SAAS,WAAW,GAAG,GAAKD,EAAK,SAAS,WAAW,GAAG,GAIjE,CACF,EAEAJ,GAAO,QAAUE,GAEjB,IAAMI,GAAe,KACf,CAAE,OAAQE,GAAI,EAAAC,EAAE,EAAI,KACpBI,GAAM,KACNN,GAAQ,KACRI,GAAS,IACTG,GAAQ,MC9Id,IAAAC,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAQ,IACRC,GAAY,CAACC,EAASC,EAAOC,IAAY,CAC7C,GAAI,CACFD,EAAQ,IAAIH,GAAMG,EAAOC,CAAO,CAClC,MAAa,CACX,MAAO,EACT,CACA,OAAOD,EAAM,KAAKD,CAAO,CAC3B,EACAH,GAAO,QAAUE,KCXjB,IAAAI,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAQ,IAGRC,GAAgB,CAACC,EAAOC,IAC5B,IAAIH,GAAME,EAAOC,CAAO,EAAE,IACvB,IAAIC,GAAQA,EAAK,IAAIC,GAAKA,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,EAAE,MAAM,GAAG,CAAC,EAEnEN,GAAO,QAAUE,KCTjB,IAAAK,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAS,IACTC,GAAQ,IAERC,GAAgB,CAACC,EAAUC,EAAOC,IAAY,CAClD,IAAIC,EAAM,KACNC,EAAQ,KACRC,EAAW,KACf,GAAI,CACFA,EAAW,IAAIP,GAAMG,EAAOC,CAAO,CACrC,MAAa,CACX,OAAO,IACT,CACA,OAAAF,EAAS,QAASM,GAAM,CAClBD,EAAS,KAAKC,CAAC,IAEb,CAACH,GAAOC,EAAM,QAAQE,CAAC,IAAM,MAE/BH,EAAMG,EACNF,EAAQ,IAAIP,GAAOM,EAAKD,CAAO,EAGrC,CAAC,EACMC,CACT,EACAP,GAAO,QAAUG,KC1BjB,IAAAQ,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAS,IACTC,GAAQ,IACRC,GAAgB,CAACC,EAAUC,EAAOC,IAAY,CAClD,IAAIC,EAAM,KACNC,EAAQ,KACRC,EAAW,KACf,GAAI,CACFA,EAAW,IAAIP,GAAMG,EAAOC,CAAO,CACrC,MAAa,CACX,OAAO,IACT,CACA,OAAAF,EAAS,QAASM,GAAM,CAClBD,EAAS,KAAKC,CAAC,IAEb,CAACH,GAAOC,EAAM,QAAQE,CAAC,IAAM,KAE/BH,EAAMG,EACNF,EAAQ,IAAIP,GAAOM,EAAKD,CAAO,EAGrC,CAAC,EACMC,CACT,EACAP,GAAO,QAAUG,KCzBjB,IAAAQ,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAS,IACTC,GAAQ,IACRC,GAAK,KAELC,GAAa,CAACC,EAAOC,IAAU,CACnCD,EAAQ,IAAIH,GAAMG,EAAOC,CAAK,EAE9B,IAAIC,EAAS,IAAIN,GAAO,OAAO,EAM/B,GALII,EAAM,KAAKE,CAAM,IAIrBA,EAAS,IAAIN,GAAO,SAAS,EACzBI,EAAM,KAAKE,CAAM,GACnB,OAAOA,EAGTA,EAAS,KACT,QAASC,EAAI,EAAGA,EAAIH,EAAM,IAAI,OAAQ,EAAEG,EAAG,CACzC,IAAMC,EAAcJ,EAAM,IAAIG,CAAC,EAE3BE,EAAS,KACbD,EAAY,QAASE,GAAe,CAElC,IAAMC,EAAU,IAAIX,GAAOU,EAAW,OAAO,OAAO,EACpD,OAAQA,EAAW,SAAU,CAC3B,IAAK,IACCC,EAAQ,WAAW,SAAW,EAChCA,EAAQ,QAERA,EAAQ,WAAW,KAAK,CAAC,EAE3BA,EAAQ,IAAMA,EAAQ,OAAO,EAE/B,IAAK,GACL,IAAK,MACC,CAACF,GAAUP,GAAGS,EAASF,CAAM,KAC/BA,EAASE,GAEX,MACF,IAAK,IACL,IAAK,KAEH,MAEF,QACE,MAAM,IAAI,MAAM,yBAAyBD,EAAW,QAAQ,EAAE,CAClE,CACF,CAAC,EACGD,IAAW,CAACH,GAAUJ,GAAGI,EAAQG,CAAM,KACzCH,EAASG,EAEb,CAEA,OAAIH,GAAUF,EAAM,KAAKE,CAAM,EACtBA,EAGF,IACT,EACAP,GAAO,QAAUI,KC9DjB,IAAAS,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAQ,IACRC,GAAa,CAACC,EAAOC,IAAY,CACrC,GAAI,CAGF,OAAO,IAAIH,GAAME,EAAOC,CAAO,EAAE,OAAS,GAC5C,MAAa,CACX,OAAO,IACT,CACF,EACAJ,GAAO,QAAUE,KCZjB,IAAAG,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAS,IACTC,GAAa,KACb,CAAE,IAAAC,EAAI,EAAID,GACVE,GAAQ,IACRC,GAAY,KACZC,GAAK,KACLC,GAAK,KACLC,GAAM,KACNC,GAAM,KAENC,GAAU,CAACC,EAASC,EAAOC,EAAMC,IAAY,CACjDH,EAAU,IAAIV,GAAOU,EAASG,CAAO,EACrCF,EAAQ,IAAIR,GAAMQ,EAAOE,CAAO,EAEhC,IAAIC,EAAMC,EAAOC,EAAMC,EAAMC,EAC7B,OAAQN,EAAM,CACZ,IAAK,IACHE,EAAOT,GACPU,EAAQR,GACRS,EAAOV,GACPW,EAAO,IACPC,EAAQ,KACR,MACF,IAAK,IACHJ,EAAOR,GACPS,EAAQP,GACRQ,EAAOX,GACPY,EAAO,IACPC,EAAQ,KACR,MACF,QACE,MAAM,IAAI,UAAU,uCAAuC,CAC/D,CAGA,GAAId,GAAUM,EAASC,EAAOE,CAAO,EACnC,MAAO,GAMT,QAASM,EAAI,EAAGA,EAAIR,EAAM,IAAI,OAAQ,EAAEQ,EAAG,CACzC,IAAMC,EAAcT,EAAM,IAAIQ,CAAC,EAE3BE,EAAO,KACPC,EAAM,KAuBV,GArBAF,EAAY,QAASG,GAAe,CAC9BA,EAAW,SAAWrB,KACxBqB,EAAa,IAAItB,GAAW,SAAS,GAEvCoB,EAAOA,GAAQE,EACfD,EAAMA,GAAOC,EACTT,EAAKS,EAAW,OAAQF,EAAK,OAAQR,CAAO,EAC9CQ,EAAOE,EACEP,EAAKO,EAAW,OAAQD,EAAI,OAAQT,CAAO,IACpDS,EAAMC,EAEV,CAAC,EAIGF,EAAK,WAAaJ,GAAQI,EAAK,WAAaH,IAM3C,CAACI,EAAI,UAAYA,EAAI,WAAaL,IACnCF,EAAML,EAASY,EAAI,MAAM,EAC3B,MAAO,GACF,GAAIA,EAAI,WAAaJ,GAASF,EAAKN,EAASY,EAAI,MAAM,EAC3D,MAAO,EAEX,CACA,MAAO,EACT,EAEAvB,GAAO,QAAUU,KCjFjB,IAAAe,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAGA,IAAMC,GAAU,KACVC,GAAM,CAACC,EAASC,EAAOC,IAAYJ,GAAQE,EAASC,EAAO,IAAKC,CAAO,EAC7EL,GAAO,QAAUE,KCLjB,IAAAI,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAU,KAEVC,GAAM,CAACC,EAASC,EAAOC,IAAYJ,GAAQE,EAASC,EAAO,IAAKC,CAAO,EAC7EL,GAAO,QAAUE,KCLjB,IAAAI,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAQ,IACRC,GAAa,CAACC,EAAIC,EAAIC,KAC1BF,EAAK,IAAIF,GAAME,EAAIE,CAAO,EAC1BD,EAAK,IAAIH,GAAMG,EAAIC,CAAO,EACnBF,EAAG,WAAWC,EAAIC,CAAO,GAElCL,GAAO,QAAUE,KCRjB,IAAAI,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAKA,IAAMC,GAAY,KACZC,GAAU,IAChBF,GAAO,QAAU,CAACG,EAAUC,EAAOC,IAAY,CAC7C,IAAMC,EAAM,CAAC,EACTC,EAAQ,KACRC,EAAO,KACLC,EAAIN,EAAS,KAAK,CAACO,EAAGC,IAAMT,GAAQQ,EAAGC,EAAGN,CAAO,CAAC,EACxD,QAAWO,KAAWH,EACHR,GAAUW,EAASR,EAAOC,CAAO,GAEhDG,EAAOI,EACFL,IACHA,EAAQK,KAGNJ,GACFF,EAAI,KAAK,CAACC,EAAOC,CAAI,CAAC,EAExBA,EAAO,KACPD,EAAQ,MAGRA,GACFD,EAAI,KAAK,CAACC,EAAO,IAAI,CAAC,EAGxB,IAAMM,EAAS,CAAC,EAChB,OAAW,CAACC,EAAKC,CAAG,IAAKT,EACnBQ,IAAQC,EACVF,EAAO,KAAKC,CAAG,EACN,CAACC,GAAOD,IAAQL,EAAE,CAAC,EAC5BI,EAAO,KAAK,GAAG,EACLE,EAEDD,IAAQL,EAAE,CAAC,EACpBI,EAAO,KAAK,KAAKE,CAAG,EAAE,EAEtBF,EAAO,KAAK,GAAGC,CAAG,MAAMC,CAAG,EAAE,EAJ7BF,EAAO,KAAK,KAAKC,CAAG,EAAE,EAO1B,IAAME,EAAaH,EAAO,KAAK,MAAM,EAC/BI,EAAW,OAAOb,EAAM,KAAQ,SAAWA,EAAM,IAAM,OAAOA,CAAK,EACzE,OAAOY,EAAW,OAASC,EAAS,OAASD,EAAaZ,CAC5D,IChDA,IAAAc,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAEA,IAAMC,GAAQ,IACRC,GAAa,KACb,CAAE,IAAAC,EAAI,EAAID,GACVE,GAAY,KACZC,GAAU,IAsCVC,GAAS,CAACC,EAAKC,EAAKC,EAAU,CAAC,IAAM,CACzC,GAAIF,IAAQC,EACV,MAAO,GAGTD,EAAM,IAAIN,GAAMM,EAAKE,CAAO,EAC5BD,EAAM,IAAIP,GAAMO,EAAKC,CAAO,EAC5B,IAAIC,EAAa,GAEjBC,EAAO,QAAWC,KAAaL,EAAI,IAAK,CACtC,QAAWM,KAAaL,EAAI,IAAK,CAC/B,IAAMM,EAAQC,GAAaH,EAAWC,EAAWJ,CAAO,EAExD,GADAC,EAAaA,GAAcI,IAAU,KACjCA,EACF,SAASH,CAEb,CAKA,GAAID,EACF,MAAO,EAEX,CACA,MAAO,EACT,EAEMM,GAA+B,CAAC,IAAId,GAAW,WAAW,CAAC,EAC3De,GAAiB,CAAC,IAAIf,GAAW,SAAS,CAAC,EAE3Ca,GAAe,CAACR,EAAKC,EAAKC,IAAY,CAC1C,GAAIF,IAAQC,EACV,MAAO,GAGT,GAAID,EAAI,SAAW,GAAKA,EAAI,CAAC,EAAE,SAAWJ,GAAK,CAC7C,GAAIK,EAAI,SAAW,GAAKA,EAAI,CAAC,EAAE,SAAWL,GACxC,MAAO,GACEM,EAAQ,kBACjBF,EAAMS,GAENT,EAAMU,EAEV,CAEA,GAAIT,EAAI,SAAW,GAAKA,EAAI,CAAC,EAAE,SAAWL,GAAK,CAC7C,GAAIM,EAAQ,kBACV,MAAO,GAEPD,EAAMS,EAEV,CAEA,IAAMC,EAAQ,IAAI,IACdC,EAAIC,EACR,QAAWC,KAAKd,EACVc,EAAE,WAAa,KAAOA,EAAE,WAAa,KACvCF,EAAKG,GAASH,EAAIE,EAAGZ,CAAO,EACnBY,EAAE,WAAa,KAAOA,EAAE,WAAa,KAC9CD,EAAKG,GAAQH,EAAIC,EAAGZ,CAAO,EAE3BS,EAAM,IAAIG,EAAE,MAAM,EAItB,GAAIH,EAAM,KAAO,EACf,OAAO,KAGT,IAAIM,EACJ,GAAIL,GAAMC,EAAI,CAEZ,GADAI,EAAWnB,GAAQc,EAAG,OAAQC,EAAG,OAAQX,CAAO,EAC5Ce,EAAW,EACb,OAAO,KACF,GAAIA,IAAa,IAAML,EAAG,WAAa,MAAQC,EAAG,WAAa,MACpE,OAAO,IAEX,CAGA,QAAWK,KAAMP,EAAO,CAKtB,GAJIC,GAAM,CAACf,GAAUqB,EAAI,OAAON,CAAE,EAAGV,CAAO,GAIxCW,GAAM,CAAChB,GAAUqB,EAAI,OAAOL,CAAE,EAAGX,CAAO,EAC1C,OAAO,KAGT,QAAWY,KAAKb,EACd,GAAI,CAACJ,GAAUqB,EAAI,OAAOJ,CAAC,EAAGZ,CAAO,EACnC,MAAO,GAIX,MAAO,EACT,CAEA,IAAIiB,EAAQC,EACRC,EAAUC,EAGVC,EAAeV,GACjB,CAACX,EAAQ,mBACTW,EAAG,OAAO,WAAW,OAASA,EAAG,OAAS,GACxCW,EAAeZ,GACjB,CAACV,EAAQ,mBACTU,EAAG,OAAO,WAAW,OAASA,EAAG,OAAS,GAExCW,GAAgBA,EAAa,WAAW,SAAW,GACnDV,EAAG,WAAa,KAAOU,EAAa,WAAW,CAAC,IAAM,IACxDA,EAAe,IAGjB,QAAWT,KAAKb,EAAK,CAGnB,GAFAqB,EAAWA,GAAYR,EAAE,WAAa,KAAOA,EAAE,WAAa,KAC5DO,EAAWA,GAAYP,EAAE,WAAa,KAAOA,EAAE,WAAa,KACxDF,GASF,GARIY,GACEV,EAAE,OAAO,YAAcA,EAAE,OAAO,WAAW,QAC3CA,EAAE,OAAO,QAAUU,EAAa,OAChCV,EAAE,OAAO,QAAUU,EAAa,OAChCV,EAAE,OAAO,QAAUU,EAAa,QAClCA,EAAe,IAGfV,EAAE,WAAa,KAAOA,EAAE,WAAa,MAEvC,GADAK,EAASJ,GAASH,EAAIE,EAAGZ,CAAO,EAC5BiB,IAAWL,GAAKK,IAAWP,EAC7B,MAAO,WAEAA,EAAG,WAAa,MAAQ,CAACf,GAAUe,EAAG,OAAQ,OAAOE,CAAC,EAAGZ,CAAO,EACzE,MAAO,GAGX,GAAIW,GASF,GARIU,GACET,EAAE,OAAO,YAAcA,EAAE,OAAO,WAAW,QAC3CA,EAAE,OAAO,QAAUS,EAAa,OAChCT,EAAE,OAAO,QAAUS,EAAa,OAChCT,EAAE,OAAO,QAAUS,EAAa,QAClCA,EAAe,IAGfT,EAAE,WAAa,KAAOA,EAAE,WAAa,MAEvC,GADAM,EAAQJ,GAAQH,EAAIC,EAAGZ,CAAO,EAC1BkB,IAAUN,GAAKM,IAAUP,EAC3B,MAAO,WAEAA,EAAG,WAAa,MAAQ,CAAChB,GAAUgB,EAAG,OAAQ,OAAOC,CAAC,EAAGZ,CAAO,EACzE,MAAO,GAGX,GAAI,CAACY,EAAE,WAAaD,GAAMD,IAAOK,IAAa,EAC5C,MAAO,EAEX,CAgBA,MAXI,EAAAL,GAAMS,GAAY,CAACR,GAAMI,IAAa,GAItCJ,GAAMS,GAAY,CAACV,GAAMK,IAAa,GAOtCO,GAAgBD,EAKtB,EAGMR,GAAW,CAACU,EAAGC,EAAGxB,IAAY,CAClC,GAAI,CAACuB,EACH,OAAOC,EAET,IAAMC,EAAO7B,GAAQ2B,EAAE,OAAQC,EAAE,OAAQxB,CAAO,EAChD,OAAOyB,EAAO,EAAIF,EACdE,EAAO,GACPD,EAAE,WAAa,KAAOD,EAAE,WAAa,KAD1BC,EAEXD,CACN,EAGMT,GAAU,CAACS,EAAGC,EAAGxB,IAAY,CACjC,GAAI,CAACuB,EACH,OAAOC,EAET,IAAMC,EAAO7B,GAAQ2B,EAAE,OAAQC,EAAE,OAAQxB,CAAO,EAChD,OAAOyB,EAAO,EAAIF,EACdE,EAAO,GACPD,EAAE,WAAa,KAAOD,EAAE,WAAa,KAD1BC,EAEXD,CACN,EAEAhC,GAAO,QAAUM,KCxPjB,IAAA6B,GAAAC,EAAA,CAAAC,GAAAC,KAAA,cAGA,IAAMC,GAAa,KACbC,GAAY,KACZC,GAAS,IACTC,GAAc,KACdC,GAAQ,IACRC,GAAQ,KACRC,GAAQ,KACRC,GAAM,KACNC,GAAO,KACPC,GAAQ,KACRC,GAAQ,KACRC,GAAQ,KACRC,GAAa,KACbC,GAAU,IACVC,GAAW,KACXC,GAAe,KACfC,GAAe,KACfC,GAAO,KACPC,GAAQ,KACRC,GAAK,KACLC,GAAK,KACLC,GAAK,KACLC,GAAM,KACNC,GAAM,KACNC,GAAM,KACNC,GAAM,KACNC,GAAS,KACTC,GAAa,KACbC,GAAQ,IACRC,GAAY,KACZC,GAAgB,KAChBC,GAAgB,KAChBC,GAAgB,KAChBC,GAAa,KACbC,GAAa,KACbC,GAAU,KACVC,GAAM,KACNC,GAAM,KACNC,GAAa,KACbC,GAAgB,KAChBC,GAAS,KACfzC,GAAO,QAAU,CACf,MAAAK,GACA,MAAAC,GACA,MAAAC,GACA,IAAAC,GACA,KAAAC,GACA,MAAAC,GACA,MAAAC,GACA,MAAAC,GACA,WAAAC,GACA,QAAAC,GACA,SAAAC,GACA,aAAAC,GACA,aAAAC,GACA,KAAAC,GACA,MAAAC,GACA,GAAAC,GACA,GAAAC,GACA,GAAAC,GACA,IAAAC,GACA,IAAAC,GACA,IAAAC,GACA,IAAAC,GACA,OAAAC,GACA,WAAAC,GACA,MAAAC,GACA,UAAAC,GACA,cAAAC,GACA,cAAAC,GACA,cAAAC,GACA,WAAAC,GACA,WAAAC,GACA,QAAAC,GACA,IAAAC,GACA,IAAAC,GACA,WAAAC,GACA,cAAAC,GACA,OAAAC,GACA,OAAAtC,GACA,GAAIF,GAAW,GACf,IAAKA,GAAW,IAChB,OAAQA,GAAW,EACnB,oBAAqBC,GAAU,oBAC/B,cAAeA,GAAU,cACzB,mBAAoBE,GAAY,mBAChC,oBAAqBA,GAAY,mBACnC,IC1FA,OAAS,mBAAAsC,GAAiB,wBAAAC,OAA4B,gBACtD,OAAS,kBAAAC,GAAgB,UAAAC,OAAc,YACvC,OAAOC,OAAY,SCFnB,OAAS,eAAAC,GAAyE,QAAAC,GAAM,UAAAC,OAAc,gBAEtG,OAAS,UAAAC,OAAc,YCFvB,OAAS,iBAAAC,OAA2C,gBCApD,OAAS,iBAAAC,OAAqB,gBAC9B,OAAS,cAAAC,GAAY,aAAAC,GAAW,gBAAAC,GAAc,iBAAAC,OAAqB,UACnE,OAAS,WAAAC,OAAe,UACxB,OAAS,WAAAC,OAAe,YAEjB,IAAMC,EAAN,cAAgCP,EAAc,CAInD,YAAYQ,EAAiB,CAC3B,MAAM,EACN,KAAK,QAAUF,GAAQD,GAAQ,EAAG,UAAU,EAC5C,KAAK,SAAWC,GAAQ,KAAK,QAASE,EAAU,OAAO,CACzD,CAEA,OAAc,CACZ,KAAK,UAAU,CAAC,CAAC,CACnB,CAEA,UAAUC,EAAiC,CACzC,OAAO,KAAK,SAAS,IAAIA,CAAG,CAC9B,CAEA,UAAUA,EAAaC,EAAiC,CACtD,IAAMC,EAAO,KAAK,SAAS,GAAK,CAAC,EAC7BD,EACFC,EAAKF,CAAG,EAAIC,EAEZ,OAAOC,EAAKF,CAAG,EAEjB,KAAK,UAAUE,CAAI,CACrB,CAEA,UAAaF,EAA4B,CACvC,IAAMG,EAAM,KAAK,UAAUH,CAAG,EAC9B,OAAOG,EAAO,KAAK,MAAMA,CAAG,EAAU,MACxC,CAEA,UAAaH,EAAaC,EAAgB,CACxC,KAAK,UAAUD,EAAKC,EAAQ,KAAK,UAAUA,CAAK,EAAI,MAAS,CAC/D,CAEQ,UAA+C,CACrD,GAAIT,GAAW,KAAK,QAAQ,EAC1B,OAAO,KAAK,MAAME,GAAa,KAAK,SAAU,MAAM,CAAC,CAGzD,CAEQ,UAAUQ,EAAoC,CAC/CV,GAAW,KAAK,OAAO,GAC1BC,GAAU,KAAK,OAAO,EAExBE,GAAc,KAAK,SAAU,KAAK,UAAUO,EAAM,KAAM,CAAC,EAAG,MAAM,CACpE,CACF,EDnDA,eAAsBE,EACpBC,EACAC,EAAmB,GACK,CACxB,IAAMC,EAAcF,EAAQ,SAAW,UAEjCG,EAAU,IAAIC,EAAkBF,CAAW,EAC3CG,EAAUF,EAAQ,UAAU,SAAS,EAC3C,GAAID,IAAgB,WAAa,CAACG,EAChC,MAAM,IAAI,MAAM,YAAYH,CAAW,kBAAkB,EAG3D,GAAM,CAAE,QAAAI,EAAS,YAAAC,EAAa,YAAAC,EAAa,SAAAC,EAAU,aAAAC,EAAc,SAAAC,EAAU,aAAAC,CAAa,EAAIC,GAC5Fb,EACAG,CACF,EACMW,EAAWd,EAAQ,OAAS,MAC5Be,EAAgB,IAAIC,GAAc,CACtC,MAAOF,EACP,QAAAR,EACA,SAAAG,EACA,YAAAF,EACA,aAAAG,EACA,QAAAP,EACA,kBAAAc,GACA,QAASjB,EAAQ,OACnB,CAAC,EAKD,OAAIC,IACEO,EAEFO,EAAc,eAAeP,CAAW,EAC/BG,GAAYC,IAErBG,EAAc,aAAaJ,EAAoBC,CAAsB,EACjEP,GAAS,WAAa,SAExB,MAAMU,EAAc,iBAAiBJ,EAAoBC,CAAsB,IAK9EG,CACT,CAEA,SAASF,GAAgBb,EAA+BG,EAAkD,CACxG,IAAMe,EAAiBf,EAAQ,UAAU,SAAS,EAC5CG,EACJN,EAAQ,SAAWkB,GAAgB,SAAW,QAAQ,IAAI,kBAAuB,2BAC7EX,EAAcP,EAAQ,aAAekB,GAAgB,aAAe,QAAQ,IAAI,sBAChFV,EAAcR,EAAQ,aAAekB,GAAgB,aAAe,QAAQ,IAAI,4BAChFT,EAAWT,EAAQ,UAAYkB,GAAgB,UAAY,QAAQ,IAAI,kBACvER,EAAeV,EAAQ,cAAgBkB,GAAgB,cAAgB,QAAQ,IAAI,sBAEnFP,EAAWX,EAAQ,UAAYkB,GAAgB,UAAY,QAAQ,IAAI,kBACvEN,EAAeZ,EAAQ,cAAgBkB,GAAgB,cAAgB,QAAQ,IAAI,sBAEzF,MAAO,CAAE,QAAAZ,EAAS,YAAAC,EAAa,YAAAC,EAAa,SAAAC,EAAU,aAAAC,EAAc,SAAAC,EAAU,aAAAC,CAAa,CAC7F,CAEO,SAASK,IAA0B,CACxC,QAAQ,IAAI,qDAAqD,CACnE,CErEA,OAAS,eAAAE,GAAa,gBAAAC,OAA2C,gBAEjE,OAAS,WAAAC,OAAe,YCFxB,OAAS,UAAAC,OAAc,cCChB,IAAMC,EAAU,IAAI,YACdC,GAAU,IAAI,YACrBC,GAAY,GAAK,GAChB,SAASC,MAAUC,EAAS,CAC/B,IAAMC,EAAOD,EAAQ,OAAO,CAACE,EAAK,CAAE,OAAAC,CAAO,IAAMD,EAAMC,EAAQ,CAAC,EAC1DC,EAAM,IAAI,WAAWH,CAAI,EAC3BI,EAAI,EACR,QAAWC,KAAUN,EACjBI,EAAI,IAAIE,EAAQD,CAAC,EACjBA,GAAKC,EAAO,OAEhB,OAAOF,CACX,CDJA,IAAMG,GAAUC,GAAUC,GAAO,KAAKD,CAAK,EAAE,SAAS,WAAW,EET1D,IAAME,EAAN,cAAwB,KAAM,CAGjC,YAAYC,EAASC,EAAS,CAC1B,MAAMD,EAASC,CAAO,EAF1BC,EAAA,YAAO,oBAGH,KAAK,KAAO,KAAK,YAAY,KAC7B,MAAM,oBAAoB,KAAM,KAAK,WAAW,CACpD,CACJ,EAPIA,EADSH,EACF,OAAO,oBAsCX,IAAMI,EAAN,cAA+BC,CAAU,CAAzC,kCAEHC,EAAA,YAAO,0BACX,EAFIA,EADSF,EACF,OAAO,0BAcX,IAAMG,EAAN,cAAyBC,CAAU,CAAnC,kCAEHC,EAAA,YAAO,mBACX,EAFIA,EADSF,EACF,OAAO,mBAGX,IAAMG,GAAN,cAAyBF,CAAU,CAAnC,kCAEHC,EAAA,YAAO,mBACX,EAFIA,EADSC,GACF,OAAO,mBA3DlB,IAAAC,GAAAC,GA6EaC,GAAN,cAAuCD,GAAAE,EACzCH,GAAA,OAAO,cADkCC,GAAU,CAIpD,YAAYG,EAAU,uDAAwDC,EAAS,CACnF,MAAMD,EAASC,CAAO,EAJ1BC,EAAA,KAACN,IAEDM,EAAA,YAAO,kCAGP,CACJ,EALIA,EAFSJ,GAEF,OAAO,mCC/ElB,UAAYK,OAAU,YACtB,IAAOC,GAASC,GAAa,SAAM,YAAYA,CAAG,ECDlD,UAAYC,OAAY,cACxB,UAAYC,OAAU,YACtB,IAAMC,GAAmB,aAClBC,GAAQD,GACFE,GAAeC,GAAa,SAAM,YAAYA,CAAG,ECJ9D,SAASC,EAASC,EAAMC,EAAO,iBAAkB,CAC7C,OAAO,IAAI,UAAU,kDAAkDA,CAAI,YAAYD,CAAI,EAAE,CACjG,CACA,SAASE,GAAYC,EAAWH,EAAM,CAClC,OAAOG,EAAU,OAASH,CAC9B,CACA,SAASI,GAAcC,EAAM,CACzB,OAAO,SAASA,EAAK,KAAK,MAAM,CAAC,EAAG,EAAE,CAC1C,CACA,SAASC,GAAcC,EAAK,CACxB,OAAQA,EAAK,CACT,IAAK,QACD,MAAO,QACX,IAAK,QACD,MAAO,QACX,IAAK,QACD,MAAO,QACX,QACI,MAAM,IAAI,MAAM,aAAa,CACrC,CACJ,CACA,SAASC,GAAWC,EAAKC,EAAQ,CAC7B,GAAIA,EAAO,QAAU,CAACA,EAAO,KAAMC,GAAaF,EAAI,OAAO,SAASE,CAAQ,CAAC,EAAG,CAC5E,IAAIC,EAAM,sEACV,GAAIF,EAAO,OAAS,EAAG,CACnB,IAAMG,EAAOH,EAAO,IAAI,EACxBE,GAAO,UAAUF,EAAO,KAAK,IAAI,CAAC,QAAQG,CAAI,GAClD,MACSH,EAAO,SAAW,EACvBE,GAAO,UAAUF,EAAO,CAAC,CAAC,OAAOA,EAAO,CAAC,CAAC,IAG1CE,GAAO,GAAGF,EAAO,CAAC,CAAC,IAEvB,MAAM,IAAI,UAAUE,CAAG,CAC3B,CACJ,CACO,SAASE,GAAkBL,EAAKF,KAAQG,EAAQ,CACnD,OAAQH,EAAK,CACT,IAAK,QACL,IAAK,QACL,IAAK,QAAS,CACV,GAAI,CAACL,GAAYO,EAAI,UAAW,MAAM,EAClC,MAAMV,EAAS,MAAM,EACzB,IAAMY,EAAW,SAASJ,EAAI,MAAM,CAAC,EAAG,EAAE,EAE1C,GADeH,GAAcK,EAAI,UAAU,IAAI,IAChCE,EACX,MAAMZ,EAAS,OAAOY,CAAQ,GAAI,gBAAgB,EACtD,KACJ,CACA,IAAK,QACL,IAAK,QACL,IAAK,QAAS,CACV,GAAI,CAACT,GAAYO,EAAI,UAAW,mBAAmB,EAC/C,MAAMV,EAAS,mBAAmB,EACtC,IAAMY,EAAW,SAASJ,EAAI,MAAM,CAAC,EAAG,EAAE,EAE1C,GADeH,GAAcK,EAAI,UAAU,IAAI,IAChCE,EACX,MAAMZ,EAAS,OAAOY,CAAQ,GAAI,gBAAgB,EACtD,KACJ,CACA,IAAK,QACL,IAAK,QACL,IAAK,QAAS,CACV,GAAI,CAACT,GAAYO,EAAI,UAAW,SAAS,EACrC,MAAMV,EAAS,SAAS,EAC5B,IAAMY,EAAW,SAASJ,EAAI,MAAM,CAAC,EAAG,EAAE,EAE1C,GADeH,GAAcK,EAAI,UAAU,IAAI,IAChCE,EACX,MAAMZ,EAAS,OAAOY,CAAQ,GAAI,gBAAgB,EACtD,KACJ,CACA,IAAK,QAAS,CACV,GAAIF,EAAI,UAAU,OAAS,WAAaA,EAAI,UAAU,OAAS,QAC3D,MAAMV,EAAS,kBAAkB,EAErC,KACJ,CACA,IAAK,UAAW,CACZ,GAAI,CAACG,GAAYO,EAAI,UAAW,SAAS,EACrC,MAAMV,EAAS,SAAS,EAC5B,KACJ,CACA,IAAK,QACL,IAAK,QACL,IAAK,QAAS,CACV,GAAI,CAACG,GAAYO,EAAI,UAAW,OAAO,EACnC,MAAMV,EAAS,OAAO,EAC1B,IAAMY,EAAWL,GAAcC,CAAG,EAElC,GADeE,EAAI,UAAU,aACdE,EACX,MAAMZ,EAASY,EAAU,sBAAsB,EACnD,KACJ,CACA,QACI,MAAM,IAAI,UAAU,2CAA2C,CACvE,CACAH,GAAWC,EAAKC,CAAM,CAC1B,CClGA,SAASK,GAAQC,EAAKC,KAAWC,EAAO,CAEpC,GADAA,EAAQA,EAAM,OAAO,OAAO,EACxBA,EAAM,OAAS,EAAG,CAClB,IAAMC,EAAOD,EAAM,IAAI,EACvBF,GAAO,eAAeE,EAAM,KAAK,IAAI,CAAC,QAAQC,CAAI,GACtD,MACSD,EAAM,SAAW,EACtBF,GAAO,eAAeE,EAAM,CAAC,CAAC,OAAOA,EAAM,CAAC,CAAC,IAG7CF,GAAO,WAAWE,EAAM,CAAC,CAAC,IAE9B,OAAID,GAAU,KACVD,GAAO,aAAaC,CAAM,GAErB,OAAOA,GAAW,YAAcA,EAAO,KAC5CD,GAAO,sBAAsBC,EAAO,IAAI,GAEnC,OAAOA,GAAW,UAAYA,GAAU,MACzCA,EAAO,aAAa,OACpBD,GAAO,4BAA4BC,EAAO,YAAY,IAAI,IAG3DD,CACX,CACA,IAAOI,GAAQ,CAACH,KAAWC,IAChBH,GAAQ,eAAgBE,EAAQ,GAAGC,CAAK,EAE5C,SAASG,GAAQC,EAAKL,KAAWC,EAAO,CAC3C,OAAOH,GAAQ,eAAeO,CAAG,sBAAuBL,EAAQ,GAAGC,CAAK,CAC5E,CC5BA,IAAOK,GAASC,GAAQC,GAAYD,CAAG,GAAKE,GAAYF,CAAG,EACrDG,EAAQ,CAAC,WAAW,GACtB,WAAW,WAAaC,IAAW,YACnCD,EAAM,KAAK,WAAW,ECL1B,IAAME,GAAa,IAAIC,IAAY,CAC/B,IAAMC,EAAUD,EAAQ,OAAO,OAAO,EACtC,GAAIC,EAAQ,SAAW,GAAKA,EAAQ,SAAW,EAC3C,MAAO,GAEX,IAAIC,EACJ,QAAWC,KAAUF,EAAS,CAC1B,IAAMG,EAAa,OAAO,KAAKD,CAAM,EACrC,GAAI,CAACD,GAAOA,EAAI,OAAS,EAAG,CACxBA,EAAM,IAAI,IAAIE,CAAU,EACxB,QACJ,CACA,QAAWC,KAAaD,EAAY,CAChC,GAAIF,EAAI,IAAIG,CAAS,EACjB,MAAO,GAEXH,EAAI,IAAIG,CAAS,CACrB,CACJ,CACA,MAAO,EACX,EACOC,GAAQP,GCrBf,SAASQ,GAAaC,EAAO,CACzB,OAAO,OAAOA,GAAU,UAAYA,IAAU,IAClD,CACe,SAARC,GAA0BC,EAAO,CACpC,GAAI,CAACH,GAAaG,CAAK,GAAK,OAAO,UAAU,SAAS,KAAKA,CAAK,IAAM,kBAClE,MAAO,GAEX,GAAI,OAAO,eAAeA,CAAK,IAAM,KACjC,MAAO,GAEX,IAAIC,EAAQD,EACZ,KAAO,OAAO,eAAeC,CAAK,IAAM,MACpCA,EAAQ,OAAO,eAAeA,CAAK,EAEvC,OAAO,OAAO,eAAeD,CAAK,IAAMC,CAC5C,CCfA,OAAS,aAAAC,OAAiB,cCCnB,SAASC,EAAMC,EAAK,CACvB,OAAOC,GAASD,CAAG,GAAK,OAAOA,EAAI,KAAQ,QAC/C,CACO,SAASE,GAAaF,EAAK,CAC9B,OAAOA,EAAI,MAAQ,OAAS,OAAOA,EAAI,GAAM,QACjD,CACO,SAASG,GAAYH,EAAK,CAC7B,OAAOA,EAAI,MAAQ,OAAS,OAAOA,EAAI,EAAM,GACjD,CACO,SAASI,GAAYJ,EAAK,CAC7B,OAAOD,EAAMC,CAAG,GAAKA,EAAI,MAAQ,OAAS,OAAOA,EAAI,GAAM,QAC/D,CDJA,IAAMK,GAAoBC,GAAe,CACrC,OAAQA,EAAY,CAChB,IAAK,aACD,MAAO,QACX,IAAK,YACD,MAAO,QACX,IAAK,YACD,MAAO,QACX,IAAK,YACD,MAAO,YACX,QACI,MAAM,IAAIC,EAAiB,0CAA0C,CAC7E,CACJ,EACMC,GAAgB,CAACC,EAAKC,IAAQ,CAChC,IAAIC,EACJ,GAAIC,GAAYH,CAAG,EACfE,EAAME,GAAU,KAAKJ,CAAG,UAEnBK,GAAYL,CAAG,EACpBE,EAAMF,MAEL,IAAIM,EAAMN,CAAG,EACd,OAAOA,EAAI,IAGX,MAAM,IAAI,UAAUO,GAAgBP,EAAK,GAAGQ,CAAK,CAAC,EAEtD,GAAIN,EAAI,OAAS,SACb,MAAM,IAAI,UAAU,qEAAqE,EAE7F,OAAQA,EAAI,kBAAmB,CAC3B,IAAK,UACL,IAAK,QACD,MAAO,KAAKA,EAAI,kBAAkB,MAAM,CAAC,CAAC,GAC9C,IAAK,SACL,IAAK,OACD,MAAO,IAAIA,EAAI,kBAAkB,MAAM,CAAC,CAAC,GAC7C,IAAK,KAAM,CACP,IAAML,EAAaK,EAAI,qBAAqB,WAC5C,OAAID,EACOJ,EAEJD,GAAiBC,CAAU,CACtC,CACA,QACI,MAAM,IAAI,UAAU,gDAAgD,CAC5E,CACJ,EACOY,GAAQV,GEzDf,OAAS,aAAAW,OAAiB,cAC1B,IAAOC,GAAQ,CAACC,EAAKC,IAAQ,CACzB,IAAIC,EACJ,GAAI,CACIF,aAAeF,GACfI,EAAgBF,EAAI,sBAAsB,cAG1CE,EAAgB,OAAO,KAAKF,EAAI,EAAG,WAAW,EAAE,YAAc,CAEtE,MACM,CAAE,CACR,GAAI,OAAOE,GAAkB,UAAYA,EAAgB,KACrD,MAAM,IAAI,UAAU,GAAGD,CAAG,uDAAuD,CAEzF,ECZA,IAAME,GAAOC,GAAQA,IAAM,OAAO,WAAW,EACvCC,GAAe,CAACC,EAAKF,EAAKG,IAAU,CACtC,GAAIH,EAAI,MAAQ,QAAaA,EAAI,MAAQ,MACrC,MAAM,IAAI,UAAU,kEAAkE,EAE1F,GAAIA,EAAI,UAAY,QAAaA,EAAI,QAAQ,WAAWG,CAAK,IAAM,GAC/D,MAAM,IAAI,UAAU,yEAAyEA,CAAK,EAAE,EAExG,GAAIH,EAAI,MAAQ,QAAaA,EAAI,MAAQE,EACrC,MAAM,IAAI,UAAU,gEAAgEA,CAAG,EAAE,EAE7F,MAAO,EACX,EACME,GAAqB,CAACF,EAAKF,EAAKG,EAAOE,IAAa,CACtD,GAAI,EAAAL,aAAe,YAEnB,IAAIK,GAAgBC,EAAMN,CAAG,EAAG,CAC5B,GAAQO,GAAYP,CAAG,GAAKC,GAAaC,EAAKF,EAAKG,CAAK,EACpD,OACJ,MAAM,IAAI,UAAU,yHAAyH,CACjJ,CACA,GAAI,CAACK,GAAUR,CAAG,EACd,MAAM,IAAI,UAAUS,GAAgBP,EAAKF,EAAK,GAAGU,EAAO,aAAcL,EAAW,eAAiB,IAAI,CAAC,EAE3G,GAAIL,EAAI,OAAS,SACb,MAAM,IAAI,UAAU,GAAGD,GAAIC,CAAG,CAAC,8DAA8D,EAErG,EACMW,GAAsB,CAACT,EAAKF,EAAKG,EAAOE,IAAa,CACvD,GAAIA,GAAgBC,EAAMN,CAAG,EACzB,OAAQG,EAAO,CACX,IAAK,OACD,GAAQS,GAAaZ,CAAG,GAAKC,GAAaC,EAAKF,EAAKG,CAAK,EACrD,OACJ,MAAM,IAAI,UAAU,kDAAkD,EAC1E,IAAK,SACD,GAAQU,GAAYb,CAAG,GAAKC,GAAaC,EAAKF,EAAKG,CAAK,EACpD,OACJ,MAAM,IAAI,UAAU,iDAAiD,CAC7E,CAEJ,GAAI,CAACK,GAAUR,CAAG,EACd,MAAM,IAAI,UAAUS,GAAgBP,EAAKF,EAAK,GAAGU,EAAOL,EAAW,eAAiB,IAAI,CAAC,EAE7F,GAAIL,EAAI,OAAS,SACb,MAAM,IAAI,UAAU,GAAGD,GAAIC,CAAG,CAAC,mEAAmE,EAEtG,GAAIG,IAAU,QAAUH,EAAI,OAAS,SACjC,MAAM,IAAI,UAAU,GAAGD,GAAIC,CAAG,CAAC,uEAAuE,EAE1G,GAAIG,IAAU,WAAaH,EAAI,OAAS,SACpC,MAAM,IAAI,UAAU,GAAGD,GAAIC,CAAG,CAAC,0EAA0E,EAE7G,GAAIA,EAAI,WAAaG,IAAU,UAAYH,EAAI,OAAS,UACpD,MAAM,IAAI,UAAU,GAAGD,GAAIC,CAAG,CAAC,wEAAwE,EAE3G,GAAIA,EAAI,WAAaG,IAAU,WAAaH,EAAI,OAAS,UACrD,MAAM,IAAI,UAAU,GAAGD,GAAIC,CAAG,CAAC,yEAAyE,CAEhH,EACA,SAASc,GAAaT,EAAUH,EAAKF,EAAKG,EAAO,CAC3BD,EAAI,WAAW,IAAI,GACjCA,IAAQ,OACRA,EAAI,WAAW,OAAO,GACtB,qBAAqB,KAAKA,CAAG,EAE7BE,GAAmBF,EAAKF,EAAKG,EAAOE,CAAQ,EAG5CM,GAAoBT,EAAKF,EAAKG,EAAOE,CAAQ,CAErD,CACA,IAAOU,GAAQD,GAAa,KAAK,OAAW,EAAK,EACpCE,GAAsBF,GAAa,KAAK,OAAW,EAAI,EC3EpE,SAASG,GAAaC,EAAKC,EAAmBC,EAAkBC,EAAiBC,EAAY,CACzF,GAAIA,EAAW,OAAS,QAAaD,GAAiB,OAAS,OAC3D,MAAM,IAAIH,EAAI,gEAAgE,EAElF,GAAI,CAACG,GAAmBA,EAAgB,OAAS,OAC7C,OAAO,IAAI,IAEf,GAAI,CAAC,MAAM,QAAQA,EAAgB,IAAI,GACnCA,EAAgB,KAAK,SAAW,GAChCA,EAAgB,KAAK,KAAME,GAAU,OAAOA,GAAU,UAAYA,EAAM,SAAW,CAAC,EACpF,MAAM,IAAIL,EAAI,uFAAuF,EAEzG,IAAIM,EACAJ,IAAqB,OACrBI,EAAa,IAAI,IAAI,CAAC,GAAG,OAAO,QAAQJ,CAAgB,EAAG,GAAGD,EAAkB,QAAQ,CAAC,CAAC,EAG1FK,EAAaL,EAEjB,QAAWM,KAAaJ,EAAgB,KAAM,CAC1C,GAAI,CAACG,EAAW,IAAIC,CAAS,EACzB,MAAM,IAAIC,EAAiB,+BAA+BD,CAAS,qBAAqB,EAE5F,GAAIH,EAAWG,CAAS,IAAM,OAC1B,MAAM,IAAIP,EAAI,+BAA+BO,CAAS,cAAc,EAExE,GAAID,EAAW,IAAIC,CAAS,GAAKJ,EAAgBI,CAAS,IAAM,OAC5D,MAAM,IAAIP,EAAI,+BAA+BO,CAAS,+BAA+B,CAE7F,CACA,OAAO,IAAI,IAAIJ,EAAgB,IAAI,CACvC,CACA,IAAOM,GAAQV,GChCA,SAARW,GAA2BC,EAAK,CACnC,OAAQA,EAAK,CACT,IAAK,QACL,IAAK,QACL,IAAK,QACL,IAAK,SACD,MAAO,SACX,IAAK,QACL,IAAK,QACL,IAAK,QACD,MAAO,SACX,IAAK,QACL,IAAK,QACL,IAAK,QACD,MAAO,SACX,IAAK,UACL,IAAK,QACD,OACJ,QACI,MAAM,IAAIC,EAAiB,OAAOD,CAAG,6DAA6D,CAC1G,CACJ,CCtBA,OAAS,aAAAE,GAAW,aAAAC,OAAiB,cAIrC,IAAMC,GAAgB,IAAI,IAAI,CAC1B,CAAC,QAAS,OAAO,EACjB,CAAC,SAAU,WAAW,EACtB,CAAC,QAAS,OAAO,EACjB,CAAC,QAAS,OAAO,CACrB,CAAC,EACc,SAARC,GAA8BC,EAAKC,EAAK,CAC3C,IAAIC,EACAC,EACAC,EACJ,GAAIH,aAAeI,GACfH,EAAoBD,EAAI,kBACxBE,EAAuBF,EAAI,yBAI3B,QADAG,EAAQ,GACAH,EAAI,IAAK,CACb,IAAK,MACDC,EAAoB,MACpB,MACJ,IAAK,KACDA,EAAoB,KACpB,MACJ,IAAK,MAAO,CACR,GAAID,EAAI,MAAQ,UAAW,CACvBC,EAAoB,UACpB,KACJ,CACA,GAAID,EAAI,MAAQ,QAAS,CACrBC,EAAoB,QACpB,KACJ,CACA,MAAM,IAAI,UAAU,kEAAkE,CAC1F,CACA,QACI,MAAM,IAAI,UAAU,iEAAiE,CAC7F,CAEJ,IAAII,EACJ,OAAQN,EAAK,CACT,IAAK,UACD,GAAIE,IAAsB,UACtB,MAAM,IAAI,UAAU,uEAAuE,EAE/F,MACJ,IAAK,QACD,GAAI,CAAC,CAAC,UAAW,OAAO,EAAE,SAASA,CAAiB,EAChD,MAAM,IAAI,UAAU,gFAAgF,EAExG,MACJ,IAAK,QACL,IAAK,QACL,IAAK,QACD,GAAIA,IAAsB,MACtB,MAAM,IAAI,UAAU,mEAAmE,EAE3FK,GAAeN,EAAKD,CAAG,EACvB,MACJ,IAAK,QACL,IAAK,QACL,IAAK,QACD,GAAIE,IAAsB,UAAW,CACjC,GAAM,CAAE,cAAAM,EAAe,kBAAAC,EAAmB,WAAAC,CAAW,EAAIP,EACnDQ,EAAS,SAASX,EAAI,MAAM,EAAE,EAAG,EAAE,EACzC,GAAIQ,IAAkB,SACjBA,IAAkB,MAAMG,CAAM,IAAMF,IAAsBD,GAC3D,MAAM,IAAI,UAAU,gGAAgGR,CAAG,EAAE,EAE7H,GAAIU,IAAe,QAAaA,EAAaC,GAAU,EACnD,MAAM,IAAI,UAAU,4GAA4GX,CAAG,EAAE,CAE7I,SACSE,IAAsB,MAC3B,MAAM,IAAI,UAAU,8EAA8E,EAEtGK,GAAeN,EAAKD,CAAG,EACvBM,EAAU,CACN,QAASM,GAAU,sBACnB,WAAYA,GAAU,sBAC1B,EACA,MACJ,IAAK,QACL,IAAK,SACL,IAAK,QACL,IAAK,QAAS,CACV,GAAIV,IAAsB,KACtB,MAAM,IAAI,UAAU,kEAAkE,EAE1F,IAAMW,EAASC,GAAcb,CAAG,EAC1Bc,EAAWjB,GAAc,IAAIE,CAAG,EACtC,GAAIa,IAAWE,EACX,MAAM,IAAI,UAAU,0DAA0DA,CAAQ,SAASF,CAAM,EAAE,EAE3GP,EAAU,CAAE,YAAa,YAAa,EACtC,KACJ,CACA,QACI,MAAM,IAAIU,EAAiB,OAAOhB,CAAG,6DAA6D,CAC1G,CACA,OAAII,EACO,CAAE,OAAQ,MAAO,IAAAH,EAAK,GAAGK,CAAQ,EAErCA,EAAU,CAAE,GAAGA,EAAS,IAAAL,CAAI,EAAIA,CAC3C,CC3GA,UAAYgB,OAAY,cACxB,OAAS,aAAAC,OAAiB,YCAX,SAARC,GAA4BC,EAAK,CACpC,OAAQA,EAAK,CACT,IAAK,QACD,MAAO,SACX,IAAK,QACD,MAAO,SACX,IAAK,QACD,MAAO,SACX,QACI,MAAM,IAAIC,EAAiB,OAAOD,CAAG,6DAA6D,CAC1G,CACJ,CCZA,OAAS,aAAAE,GAAW,mBAAAC,OAAuB,cAM5B,SAARC,GAAkCC,EAAKC,EAAKC,EAAO,CACtD,GAAID,aAAe,WAAY,CAC3B,GAAI,CAACD,EAAI,WAAW,IAAI,EACpB,MAAM,IAAI,UAAUG,GAAgBF,EAAK,GAAGG,CAAK,CAAC,EAEtD,OAAOC,GAAgBJ,CAAG,CAC9B,CACA,GAAIA,aAAeK,GACf,OAAOL,EAEX,GAAIM,GAAYN,CAAG,EACf,OAAAO,GAAkBP,EAAKD,EAAKE,CAAK,EAC1BI,GAAU,KAAKL,CAAG,EAE7B,GAAQQ,EAAMR,CAAG,EACb,OAAID,EAAI,WAAW,IAAI,EACZK,GAAgB,OAAO,KAAKJ,EAAI,EAAG,WAAW,CAAC,EAEnDA,EAEX,MAAM,IAAI,UAAUE,GAAgBF,EAAK,GAAGG,EAAO,aAAc,cAAc,CAAC,CACpF,CFrBA,IAAMM,GAAcC,GAAiB,OAAI,EACnCC,GAAO,MAAOC,EAAKC,EAAKC,IAAS,CACnC,IAAMC,EAAIC,GAAWJ,EAAKC,EAAK,MAAM,EACrC,GAAID,EAAI,WAAW,IAAI,EAAG,CACtB,IAAMK,EAAc,cAAWC,GAAWN,CAAG,EAAGG,CAAC,EACjD,OAAAE,EAAK,OAAOH,CAAI,EACTG,EAAK,OAAO,CACvB,CACA,OAAOR,GAAYU,GAAWP,CAAG,EAAGE,EAAMM,GAAQR,EAAKG,CAAC,CAAC,CAC7D,EACOM,GAAQV,GGhBf,IAAOW,EAASC,GAAS,KAAK,MAAMA,EAAK,QAAQ,EAAI,GAAI,ECKzD,IAAMC,GAAQ,oIACPC,GAASC,GAAQ,CACpB,IAAMC,EAAUH,GAAM,KAAKE,CAAG,EAC9B,GAAI,CAACC,GAAYA,EAAQ,CAAC,GAAKA,EAAQ,CAAC,EACpC,MAAM,IAAI,UAAU,4BAA4B,EAEpD,IAAMC,EAAQ,WAAWD,EAAQ,CAAC,CAAC,EAC7BE,EAAOF,EAAQ,CAAC,EAAE,YAAY,EAChCG,EACJ,OAAQD,EAAM,CACV,IAAK,MACL,IAAK,OACL,IAAK,SACL,IAAK,UACL,IAAK,IACDC,EAAc,KAAK,MAAMF,CAAK,EAC9B,MACJ,IAAK,SACL,IAAK,UACL,IAAK,MACL,IAAK,OACL,IAAK,IACDE,EAAc,KAAK,MAAMF,EAAQ,EAAM,EACvC,MACJ,IAAK,OACL,IAAK,QACL,IAAK,KACL,IAAK,MACL,IAAK,IACDE,EAAc,KAAK,MAAMF,EAAQ,IAAI,EACrC,MACJ,IAAK,MACL,IAAK,OACL,IAAK,IACDE,EAAc,KAAK,MAAMF,EAAQ,KAAG,EACpC,MACJ,IAAK,OACL,IAAK,QACL,IAAK,IACDE,EAAc,KAAK,MAAMF,EAAQ,MAAI,EACrC,MACJ,QACIE,EAAc,KAAK,MAAMF,EAAQ,QAAI,EACrC,KACR,CACA,OAAID,EAAQ,CAAC,IAAM,KAAOA,EAAQ,CAAC,IAAM,MAC9B,CAACG,EAELA,CACX,EC/CO,IAAMC,GAAN,KAAoB,CAIvB,YAAYC,EAAS,CAHrBC,EAAA,iBACAA,EAAA,yBACAA,EAAA,2BAEI,GAAI,EAAED,aAAmB,YACrB,MAAM,IAAI,UAAU,2CAA2C,EAEnE,KAAK,SAAWA,CACpB,CACA,mBAAmBE,EAAiB,CAChC,GAAI,KAAK,iBACL,MAAM,IAAI,UAAU,4CAA4C,EAEpE,YAAK,iBAAmBA,EACjB,IACX,CACA,qBAAqBC,EAAmB,CACpC,GAAI,KAAK,mBACL,MAAM,IAAI,UAAU,8CAA8C,EAEtE,YAAK,mBAAqBA,EACnB,IACX,CACA,MAAM,KAAKC,EAAKC,EAAS,CACrB,GAAI,CAAC,KAAK,kBAAoB,CAAC,KAAK,mBAChC,MAAM,IAAIC,EAAW,iFAAiF,EAE1G,GAAI,CAACC,GAAW,KAAK,iBAAkB,KAAK,kBAAkB,EAC1D,MAAM,IAAID,EAAW,2EAA2E,EAEpG,IAAME,EAAa,CACf,GAAG,KAAK,iBACR,GAAG,KAAK,kBACZ,EACMC,EAAaC,GAAaJ,EAAY,IAAI,IAAI,CAAC,CAAC,MAAO,EAAI,CAAC,CAAC,EAAGD,GAAS,KAAM,KAAK,iBAAkBG,CAAU,EAClHG,EAAM,GACV,GAAIF,EAAW,IAAI,KAAK,IACpBE,EAAM,KAAK,iBAAiB,IACxB,OAAOA,GAAQ,WACf,MAAM,IAAIL,EAAW,yEAAyE,EAGtG,GAAM,CAAE,IAAAM,CAAI,EAAIJ,EAChB,GAAI,OAAOI,GAAQ,UAAY,CAACA,EAC5B,MAAM,IAAIN,EAAW,2DAA2D,EAEpFO,GAAoBD,EAAKR,EAAK,MAAM,EACpC,IAAIJ,EAAU,KAAK,SACfW,IACAX,EAAUc,EAAQ,OAAOC,GAAUf,CAAO,CAAC,GAE/C,IAAIE,EACA,KAAK,iBACLA,EAAkBY,EAAQ,OAAOC,GAAU,KAAK,UAAU,KAAK,gBAAgB,CAAC,CAAC,EAGjFb,EAAkBY,EAAQ,OAAO,EAAE,EAEvC,IAAME,EAAOC,GAAOf,EAAiBY,EAAQ,OAAO,GAAG,EAAGd,CAAO,EAC3DkB,EAAY,MAAMC,GAAKP,EAAKR,EAAKY,CAAI,EACrCI,EAAM,CACR,UAAWL,GAAUG,CAAS,EAC9B,QAAS,EACb,EACA,OAAIP,IACAS,EAAI,QAAUC,GAAQ,OAAOrB,CAAO,GAEpC,KAAK,qBACLoB,EAAI,OAAS,KAAK,oBAElB,KAAK,mBACLA,EAAI,UAAYC,GAAQ,OAAOnB,CAAe,GAE3CkB,CACX,CACJ,EClFO,IAAME,GAAN,KAAkB,CAErB,YAAYC,EAAS,CADrBC,EAAA,mBAEI,KAAK,WAAa,IAAIC,GAAcF,CAAO,CAC/C,CACA,mBAAmBG,EAAiB,CAChC,YAAK,WAAW,mBAAmBA,CAAe,EAC3C,IACX,CACA,MAAM,KAAKC,EAAKC,EAAS,CACrB,IAAMC,EAAM,MAAM,KAAK,WAAW,KAAKF,EAAKC,CAAO,EACnD,GAAIC,EAAI,UAAY,OAChB,MAAM,IAAI,UAAU,2DAA2D,EAEnF,MAAO,GAAGA,EAAI,SAAS,IAAIA,EAAI,OAAO,IAAIA,EAAI,SAAS,EAC3D,CACJ,ECdA,SAASC,EAAcC,EAAOC,EAAO,CACjC,GAAI,CAAC,OAAO,SAASA,CAAK,EACtB,MAAM,IAAI,UAAU,WAAWD,CAAK,QAAQ,EAEhD,OAAOC,CACX,CACO,IAAMC,GAAN,KAAiB,CAEpB,YAAYC,EAAU,CAAC,EAAG,CAD1BC,EAAA,iBAEI,GAAI,CAACC,GAASF,CAAO,EACjB,MAAM,IAAI,UAAU,kCAAkC,EAE1D,KAAK,SAAWA,CACpB,CACA,UAAUG,EAAQ,CACd,YAAK,SAAW,CAAE,GAAG,KAAK,SAAU,IAAKA,CAAO,EACzC,IACX,CACA,WAAWC,EAAS,CAChB,YAAK,SAAW,CAAE,GAAG,KAAK,SAAU,IAAKA,CAAQ,EAC1C,IACX,CACA,YAAYC,EAAU,CAClB,YAAK,SAAW,CAAE,GAAG,KAAK,SAAU,IAAKA,CAAS,EAC3C,IACX,CACA,OAAOC,EAAO,CACV,YAAK,SAAW,CAAE,GAAG,KAAK,SAAU,IAAKA,CAAM,EACxC,IACX,CACA,aAAaR,EAAO,CAChB,OAAI,OAAOA,GAAU,SACjB,KAAK,SAAW,CAAE,GAAG,KAAK,SAAU,IAAKF,EAAc,eAAgBE,CAAK,CAAE,EAEzEA,aAAiB,KACtB,KAAK,SAAW,CAAE,GAAG,KAAK,SAAU,IAAKF,EAAc,eAAgBW,EAAMT,CAAK,CAAC,CAAE,EAGrF,KAAK,SAAW,CAAE,GAAG,KAAK,SAAU,IAAKS,EAAM,IAAI,IAAM,EAAIC,GAAKV,CAAK,CAAE,EAEtE,IACX,CACA,kBAAkBA,EAAO,CACrB,OAAI,OAAOA,GAAU,SACjB,KAAK,SAAW,CAAE,GAAG,KAAK,SAAU,IAAKF,EAAc,oBAAqBE,CAAK,CAAE,EAE9EA,aAAiB,KACtB,KAAK,SAAW,CAAE,GAAG,KAAK,SAAU,IAAKF,EAAc,oBAAqBW,EAAMT,CAAK,CAAC,CAAE,EAG1F,KAAK,SAAW,CAAE,GAAG,KAAK,SAAU,IAAKS,EAAM,IAAI,IAAM,EAAIC,GAAKV,CAAK,CAAE,EAEtE,IACX,CACA,YAAYA,EAAO,CACf,OAAI,OAAOA,EAAU,IACjB,KAAK,SAAW,CAAE,GAAG,KAAK,SAAU,IAAKS,EAAM,IAAI,IAAM,CAAE,EAEtDT,aAAiB,KACtB,KAAK,SAAW,CAAE,GAAG,KAAK,SAAU,IAAKF,EAAc,cAAeW,EAAMT,CAAK,CAAC,CAAE,EAE/E,OAAOA,GAAU,SACtB,KAAK,SAAW,CACZ,GAAG,KAAK,SACR,IAAKF,EAAc,cAAeW,EAAM,IAAI,IAAM,EAAIC,GAAKV,CAAK,CAAC,CACrE,EAGA,KAAK,SAAW,CAAE,GAAG,KAAK,SAAU,IAAKF,EAAc,cAAeE,CAAK,CAAE,EAE1E,IACX,CACJ,ECvEO,IAAMW,GAAN,cAAsBC,EAAW,CAAjC,kCACHC,EAAA,yBACA,mBAAmBC,EAAiB,CAChC,YAAK,iBAAmBA,EACjB,IACX,CACA,MAAM,KAAKC,EAAKC,EAAS,CACrB,IAAMC,EAAM,IAAIC,GAAYC,EAAQ,OAAO,KAAK,UAAU,KAAK,QAAQ,CAAC,CAAC,EAEzE,GADAF,EAAI,mBAAmB,KAAK,gBAAgB,EACxC,MAAM,QAAQ,KAAK,kBAAkB,IAAI,GACzC,KAAK,iBAAiB,KAAK,SAAS,KAAK,GACzC,KAAK,iBAAiB,MAAQ,GAC9B,MAAM,IAAIG,GAAW,qCAAqC,EAE9D,OAAOH,EAAI,KAAKF,EAAKC,CAAO,CAChC,CACJ,E1BhBA,OAAS,cAAAK,GAAY,oBAAAC,GAAkB,eAAAC,OAAmB,cAC1D,OAAS,cAAAC,GAAY,gBAAAC,GAAc,iBAAAC,OAAqB,UACxD,OAAS,YAAAC,GAAU,WAAAC,GAAS,WAAAC,OAAe,YAC3C,OAAS,aAAAC,OAAiB,kBAC1B,OAAS,WAAAC,OAAe,MAqCjB,SAASC,EAAYC,EAAsB,CAChD,QAAQ,IAAI,KAAK,UAAUA,EAAO,KAAM,CAAC,CAAC,CAC5C,CAEA,eAAsBC,GAAQC,EAAwBC,EAA6BC,EAAyB,CAC1G,IAAMC,EAAWF,EAAU,OACrBG,EAAOC,GAAiBF,CAAQ,EACtC,GAAI,CAACC,EACH,OAGF,QAAQ,IAAI,uBAAuB,EACnC,IAAME,EAAa,MAAMN,EAAQ,iBAAiB,CAChD,KAAMI,EACN,SAAUG,GAASJ,CAAQ,EAC3B,YAAaK,GAAmBL,CAAQ,CAC1C,CAAC,EAED,QAAQ,IAAI,iBAAiB,EAC7B,IAAMM,EAAe,MAAMT,EAAQ,eAAe,CAChD,GAAGE,EACH,WAAAI,CACF,CAAC,EACD,QAAQ,IAAI,6BAA+BG,EAAa,MAAM,SAAS,CACzE,CAEA,eAAsBC,GAAUV,EAAwBC,EAA6BC,EAAiC,CACpH,IAAMC,EAAWF,EAAU,MAAQA,EAAU,OACvCG,EAAOC,GAAiBF,CAAQ,EACtC,GAAI,CAACC,EACH,OAGF,QAAQ,IAAI,kBAAkB,EAC9B,IAAMO,EAAgB,MAAMX,EAAQ,KAAKA,EAAQ,QAAQ,MAAOE,EAAI,GAAI,SAAS,EAAG,CAClF,KAAAE,EACA,SAAUG,GAASJ,CAAQ,CAC7B,CAAC,EACD,QAAQ,IAAI,kBAAoBQ,EAAa,QAAQ,CAAC,GAAG,SAAS,IAAI,CACxE,CAEA,eAAsBC,GACpBZ,EACAa,EACAC,EACAC,EACAC,EACAC,EACAC,EACe,CACf,IAAMC,EAAO,CACX,KAAMN,EACN,YAAa,GACb,eAAAI,CACF,EACMG,EAAS,MAAMpB,EAAQ,KAAK,kBAAoBc,EAAY,OAAQK,CAAI,EACxEjB,EAAM,MAAMF,EAAQ,aAAa,MAAOoB,EAAO,EAAE,EAEjDnB,EAAY,CAChB,KAAMY,EACN,GAAIO,EAAO,GACX,OAAQL,EACR,KAAMC,CACR,EAEA,MAAMjB,GAAQC,EAASC,EAA+BC,CAAG,EACzD,MAAMQ,GAAUV,EAASC,EAA+BC,CAAG,EAC3D,QAAQ,IAAI,yBAAyBA,EAAI,EAAE,EAAE,EAEzCgB,GACFG,GAAepB,CAAS,CAE5B,CAEO,SAASqB,GAAeT,EAAqC,CAClE,IAAMU,EAAe,IAAI,OAAO,IAAMC,GAAYX,CAAO,EAAE,QAAQ,QAAS,IAAI,EAAI,GAAG,EACjFY,EAAaC,EAAW,GAAG,MAAM,OAAQC,GAAMJ,EAAa,KAAKI,EAAE,IAAI,CAAC,EAC9E,OAAKF,GACI,CAAC,CAGZ,CAQO,SAASG,EAAkBC,EAAkBC,EAAuC,CACzF,GAAIA,GAAS,KACX,OAAOA,EAAQ,KAEjB,IAAMC,EAAQ,CAAC,SAAS,EACxB,OAAIF,GACFE,EAAM,KAAKF,CAAO,EAEpBE,EAAM,KAAK,QAAQ,EACfD,GAAS,QACXC,EAAM,KAAK,QAAQ,EAErBA,EAAM,KAAK,MAAM,EACVA,EAAM,KAAK,GAAG,CACvB,CAOO,SAASb,EAAYc,EAAwBC,EAAmC,CACrFC,GAAcC,GAAQH,CAAc,EAAG,KAAK,UAAUC,EAAQ,OAAW,CAAC,EAAG,OAAO,CACtF,CAEO,SAASP,EAAWG,EAAkBC,EAAwD,CACnG,IAAMM,EAAWR,EAAkBC,EAASC,CAAO,EAC7CO,EAAUhC,GAAiB+B,CAAQ,EACzC,GAAKC,EAGL,OAAO,KAAK,MAAMA,CAAO,CAC3B,CAEO,SAASC,GAAiBT,EAA+D,CAC9F,IAAMQ,EAAUhC,GAAiBuB,EAAkBC,EAAS,CAAE,OAAQ,EAAK,CAAC,CAAC,EAC7E,GAAKQ,EAGL,OAAO,KAAK,MAAMA,CAAO,CAC3B,CAEA,SAAShC,GAAiB+B,EAA0B,CAClD,IAAMG,EAAOJ,GAAQC,CAAQ,EAC7B,OAAKI,GAAWD,CAAI,EAGbE,GAAaF,EAAM,MAAM,EAFvB,EAGX,CAEA,SAASlB,GAAepB,EAAmC,CACzD,IAAMgC,EAASP,EAAW,GAAK,CAAC,EAC3BO,EAAO,OACVA,EAAO,KAAO,CAAC,GAEjBA,EAAO,KAAK,KAAKhC,CAAS,EAC1BiC,GAAc,sBAAuB,KAAK,UAAUD,EAAQ,KAAM,CAAC,EAAG,MAAM,EAC5E,QAAQ,IAAI,wBAAwBhC,EAAU,EAAE,EAAE,CACpD,CAEA,SAASuB,GAAYkB,EAAqB,CACxC,OAAOA,EAAI,QAAQ,yBAA0B,MAAM,CACrD,CAWO,SAASC,GAAiBC,EAA+C,CAI9E,IAAIC,EAAY,EACZC,EAAY,EAEhB,OAAOC,GAAQ,CACb,IAAKH,EACL,OAAQ,CAACI,EAAOC,IAAU,CAExB,GADAJ,IACIA,EAAY,IACd,MAAM,IAAI,MAAM,2CAA2C,EAI7D,GADAC,GAAaG,EAAM,KACfH,EAAY,SACd,MAAM,IAAI,MAAM,gCAAgC,EAGlD,MAAO,EACT,CACF,CAAC,CACH,CAEO,SAASI,IAAqC,CACnD,MAAO,CACL,IAAK,6DACL,UAAW,aACb,CACF,CAEO,SAAS1C,GAAmB2C,EAA0B,CAC3D,IAAMC,EAAMC,GAAQF,CAAQ,EAAE,YAAY,EAC1C,MAAI,CAAC,OAAQ,OAAQ,KAAK,EAAE,SAASC,CAAG,EAC/BE,GAAY,WAEjB,CAAC,OAAQ,OAAQ,KAAK,EAAE,SAASF,CAAG,EAC/BE,GAAY,WAEdA,GAAY,IACrB,CAEO,SAASC,GAAYC,EAAqB1B,EAA2B,CAC1E,IAAM2B,EAAU,IAAIC,EAAkBF,CAAW,EAC3CG,EAAgB,CAAE,KAAMH,EAAa,GAAG1B,CAAQ,EACtD,OAAA2B,EAAQ,UAAU,UAAWE,CAAa,EACnCA,CACT,CAEO,SAASC,GAAYJ,EAA8B,CAExD,OADgB,IAAIE,EAAkBF,CAAW,EAClC,UAAU,SAAS,CACpC,CAaA,eAAsBK,GAAeC,EAAwBC,EAAiC,CAC5F,IAAMC,EAAS,CACb,IAAK,MACL,IAAK,OACP,EAEMC,EAAmB,KAAK,MAAM,KAAK,IAAI,EAAI,GAAI,EAC/CC,EAAO,CACX,IAAK,GAAGH,EAAQ,OAAO,GAAGA,EAAQ,QAAQ,GAC1C,IAAKA,EAAQ,OACb,IAAKA,EAAQ,QACb,IAAKE,EACL,IAAKA,EACL,IAAKA,EAAmB,MAC1B,EACME,EAAgBC,GAAa,KAAK,UAAUJ,CAAM,CAAC,EACnDK,EAAcD,GAAa,KAAK,UAAUF,CAAI,CAAC,EAC/CI,EAAQ,GAAGH,CAAa,IAAIE,CAAW,GACvCE,EAAYC,GAAW,SAAUT,EAAQ,YAAsB,EAClE,OAAOO,CAAK,EACZ,OAAO,WAAW,EACfG,EAAc,GAAGH,CAAK,IAAIC,CAAS,GACzC,MAAMT,EAAQ,oBAAoBC,EAAQ,SAAoBU,EAAaV,EAAQ,OAAS,EAAE,CAChG,CAEA,eAAsBW,GAAkBZ,EAAwBC,EAAiC,CAC/F,IAAMY,EAAaC,GAAiBC,GAAaC,GAAQf,EAAQ,cAAwB,CAAC,CAAC,EACrFgB,EAAM,MAAM,IAAIC,GAAQ,CAAC,CAAC,EAC7B,mBAAmB,CAAE,IAAK,QAAS,IAAK,KAAM,CAAC,EAC/C,UAAUjB,EAAQ,QAAkB,EACpC,WAAWA,EAAQ,QAAkB,EACrC,YAAY,GAAGA,EAAQ,OAAO,GAAGA,EAAQ,QAAQ,EAAE,EACnD,OAAOkB,GAAY,EAAE,EAAE,SAAS,KAAK,CAAC,EACtC,YAAY,EACZ,kBAAkB,IAAI,EACtB,KAAKN,CAAU,EAClB,MAAMb,EAAQ,uBAAuBiB,CAAG,CAC1C,CAWO,SAASG,EAAcC,EAAkBC,EAA2B,CACzEA,EAAW,cAAc,CAAE,kBAAmB,EAAK,CAAC,EACpDD,EAAQ,WAAWC,CAAU,CAC/B,CAEO,IAAMC,EAAN,cAA6BC,EAAQ,CAC1C,OAAOC,EAAoD,CAGzD,IAAMC,EAAYC,GAAkB,KAAMF,CAAE,EAI5C,aAAM,eAAiBC,EAChB,IACT,CAUA,qBAA4B,CAE1B,KAAK,cAAgB,CAAC,EACtB,QAAWE,KAAU,KAAK,QAGpBA,EAAO,eAAiB,SAG1B,KAAK,cAAcA,EAAO,cAAc,CAAC,EAAIA,EAAO,aAG1D,CACF,EAEO,SAASD,GACdN,EACAI,EACgC,CAEhC,MAAO,OAAOI,GAA+B,CAC3C,IAAMC,EAAoBT,EAAQ,oBAAoB,OAChDU,EAAaF,EAAK,MAAM,EAAGC,CAAiB,EAClDC,EAAWD,CAAiB,EAAIT,EAAQ,gBAAgB,EACxD,GAAI,CACF,IAAMW,EAA+BP,EAAG,GAAGM,CAAU,EACjDE,GAAUD,CAAM,GAClB,MAAMA,CAEV,QAAE,CAGAX,EAAQ,oBAAoB,CAC9B,CACF,CACF,CHhVA,IAAMa,GAAqB,IAAIC,EAAe,QAAQ,EAAE,QAAQ,CAAC,OAAQ,OAAQ,IAAI,CAAC,EAChFC,GAAmB,IAAID,EAAe,MAAM,EAC5CE,GAAmB,IAAIF,EAAe,MAAM,EAC5CG,GAA2B,IAAIH,EAAe,eAAe,EAC7DI,GAAsB,IAAIJ,EAAe,SAAS,EAE3CK,EAAQ,IAAIL,EAAe,OAAO,EAC/CM,EAAcD,EAAON,EAAkB,EACvCO,EAAcD,EAAOJ,EAAgB,EACrCK,EAAcD,EAAOH,EAAgB,EACrCI,EAAcD,EAAOF,EAAwB,EAC7CG,EAAcD,EAAOD,EAAmB,EAExCL,GACG,YAAY,qCAAqC,EACjD,SAAS,gBAAiB,gDAAgD,EAC1E,OACC,wBACA,uHACF,EACC,UACC,IAAIQ,GAAO,oBAAqB,8CAA8C,EAC3E,QAAQ,CAAC,QAAS,MAAM,CAAC,EACzB,QAAQ,OAAO,CACpB,EACC,OAAO,MAAOC,EAAUC,IAAY,CACnC,MAAMC,GAAuB,CAC3B,UAAW,eACX,SAAAF,EACA,QAAAC,EACA,wBAA0BE,GAA8C,CACtE,IAAMC,EAAcC,GAAqBF,EAAS,OAAQ,CACxD,SAAU,CAAC,SAAU,SAAS,EAC9B,SAAU,CAAC,aAAa,CAC1B,CAAC,EAED,MAAO,CACL,GAAIA,EAAS,MAAM,GACnB,KAAMA,EAAS,MAAM,KACrB,cAAeA,EAAS,MAAM,OAC9B,QAASC,EAAY,QACrB,iBAAkBA,EAAY,OAC9B,kBAAmBA,EAAY,aAAe,KAChD,CACF,CACF,CAAC,CACH,CAAC,EAEHX,GACG,YAAY,oCAAoC,EAChD,SAAS,eAAgB,yCAAyC,EAClE,SACC,YACA,2GACF,EACC,OAAO,kBAAmB,gEAAiE,GAAG,EAC9F,OACC,wBACA,2GACF,EACC,OAAO,MAAOa,EAAYC,EAASN,IAAY,CAC9C,IAAMO,EAAU,MAAMC,EAAoBR,CAAO,EAC3CS,EAAW,MAAMC,GAAsBH,EAASD,EAASN,CAAO,EAEhEW,EAAQ,OAAO,SAASX,EAAQ,MAAO,EAAE,EAC/C,GAAI,OAAO,MAAMW,CAAK,EACpB,MAAM,IAAI,MAAM,iDAAiD,EAGnE,GAAI,CACF,IAAMC,EAAc,MAAML,EAAQ,YAAYE,EAAUJ,EAAY,QAAQM,CAAK,GAAIE,GAAY,KAAM,GAAM,CAC3G,WAAY,CACd,CAAC,EACD,QAAQ,KAAKD,CAAU,CACzB,OAASE,EAAK,CACZ,MAAM,IAAI,MAAM,+CAAgD,CAAE,MAAOA,CAAI,CAAC,CAChF,CACF,CAAC,EAEHrB,GACG,YAAY,yDAAyD,EACrE,SAAS,aAAc,6CAA6C,EACpE,SAAS,YAAa,+CAA+C,EACrE,SACC,YACA,uHACF,EACC,OAAO,+BAAgC,kCAAmCoB,GAAY,MAAM,EAC5F,OAAO,YAAa,yEAAyE,EAC7F,OACC,wBACA,2GACF,EACC,OAAO,MAAOE,EAAUC,EAASV,EAASN,IAAY,CACrD,IAAMO,EAAU,MAAMC,EAAoBR,CAAO,EAC3CS,EAAW,MAAMC,GAAsBH,EAASD,EAASN,CAAO,EAElEiB,EACJ,GAAI,CACFA,EAAc,MAAMV,EAAQ,YAC1BE,EACA,CAAE,UAAW,UAAUM,CAAQ,EAAG,EAClCC,EACAhB,EAAQ,YACRA,EAAQ,OAAS,GACjB,CAAE,WAAY,CAAE,CAClB,CACF,OAASc,EAAK,CACZ,MAAM,IAAI,MAAM,gEAAiE,CAAE,MAAOA,CAAI,CAAC,CACjG,CAEA,QAAQ,KAAKG,CAAU,CACzB,CAAC,EAEHvB,GACG,YAAY,8CAA8C,EAC1D,SACC,gBACA,uHACF,EACC,OACC,wBACA,gJACF,EACC,UACC,IAAII,GAAO,oBAAqB,8CAA8C,EAC3E,QAAQ,CAAC,QAAS,MAAM,CAAC,EACzB,QAAQ,OAAO,CACpB,EACC,OAAO,MAAOC,EAAUC,IAAY,CACnC,MAAMC,GAAuB,CAC3B,UAAW,iBACX,SAAAF,EACA,QAAAC,EACA,wBAA0BE,IACjB,CACL,GAAIA,EAAS,MAAM,GACnB,KAAMA,EAAS,MAAM,IACvB,EAEJ,CAAC,CACH,CAAC,EAEHP,GACG,YAAY,gDAAgD,EAC5D,SACC,gBACA,uGACF,EACC,OACC,wBACA,gHACF,EACC,OACC,sBACA,wFACF,EACC,UACC,IAAIG,GAAO,oBAAqB,8CAA8C,EAC3E,QAAQ,CAAC,QAAS,MAAM,CAAC,EACzB,QAAQ,OAAO,CACpB,EACC,OAAO,MAAOC,EAAUC,IAAY,CACnC,MAAMC,GAAuB,CAC3B,UAAW,WACX,SAAAF,EACA,QAAAC,EACA,wBAA0BE,IACjB,CACL,GAAIA,EAAS,MAAM,GACnB,KAAMA,EAAS,MAAM,KACrB,QAASF,EAAQ,SAAW,QAC9B,EAEJ,CAAC,CACH,CAAC,EAEH,eAAsBC,GAGpB,CAAE,UAAAiB,EAAW,SAAAnB,EAAU,QAAAC,EAAS,wBAAAmB,CAAwB,EAAoD,CAC5G,IAAMC,EAAaC,GAAyBtB,EAAUC,CAAO,EACvDO,EAAU,MAAMC,EAAoBR,CAAO,EAC3CsB,EAAeF,EAAW,OAAS,WAAaA,EAAW,SAAW,aAAaA,EAAW,IAAI,KAAK,GAAG,CAAC,GAC3GG,EAAe,IAAI,gBAAgBD,EAAa,MAAM,GAAG,EAAE,CAAC,CAAC,EAE/DE,EACJ,GAAI,CACF,IAAMC,EAAMlB,EAAQ,QAAQ,QAASW,CAAS,EAC9CO,EAAI,OAASF,EAAa,SAAS,EACnCC,EAAS,MAAMjB,EAAQ,IAAIkB,EAAK,CAC9B,MAAO,QACT,CAAC,CACH,OAASX,EAAK,CACZ,MAAM,IAAI,MAAM,cAAcI,CAAS,WAAY,CAAE,MAAOJ,CAAI,CAAC,CACnE,CAEA,GAAId,EAAQ,SAAW,OAAQ,CAC7B,QAAQ,KAAK,KAAK,UAAUwB,EAAQ,KAAM,CAAC,CAAC,EAC5C,MACF,CAEA,IAAME,EAAsB,CAAC,EACvBC,EAAkB,CAAC,EAEzB,OAAQH,EAAO,aAAc,CAC3B,IAAK,SAAU,CACb,IAAMI,EAAYC,GAAuBL,CAAM,EAC/C,QAAWtB,KAAY0B,EACjB1B,EAAS,OAAO,eAAiB,cAAgB4B,GAAK5B,EAAS,MAAM,EACvEwB,EAAoB,KAAKxB,CAAkC,EAE3DyB,EAAgB,KAAKzB,CAAiD,EAG1E,KACF,CACA,IAAK,aACL,IAAK,mBAAoB,CACvB,IAAMN,EAAQ,MAAMW,EAAQ,UAAU,QAASgB,EAAc,CAAE,MAAO,QAAS,CAAC,EAChF,GAAI,CAAC3B,EACH,MAAM,IAAI,MAAM,iBAAiB,EAE/B4B,EAAO,eAAiB,aAC1BE,EAAoB,KAAK,CAAE,MAAA9B,EAAO,OAAA4B,CAAO,CAA2B,EAEpEG,EAAgB,KAAK,CAAE,MAAA/B,EAAO,OAAA4B,CAAO,CAAC,EAExC,KACF,CACA,QACE,MAAM,IAAI,MAAM,gCAAgCN,CAAS,gBAAgB,KAAK,UAAUM,CAAM,CAAC,EAAE,CACrG,CAEA,IAAMO,EAAiB,CAAC,EACxB,QAAW7B,KAAYwB,EAAqB,CAC1C,IAAMM,EAAMb,EAAwBjB,CAAQ,EAC5C6B,EAAe,KAAKC,CAAG,CACzB,CAEA,IAAMC,EAAa,CAAC,EACpB,QAAW/B,KAAYyB,EAAiB,CAEtC,IAAMO,GADUhC,EAAS,OACH,QAAQ,CAAC,EACzB8B,GAAM,CACV,GAAI9B,EAAS,MAAM,GACnB,KAAMA,EAAS,MAAM,KACrB,SAAUgC,GAAM,SAChB,KAAMA,GAAM,KACZ,QAASA,GAAM,SAAS,MAAQ,oBAClC,EACAD,EAAW,KAAKD,EAAG,CACrB,CAEA,QAAQ,KAAK;AAAA,EAAKD,EAAe,MAAM;AAAA,CAA4B,EACnE,QAAQ,MAAMA,EAAe,OAASA,EAAiB,kCAAkC,EACzF,QAAQ,KAAK,EAETE,EAAW,SACb,QAAQ,KAAK,GAAGA,EAAW,MAAM,sBAAsB,EACvD,QAAQ,KAAK,EACb,QAAQ,MAAMA,CAAU,EAE5B,CAEA,eAAsBvB,GACpBH,EACAD,EACAN,EAC2B,CAC3B,GAAI,EAAEM,GAAWN,EAAQ,UACvB,MAAM,IAAI,MAAM,2EAA2E,EAE7F,GAAIM,GAAWN,EAAQ,SACrB,MAAM,IAAI,MACR,kHACF,EAGF,IAAImC,EACJ,GAAI7B,EACF6B,EAAS7B,MACJ,CACL8B,GAAyBpC,EAAQ,QAAQ,EACzC,IAAMwB,EAAS,MAAMjB,EAAQ,OAAO,QAAS,GAAGP,EAAQ,SAAS,MAAM,GAAG,EAAE,CAAC,CAAC,WAAW,EACzF,GAAI,CAACwB,GAAQ,OAAO,OAClB,MAAM,IAAI,MAAM,wDAAwD,EAE1E,GAAIA,EAAO,MAAM,SAAW,EAC1B,MAAM,IAAI,MACR,wHACF,EAEFW,EAASX,EAAO,MAAM,CAAC,EAAE,UAAU,EACrC,CAEA,MAAO,CAAE,UAAW,SAASW,CAAM,EAAG,CACxC,CAEO,SAASN,GAAuBQ,EAAmD,CACxF,IAAMT,EAAY,CAAC,EACnB,QAAWU,KAASD,EAAO,OAAS,CAAC,EAAG,CACtC,GAAI,CAACC,EAAM,SACT,MAAM,IAAI,MAAM,sCAAsC,EAExDV,EAAU,KAAKW,GAA2BD,EAAM,QAAQ,CAAC,CAC3D,CACA,OAAOV,CACT,CAEO,SAASW,GAA2BC,EAAyC,CAClF,IAAM5C,EAAQ4C,EAAO,WAAW,KAAMC,GAAMA,EAAE,OAAS,OAAO,GAAG,SACjE,GAAI,CAAC7C,EACH,MAAM,IAAI,MAAM,+CAA+C,EAEjE,GAAIA,EAAM,eAAiB,QACzB,MAAM,IAAI,MAAM,oDAAoDA,EAAM,YAAY,GAAG,EAE3F,IAAM4B,EAASgB,EAAO,WAAW,KAAMC,GAAMA,EAAE,OAAS,QAAQ,GAAG,SACnE,GAAI,CAACjB,EACH,MAAM,IAAI,MAAM,+CAA+C,EAEjE,GAAI,EAAEA,EAAO,eAAiB,cAAgBA,EAAO,eAAiB,oBACpE,MAAM,IAAI,MAAM,qDAAqDA,EAAO,YAAY,GAAG,EAE7F,MAAO,CAAE,MAAA5B,EAAO,OAAA4B,CAAO,CACzB,CAEO,SAASpB,GACdoC,EACAE,EAC2B,CAC3B,IAAMC,EAAM,CAAC,EACPC,EAAiBF,EAAW,SAC5BG,EAAiBH,EAAW,SAElC,QAAWI,KAAaF,EAAgB,CACtC,IAAMG,EAAcP,EAAO,WAAW,KAAMC,GAAMA,EAAE,OAASK,CAAS,EACtE,GAAI,CAACC,EACH,MAAM,IAAI,MAAM,6BAA6BD,CAAS,GAAG,EAE3D,IAAIE,EACJ,QAAWC,KAAQF,EAEjB,GAAIE,EAAK,WAAW,OAAO,EAAG,CAC5B,GAAID,EACF,MAAM,IAAI,MAAM,wCAAwCF,CAAS,GAAG,EAEtEE,EAAYC,CACd,CAEF,GAAI,CAACD,EACH,MAAM,IAAI,MAAM,yCAAyCF,CAAS,GAAG,EAIvEH,EAAIG,CAAS,EAAIC,EAAYC,CAAS,CACxC,CAEA,GAAIH,GAAgB,OAClB,QAAWC,KAAaD,EAAgB,CACtC,IAAME,EAAcP,EAAO,WAAW,KAAMC,GAAMA,EAAE,OAASK,CAAS,EACtE,GAAI,CAACC,EACH,SAEF,IAAMG,EAAQC,GAAoCL,EAAWC,CAAW,EAExEJ,EAAIG,CAAS,EAAII,CACnB,CAGF,OAAOP,CACT,CAEO,SAASQ,GAAoCL,EAAmBC,EAA0C,CAC/G,IAAIC,EACJ,QAAWC,KAAQF,EAEjB,GAAIE,EAAK,WAAW,OAAO,EAAG,CAC5B,GAAID,EACF,MAAM,IAAI,MAAM,wCAAwCF,CAAS,GAAG,EAEtEE,EAAYC,CACd,CAEF,GAAI,CAACD,EACH,MAAM,IAAI,MAAM,yCAAyCF,CAAS,GAAG,EAGvE,OAAOC,EAAYC,CAAS,CAC9B,CAEO,SAAS3B,GAAyBtB,EAAoBC,EAAmD,CAC9G,GAAI,CAAC,MAAM,QAAQD,CAAQ,EACzB,MAAM,IAAI,MAAM,yBAAyB,EAE3C,GAAIA,EAAS,OAAQ,CAEnB,GAAIC,EAAQ,SACV,MAAM,IAAI,MACR,sHACF,EAEF,QAAWoD,KAAMrD,EACf,GAAI,CAACsD,GAAOD,CAAE,EACZ,MAAM,IAAI,MAAM,UAAUA,CAAE,0BAA0B,EAG1D,MAAO,CAAE,KAAM,MAAO,IAAKrD,CAAS,CACtC,CACA,GAAIC,EAAQ,SACV,OAAAoC,GAAyBpC,EAAQ,QAAQ,EAClC,CAAE,KAAM,WAAY,SAAUA,EAAQ,QAAS,EAGxD,MAAM,IAAI,MAAM,wEAAwE,CAC1F,CAEA,SAASoC,GAAyBkB,EAAwB,CACxD,IAAMC,EACJ,+JACF,GAAI,OAAOD,GAAa,SACtB,MAAM,IAAI,MAAMC,CAAkB,EAEpC,GAAM,CAACC,EAAcC,CAAQ,EAAIH,EAAS,MAAM,GAAG,EACnD,GAAIE,IAAiB,SAAW,CAACC,EAC/B,MAAM,IAAI,MAAMF,CAAkB,EAEpC,GAAI,CAEF,IAAI,gBAAgBE,CAAQ,CAC9B,OAAS3C,EAAK,CACZ,MAAM,IAAI,MAAMyC,EAAoB,CAAE,MAAOzC,CAAI,CAAC,CACpD,CACA,GAAI,CAAC2C,EAAS,SAAS,GAAG,EACxB,MAAM,IAAI,MAAMF,EAAoB,CAAE,MAAO,IAAI,MAAM,qCAAqC,CAAE,CAAC,CAEnG,C8BleA,OACE,eAAAG,GACA,oBAAAC,GACA,yBAAAC,GAEA,wBAAAC,OACK,gBACP,OAAS,QAAAC,OAAY,qBACrB,OAAS,gBAAAC,OAAoB,YAC7B,OAAS,YAAAC,OAAgB,UAIzB,IAAMC,GAAWC,GACXC,GAAc,wBAEPC,GAAQ,IAAIC,EAAe,OAAO,EAClCC,GAAS,IAAID,EAAe,QAAQ,EACpCE,GAAQ,IAAIF,EAAe,OAAO,EAE/CD,GAAM,OAAO,MAAOI,GAAY,CAC9B,IAAMC,EAAcD,EAAQ,SAAW,UAGjCE,EAAUC,GAAYF,EAAaD,CAAO,EAE1CI,EAAU,MAAMC,EAAoBL,EAAS,EAAK,EACxD,MAAMM,GAAWF,EAASF,CAAO,CACnC,CAAC,EAEDJ,GAAO,OAAO,MAAOE,GAAY,CAC/B,IAAMI,EAAU,MAAMC,EAAoBL,CAAO,EACjDO,GAAQH,CAAO,CACjB,CAAC,EAEDL,GAAM,OAAO,MAAOC,GAAY,CAC9B,IAAMI,EAAU,MAAMC,EAAoBL,CAAO,EACjD,MAAMI,EAAQ,gBAAgB,EAC9B,IAAML,EAAQK,EAAQ,eAAe,EACrC,GAAI,CAACL,EACH,MAAM,IAAI,MAAM,eAAe,EAEjC,QAAQ,IAAIA,CAAK,CACnB,CAAC,EAED,eAAeO,GAAWF,EAAwBF,EAAiC,CAEjF,OADiBA,GAAS,UAAY,qBACpB,CAChB,IAAK,qBACH,MAAMM,GAA8BJ,CAAO,EAC3C,MACF,IAAK,QACHA,EAAQ,aAAaF,EAAQ,SAAoBA,EAAQ,YAAsB,EAC/E,MACF,IAAK,qBACHE,EAAQ,aAAaF,EAAQ,SAAoBA,EAAQ,YAAsB,EAC/E,MAAME,EAAQ,iBAAiBF,EAAQ,SAAoBA,EAAQ,YAAsB,EACzF,MACF,IAAK,aACH,MAAMO,GAAeL,EAASF,CAAO,EACrC,MACF,IAAK,gBACH,MAAMQ,GAAkBN,EAASF,CAAO,EACxC,KACJ,CACF,CAEA,eAAeS,GAAeP,EAAuC,CACnE,IAAMQ,EAASC,GAAa,MAAOC,EAAKC,IAAQ,CAC9C,IAAMC,EAAM,IAAI,IAAIF,EAAI,IAAe,uBAAuB,EACxDG,EAAOD,EAAI,aAAa,IAAI,MAAM,EACxC,GAAIF,EAAI,SAAW,UAAW,CAC5BC,EAAI,UAAU,IAAK,CACjB,MAAO,YACP,eAAgBG,GAAY,IAC9B,CAAC,EACDH,EAAI,IAAI,IAAI,EACZ,MACF,CACA,GAAIC,EAAI,WAAa,KAAOC,EAC1B,GAAI,CACF,IAAMf,EAAU,MAAME,EAAQ,YAAYa,EAAM,CAAE,SAAAxB,GAAU,YAAAE,EAAY,CAAC,EACzEoB,EAAI,UAAU,IAAK,CAAE,eAAgBG,GAAY,IAAK,CAAC,EACvDH,EAAI,IAAI,gBAAgBI,GAAiBjB,CAAO,CAAC,8BAA8B,CACjF,OAASkB,EAAK,CACZL,EAAI,UAAU,IAAK,CAAE,eAAgBG,GAAY,IAAK,CAAC,EACvDH,EAAI,IAAI,UAAUM,GAAqBD,CAAG,CAAC,EAAE,CAC/C,QAAE,CACAR,EAAO,MAAM,CACf,MAEAG,EAAI,UAAU,IAAK,CAAE,eAAgBG,GAAY,IAAK,CAAC,EACvDH,EAAI,IAAI,WAAW,CAEvB,CAAC,EAAE,OAAO,IAAI,CAChB,CAOA,eAAeO,GAAYN,EAA4B,CACrD,IAAMO,EAAKC,GAAS,EAChBC,EACJ,OAAQF,EAAI,CACV,IAAK,UACL,IAAK,QACHE,EAAM,aAAaT,CAAG,IACtB,MACF,IAAK,SACHS,EAAM,SAAST,CAAG,IAClB,MACF,IAAK,QACHS,EAAM,oBAAoBT,CAAG,IAC7B,MACF,QACE,MAAM,IAAI,MAAM,yBAA2BO,CAAE,CACjD,CACA,OAAO,IAAI,QAAQ,CAACG,EAASC,IAAW,CACtCC,GAAKH,EAAK,CAACI,EAAOC,EAAGC,IAAW,CAC9B,GAAIF,EAAO,CACTF,EAAOE,CAAK,EACZ,MACF,CACA,GAAIE,EAAQ,CACVJ,EAAO,IAAI,MAAM,2BAA6BI,CAAM,CAAC,EACrD,MACF,CACAL,EAAQ,CACV,CAAC,CACH,CAAC,CACH,CAMA,SAASnB,GAAQH,EAA8B,CAC7C,IAAM4B,EAAa5B,EAAQ,eAAe,EACtC4B,GACF,QAAQ,IAAI,YAAY5B,EAAQ,WAAW,CAAC,EAAE,EAC9C,QAAQ,IAAI,YAAY4B,EAAW,QAAQ,OAAO,KAAKA,EAAW,QAAQ,SAAS,GAAG,EACtF,QAAQ,IAAI,YAAYA,EAAW,QAAQ,OAAO,KAAKA,EAAW,QAAQ,SAAS,GAAG,GAEtF,QAAQ,IAAI,eAAe,CAE/B,CAEA,eAAexB,GAA8BJ,EAAuC,CAClF,MAAMO,GAAeP,CAAO,EAC5B,IAAM6B,EAAW,IAAI,IAAI7B,EAAQ,gBAAgB,CAAC,EAClD6B,EAAS,aAAa,IAAI,YAAaxC,EAAQ,EAC/CwC,EAAS,aAAa,IAAI,eAAgBtC,EAAW,EACrDsC,EAAS,aAAa,IAAI,QAAS,QAAQ,EAC3CA,EAAS,aAAa,IAAI,gBAAiB,MAAM,EACjDA,EAAS,aAAa,IAAI,SAAU,OAAO,EAC3C,MAAMX,GAAYW,EAAS,SAAS,CAAC,CACvC,CC7JA,IAAMC,GAAQ,UACRC,GAAO,UACPC,GAAM,WACNC,GAAQ,WACRC,GAAS,WACTC,GAAO,WAEAC,EAAQ,CACnB,IAAMC,GAAiB,GAAGL,EAAG,GAAGK,CAAI,GAAGP,EAAK,GAC5C,MAAQO,GAAiB,GAAGJ,EAAK,GAAGI,CAAI,GAAGP,EAAK,GAChD,OAASO,GAAiB,GAAGH,EAAM,GAAGG,CAAI,GAAGP,EAAK,GAClD,KAAOO,GAAiB,GAAGF,EAAI,GAAGE,CAAI,GAAGP,EAAK,GAC9C,KAAOO,GAAiB,GAAGN,EAAI,GAAGM,CAAI,GAAGP,EAAK,EAChD,EAGaQ,GAAsBC,GAC1BA,EAAK,QAAQ,iBAAkB,CAACC,EAAGH,IAASD,EAAM,KAAKC,CAAI,CAAC,ECDrE,IAAAI,GAAwB,SAjBxB,OACE,wBAAAC,GACA,iCAAAC,GACA,yBAAAC,GACA,sBAAAC,OAIK,iCACP,OAAS,oBAAAC,GAAkB,6BAAAC,OAAiC,6BAC5D,OAAS,aAAAC,OAAiB,sBAC1B,OAAS,YAAAC,OAAgB,qBACzB,OAAS,uBAAAC,GAAqB,uBAAAC,GAAqB,aAAAC,OAAiB,sBACpE,OAAS,4BAAAC,GAA0B,aAAAC,OAAiB,sBACpD,OAAS,wBAAAC,OAA4B,gBACrC,OAAOC,OAAW,aAClB,OAAS,eAAAC,OAAmB,UChB5B,OAAOC,OAAc,gBAErB,IAAIC,GAEG,SAASC,IAAqB,CACnCD,GAAWD,GAAS,gBAAgB,CAAE,MAAO,QAAQ,MAAO,OAAQ,QAAQ,MAAO,CAAC,CACtF,CAEO,SAASG,IAAsB,CACpCF,GAAS,MAAM,CACjB,CAMO,SAASG,EAAMC,EAAoB,CACxCJ,GAAS,MAAMI,EAAO;AAAA,CAAI,CAC5B,CAMO,SAASC,EAAOD,EAAoB,CACzCD,EAAM;AAAA,EAAOC,EAAO;AAAA,CAAI,CAC1B,CAQO,SAASE,EAAIF,EAAcG,EAAgC,GAAqB,CACrF,OAAO,IAAI,QAASC,GAAY,CAC9BR,GAAS,SAASI,GAAQG,EAAe,KAAOA,EAAe,IAAM,IAAM,IAAME,GAAmB,CAClGD,EAAQC,GAAUF,EAAa,SAAS,CAAC,CAC3C,CAAC,CACH,CAAC,CACH,CASA,eAAsBG,GAAON,EAAcO,EAA8BJ,EAAe,GAAqB,CAC3G,IAAMK,EAAMR,EAAO,KAAOO,EAAQ,IAAKE,GAAOA,IAAMN,EAAe,IAAMM,EAAI,IAAMA,CAAE,EAAE,KAAK,GAAG,EAAI,IAEnG,OAAa,CACX,IAAMJ,EAAU,MAAMH,EAAIM,CAAG,GAAML,EACnC,GAAII,EAAQ,SAASF,CAAM,EACzB,OAAOA,EAETN,EAAM,+CAAiDQ,EAAQ,KAAK,IAAI,CAAC,CAC3E,CACF,CASA,eAAsBG,GAAUV,EAAcO,EAAmBJ,EAAuC,CACtG,OAAO,SACL,MAAMG,GACJN,EACAO,EAAQ,IAAK,GAAM,EAAE,SAAS,CAAC,EAC/BJ,EAAa,SAAS,CACxB,EACA,EACF,CACF,CAOA,eAAsBQ,EAAQX,EAAgC,CAC5D,OAAQ,MAAMM,GAAON,EAAM,CAAC,IAAK,GAAG,CAAC,GAAG,YAAY,IAAM,GAC5D,CAMA,eAAsBY,GAAQZ,EAA6B,CACzD,GAAI,CAAE,MAAMW,EAAQX,CAAI,EACtB,MAAAD,EAAM,YAAY,EACZ,IAAI,MAAM,gBAAgB,CAEpC,CD/DO,IAAMc,GAAuB,IAAIC,GAAqB,CAAC,CAAC,EAClDC,GAAmB,IAAIC,GAAiB,CAAE,OAAQ,WAAY,CAAC,EAC/DC,GAAY,IAAIC,GAAU,CAAC,CAAC,EAC5BC,GAAW,IAAIC,GAAS,CAAC,CAAC,EAC1BC,GAAS,sBAMtB,eAAsBC,IAAkE,CACtF,IAAMC,EAAa,CAAC,EACdC,EAAYC,GAChB,CAAE,OAAQZ,EAAqB,EAC/B,CACE,kBAAmB,CACjB,kBACA,gBACA,qBACA,gBACA,qBACA,kBACA,qBACA,2BACA,yBACA,8BACA,qBACA,oBACA,kBACA,uBACA,kBACA,sCACA,gBACA,qBACA,2BACA,+CACA,yBACA,6BACF,CACF,CACF,EAEA,cAAiBa,KAAQF,EACvB,GAAIE,EAAK,eACP,QAAWC,KAASD,EAAK,eACvBH,EAAW,KAAKI,CAAK,EAK3B,OAAOJ,CACT,CAOA,eAAsBK,GAAcC,EAAuD,CACzF,IAAMC,EAAiB,MAAMR,GAAa,EAC1C,QAAWS,KAAgBD,EAAgB,CACzC,IAAME,EAAYD,EAAa,UACzBE,EAAU,MAAMC,GAAgBF,CAAS,EAC/C,GAAIC,GAAS,MAAQJ,EACnB,OAAOI,CAEX,CAEF,CAOA,eAAsBC,GAAgBF,EAA6D,CACjG,IAAMG,EAAS,CAAC,EAEhB,GADA,MAAMC,GAAkBvB,GAAsBmB,EAAWG,CAAM,EAC1D,MAAMtB,GAAqB,OAAO,OAAO,IAAO,YACnD,GAAI,CACF,MAAMuB,GAAkB,IAAItB,GAAqB,CAAE,OAAQ,WAAY,CAAC,EAAGkB,EAAY,aAAcG,CAAM,CAC7G,MAAQ,CAER,CAEF,OAAOA,CACT,CAQA,eAAeC,GACbC,EACAL,EACAG,EACe,CACf,IAAMG,EAAwB,IAAIC,GAAsB,CAAE,UAAWP,CAAU,CAAC,EAE1EL,GADe,MAAMU,EAAO,KAAKC,CAAqB,IAChC,SAAS,CAAC,EAChCE,EAAab,GAAO,MAAM,KAAME,GAAQA,EAAI,MAAQR,EAAM,EAChE,GAAI,CAACmB,EACH,OAGF,IAAMC,EAAiB,MAAMJ,EAAO,KAAK,IAAIK,GAA8B,CAAE,UAAWV,CAAU,CAAC,CAAC,EACpG,GAAKS,EAAe,eAIpB,CAAIJ,IAAWxB,KACbsB,EAAO,MAAQR,EACfQ,EAAO,IAAMK,EAAW,OAG1B,QAAWG,KAAYF,EAAe,eACpCG,GAAmBD,EAAUR,CAAM,EAEvC,CAEA,SAASS,GAAmBD,EAAyBR,EAA4C,CAC3FQ,EAAS,eAAiB,oBAC5BR,EAAO,WAAaQ,EACXA,EAAS,eAAiB,oBACnCR,EAAO,WAAaQ,EAEpBA,EAAS,eAAiB,mBAC1BA,EAAS,mBAAmB,WAAW,mBAAmB,EAE1DR,EAAO,UAAYQ,EAEnBA,EAAS,eAAiB,iCAC1BA,EAAS,mBAAmB,WAAW,yBAAyB,EAEhER,EAAO,gBAAkBQ,EAEzBA,EAAS,eAAiB,mDAC1BA,EAAS,mBAAmB,WAAW,8BAA8B,EAErER,EAAO,wBAA0BQ,EAEjCA,EAAS,eAAiB,mBAC1BA,EAAS,mBAAmB,WAAW,sBAAsB,EAE7DR,EAAO,cAAgBQ,EAEvBA,EAAS,eAAiB,iCAC1BA,EAAS,mBAAmB,WAAW,4BAA4B,EAEnER,EAAO,oBAAsBQ,EAE7BA,EAAS,eAAiB,mDAC1BA,EAAS,mBAAmB,WAAW,6BAA6B,IAEpER,EAAO,4BAA8BQ,EAEzC,CAMO,SAASE,GAAkBZ,EAAoC,CACpE,QAAQ,IAAI,0BAA0BA,EAAQ,GAAG,EAAE,EACnD,QAAQ,IAAI,0BAA0BA,EAAQ,OAAO,SAAS,EAAE,EAChE,QAAQ,IAAI,0BAA0BA,EAAQ,OAAO,OAAO,EAAE,EAC9D,QAAQ,IAAI,0BAA0BA,EAAQ,OAAO,WAAW,EAAE,EAClE,QAAQ,IAAI,0BAA0BA,EAAQ,YAAY,kBAAkB,EAAE,EAC9E,QAAQ,IAAI,0BAA0Ba,GAAkBb,EAAQ,UAAU,CAAC,EAAE,EAC7E,QAAQ,IAAI,0BAA0BA,EAAQ,WAAW,kBAAkB,EAAE,EAC7E,QAAQ,IAAI,0BAA0BA,EAAQ,iBAAiB,kBAAkB,EAAE,EACnF,QAAQ,IAAI,0BAA0BA,EAAQ,yBAAyB,kBAAkB,EAAE,EAC3F,QAAQ,IAAI,0BAA0BA,EAAQ,eAAe,kBAAkB,EAAE,EACjF,QAAQ,IAAI,0BAA0BA,EAAQ,qBAAqB,kBAAkB,EAAE,EACvF,QAAQ,IAAI,0BAA0BA,EAAQ,6BAA6B,kBAAkB,EAAE,CACjG,CAOO,SAASa,GAAkBH,EAAyD,CACzF,OAAOA,GAAU,oBAAoB,MAAM,GAAG,GAAG,IAAI,GAAK,EAC5D,CAUA,eAAsBI,GAAmBC,EAAuC,CAC9E,IAAMC,EAAW,MAAMlC,GAAiB,KACtC,IAAImC,GAA0B,CAC5B,eAAgBF,EAChB,kBAAmB,CACjB,gBAAiB,kBAAkB,KAAK,IAAI,CAAC,GAC7C,MAAO,CACL,SAAU,EACV,MAAO,CAAC,IAAI,CACd,CACF,CACF,CAAC,CACH,EACA,QAAQ,IAAI,iCAAiCC,EAAS,cAAc,EAAE,EAAE,CAC1E,CAEA,eAAsBE,GAAkBC,EAAkC,CASxE,IAAMC,GADQ,MAPG,MAAMC,GAAM,qEAAsE,CACjG,QAAS,CACP,OAAQ,8BACR,uBAAwB,YAC1B,CACF,CAAC,GAE4B,KAAK,GACZ,IAAKC,GACzBA,EAAQ,SAAS,WAAW,GAAG,EAAIA,EAAQ,SAAS,MAAM,CAAC,EAAIA,EAAQ,QACzE,EAGA,OAAAF,EAAS,KAAK,CAACG,EAAGC,IAAa,WAAQA,EAAGD,CAAC,CAAC,EAErCJ,EAAOC,EAAS,MAAM,EAAGA,EAAS,QAAQD,CAAI,CAAC,EAAIC,CAC5D,CAQA,eAAsBK,GACpBC,EACAC,EACAC,EACe,CACf,IAAMxB,EAAS,IAAIyB,GAAU,CAAE,OAAAH,CAAO,CAAC,EACvC,OAAW,CAACI,EAAKC,CAAK,IAAK,OAAO,QAAQH,CAAM,EAAG,CACjD,IAAMI,EAAOL,EAASG,EAChBG,EAAWF,EAAM,SAAS,EAC1BG,EAAgB,MAAMC,GAAc/B,EAAQ4B,CAAI,EAElDE,IAAkB,QAAaA,IAAkBD,IACnDG,EAAM,cAAcJ,CAAI,gCAAgC,EACxD,MAAMK,GAAQ,6BAA6BL,CAAI,IAAI,GAGrD,MAAMM,GAAelC,EAAQ4B,EAAMC,CAAQ,CAC7C,CACF,CAQA,eAAeE,GAAc/B,EAAmB4B,EAA2C,CACzF,IAAMO,EAAU,IAAIC,GAAoB,CACtC,KAAMR,EACN,eAAgB,EAClB,CAAC,EACD,GAAI,CAEF,OADe,MAAM5B,EAAO,KAAKmC,CAAO,GAC1B,WAAW,KAC3B,OAASE,EAAU,CACjB,GAAIA,EAAI,OAAS,oBACf,OAEF,MAAMA,CACR,CACF,CAQA,eAAeH,GAAelC,EAAmB4B,EAAcD,EAA8B,CAC3F,IAAMQ,EAAU,IAAIG,GAAoB,CACtC,KAAMV,EACN,MAAOD,EACP,KAAM,eACN,UAAW,EACb,CAAC,EACD,MAAM3B,EAAO,KAAKmC,CAAO,CAC3B,CAQO,SAASI,EAAoBC,EAAiBC,EAAqC,CAGxF,GAFA,QAAQ,IAAI,qBAAqBD,CAAO,KAAKE,EAAkBF,EAASC,CAAO,CAAC,GAAG,EAE/EA,EAAS,CACX,IAAME,EAAU,OAAO,QAAQF,CAAO,EACtC,GAAIE,EAAQ,OAAS,EAAG,CACtB,QAAQ,IAAI,qBAAqB,EACjC,OAAW,CAACjB,EAAKC,CAAK,IAAKgB,EACzB,QAAQ,IAAI,KAAKjB,CAAG,KAAKC,CAAK,EAAE,CAEpC,CACF,CAEA,QAAQ,IAAI,EAEZ,IAAIiB,EAAeC,GAAY,IAAK,CAAE,cAAe,EAAK,CAAC,EAK3D,GAJAD,EAAQA,EACL,OAAQE,GAAMA,EAAE,OAAO,GAAKA,EAAE,KAAK,WAAW,UAAU,GAAKA,EAAE,KAAK,SAAS,OAAO,CAAC,EACrF,IAAKA,GAAMA,EAAE,IAAI,EAEhBF,EAAM,SAAW,EACnB,QAAQ,IAAI,kBAAkB,MACzB,CACL,QAAQ,IAAI,oBAAoB,EAChC,QAAWG,KAAQH,EACjB,QAAQ,IACN,KAAKG,EACF,WAAW,WAAY,EAAE,EACzB,WAAW,UAAW,EAAE,EACxB,WAAW,UAAW,EAAE,EACxB,WAAW,QAAS,EAAE,EACtB,OAAO,GAAI,GAAG,CAAC,KAAKA,CAAI,GAC7B,CAEJ,CACF,CAOA,eAAsBC,GAAmBR,EAAgC,CACvE,QAAQ,IAAI,oBAAoBA,CAAO,EAAE,EACzC,QAAQ,IAAI,EAEZ,GAAI,CACF,IAAMxC,EAAS,IAAIiD,GACbd,EAAU,IAAIe,GAAyB,CAAC,CAAC,EACzCtC,EAAW,MAAMZ,EAAO,KAAKmC,CAAO,EACpCb,EAAS,MAAMtB,EAAO,OAAO,OAAO,EAC1C,QAAQ,IAAI,sBAAuBsB,CAAM,EACzC,QAAQ,IAAI,sBAAuBV,EAAS,OAAO,EACnD,QAAQ,IAAI,sBAAuBA,EAAS,GAAG,EAC/C,QAAQ,IAAI,sBAAuBA,EAAS,MAAM,CACpD,OAASyB,EAAK,CACZ,QAAQ,IAAI,wCAAyCc,GAAqBd,CAAG,CAAC,CAChF,CACF,CEnYA,eAAsBe,GAAsBC,EAA4B,CACtE,IAAMC,EAAU,MAAMC,GAAcF,CAAG,EACvC,GAAI,CAACC,EACH,YAAME,GAAmBH,CAAG,EACtB,IAAI,MAAM,oBAAoBA,CAAG,EAAE,EAE3CI,GAAkBH,CAAO,CAC3B,CCbA,OACE,aAAAI,GAEA,2BAAAC,GACA,6BAAAC,OAEK,sBACP,OAAS,oBAAAC,GAAkB,0BAAAC,OAA8B,6BACzD,OAAS,4BAAAC,GAA0B,aAAAC,OAAiB,sBACpD,OAA6B,wBAAAC,OAA4B,gBACzD,OAAS,uBAAAC,GAAqB,cAAAC,OAAkB,cAChD,OAAS,cAAAC,OAAkB,UAS3B,IAAMC,GAAoBC,GAAoD,GAAGA,CAAM,aACjFC,GAAwBD,GAAwD,GAAGA,CAAM,aAE/F,eAAsBE,IAAkC,CACtD,IAAMC,EAAS,CAAE,QAAS,KAAM,OAAQ,WAAY,EACpDC,GAAa,EACbC,EAAO,SAAS,EAChBC,EAAM,2FAA2F,EACjGA,EAAM,EAAE,EACRA,EAAM,4DAA4D,EAClEA,EAAM,qGAAqG,EAC3GA,EAAM,iDAAiD,EACvDA,EAAM,EAAE,EACRA,EAAM,kCAAkC,EACxCA,EAAM,0EAA0E,EAChFA,EAAM,wDAAwD,EAC9DA,EAAM,uEAAuE,EAC7EA,EAAM,qEAAqE,EAC3EA,EAAM,EAAE,EACRA,EAAM,+DAA+D,EACrEA,EAAM,qEAAqE,EAC3EA,EAAM,EAAE,EACRA,EAAM,uEAAuE,EAC7EA,EAAM,8DAA8D,EACpEA,EAAM,8FAA8F,EACpGA,EAAM,mCAAmC,EAEzC,IAAMC,EAAmB,MAAMC,GAAaL,EAAO,MAAM,EACpDI,IACHD,EAAM,6DAA6D,EACnEA,EAAM,sFAAsF,EAC5FA,EAAM,kEAAkE,EACxE,MAAMG,GAAQ,kDAAkD,GAGlEJ,EAAO,kBAAkB,EACzBC,EAAM,kGAAkG,EACxGA,EAAM,kDAAkD,EACxDA,EAAM,oEAAoE,EAC1EA,EAAM,oEAAoE,EAC1EA,EAAM,yDAAyD,EAC/DH,EAAO,KAAO,MAAMO,EAAI,iCAAkC,MAAM,EAChEJ,EAAM,2BAA6BH,EAAO,KAAO,MAAM,EAEvDE,EAAO,aAAa,EACpBC,EAAM,4EAA4E,EAClF,IAAMK,EAAiB,MAAMD,EAAI,gCAAiC,WAAWP,EAAO,IAAI,cAAc,EAClGS,GAAWD,CAAc,IAC3BL,EAAM,6BAA6B,EACnC,MAAMG,GAAQ,2CAA2C,GAE3DH,EAAM,sBAAwBK,EAAiB,MAAM,EACrDE,EAAYF,EAAgBR,CAAM,EAElCE,EAAO,YAAY,EACnBC,EAAM,gEAAgE,EACtEH,EAAO,OAAS,MAAMO,EAAI,yBAA0B,WAAW,EAC/DG,EAAYF,EAAgBR,CAAM,EAElCE,EAAO,oBAAoB,EAC3BC,EAAM,kFAAkF,EACpFC,GACFD,EAAM,kDAAoDC,CAAgB,EAE5EJ,EAAO,cAAgB,MAAMO,EAAI,mCAAoCH,CAAgB,EACrFM,EAAYF,EAAgBR,CAAM,EAElCE,EAAO,YAAY,EACnBC,EAAM,qEAAqE,EAC3EA,EAAM,iCAAiC,EACvC,IAAMQ,EAAmB,UAAYX,EAAO,KAAK,OAAO,CAAC,EAAE,YAAY,EAAIA,EAAO,KAAK,MAAM,CAAC,EAkB9F,IAjBAA,EAAO,UAAY,MAAMO,EAAI,wCAAyCI,CAAgB,EACtFD,EAAYF,EAAgBR,CAAM,EAElCE,EAAO,kBAAkB,EACzBC,EAAM,gEAAgE,EACtEA,EAAM,EAAE,EACRA,EAAM,2DAA2D,EACjEA,EAAM,EAAE,EACRA,EAAM,0EAA0E,EAChFA,EAAM,+DAA+D,EACrEA,EAAM,EAAE,EACRA,EAAM,yDAAyD,EAC/DA,EAAM,8CAA8C,EACpDA,EAAM,EAAE,EACRA,EAAM,wEAAwE,EAC9EA,EAAM,EAAE,EACRA,EAAM,sEAAsE,EACrE,CAACH,EAAO,YACbA,EAAO,WAAa,MAAMO,EAAI,8BAA8B,EAE9DG,EAAYF,EAAgBR,CAAM,EAElCE,EAAO,eAAe,EACtBC,EAAM,8CAA8C,EACpDA,EAAM,yDAAyD,EAC/DA,EAAM,kEAAkE,EACxEA,EAAM,6DAA6D,EACnE,IAAMS,EAAe,MAAML,EAAI,mCAAmC,EAElEL,EAAO,iBAAiB,EACxBC,EAAM,sDAAsD,EAC5DH,EAAO,cAAgB,MAAMO,EAAI,mCAAoC,OAASP,EAAO,UAAU,EAC/FA,EAAO,QAAU,WAAWA,EAAO,aAAa,IAChDU,EAAYF,EAAgBR,CAAM,EAElCE,EAAO,iBAAiB,EACxBC,EAAM,2DAA2D,EACjEH,EAAO,cAAgB,MAAMO,EAAI,0CAA2C,OAASP,EAAO,UAAU,EACtGU,EAAYF,EAAgBR,CAAM,EAElCE,EAAO,qBAAqB,EAC5BC,EAAM,qDAAqD,EAC3DH,EAAO,kBAAoB,MAAMO,EAAI,kCAAmC,WAAaP,EAAO,UAAU,EACtGU,EAAYF,EAAgBR,CAAM,EAElCE,EAAO,gBAAgB,EACvBC,EAAM,yEAAyE,EAC/EA,EAAM,0EAA0E,EAChFH,EAAO,kBAAoB,MAAMO,EAAI,kCAAmCP,EAAO,iBAAiB,EAChGU,EAAYF,EAAgBR,CAAM,EAElCE,EAAO,wBAAwB,EAC/BC,EAAM,qEAAqE,EAC3EA,EAAM,iDAAiD,EACvDA,EAAM,wDAAwD,EAC9DA,EAAM,8EAA8E,EACpFA,EAAM,uEAAuE,EAC7EA,EAAM,4CAA4C,EAClDH,EAAO,OAAS,MAAMa,GAAU,kDAAmD,CAAC,EAAG,EAAG,EAAE,EAAG,CAAC,EAEhGX,EAAO,oBAAoB,EAC3BC,EAAM,mDAAmD,EACzDA,EAAM,4EAA4E,EAClFA,EAAM,sFAAsF,EACxF,MAAMW,EAAQ,+EAA+E,GAC/FX,EAAM,6EAA6E,EACnFA,EAAM,EAAE,EACRA,EAAM,mEAAmE,EACzEA,EAAM,gEAAgE,EACtEH,EAAO,aAAe,MAAMa,GAAU,0CAA2C,CAAC,EAAG,CAAC,EAAG,CAAC,IAE1FV,EAAM,6CAA6C,EACnDA,EAAM,uFAAuF,EAC7FA,EAAM,2FAA2F,EACjGH,EAAO,cAAgB,QAEzBU,EAAYF,EAAgBR,CAAM,EAElCE,EAAO,kBAAkB,EACzBC,EAAM,kDAAkD,EACxDA,EAAM,gFAAgF,EACtFA,EAAM,qEAAqE,EAC3EA,EAAM,mEAAmE,EACzEH,EAAO,mBAAqB,MAAMa,GAAU,wCAAyC,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,EAAG,CAAC,EAC1GH,EAAYF,EAAgBR,CAAM,EAElCE,EAAO,eAAe,EACtBC,EAAM,+DAA+D,EACrEA,EAAM,iEAAiE,EACvEA,EAAM,oEAAoE,EAC1EA,EAAM,wEAAwE,EAC9EH,EAAO,aAAe,MAAMa,GAAU,gCAAiC,CAAC,IAAK,KAAM,KAAM,KAAM,KAAM,KAAK,EAAG,GAAG,EAChHH,EAAYF,EAAgBR,CAAM,EAElCE,EAAO,YAAY,EACnBC,EAAM,4DAA4D,EAClEA,EAAM,oDAAoD,EAC1DA,EAAM,8DAA8D,EACpEA,EAAM,oEAAoE,EAC1EA,EAAM,wEAAwE,EAC9EH,EAAO,UAAY,MAAMa,GAAU,wBAAyB,CAAC,IAAK,IAAK,KAAM,KAAM,KAAM,KAAM,KAAK,EAAG,GAAG,EAC1GH,EAAYF,EAAgBR,CAAM,EAElCE,EAAO,cAAc,EACrBC,EAAM,iDAAiD,EACvDA,EAAM,kDAAkD,EACxDA,EAAM,gEAAgE,EACtEA,EAAM,4CAA4C,EAClD,IAAMY,GAAiB,MAAMC,GAAkB,GAAG,CAAC,GAAK,SACxDhB,EAAO,YAAc,MAAMO,EAAI,0BAA2B,0BAA0BQ,CAAa,EAAE,EACnGL,EAAYF,EAAgBR,CAAM,EAElCE,EAAO,aAAa,EACpBC,EAAM,qFAAqF,EAC3F,IAAMc,EAAa,MAAMC,GAAmBlB,EAAO,OAAQA,EAAO,UAAY,YAAY,EACtFiB,GACFjB,EAAO,aAAeiB,EAAW,MACjCjB,EAAO,iBAAmBiB,EAAW,UACrCP,EAAYF,EAAgBR,CAAM,IAElCG,EAAM,iCAAiC,EACvCA,EAAM,8FAA8F,EACpGA,EAAM,qFAAqF,GAG7FD,EAAO,kBAAkB,EACzBC,EAAM,0EAA0E,EAChF,IAAMgB,EAAW,MAAMC,GAAoBpB,EAAO,MAAM,EACxDG,EAAM,SAAWgB,EAAS,OAAS,kBAAkB,EAKrD,OAAW,CAAE,OAAAE,EAAQ,SAAAC,CAAS,GAAK,CACjC,CAAE,OAAQtB,EAAO,OAAQ,SAAU,KAAM,EACzC,CAAE,OAAQ,YAAa,SAAU,KAAM,EACvC,CAAE,OAAQ,YAAa,SAAU,SAAU,CAC7C,EAAY,CACVG,EAAM,EAAE,EACR,IAAMoB,EAAM,MAAMC,GAAYxB,EAAQmB,EAAUE,EAAQC,CAAQ,EAChEtB,EAAOF,GAAqBwB,CAAQ,CAAC,EAAIC,EACzCb,EAAYF,EAAgBR,CAAM,CACpC,CAEAE,EAAO,qBAAqB,EAC5BC,EAAM,2EAA2E,EACjFA,EAAM,yCAAyC,EAC/CA,EAAM,8CAA8CH,EAAO,IAAI,SAAS,EAExE,IAAMyB,EAAgD,CACpD,KAAMzB,EAAO,QACb,QAASA,EAAO,QAChB,WAAY,WAAWA,EAAO,aAAa,IAC3C,eAAgB,WAAWA,EAAO,iBAAiB,WACnD,cAAe,MAAMA,EAAO,iBAAiB,GAC7C,aAAcY,CAChB,EAoBA,GAlBIK,IACFQ,EAAa,aAAeR,EAAW,MACvCQ,EAAa,WAAaR,EAAW,WACrCQ,EAAa,qBAAuBR,EAAW,YAGjDd,EACE,KAAK,UACH,CACE,GAAGsB,EACH,WAAY,OACZ,qBAAsB,MACxB,EACA,KACA,CACF,CACF,EAEI,MAAMX,EAAQ,2DAA2D,EAC3E,MAAMY,GAAgB1B,EAAO,OAAQ,YAAYA,EAAO,IAAI,IAAKyB,CAAY,MACxE,CACL,IAAME,EAAuBC,EAAkB5B,EAAO,KAAM,CAAE,OAAQ,EAAK,CAAC,EAC5EU,EAAYiB,EAAsBF,CAAY,EAC9CtB,EAAM,+BAA+B,EACrCA,EAAM,wCAAwCwB,CAAoB,EAAE,EACpExB,EAAM,0DAA0D,CAClE,CAEAD,EAAO,OAAO,EACdC,EAAM,iCAAiC,EACvCA,EAAM,uEAAuE,EAC7EA,EAAM,MAAM,EACZA,EAAM,EAAE,EACRA,EAAM,mCAAmCK,CAAc,EAAE,EACzDL,EAAM,+BAA+BK,CAAc,EAAE,EACjDR,EAAO,SAAW,YACpBG,EAAM,gCAAgCK,CAAc,EAAE,EAEtDL,EAAM,gCAAgCK,CAAc,QAAQ,EAE9DL,EAAM,EAAE,EACRA,EAAM,iDAAiD,EACvDA,EAAM,EAAE,EACRA,EAAM,8DAA8D,EACpEA,EAAM,EAAE,EACR0B,GAAc,CAChB,CAQA,eAAexB,GAAagB,EAA6C,CACvE,GAAI,CACF,IAAMS,EAAS,IAAIC,GAAU,CAAE,OAAAV,CAAO,CAAC,EACjCW,EAAU,IAAIC,GAAyB,CAAC,CAAC,EAE/C,OADiB,MAAMH,EAAO,KAAKE,CAAO,GAC1B,OAClB,OAASE,EAAK,CACZ,QAAQ,IAAI,wCAA0CA,EAAc,OAAO,EAC3E,MACF,CACF,CASA,eAAed,GAAoBC,EAA+C,CAChF,IAAMc,EAAS,MAAMC,GAAiBf,CAAM,EAC5C,GAAIA,IAAW,YAAa,CAC1B,IAAMgB,EAAgB,MAAMD,GAAiB,WAAW,EACxDD,EAAO,KAAK,GAAGE,CAAa,CAC9B,CACA,OAAOF,CACT,CAQA,eAAeC,GAAiBf,EAA+C,CAC7E,GAAI,CACF,IAAMS,EAAS,IAAIQ,GAAU,CAAE,OAAAjB,CAAO,CAAC,EACjCW,EAAU,IAAIO,GAAwB,CAAE,SAAU,GAAK,CAAC,EAE9D,OADiB,MAAMT,EAAO,KAAKE,CAAO,GAC1B,sBAClB,OAASE,EAAK,CACZ,eAAQ,IAAI,uCAAyCA,EAAc,OAAO,EACnE,CAAC,CACV,CACF,CAcA,eAAeV,GACbxB,EACAmB,EACAE,EACAC,EACiB,CACjB,IAAMkB,EAAaxC,EAAOJ,GAAiB0B,CAAQ,CAAC,EAC9CmB,EAAetB,EAAS,KAAMuB,GAASA,EAAK,gBAAgB,SAASrB,CAAM,GAAKqB,EAAK,aAAeF,CAAU,EACpH,GAAIC,EACF,OAAAtC,EAAM,mCAAmCqC,CAAU,SAASnB,CAAM,GAAG,EAC9DoB,EAAa,eAItB,GADAtC,EAAM,sCAAsCqC,CAAU,SAASnB,CAAM,GAAG,EACpE,CAAE,MAAMP,EAAQ,2CAA2C,EAC7D,OAAAX,EAAM,8DAA8DL,GAAqBwB,CAAQ,CAAC,YAAY,EACvG,OAGT,IAAMC,EAAM,MAAMoB,GAAYtB,EAAQmB,CAAU,EAChD,OAAArC,EAAM,oBAAsBoB,CAAG,EACxBA,CACT,CAQA,eAAeoB,GAAYtB,EAAgBxB,EAAiC,CAC1E,GAAI,CACF,IAAM+C,EAAmB,MAAMC,GAC7B,sDACA,CAAC,MAAO,OAAO,EACf,KACF,EACMf,EAAS,IAAIQ,GAAU,CAAE,OAAAjB,CAAO,CAAC,EACjCW,EAAU,IAAIc,GAA0B,CAC5C,WAAYjD,EACZ,iBAAkB+C,EAAiB,YAAY,CACjD,CAAC,EAED,OADiB,MAAMd,EAAO,KAAKE,CAAO,GAC1B,cAClB,OAASE,EAAK,CACZ,eAAQ,IAAI,uCAAyCA,EAAc,OAAO,EACnE,MACT,CACF,CAiBA,eAAehB,GACbG,EACA0B,EASA,CACA,IAAMC,EAAaC,GAAW,EACxBhC,EAAaiC,GAAoB,MAAO,CAC5C,cAAe,KACf,kBAAmB,CACjB,KAAM,OACN,OAAQ,KACV,EACA,mBAAoB,CAClB,KAAM,QACN,OAAQ,MACR,OAAQ,cACR,WAAAF,CACF,CACF,CAAC,EAED,GAAI,CAWF,MAAO,CACL,OAXe,MAAM,IAAIG,GAAiB,CAAE,OAAA9B,CAAO,CAAC,EAAE,KACtD,IAAI+B,GAAuB,CACzB,gBAAiB,CACf,KAAML,EACN,gBAAiBE,GAAW,EAC5B,WAAYhC,EAAW,SACzB,CACF,CAAC,CACH,GAGkB,WAAW,GAC3B,UAAWA,EAAW,UACtB,WAAYA,EAAW,WACvB,WAAA+B,CACF,CACF,OAASd,EAAK,CACZ,QAAQ,IAAI,wCAAyCmB,GAAqBnB,CAAG,CAAC,EAC9E,MACF,CACF,CCrdA,eAAsBoB,IAAmC,CACvD,IAAMC,EAAiB,MAAMC,GAAa,EAC1C,QAAWC,KAAgBF,EAAgB,CACzC,IAAMG,EAAYD,EAAa,UACzBE,EAAU,MAAMC,GAAgBF,CAAS,EAC1CC,IAGLE,GAAkBF,CAAO,EACzB,QAAQ,IAAI,EAAE,EAChB,CACF,CChBA,OAAS,oBAAAG,OAAwB,qBACjC,OAAS,eAAAC,MAAmB,gBAC5B,OAAOC,OAAc,YACrB,OAAOC,OAAW,aAClB,OAAS,oBAAAC,GAAkB,eAAAC,GAAa,eAAAC,GAAa,gBAAAC,GAAc,UAAAC,GAAQ,iBAAAC,OAAqB,UAChG,OAAS,UAAAC,OAAc,UACvB,OAAS,QAAAC,GAAM,OAAAC,OAAW,YAC1B,OAAS,YAAAC,OAAgB,uBAgBzB,eAAsBC,GAAiBC,EAAaC,EAA0C,CAC5F,IAAMC,EAASC,EAAWH,EAAKC,CAAO,EACtC,GAAI,CAACC,EACH,MAAAE,EAAoBJ,EAAKC,CAAO,EAC1B,IAAI,MAAM,qBAAqBD,CAAG,EAAE,EAE5C,IAAMK,EAAU,MAAMC,GAAcN,CAAG,EACvC,GAAI,CAACK,EACH,YAAME,GAAmBP,CAAG,EACtB,IAAI,MAAM,oBAAoBA,CAAG,EAAE,EAE3C,IAAMQ,EAAYH,EAAQ,UAC1B,GAAI,CAACG,EACH,MAAM,IAAI,MAAM,kCAAkCR,CAAG,EAAE,EAGzD,IAAIS,EAEJ,GAAIR,EAAQ,QACVQ,EAASR,EAAQ,YACZ,CACL,IAAMS,EAAUT,GAAS,WAAa,SACtCQ,EAAS,MAAME,GAAmB,eAAgBD,CAAO,CAC3D,CAGAE,GAAiBH,EAAQ,CACvB,iBAAkBP,EAAO,QACzB,kBAAmBA,EAAO,UAAY,GACtC,iBAAkBA,EAAO,gBAAkB,GAC3C,mBAAoBA,EAAO,kBAAoB,GAC/C,yBAA0BA,EAAO,gBAAkB,OAAS,OAC9D,CAAC,EAGD,MAAMW,GAAcJ,EAAQD,EAAU,mBAA8BP,CAAO,EAGvEI,EAAQ,iBAAiB,oBAAsB,CAACJ,EAAQ,QAC1D,MAAMa,GAAmBT,EAAQ,gBAAgB,kBAAkB,EAGrE,QAAQ,IAAI,MAAM,CACpB,CASA,eAAeU,GAAsBC,EAAqBN,EAA+B,CACvF,IAAMO,EAAM,8BAA8BD,CAAW,IAAIN,CAAO,GAEhE,OADiB,MAAMQ,GAAMD,CAAG,GAChB,KAAK,CACvB,CAQA,eAAeN,GAAmBK,EAAqBN,EAAkC,CAEvF,IAAMS,GADkB,MAAMJ,GAAsBC,EAAaN,CAAO,GACrC,KAAK,QAClCD,EAASW,GAAYC,GAAKC,GAAO,EAAG,UAAU,CAAC,EACrD,GAAI,CACF,IAAMC,EAAW,MAAML,GAAMC,CAAU,EACjCK,EAAYC,GAAiBhB,CAAM,EACzC,aAAMiB,GAASH,EAAS,KAAMC,CAAS,EAChCH,GAAKZ,EAAQ,UAAW,MAAM,CACvC,OAASkB,EAAO,CACd,MAAAC,GAAOnB,EAAQ,CAAE,UAAW,GAAM,MAAO,EAAK,CAAC,EACzCkB,CACR,CACF,CAOA,SAASf,GAAiBiB,EAAoBC,EAA4C,CACxF,QAAWC,KAAQC,GAAYH,EAAY,CAAE,cAAe,EAAK,CAAC,EAAG,CACnE,IAAMI,EAAWZ,GAAKQ,EAAYE,EAAK,IAAI,EACvCA,EAAK,YAAY,EACnBnB,GAAiBqB,EAAUH,CAAY,EAC9BC,EAAK,OAAO,GAAKE,EAAS,SAAS,KAAK,GACjDC,GAAuBD,EAAUH,CAAY,CAEjD,CACF,CAOA,SAASI,GAAuBC,EAAkBL,EAA4C,CAC5F,IAAIM,EAAWC,GAAaF,EAAU,OAAO,EAC7C,OAAW,CAACG,EAAaC,CAAW,IAAK,OAAO,QAAQT,CAAY,EAClEM,EAAWA,EAAS,WAAW,KAAKE,CAAW,KAAMC,CAAW,EAElEC,GAAcL,EAAUC,CAAQ,CAClC,CASA,eAAevB,GAAcJ,EAAgBgC,EAAoBxC,EAA0C,CAIzG,IAAMyC,EAA8C,CAIlD,CAAC,kBAAmBC,EAAY,IAAK,EAAI,EACzC,CAAC,sBAAuBA,EAAY,KAAM,EAAI,EAC9C,CAAC,iBAAkBA,EAAY,WAAY,EAAI,EAC/C,CAAC,qBAAsBA,EAAY,KAAM,EAAI,EAC7C,CAAC,kBAAmBA,EAAY,KAAM,EAAI,EAC1C,CAAC,kBAAmBA,EAAY,QAAS,EAAI,EAC7C,CAAC,eAAgBA,EAAY,IAAK,EAAI,EACtC,CAAC,eAAgBA,EAAY,IAAK,EAAI,EACtC,CAAC,aAAcA,EAAY,KAAM,EAAI,EAGrC,CAAC,aAAcA,EAAY,KAAM,EAAK,CACxC,EACA,QAAWC,KAAiBF,EAC1B,MAAMG,GAAiB,CACrB,QAASpC,EACT,WAAAgC,EACA,gBAAiBG,EAAc,CAAC,EAChC,YAAaA,EAAc,CAAC,EAC5B,OAAQA,EAAc,CAAC,EACvB,OAAQ3C,EAAQ,MAClB,CAAC,CAEL,CAYA,eAAe4C,GAAiB5C,EAOd,CAChB,IAAM6C,EAAQC,GAAS,KAAK9C,EAAQ,gBAAiB,CAAE,IAAKA,EAAQ,OAAQ,CAAC,EAC7E,QAAW8B,KAAQe,EACjB,MAAME,GAAe3B,GAAKpB,EAAQ,QAAS8B,CAAI,EAAG9B,CAAO,CAE7D,CAYA,eAAe+C,GACbC,EACAhD,EAOe,CACf,IAAMiD,EAAaC,GAAiBF,CAAQ,EACtCG,EAAQH,EACX,UAAUhD,EAAQ,QAAQ,OAAS,CAAC,EACpC,MAAMoD,EAAG,EACT,KAAK,GAAG,EAELC,EAAkB,CACtB,OAAQrD,EAAQ,WAChB,IAAKmD,EACL,KAAMF,EACN,YAAajD,EAAQ,YACrB,aAAcA,EAAQ,OAAS,2BAA6B,qCAC9D,EAEA,QAAQ,IAAI,aAAamD,CAAK,OAAOnD,EAAQ,UAAU,KAAK,EACvDA,EAAQ,QACX,MAAMsD,GAAS,KAAK,IAAIC,GAAiBF,CAAe,CAAC,CAE7D,CCxOA,OAAS,0BAAAG,GAAwB,0BAAAC,OAA8B,qBA6B/D,eAAsBC,GAA4BC,EAAaC,EAAqD,CAElH,GAAI,CADWC,EAAWF,EAAKC,CAAO,EAEpC,MAAAE,EAAoBH,EAAKC,CAAO,EAC1B,IAAI,MAAM,qBAAqBD,CAAG,EAAE,EAG5C,IAAMI,EAAU,MAAMC,GAAcL,CAAG,EACvC,GAAI,CAACI,EACH,YAAME,GAAmBN,CAAG,EACtB,IAAI,MAAM,oBAAoBA,CAAG,EAAE,EAG3C,MAAMO,GAAmB,MAAOH,EAAQ,UAAWA,EAAQ,gBAAiBA,EAAQ,wBAAyBH,CAAO,EAEpH,MAAMM,GACJ,UACAH,EAAQ,cACRA,EAAQ,oBACRA,EAAQ,4BACRH,CACF,EAEA,QAAQ,IAAI,MAAM,CACpB,CAEA,eAAsBM,GACpBC,EACAC,EACAC,EACAC,EACAV,EACe,CACf,GAAI,CAACQ,GAAgB,mBACnB,MAAM,IAAI,MAAM,GAAGD,CAAY,mBAAmB,EAGpD,GAAI,CAACE,GAAsB,mBACzB,MAAM,IAAI,MAAM,GAAGF,CAAY,yBAAyB,EAG1D,GAAI,CAACG,GAAa,mBAChB,MAAM,IAAI,MAAM,GAAGH,CAAY,gBAAgB,EAGjD,IAAMI,EAAaH,EAAe,mBAC5BI,EAAQF,EAAY,mBACpBG,EAAe,MAAMC,GAAUH,CAAU,EAC/C,GAAII,GAAmBF,EAAcF,EAAYC,CAAK,EACpD,MAAM,IAAI,MAAM,GAAGL,CAAY,sCAAsC,EAGvES,GAAmBH,EAAcF,EAAYC,CAAK,EAClD,QAAQ,IAAI,GAAGL,CAAY,iBAAiB,EAC5C,QAAQ,IAAI,KAAK,UAAUM,EAAc,OAAW,CAAC,CAAC,EAElDb,EAAQ,OACV,QAAQ,IAAI,4BAA4B,GAGxC,QAAQ,IAAI,2BAA2B,EACvC,MAAMiB,GAAUN,EAAYE,CAAY,EACxC,QAAQ,IAAI,uBAAuB,EAGnC,QAAQ,IAAI,qCAAqC,EACjD,MAAMK,GAAmBT,EAAqB,kBAAkB,EAChE,QAAQ,IAAI,iCAAiC,EAE7C,QAAQ,IAAI,GAAGF,CAAY,wBAAwB,EAEvD,CAEA,eAAeO,GAAUH,EAAqC,CAC5D,IAAMQ,EAAiB,MAAMC,GAAS,KACpC,IAAIC,GAAuB,CACzB,OAAQV,CACV,CAAC,CACH,EACA,OAAO,KAAK,MAAMQ,EAAe,QAAU,IAAI,CACjD,CAEA,eAAeF,GAAUN,EAAoBW,EAA+B,CAC1E,MAAMF,GAAS,KACb,IAAIG,GAAuB,CACzB,OAAQZ,EACR,OAAQ,KAAK,UAAUW,CAAM,CAC/B,CAAC,CACH,CACF,CAEA,SAASP,GAAmBO,EAAgBX,EAAoBC,EAAwB,CACtF,MAAO,CAAC,CAACU,GAAQ,WAAW,KAAME,GAE9BA,GAAG,SAAW,SACdA,GAAG,WAAW,MAAQ,kEAAkEZ,CAAK,IAC7F,MAAM,QAAQY,GAAG,MAAM,GACvBA,GAAG,QAAQ,SAAS,eAAe,GACnCA,GAAG,QAAQ,SAAS,eAAe,GACnCA,GAAG,QAAQ,SAAS,UAAU,GAC9B,MAAM,QAAQA,GAAG,QAAQ,GACzBA,GAAG,UAAU,SAAS,gBAAgBb,CAAU,EAAE,GAClDa,GAAG,UAAU,SAAS,gBAAgBb,CAAU,IAAI,CAEvD,CACH,CAEA,SAASK,GAAmBM,EAAgBX,EAAoBC,EAAqB,CAC9EU,EAAO,UACVA,EAAO,QAAU,cAGdA,EAAO,YACVA,EAAO,UAAY,CAAC,GAGtBA,EAAO,UAAU,KAAK,CACpB,OAAQ,QACR,UAAW,CACT,IAAK,kEAAkEV,CAAK,EAC9E,EACA,OAAQ,CAAC,gBAAiB,gBAAiB,UAAU,EACrD,SAAU,CAAC,gBAAgBD,CAAU,GAAI,gBAAgBA,CAAU,IAAI,CACzE,CAAC,CACH,CCzIA,eAAsBc,GAAoBC,EAAaC,EAA6C,CAClG,GAAI,CACFC,GAAa,EAEb,IAAMC,EAAcC,EAAWJ,EAAKC,CAAO,EAC3C,GAAI,CAACE,EACH,MAAAE,EAAoBL,EAAKC,CAAO,EAC1B,IAAI,MAAM,qBAAqBD,CAAG,EAAE,EAG5C,IAAMM,EAAeC,GAAiBP,CAAG,GAAK,CAAC,EAG/C,GAAI,CAACC,EAAQ,KAAO,OAAO,KAAKK,CAAY,EAAE,SAAW,EAAG,CAC1D,IAAME,EAAuBC,EAAkBT,EAAK,CAAE,OAAQ,EAAK,CAAC,EAEpE,GADA,QAAQ,IAAIU,EAAM,OAAO,eAAeF,CAAoB,aAAa,CAAC,EACtE,CAAE,MAAMG,EAAQ,yBAAyB,EAAI,CAC/C,QAAQ,IAAID,EAAM,IAAI,8BAA8BF,CAAoB,4BAA4B,CAAC,EACrG,MACF,CACF,CAEAI,GAAqBT,EAAaG,CAAY,EAC9CO,GAAaV,EAAaG,CAAY,EAEtCQ,EAAM,2EAA2E,EACjFA,EAAM,yCAAyC,EAC/CA,EAAM,8CAA8CX,EAAY,IAAI,SAAS,EAE7EW,EACE,KAAK,UACH,CACE,GAAGR,EACH,WAAY,OACZ,qBAAsB,MACxB,EACA,KACA,CACF,CACF,EAEIL,EAAQ,OACV,QAAQ,IAAIS,EAAM,OAAO,6BAA6B,CAAC,GAC9CT,EAAQ,KAAQ,MAAMU,EAAQ,2DAA2D,IAClG,MAAMI,GAAgBZ,EAAY,OAAQ,YAAYA,EAAY,IAAI,IAAKG,CAAY,CAE3F,QAAE,CACAU,GAAc,CAChB,CACF,CAEO,SAASJ,GACdT,EACAG,EACM,CACNW,GACEd,EAAY,QACZG,EAAa,KACb,oBAAoBH,EAAY,OAAO,mCAAmCG,EAAa,IAAI,GAC7F,EAEAW,GACEd,EAAY,QACZG,EAAa,QACb,oBAAoBH,EAAY,OAAO,sCAAsCG,EAAa,OAAO,GACnG,EAEAW,GACEd,EAAY,eAAiB,WAAWA,EAAY,aAAa,IACjEG,EAAa,WACb,0BAA0BH,EAAY,aAAa,yCAAyCG,EAAa,UAAU,GACrH,EAEAW,GACEd,EAAY,mBAAqB,WAAWA,EAAY,iBAAiB,WACzEG,EAAa,eACb,8BAA8BH,EAAY,iBAAiB,6CAA6CG,EAAa,cAAc,GACrI,CACF,CAEA,SAASW,GAAiBC,EAAMC,EAAMC,EAAuB,CAC3D,GAAIC,GAAWH,EAAGC,CAAC,EACjB,MAAM,IAAI,MAAMC,CAAO,CAE3B,CAEA,SAASC,GAAcH,EAAMC,EAAe,CAC1C,OAAOD,IAAM,QAAaC,IAAM,QAAaD,IAAMC,CACrD,CAEO,SAASN,GAAaV,EAAiCG,EAAqD,CAC7GH,EAAY,UACdG,EAAa,KAAOH,EAAY,SAE9BA,EAAY,UACdG,EAAa,QAAUH,EAAY,SAEjCA,EAAY,gBACdG,EAAa,WAAa,WAAWH,EAAY,aAAa,KAE5DA,EAAY,oBACdG,EAAa,eAAiB,WAAWH,EAAY,iBAAiB,IAE1E,CCtHA,IAAAmB,GAAwB,SADxB,OAAS,aAAAC,OAAiB,qBAgB1B,eAAsBC,GAAoBC,EAAaC,EAA6C,CAClG,IAAMC,EAAS,MAAMC,EAAoBF,CAAO,EAC1CG,EAASC,EAAWL,EAAKC,CAAO,EACtC,GAAI,CAACG,EACH,cAAQ,IAAI,sBAAsBE,EAAkBN,CAAG,CAAC,YAAY,EACpEO,EAAoBP,EAAKC,CAAO,EAC1B,IAAI,MAAM,qBAAqBD,CAAG,EAAE,EAG5C,IAAMQ,EAAiBJ,EAAO,YAAY,YAAY,GAAG,EACnDK,EAAoBL,EAAO,YAAY,MAAM,EAAGI,CAAc,EAE9DE,EAAiB,MAAMC,GAAkBT,EAAQE,CAAM,EAEzDQ,EAAgB,MAAMC,GAAkBH,CAAc,EAC1D,KAAOE,GAAe,CACpB,GAAIX,EAAQ,WAAoB,MAAGW,EAAeX,EAAQ,SAAS,EAAG,CACpE,QAAQ,IAAI,uBAAuBW,CAAa,EAAE,EAClD,KACF,CAEA,QAAQ,IAAI,yBAAyBA,CAAa,EAAE,EACpDR,EAAO,YAAc,GAAGK,CAAiB,IAAIG,CAAa,GAC1DE,GAAmBd,EAAKI,CAAM,EAG9B,MAAMF,EAAO,kBAAkB,sBAAsB,EAErDU,EAAgB,MAAMC,GAAkBD,CAAa,CACvD,CACF,CAEA,eAAeD,GAAkBI,EAAwBX,EAA6C,CACpG,IAAMI,EAAiBJ,EAAO,YAAY,YAAY,GAAG,EACrDM,EAAiBN,EAAO,YAAY,MAAMI,EAAiB,CAAC,EAChE,GAAIE,IAAmB,SAAU,CAE/BA,GADmB,MAAMK,EAAQ,IAAI,cAAc,GACvB,QAC5B,IAAMC,EAAMN,EAAe,QAAQ,GAAG,EAClCM,EAAM,KACRN,EAAiBA,EAAe,MAAM,EAAGM,CAAG,EAEhD,CACA,OAAON,CACT,CAEA,eAAeG,GAAkBI,EAAwBC,EAAqD,CAO5G,IAAMC,EAAc,MAAMC,GAAkBH,CAAc,EACpDI,EAAgBF,EAAY,CAAC,EACnC,OAAOA,EACJ,OACEG,GAAMA,IAAMD,GAAiBC,IAAMJ,GAAwB,OAAII,EAAU,OAAIL,EAAgB,OAAO,CAAW,CAClH,EACC,IAAI,CACT,CAEA,SAASH,GAAmBd,EAAaI,EAAkC,CACzE,IAAMmB,EAAajB,EAAkBN,CAAG,EACxCwB,EAAYD,EAAYnB,CAAM,EAE9B,IAAMqB,EAAM,4BAA4BF,CAAU,GAAGnB,EAAO,SAAW,YAAc,SAAW,EAAE,GAClG,QAAQ,IAAI,KAAOqB,CAAG,EACtB,IAAMC,EAASC,GAAUF,EAAK,CAAE,MAAO,SAAU,CAAC,EAElD,GAAIC,EAAO,SAAW,EACpB,MAAM,IAAI,MAAM,aAAatB,EAAO,WAAW,sBAAsBsB,EAAO,MAAM,MAAMA,EAAO,MAAM,EAAE,EAEzG,QAAQ,IAAIA,EAAO,MAAM,CAC3B,CCjFO,SAASE,IAAkC,CAChD,IAAMC,EAAM,IAAIC,EAAe,KAAK,EAAE,YAAY,kCAAkC,EAEpF,OAAAD,EAAI,QAAQ,MAAM,EAAE,YAAY,oDAAoD,EAAE,OAAOE,EAAgB,EAE7GF,EAAI,QAAQ,MAAM,EAAE,YAAY,wCAAwC,EAAE,OAAOG,EAAiB,EAElGH,EACG,QAAQ,UAAU,EAClB,YAAY,oDAAoD,EAChE,SAAS,QAAS,uBAAuB,EACzC,OAAOI,EAAqB,EAE/BJ,EACG,QAAQ,eAAe,EACvB,MAAM,eAAe,EACrB,QAAQ,+CAA+C,EACvD,YACCK,GACE;AAAA;AAAA;AAAA;AAAA,EACEC,EAAM,OAAO,kDAAkD,CACnE,CACF,EACC,SAAS,QAAS,uBAAuB,EACzC,OACC,gBACAD,GACE,4IACF,CACF,EACC,OACC,WACA,4GACF,EACC,OAAO,QAAS,kCAAkC,EAClD,OAAOE,EAAmB,EAE7BC,EACER,EACA,IAAIC,EAAe,eAAe,EAC/B,MAAM,eAAe,EACrB,YAAY,yBAAyB,EACrC,SAAS,QAAS,uBAAuB,EACzC,OAAO,gBAAiB,mFAAmF,EAC3G,OACC,yBACA,wGACF,EACC,OAAOQ,EAAmB,CAC/B,EAEAT,EACG,QAAQ,YAAY,EACpB,MAAM,YAAY,EAClB,YAAY,qBAAqB,EACjC,SAAS,QAAS,uBAAuB,EACzC,OAAO,gBAAiB,mFAAmF,EAC3G,OACC,yBACA,wGACF,EACC,OACC,WACA,4GACF,EACC,OAAO,uBAAwB,0EAA0E,EACzG,OAAOU,EAAgB,EAE1BV,EACG,QAAQ,wBAAwB,EAChC,YAAY,2BAA2B,EACvC,SAAS,QAAS,uBAAuB,EACzC,OAAO,gBAAiB,mFAAmF,EAC3G,OACC,WACA,4GACF,EACC,OAAOW,EAA2B,EAE9BX,CACT,CCtFA,IAAMY,GAAiB,IAAIC,EAAe,MAAM,EAC1CC,GAAmB,IAAID,EAAe,QAAQ,EAC9CE,GAAmB,IAAIF,EAAe,QAAQ,EAEvCG,GAAM,IAAIH,EAAe,KAAK,EAC3CI,EAAcD,GAAKJ,EAAc,EACjCK,EAAcD,GAAKF,EAAgB,EACnCG,EAAcD,GAAKD,EAAgB,EAG5B,IAAMG,GAAmB,IAAIL,EAAe,UAAU,EAChDM,GAAqB,IAAIN,EAAe,YAAY,EACpDO,GAAqB,IAAIP,EAAe,YAAY,EAEjED,GACG,YAAY,gBAAgB,EAC5B,SAAS,WAAW,EACpB,OAAO,MAAOS,EAASC,IAAY,CAClC,IAAMC,EAAU,MAAMC,EAAoBF,CAAO,EAEjD,MAAMG,GAAWF,EAASF,CAAO,CACnC,CAAC,EAEHP,GACG,YAAY,uBAAuB,EACnC,SAAS,WAAW,EACpB,OAAO,MAAOO,EAASC,IAAY,CAClC,IAAMC,EAAU,MAAMC,EAAoBF,CAAO,EAEjD,MAAMG,GAAWF,EAASF,EAAS,EAAI,CACzC,CAAC,EAEHN,GACG,UAAU,+CAA+C,EACzD,YAAY,gBAAgB,EAC5B,OAAO,qCAAsC,wCAAwC,EACrF,OAAO,oBAAqB,4BAA4B,EACxD,OAAO,MAAOM,EAASK,EAAWC,EAAYC,EAAUN,IAAY,CACnE,IAAMC,EAAU,MAAMC,EAAoBF,CAAO,EAEjD,MAAMO,GAAUN,EAASF,EAASK,EAAWC,EAAYC,EAAUN,EAAQ,eAAgB,CAAC,CAACA,EAAQ,WAAW,CAClH,CAAC,EAEH,eAAsBG,GAAWF,EAAwBF,EAAiBS,EAAS,GAAsB,CACvG,IAAMC,EAAaC,GAAeX,CAAO,EACnCY,EAAS,CAAC,EACVC,EAAU,CAAC,EACbC,EAAQ,EACRC,EAAW,EAEf,QAAWC,KAAaN,EACtB,GAAI,CACF,IAAMf,EAAM,MAAMO,EAAQ,aAAa,MAAOc,EAAU,EAAE,EAC1D,MAAMC,GAAQf,EAASc,EAAWrB,CAAG,EACrCmB,IACIL,IACF,MAAMS,GAAUhB,EAASc,EAAWrB,CAAG,EACvCoB,IAEJ,OAASI,EAAc,CACrBP,EAAO,KAAKO,CAAY,EACxBN,EAAQ,KAAK,GAAGG,EAAU,IAAI,KAAKA,EAAU,EAAE,GAAG,CACpD,CAOF,GAJA,QAAQ,IAAI,yBAAyBF,CAAK,EAAE,EAC5C,QAAQ,IAAI,4BAA4BC,CAAQ,EAAE,EAClD,QAAQ,IAAI,qBAAqBH,EAAO,MAAM,EAAE,EAE5CA,EAAO,OACT,MAAM,IAAI,MAAM,GAAGA,EAAO,MAAM;AAAA;AAAA,MAAoDC,EAAQ,KAAK;AAAA,KAAQ,CAAC,GAAI,CAC5G,MAAOD,CACT,CAAC,CAEL,CAGAf,GACG,YAAY,eAAe,EAC3B,SAAS,WAAW,EACpB,OAAO,MAAOG,EAASC,IAAY,CAClC,IAAMC,EAAU,MAAMC,EAAoBF,CAAO,EAEjD,MAAMG,GAAWF,EAASF,CAAO,CACnC,CAAC,EAEHF,GACG,YAAY,uBAAuB,EACnC,SAAS,WAAW,EACpB,OAAO,MAAOE,EAASC,IAAY,CAClC,IAAMC,EAAU,MAAMC,EAAoBF,CAAO,EAEjD,MAAMG,GAAWF,EAASF,EAAS,EAAI,CACzC,CAAC,EAEHD,GACG,UAAU,+CAA+C,EACzD,YAAY,2BAA2B,EACvC,OAAO,MAAOC,EAASK,EAAWC,EAAYC,EAAUN,IAAY,CACnE,IAAMC,EAAU,MAAMC,EAAoBF,CAAO,EAEjD,MAAMO,GAAUN,EAASF,EAASK,EAAWC,EAAYC,CAAQ,CACnE,CAAC,ECxGH,OAAS,oBAAAa,GAAkB,aAAAC,OAAiB,UAC5C,OAAS,WAAAC,OAAe,YACxB,OAAS,mBAAAC,OAAuB,gBAIhC,IAAMC,GAAoB,IAAIC,EAAe,QAAQ,EAC/CC,GAAoB,IAAID,EAAe,QAAQ,EAExCE,GAAO,IAAIF,EAAe,MAAM,EAC7CG,EAAcD,GAAMH,EAAiB,EACrCI,EAAcD,GAAMD,EAAiB,EAErCF,GACG,OACC,mCACA,oHACF,EACC,OAAO,sBAAuB,mCAAmC,EACjE,OACC,sBACA,oLACF,EACC,OACC,2CACA,0EACF,EACC,OAAO,MAAOK,GAAY,CACzB,GAAM,CAAE,YAAAC,EAAa,MAAAC,EAAO,MAAAC,EAAO,gBAAAC,CAAgB,EAAIJ,EACjDK,EAAU,MAAMC,EAAoBN,CAAO,GAChC,MAAMK,EAAQ,WAAWJ,EAAaC,EAAOC,EAAO,CAAE,qBAAsB,EAAK,CAAC,GAE1F,QAAQ,QAAQ,MAAO,CAAE,KAAAI,EAAM,IAAAC,CAAI,IAAM,CAChD,IAAMC,EAAU,IAAI,IAAID,CAAa,EAC/BE,EAAO,MAAML,EAAQ,SAASG,CAAa,EAC3CG,EAAW,GAAGJ,CAAI,IAAIE,EAAQ,QAAQ,GAAG,QAAQ,iBAAkB,GAAG,EAAI,UAC1EG,EAAOC,GAAQT,GAAmB,GAAIO,CAAQ,EAEpDG,GAAU,GAAGF,CAAI,GAAI,MAAMF,EAAK,KAAK,EAAG,IAAM,CAC5C,QAAQ,IAAI,GAAGE,CAAI,aAAa,CAClC,CAAC,CACH,CAAC,CACH,CAAC,EAEHf,GACG,SAAS,aAAc,WAAW,EAClC,OACC,uDACA,4EACA,IACF,EACC,OACC,sCACA,mEACA,EACF,EACC,OAAO,2CAA4C,kDAAkD,EACrG,OAAO,MAAOc,EAAUX,IAAY,CACnC,GAAM,CAAE,uBAAAe,EAAwB,8BAAAC,EAA+B,gBAAAZ,CAAgB,EAAIJ,EAC7EY,EAAOC,GAAQT,GAAmB,QAAQ,IAAI,EAAGO,CAAQ,EACzDN,EAAU,MAAMC,EAAoBN,CAAO,EAEjD,MAAMiB,GAAWL,EAAM,OAAO,SAASG,EAAwB,EAAE,EAAGV,EAASW,CAA6B,CAC5G,CAAC,EAEH,eAAeC,GACbL,EACAG,EACAV,EACAW,EACe,CACf,IAAIE,EAAyB,CAAC,EACxBC,EAAaC,GAAiBR,CAAI,EAClCS,EAAKC,GAAgB,CACzB,MAAOH,CACT,CAAC,EAED,cAAiBI,KAAQF,EAAI,CAC3B,IAAMG,EAAWC,GAAcF,EAAMP,CAA6B,EAClEE,EAAQ,KAAK,CACX,SAAUM,EACV,QAAS,CACP,OAAQ,OACR,IAAKA,EAAS,YAChB,CACF,CAAC,EACGN,EAAQ,OAASH,IAA2B,IAC9C,MAAMW,GAAiBR,EAASb,CAAO,EACvCa,EAAU,CAAC,EAEf,CACIA,EAAQ,OAAS,GACnB,MAAMQ,GAAiBR,EAASb,CAAO,CAE3C,CAEA,eAAeqB,GAAiBR,EAAwBb,EAAuC,EAC9E,MAAMA,EAAQ,aAAa,CACxC,aAAc,SACd,KAAM,cACN,MAAOa,CACT,CAAC,GAEM,OAAO,QAASS,GAAgB,CACrCC,EAAYD,EAAY,QAAQ,CAClC,CAAC,CACH,CAEA,SAASF,GAAcI,EAAoBb,EAAkD,CAC3F,IAAMQ,EAAW,KAAK,MAAMK,CAAU,EAEtC,OAAIb,EACKc,GAAsCN,CAAQ,EAGhDA,CACT,CAEA,SAASM,GAAsCN,EAA8B,CAC3E,OAAIA,EAAS,eAAiB,uBACrBO,GAAmDP,CAAQ,EAE7DA,CACT,CAEA,SAASO,GAAmDP,EAAsD,CAChH,OAAKA,EAAS,WACZA,EAAS,SAAWQ,GAAwB,GAG9CR,EAAS,MAAM,QAASS,GAAmC,CACpDA,GAAM,mBACTA,EAAK,iBAAmBD,GAAwB,EAEpD,CAAC,EAEMR,CACT,CC3IA,OAAS,qBAAAU,GAAmB,cAAAC,OAAkB,gBEC9C,OAAS,WAAAC,OAAuB,WCDhC,OAAS,cAAAC,OAAkB,gBAC3B,OAAOC,OAAW,aGDlB,OAAOC,OAAS,WLQT,IAAeC,GAAf,cAA+B,WAAY,CAOhD,iBACEC,EACAC,EACAC,EACM,CACN,MAAM,iBAAiBF,EAAMC,EAAUC,CAAO,CAChD,CAOA,oBACEF,EACAC,EACAC,EACM,CACN,MAAM,oBAAoBF,EAAMC,EAAUC,CAAO,CACnD,CACF,EIhCO,IAAMC,GAAN,cAA8B,KAAM,CAIzC,YAAYC,EAA2BC,EAAqB,CAC1D,MAAM,SAAS,EACf,KAAK,WAAaD,EAClB,KAAK,QAAUC,CACjB,CACF,EAEaC,GAAN,cAA4B,KAAM,CAGvC,YAAYC,EAAc,CACxB,MAAM,OAAO,EACb,KAAK,MAAQA,CACf,CACF,EAEaC,GAAN,cAA4B,KAAM,CACvC,aAAc,CACZ,MAAM,OAAO,CACf,CACF,EFXaC,GAAN,cAA4BC,EAAQ,CAOzC,YAAYC,EAAoBC,EAAmB,QAASC,EAAe,GAAO,CAChF,MAAM,EAJR,KAAQ,OAAmB,CAAC,EAC5B,KAAiB,aAAsC,CAAC,EAKtD,KAAK,OAASF,EACd,KAAK,SAAWC,EAChB,KAAK,aAAeC,EAEpBF,EAAO,GAAG,OAASG,GAAiB,CAClC,GAAI,CAEF,GADA,KAAK,WAAWA,CAAI,EAChBA,EAAK,GAAG,EAAE,IAAM,IAAMA,EAAK,GAAG,EAAE,IAAM,GAAI,CAC5C,IAAMC,EAAS,OAAO,OAAO,KAAK,MAAM,EAClCC,EAAgBD,EAAO,SAAS,EAAGA,EAAO,OAAS,CAAC,EACpDE,EAAgBC,GAAM,OAAOF,EAAe,KAAK,QAAQ,EACzDX,EAAUc,GAAW,MAAMF,CAAa,EAC9C,KAAK,cAAc,IAAId,GAAgB,KAAME,CAAO,CAAC,EACrD,KAAK,YAAY,CACnB,CACF,OAASe,EAAK,CACZ,KAAK,cAAc,IAAId,GAAcc,CAAY,CAAC,CACpD,CACF,CAAC,EAEDT,EAAO,GAAG,QAAUS,GAAQ,CAC1B,KAAK,YAAY,EACjB,KAAK,cAAc,IAAId,GAAcc,CAAG,CAAC,CAC3C,CAAC,EAEDT,EAAO,GAAG,MAAO,IAAM,CACrB,KAAK,MAAM,CACb,CAAC,EAED,KAAK,iBAAiB,UAAYU,GAAU,CACtCR,GACF,KAAK,KAAKQ,EAAM,QAAQ,SAAS,CAAE,QAAS,IAAK,CAAC,CAAC,EAGrD,IAAMC,EAAO,KAAK,aAAa,MAAM,EAErC,GAAI,CAACA,EAAM,CACT,KAAK,cACH,IAAIhB,GACF,IAAI,MAAM,2EAA2Ee,EAAM,OAAO,EAAE,CACtG,CACF,EACA,MACF,CAEAC,EAAK,UAAUD,EAAM,OAAO,CAC9B,CAAC,CACH,CAEQ,SAASE,EAAmBC,EAAsC,CACxE,KAAK,aAAa,KAAKA,CAAS,EAChC,IAAMC,EAAcF,EAAM,SAAS,EAC7BG,EAAcR,GAAM,OAAOO,EAAa,KAAK,QAAQ,EACrDE,EAAe,OAAO,MAAMD,EAAY,OAAS,CAAC,EACxDC,EAAa,UAAU,GAAI,CAAC,EAC5BD,EAAY,KAAKC,EAAc,CAAC,EAChCA,EAAa,UAAU,GAAID,EAAY,OAAS,CAAC,EACjDC,EAAa,UAAU,GAAID,EAAY,OAAS,CAAC,EACjD,KAAK,OAAO,MAAMC,CAAY,CAChC,CAEA,KAAKJ,EAAyB,CAC5B,KAAK,SAASA,EAAO,CAAE,QAASA,CAAM,CAAC,CACzC,CAEA,MAAM,YAAYK,EAAsC,CACtD,OAAO,IAAI,QAAoB,CAACC,EAASC,IAAW,CAClD,IAAMN,EAAY,CAAE,QAASI,EAAK,QAAAC,EAAS,OAAAC,CAAO,EAClD,KAAK,SAASF,EAAKJ,CAAS,CAC9B,CAAC,CACH,CAEA,OAAc,CACZ,KAAK,OAAO,IAAI,EAChB,KAAK,OAAO,QAAQ,EACpB,KAAK,cAAc,IAAIhB,EAAe,CACxC,CAEQ,WAAWM,EAAoB,CACrC,KAAK,OAAO,KAAKA,CAAI,CACvB,CAEQ,aAAoB,CAC1B,KAAK,OAAS,CAAC,CACjB,CACF,EDjGaiB,GAAN,cAAwBrB,EAAQ,CAUrC,YAAYsB,EAA2B,CACrC,MAAM,EACN,KAAK,QAAUA,EACf,KAAK,KAAO,KAAK,QAAQ,KACzB,KAAK,KAAO,KAAK,QAAQ,KACzB,KAAK,SAAW,KAAK,QAAQ,SAC7B,KAAK,UAAY,KAAK,QAAQ,WAAa,GAC3C,KAAK,eAAiB,KAAK,QAAQ,gBAAkB,GACvD,CAEA,SAAkC,CAEhC,OAAI,KAAK,WACA,QAAQ,QAAQ,KAAK,UAAU,GAIpC,KAAK,SACP,KAAK,OAAO,mBAAmB,EAC/B,KAAK,OAAO,QAAQ,EACpB,KAAK,OAAS,QAGT,IAAI,QAAQ,CAACH,EAASC,IAAW,CAEtC,KAAK,OAASG,GAAQ,CACpB,KAAM,KAAK,KACX,KAAM,KAAK,KACX,UAAW,KAAK,SAClB,CAAC,EAGG,KAAK,eAAiB,IACxB,KAAK,OAAO,WAAW,KAAK,cAAc,EAG1C,KAAK,OAAO,GAAG,UAAW,IAAM,CAC9B,IAAM1B,EAAQ,IAAI,MAAM,4BAA4B,KAAK,cAAc,IAAI,EACvE,KAAK,SACP,KAAK,OAAO,QAAQ,EACpB,KAAK,OAAS,QAEhBuB,EAAOvB,CAAK,CACd,CAAC,GAIH,KAAK,OAAO,GAAG,UAAW,IAAM,CAC9B,GAAI,CAAC,KAAK,OACR,OAIF,IAAIH,EACJ,KAAK,WAAaA,EAAa,IAAIK,GAAc,KAAK,OAAQ,KAAK,QAAQ,EAG3E,KAAK,OAAO,WAAW,CAAC,EAGxBL,EAAW,iBAAiB,QAAS,IAAM,CACzC,KAAK,OAAS,OACd,KAAK,cAAc,IAAII,EAAe,CACxC,CAAC,EAEDJ,EAAW,iBAAiB,QAAUiB,GAAU,CAC9C,KAAK,cAAc,IAAIf,GAAce,EAAM,KAAK,CAAC,CACnD,CAAC,EAEDQ,EAAQ,KAAK,UAAU,CACzB,CAAC,EAGD,KAAK,OAAO,GAAG,QAAUT,GAAQ,CAC3B,KAAK,SACP,KAAK,OAAO,QAAQ,EACpB,KAAK,OAAS,QAEhBU,EAAOV,CAAG,CACZ,CAAC,CACH,CAAC,EACH,CAEA,MAAM,KAAKQ,EAAgC,CACzC,OAAQ,MAAM,KAAK,QAAQ,GAAG,KAAKA,CAAG,CACxC,CAEA,MAAM,YAAYA,EAAsC,CACtD,OAAQ,MAAM,KAAK,QAAQ,GAAG,YAAYA,CAAG,CAC/C,CAEA,OAAc,CAER,KAAK,SACP,KAAK,OAAO,mBAAmB,EAC/B,KAAK,OAAO,QAAQ,EACpB,KAAK,OAAS,QAIZ,KAAK,aACP,KAAK,WAAW,MAAM,EACtB,OAAO,KAAK,WAEhB,CACF,EI9HaM,GAAN,KAAgB,CAIrB,YAAYC,EAA8C,CACxD,KAAK,QAAUA,CACjB,CAEA,MAAMC,EAAcxB,EAAmBC,EAAe,GAAa,CACjE,IAAMwB,EAASC,GAAI,aAAc3B,GAAW,CAC1C,IAAMP,EAAa,IAAIK,GAAcE,EAAQC,EAAUC,CAAY,EACnE,KAAK,QAAQT,CAAU,CACzB,CAAC,EAEDiC,EAAO,OAAOD,CAAI,EAClB,KAAK,OAASC,CAChB,CAEA,MAAM,MAAsB,CAC1B,OAAO,IAAI,QAAc,CAACR,EAASC,IAAW,CAC5C,GAAI,CAAC,KAAK,OAAQ,CAChBA,EAAO,IAAI,MAAM,gDAAgD,CAAC,EAClE,MACF,CACA,KAAK,OAAO,MAAOV,GAAQ,CACzB,GAAIA,EAAK,CACPU,EAAOV,CAAG,EACV,MACF,CACAS,EAAQ,CACV,CAAC,EACD,KAAK,OAAS,MAChB,CAAC,CACH,CACF,ENnCA,OAAS,gBAAAU,OAAoB,UAG7B,IAAMC,GAAO,IAAIC,EAAe,MAAM,EACnC,YAAY,iCAAiC,EAC7C,SAAS,SAAU,yCAAyC,EAC5D,SAAS,SAAU,6BAA6B,EAChD,SAAS,SAAU,2BAA2B,EAC9C,OAAO,qBAAsB,+BAA+B,EAC5D,OAAO,gBAAiB,kCAAkC,EAC1D,OAAO,wBAAyB,qBAAqB,EACrD,OAAO,MAAOC,EAAMC,EAAMC,EAAMC,IAAY,CAO3C,GANIA,EAAQ,gBACVD,EAAOE,GAAyB,EACvBD,EAAQ,OACjBD,EAAOG,GAAaF,EAAQ,KAAM,MAAM,GAGtC,CAACD,EACH,MAAM,IAAI,MAAM,0BAA0B,EAG5C,IAAMI,EAAS,IAAIC,GAAU,CAC3B,KAAAP,EACA,KAAM,OAAO,SAASC,EAAM,EAAE,EAC9B,SAAUE,EAAQ,QACpB,CAAC,EAED,GAAI,CACF,IAAMK,EAAW,MAAMF,EAAO,YAAYG,GAAW,MAAMP,CAAI,CAAC,EAChE,QAAQ,IAAIM,EAAS,SAAS,EAAE,WAAW,KAAM;AAAA,CAAI,CAAC,CACxD,QAAE,CACAF,EAAO,MAAM,CACf,CACF,CAAC,EAEGI,GAAS,IAAIX,EAAe,QAAQ,EACvC,YAAY,8BAA8B,EAC1C,SAAS,QAAQ,EACjB,OAAO,wBAAyB,qBAAqB,EACrD,OAAO,MAAOE,EAAME,IAAY,CAChB,IAAIQ,GAAWC,GAAe,CAC3CA,EAAW,iBAAiB,UAAW,CAAC,CAAE,QAAAC,CAAQ,IAAM,CACtD,QAAQ,IAAIA,EAAQ,SAAS,EAAE,WAAW,KAAM;AAAA,CAAI,CAAC,EACrDD,EAAW,KAAKC,EAAQ,SAAS,CAAC,CACpC,CAAC,CACH,CAAC,EAEM,MAAM,OAAO,SAASZ,EAAM,EAAE,EAAGE,EAAQ,QAAQ,EACxD,QAAQ,IAAI,qBAAuBF,CAAI,CACzC,CAAC,EAEUa,GAAM,IAAIf,EAAe,KAAK,EAC3CgB,EAAcD,GAAKhB,EAAI,EACvBiB,EAAcD,GAAKJ,EAAM,EAElB,SAASN,IAAmC,CACjD,IAAMY,EAAMC,GAAkB,IAAI,IAAM,EAClCC,EAAY,KAAK,IAAI,EAAE,SAAS,EACtC,MAAO,2CAA2CF,CAAG,aAAaE,CAAS;AAAA,UACnEF,CAAG;AAAA;AAAA,gHAGb,COjEA,OAAS,eAAAG,OAAmB,UAC5B,OAAS,WAAAC,OAAe,UACxB,OAAS,WAAAC,OAAe,YAIxB,IAAMC,GAAa,IAAIC,EAAe,KAAK,EACrCC,GAAgB,IAAID,EAAe,QAAQ,EAC3CE,GAAe,IAAIF,EAAe,MAAM,EACxCG,GAAkB,IAAIH,EAAe,UAAU,EAExCI,GAAU,IAAIJ,EAAe,SAAS,EACnDK,EAAcD,GAASL,EAAU,EACjCM,EAAcD,GAASH,EAAa,EACpCI,EAAcD,GAASF,EAAY,EACnCG,EAAcD,GAASD,EAAe,EAEtCJ,GACG,SAAS,gBAAiB,qBAAqB,EAC/C,YAAY,sFAAsF,EAClG,OAAO,MAAOO,EAAaC,IAAY,CACtCC,GAAYF,EAAaC,CAAO,CAClC,CAAC,EAEHN,GACG,SAAS,gBAAiB,qBAAqB,EAC/C,YAAY,0BAA0B,EACtC,OAAO,MAAOK,GAAgB,CACb,IAAIG,EAAkBH,CAAW,EACzC,UAAU,UAAW,MAAS,EACtC,QAAQ,IAAI,GAAGA,CAAW,kBAAkB,CAC9C,CAAC,EAEHJ,GAAa,YAAY,yBAAyB,EAAE,OAAO,SAAY,CACrE,IAAMQ,EAAMC,GAAQC,GAAQ,EAAG,UAAU,EACnCC,EAAQC,GAAYJ,CAAG,EACvBK,EAAqB,CAAC,EAC5BF,EAAM,QAASG,GAAS,CACtB,IAAMC,EAAWD,EAAK,MAAM,GAAG,EAAE,CAAC,EAE5BZ,EADU,IAAIK,EAAkBQ,CAAQ,EACtB,UAAU,SAAS,EACvCb,GACFW,EAAY,KAAK,CAAE,YAAaE,EAAU,QAAAb,CAAQ,CAAC,CAEvD,CAAC,EACD,QAAQ,IAAIW,CAAW,CACzB,CAAC,EAEDZ,GACG,SAAS,gBAAiB,qBAAqB,EAC/C,YAAY,qBAAqB,EACjC,OAAO,MAAOG,GAAgB,CAC7B,IAAMF,EAAUc,GAAYZ,CAAW,EACvC,QAAQ,IAAIF,CAAO,CACrB,CAAC,ECrDH,OAAS,UAAAe,OAAc,YAIvB,IAAMC,GAAqB,IAAIC,EAAe,MAAM,EAC9CC,GAAwB,IAAID,EAAe,SAAS,EACpDE,GAAuB,IAAIF,EAAe,QAAQ,EAClDG,GAAuB,IAAIH,EAAe,QAAQ,EAE3CI,GAAU,IAAIJ,EAAe,SAAS,EACnDK,EAAcD,GAASL,EAAkB,EACzCM,EAAcD,GAASH,EAAqB,EAC5CI,EAAcD,GAASF,EAAoB,EAC3CG,EAAcD,GAASD,EAAoB,EAE3CJ,GAAmB,YAAY,0BAA0B,EAAE,OAAO,MAAOO,GAAY,CACnF,IAAMC,EAAU,MAAMC,EAAoBF,CAAO,EACjDG,GAAYF,CAAO,CACrB,CAAC,EAED,SAASE,GAAYF,EAA8B,CAGjD,IAAMG,EAFSH,EAAQ,UAAU,EAG9B,IAAKI,GAAsB,GAAGA,EAAM,QAAQ,OAAO,KAAKA,EAAM,QAAQ,SAAS,GAAG,EAClF,KAAK;AAAA;AAAA,CAAM,EAEd,QAAQ,IAAID,CAAQ,CACtB,CAEAT,GAAsB,YAAY,8BAA8B,EAAE,OAAO,MAAOK,GAAY,CAE1F,IAAMK,GADU,MAAMH,EAAoBF,CAAO,GAC3B,eAAe,EACrC,GAAI,CAACK,EACH,MAAM,IAAI,MAAM,mDAAmD,EAErE,QAAQ,IAAI,GAAGA,EAAM,QAAQ,OAAO,KAAKA,EAAM,QAAQ,SAAS,GAAG,CACrE,CAAC,EAEDT,GACG,YAAY,mDAAmD,EAC/D,SAAS,aAAa,EACtB,OAAO,MAAOU,EAAWN,IAAY,CACpC,IAAMC,EAAU,MAAMC,EAAoBF,CAAO,EACjD,MAAMO,GAAcN,EAASK,CAAS,CACxC,CAAC,EAEHT,GACG,YAAY,sFAAsF,EAClG,UAAU,gCAAgC,EAC1C,OAAO,eAAgB,sDAAsD,EAC7E,OAAO,UAAW,0CAA0C,EAC5D,UACC,IAAIW,GAAO,oBAAqB,cAAc,EAC3C,QAAQ,CAAC,eAAgB,UAAW,eAAe,CAAC,EACpD,QAAQ,cAAc,CAC3B,EACC,OAAO,MAAOC,EAAWC,EAAUC,EAAOX,IAAY,CACrD,IAAMC,EAAU,MAAMC,EAAoBF,CAAO,EAC3CK,EAAQJ,EAAQ,eAAe,EACrC,GAAI,CAACI,EACH,MAAM,IAAI,MAAM,mDAAmD,EAErE,GAAI,CAACA,GAAO,SAAS,UACnB,MAAM,IAAI,MAAM,sCAAsC,EAGxD,IAAMC,EAAYD,EAAM,QAAQ,UAAU,MAAM,GAAG,EAAE,CAAC,EAChDO,EAA4B,CAChC,aAAcZ,EAAQ,KACtB,UAAAS,EACA,SAAAC,EACA,MAAAC,EACA,UAAW,CAAC,CAACX,EAAQ,UACrB,MAAO,CAAC,CAACA,EAAQ,KACnB,EACA,MAAMa,GAAWP,EAAWM,EAAYX,CAAO,CACjD,CAAC,EAEH,eAAeM,GAAcN,EAAwBK,EAAkC,CAErF,IAAMD,EADSJ,EAAQ,UAAU,EACZ,KAAMI,GAAsBA,EAAM,QAAQ,WAAW,SAASC,CAAS,CAAC,EAC7F,GAAI,CAACD,EACH,MAAM,IAAI,MAAM,WAAWC,CAAS,+DAA+D,EAErG,MAAML,EAAQ,eAAeI,CAAK,EAClC,QAAQ,IAAI,uBAAuBC,CAAS;AAAA,CAAI,CAClD,CAEA,eAAeO,GAAWP,EAAmBM,EAA2BX,EAAuC,CAC7G,MAAMA,EAAQ,OAAOK,EAAWM,CAAU,EACtCA,EAAW,WACb,QAAQ,IAAI,YAAY,EAE1B,QAAQ,IAAI,uDAAuD,CACrE,CChGA,OAAS,8BAAAE,OAAiD,gBAInD,IAAMC,GAAe,IAAIC,EAAe,QAAQ,EAC1CC,GAAM,IAAID,EAAe,KAAK,EAC9BE,GAAQ,IAAIF,EAAe,OAAO,EAClCG,GAAO,IAAIH,EAAe,MAAM,EAChCI,GAAM,IAAIJ,EAAe,KAAK,EAE3CD,GAAa,SAAS,QAAS,cAAc,EAAE,OAAO,MAAOM,EAAKC,IAAY,CAC5E,IAAMC,EAAU,MAAMC,EAAoBF,CAAO,EACjDG,EAAY,MAAMF,EAAQ,OAAOG,GAASH,EAASF,CAAG,CAAC,CAAC,CAC1D,CAAC,EAEDJ,GACG,SAAS,QAAS,cAAc,EAChC,OAAO,mBAAoB,4CAA4C,EACvE,OAAO,MAAOI,EAAKC,IAAY,CAC9B,IAAMC,EAAU,MAAMC,EAAoBF,CAAO,EAC3CK,EAAW,MAAMJ,EAAQ,IAAIG,GAASH,EAASF,CAAG,CAAC,EACrDC,EAAQ,cACVG,EAAYG,GAA2BD,CAAQ,CAAC,EAEhDF,EAAYE,CAAQ,CAExB,CAAC,EAEHT,GAAM,UAAU,cAAc,EAAE,OAAO,MAAOG,EAAKQ,EAAMP,IAAY,CACnE,IAAMC,EAAU,MAAMC,EAAoBF,CAAO,EAEjDG,EAAY,MAAMF,EAAQ,MAAMG,GAASH,EAASF,CAAG,EAAGS,GAAUD,CAAI,CAAC,CAAC,CAC1E,CAAC,EAEDV,GACG,UAAU,cAAc,EACxB,OAAO,iBAAkB,2CAA2C,EACpE,OAAO,MAAOE,EAAKQ,EAAMP,IAAY,CACpC,IAAMC,EAAU,MAAMC,EAAoBF,CAAO,EAE3CS,EAAUT,EAAQ,YAAc,CAAE,OAAQ,eAAgB,EAAI,OACpEG,EAAY,MAAMF,EAAQ,KAAKG,GAASH,EAASF,CAAG,EAAGS,GAAUD,CAAI,EAAG,OAAW,CAAE,QAAAE,CAAQ,CAAC,CAAC,CACjG,CAAC,EAEHX,GAAI,UAAU,cAAc,EAAE,OAAO,MAAOC,EAAKQ,EAAMP,IAAY,CACjE,IAAMC,EAAU,MAAMC,EAAoBF,CAAO,EAEjDG,EAAY,MAAMF,EAAQ,IAAIG,GAASH,EAASF,CAAG,EAAGS,GAAUD,CAAI,CAAC,CAAC,CACxE,CAAC,EAED,SAASC,GAAUE,EAAgC,CACjD,GAAKA,EAGL,GAAI,CACF,OAAO,KAAK,MAAMA,CAAK,CACzB,MAAe,CACb,OAAOA,CACT,CACF,CAEO,SAASN,GAASH,EAAwBS,EAAuB,CAEtE,MADsB,CAAC,SAAU,QAAS,SAAS,EACjC,KAAMC,GAAMD,EAAM,WAAWC,CAAC,CAAC,EAExCD,EAIFT,EAAQ,QAAQS,CAAK,EAAE,SAAS,CACzC,CtDxDA,eAAsBE,GAAKC,EAA+B,CACxD,IAAMC,EAAQ,IAAIC,EAAe,SAAS,EACvC,YAAY,+BAA+B,EAC3C,OAAO,yBAA0B,uBAAuB,EACxD,OAAO,iCAAkC,2BAA2B,EACpE,OAAO,uBAAwB,wCAAwC,EACvE,OAAO,yBAA0B,yDAAyD,EAC1F,OAAO,iCAAkC,6DAA6D,EACtG,OAAO,4CAA6C,mDAAmD,EACvG,OAAO,kBAAmB,WAAW,EACrC,OAAO,+BAAgC,gDAAgD,EACvF,OAAO,+BAAgC,0CAA0C,EACjF,OAAO,sBAAuB,gCAAgC,EAC9D,OAAO,wBAAyB,iCAAiC,EACjE,OAAO,oBAAqB,+BAA+B,EAC3D,OAAO,sCAAuC,oCAAoC,EAClF,OAAO,0BAA2B,cAAc,EAChD,OAAO,eAAgB,gBAAgB,EACvC,UACC,IAAIC,GAAO,yBAA0B,wBAAwB,EAAE,QAAQ,CACrE,QACA,qBACA,qBACA,aACA,iBACA,eACF,CAAC,CACH,EACC,GAAG,iBAAkB,IAAM,CAC1B,QAAQ,IAAI,QAAU,GACxB,CAAC,EAGHF,EAAM,aAAa,EACnBA,EAAM,QAAQG,EAAe,EAC7BH,EAAM,cAAc,CAAE,kBAAmB,EAAK,CAAC,EAG/CI,EAAcJ,EAAOK,EAAK,EAC1BD,EAAcJ,EAAOM,EAAM,EAC3BF,EAAcJ,EAAOO,EAAK,EAG1BH,EAAcJ,EAAOQ,EAAG,EACxBJ,EAAcJ,EAAOS,EAAI,EACzBL,EAAcJ,EAAOU,EAAK,EAC1BN,EAAcJ,EAAOW,EAAG,EACxBP,EAAcJ,EAAOY,EAAY,EAGjCR,EAAcJ,EAAOa,EAAO,EAG5BT,EAAcJ,EAAOc,EAAI,EAGzBV,EAAcJ,EAAOe,EAAG,EAGxBX,EAAcJ,EAAOgB,CAAK,EAG1BZ,EAAcJ,EAAOiB,EAAgB,EACrCb,EAAcJ,EAAOkB,EAAkB,EACvCd,EAAcJ,EAAOmB,EAAkB,EAGvCf,EAAcJ,EAAOoB,EAAO,EAG5BhB,EAAcJ,EAAOqB,GAAgB,CAAC,EAGtCjB,EAAcJ,EAAOsB,EAAG,EAExB,GAAI,CACF,MAAMtB,EAAM,WAAWD,CAAI,CAC7B,OAASwB,EAAK,CACZC,GAAYD,CAAY,CAC1B,CACF,CAEO,SAASC,GAAYD,EAAmC,CAC7D,IAAIE,EAAW,EACXC,EAAc,GAUlB,GATIH,aAAeI,KAIZ,QAAQ,IAAI,UACfD,EAAc,IAEhBD,EAAWF,EAAI,UAEbE,IAAa,GAAKC,EAAa,CACjCE,GAAmBL,EAAK,CAAC,CAAC,QAAQ,IAAI,OAAO,EAC7C,IAAMM,EAAQN,EAAI,MAClB,GAAI,QAAQ,IAAI,QACd,GAAI,MAAM,QAAQM,CAAK,EACrB,QAAWN,KAAOM,EAChBD,GAAmBL,EAAK,EAAI,OAErBM,aAAiB,OAC1BD,GAAmBC,EAAO,EAAI,CAGpC,CACA,QAAQ,KAAKJ,CAAQ,CACvB,CAEA,SAASG,GAAmBL,EAAcO,EAAU,GAAa,CAC/D,GAAIA,EAAS,CACX,QAAQ,MAAMP,CAAG,EACjB,MACF,CACIA,aAAeI,GACjB,QAAQ,OAAO,MAAM,GAAGI,GAAqBR,CAAG,CAAC;AAAA,CAAI,EAErD,QAAQ,OAAO,MAAM,UAAUQ,GAAqBR,CAAG,CAAC;AAAA,CAAI,CAEhE,CAEA,eAAsBS,IAAqB,CACzCC,GAAO,OAAO,EACd,MAAMnC,GAAK,QAAQ,IAAI,CACzB,CAEIoC,GAAQ,OAAS,QACnBF,GAAI,EAAE,MAAOT,GAAQ,CACnB,QAAQ,MAAM,mBAAoBQ,GAAqBR,CAAG,CAAC,EAC3D,QAAQ,KAAK,CAAC,CAChB,CAAC", "names": ["require_constants", "__commonJSMin", "exports", "module", "SEMVER_SPEC_VERSION", "MAX_SAFE_INTEGER", "MAX_SAFE_COMPONENT_LENGTH", "MAX_SAFE_BUILD_LENGTH", "RELEASE_TYPES", "require_debug", "__commonJSMin", "exports", "module", "debug", "args", "require_re", "__commonJSMin", "exports", "module", "MAX_SAFE_COMPONENT_LENGTH", "MAX_SAFE_BUILD_LENGTH", "MAX_LENGTH", "debug", "re", "safeRe", "src", "safeSrc", "t", "R", "LETTERDASHNUMBER", "safeRegexReplacements", "makeSafeRegex", "value", "token", "max", "createToken", "name", "isGlobal", "safe", "index", "require_parse_options", "__commonJSMin", "exports", "module", "looseOption", "emptyOpts", "parseOptions", "options", "require_identifiers", "__commonJSMin", "exports", "module", "numeric", "compareIdentifiers", "a", "b", "anum", "bnum", "rcompareIdentifiers", "require_semver", "__commonJSMin", "exports", "module", "debug", "MAX_LENGTH", "MAX_SAFE_INTEGER", "re", "t", "parseOptions", "compareIdentifiers", "Se<PERSON><PERSON><PERSON>", "_SemVer", "version", "options", "m", "id", "num", "other", "i", "a", "b", "release", "identifier", "identifierBase", "match", "base", "prerelease", "require_parse", "__commonJSMin", "exports", "module", "Se<PERSON><PERSON><PERSON>", "parse", "version", "options", "throwErrors", "er", "require_valid", "__commonJSMin", "exports", "module", "parse", "valid", "version", "options", "v", "require_clean", "__commonJSMin", "exports", "module", "parse", "clean", "version", "options", "s", "require_inc", "__commonJSMin", "exports", "module", "Se<PERSON><PERSON><PERSON>", "inc", "version", "release", "options", "identifier", "identifierBase", "require_diff", "__commonJSMin", "exports", "module", "parse", "diff", "version1", "version2", "v1", "v2", "comparison", "v1<PERSON><PERSON><PERSON>", "highVersion", "lowVersion", "highHasPre", "prefix", "require_major", "__commonJSMin", "exports", "module", "Se<PERSON><PERSON><PERSON>", "major", "a", "loose", "require_minor", "__commonJSMin", "exports", "module", "Se<PERSON><PERSON><PERSON>", "minor", "a", "loose", "require_patch", "__commonJSMin", "exports", "module", "Se<PERSON><PERSON><PERSON>", "patch", "a", "loose", "require_prerelease", "__commonJSMin", "exports", "module", "parse", "prerelease", "version", "options", "parsed", "require_compare", "__commonJSMin", "exports", "module", "Se<PERSON><PERSON><PERSON>", "compare", "a", "b", "loose", "require_rcompare", "__commonJSMin", "exports", "module", "compare", "rcompare", "a", "b", "loose", "require_compare_loose", "__commonJSMin", "exports", "module", "compare", "compareLoose", "a", "b", "require_compare_build", "__commonJSMin", "exports", "module", "Se<PERSON><PERSON><PERSON>", "compareBuild", "a", "b", "loose", "versionA", "versionB", "require_sort", "__commonJSMin", "exports", "module", "compareBuild", "sort", "list", "loose", "a", "b", "require_rsort", "__commonJSMin", "exports", "module", "compareBuild", "rsort", "list", "loose", "a", "b", "require_gt", "__commonJSMin", "exports", "module", "compare", "gt", "a", "b", "loose", "require_lt", "__commonJSMin", "exports", "module", "compare", "lt", "a", "b", "loose", "require_eq", "__commonJSMin", "exports", "module", "compare", "eq", "a", "b", "loose", "require_neq", "__commonJSMin", "exports", "module", "compare", "neq", "a", "b", "loose", "require_gte", "__commonJSMin", "exports", "module", "compare", "gte", "a", "b", "loose", "require_lte", "__commonJSMin", "exports", "module", "compare", "lte", "a", "b", "loose", "require_cmp", "__commonJSMin", "exports", "module", "eq", "neq", "gt", "gte", "lt", "lte", "cmp", "a", "op", "b", "loose", "require_coerce", "__commonJSMin", "exports", "module", "Se<PERSON><PERSON><PERSON>", "parse", "re", "t", "coerce", "version", "options", "match", "coerceRtlRegex", "next", "major", "minor", "patch", "prerelease", "build", "require_lrucache", "__commonJSMin", "exports", "module", "L<PERSON><PERSON><PERSON>", "key", "value", "firstKey", "require_range", "__commonJSMin", "exports", "module", "SPACE_CHARACTERS", "Range", "_Range", "range", "options", "parseOptions", "Comparator", "r", "c", "first", "isNullSet", "isAny", "i", "comps", "k", "memoKey", "FLAG_INCLUDE_PRERELEASE", "FLAG_LOOSE", "cached", "cache", "loose", "hr", "re", "t", "hyphen<PERSON>eplace", "debug", "comparator<PERSON><PERSON>Replace", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caretTrimReplace", "rangeList", "comp", "parseComparator", "replaceGTE0", "rangeMap", "comparators", "result", "thisComparators", "isSatisfiable", "rangeComparators", "thisComparator", "rangeComparator", "version", "Se<PERSON><PERSON><PERSON>", "testSet", "LRU", "remainingComparators", "testComparator", "otherComparator", "replaceCarets", "replaceTildes", "replaceXRanges", "replaceStars", "isX", "id", "replaceTilde", "_", "M", "m", "p", "pr", "ret", "replaceCaret", "z", "replaceXRange", "gtlt", "xM", "xm", "xp", "anyX", "incPr", "$0", "from", "fM", "fm", "fp", "fpr", "fb", "to", "tM", "tm", "tp", "tpr", "set", "allowed", "require_comparator", "__commonJSMin", "exports", "module", "ANY", "Comparator", "_Comparator", "comp", "options", "parseOptions", "debug", "re", "t", "m", "Se<PERSON><PERSON><PERSON>", "version", "cmp", "Range", "require_satisfies", "__commonJSMin", "exports", "module", "Range", "satisfies", "version", "range", "options", "require_to_comparators", "__commonJSMin", "exports", "module", "Range", "toComparators", "range", "options", "comp", "c", "require_max_satisfying", "__commonJSMin", "exports", "module", "Se<PERSON><PERSON><PERSON>", "Range", "maxSatisfying", "versions", "range", "options", "max", "maxSV", "rangeObj", "v", "require_min_satisfying", "__commonJSMin", "exports", "module", "Se<PERSON><PERSON><PERSON>", "Range", "minSatisfying", "versions", "range", "options", "min", "minSV", "rangeObj", "v", "require_min_version", "__commonJSMin", "exports", "module", "Se<PERSON><PERSON><PERSON>", "Range", "gt", "minVersion", "range", "loose", "minver", "i", "comparators", "setMin", "comparator", "compver", "require_valid", "__commonJSMin", "exports", "module", "Range", "validRange", "range", "options", "require_outside", "__commonJSMin", "exports", "module", "Se<PERSON><PERSON><PERSON>", "Comparator", "ANY", "Range", "satisfies", "gt", "lt", "lte", "gte", "outside", "version", "range", "hilo", "options", "gtfn", "ltefn", "ltfn", "comp", "ecomp", "i", "comparators", "high", "low", "comparator", "require_gtr", "__commonJSMin", "exports", "module", "outside", "gtr", "version", "range", "options", "require_ltr", "__commonJSMin", "exports", "module", "outside", "ltr", "version", "range", "options", "require_intersects", "__commonJSMin", "exports", "module", "Range", "intersects", "r1", "r2", "options", "require_simplify", "__commonJSMin", "exports", "module", "satisfies", "compare", "versions", "range", "options", "set", "first", "prev", "v", "a", "b", "version", "ranges", "min", "max", "simplified", "original", "require_subset", "__commonJSMin", "exports", "module", "Range", "Comparator", "ANY", "satisfies", "compare", "subset", "sub", "dom", "options", "sawNonNull", "OUTER", "simpleSub", "simpleDom", "isSub", "simpleSubset", "minimumVersionWithPreRelease", "minimumVersion", "eqSet", "gt", "lt", "c", "higherGT", "lowerLT", "gtltComp", "eq", "higher", "lower", "hasDomLT", "hasDomGT", "needDomLTPre", "needDomGTPre", "a", "b", "comp", "require_semver", "__commonJSMin", "exports", "module", "internalRe", "constants", "Se<PERSON><PERSON><PERSON>", "identifiers", "parse", "valid", "clean", "inc", "diff", "major", "minor", "patch", "prerelease", "compare", "rcompare", "compareLoose", "compareBuild", "sort", "rsort", "gt", "lt", "eq", "neq", "gte", "lte", "cmp", "coerce", "Comparator", "Range", "satisfies", "toComparators", "maxSatisfying", "minSatisfying", "minVersion", "validRange", "outside", "gtr", "ltr", "intersects", "simplifyRange", "subset", "MEDPLUM_VERSION", "normalizeErrorString", "Commander<PERSON><PERSON><PERSON>", "Option", "dotenv", "ContentType", "isOk", "isUUID", "Option", "MedplumClient", "ClientStorage", "existsSync", "mkdirSync", "readFileSync", "writeFileSync", "homedir", "resolve", "FileSystemStorage", "profile", "key", "value", "data", "str", "createMedplumClient", "options", "setupCredentials", "profileName", "storage", "FileSystemStorage", "profile", "baseUrl", "fhirUrlPath", "accessToken", "tokenUrl", "authorizeUrl", "clientId", "clientSecret", "getClientValues", "fetchApi", "medplumClient", "MedplumClient", "onUnauthenticated", "storageOptions", "ContentType", "encodeBase64", "Command", "<PERSON><PERSON><PERSON>", "encoder", "decoder", "MAX_INT32", "concat", "buffers", "size", "acc", "length", "buf", "i", "buffer", "encode", "input", "<PERSON><PERSON><PERSON>", "JOSEError", "message", "options", "__publicField", "JOSENotSupported", "JOSEError", "__publicField", "JWSInvalid", "JOSEError", "__publicField", "JWTInvalid", "_a", "_b", "JWKSMultipleMatchingKeys", "JOSEError", "message", "options", "__publicField", "util", "is_key_object_default", "obj", "crypto", "util", "webcrypto", "webcrypto_default", "isCryptoKey", "key", "unusable", "name", "prop", "isAlgorithm", "algorithm", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "hash", "getNamedCurve", "alg", "checkUsage", "key", "usages", "expected", "msg", "last", "checkSigCryptoKey", "message", "msg", "actual", "types", "last", "invalid_key_input_default", "withAlg", "alg", "is_key_like_default", "key", "is_key_object_default", "isCryptoKey", "types", "webcrypto_default", "isDisjoint", "headers", "sources", "acc", "header", "parameters", "parameter", "is_disjoint_default", "isObjectLike", "value", "isObject", "input", "proto", "KeyObject", "isJWK", "key", "isObject", "isPrivateJWK", "isPublicJWK", "isSecretJWK", "namedCurveToJOSE", "namedCurve", "JOSENotSupported", "getNamedCurve", "kee", "raw", "key", "isCryptoKey", "KeyObject", "is_key_object_default", "isJWK", "invalid_key_input_default", "types", "get_named_curve_default", "KeyObject", "check_key_length_default", "key", "alg", "modulus<PERSON>ength", "tag", "key", "jwkMatchesOp", "alg", "usage", "symmetricTypeCheck", "allowJwk", "isJWK", "isSecretJWK", "is_key_like_default", "withAlg", "types", "asymmetricType<PERSON><PERSON>ck", "isPrivateJWK", "isPublicJWK", "checkKeyType", "check_key_type_default", "checkKeyTypeWithJwk", "validateCrit", "Err", "<PERSON><PERSON><PERSON><PERSON>", "recognizedOption", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "input", "recognized", "parameter", "JOSENotSupported", "validate_crit_default", "dsaDigest", "alg", "JOSENotSupported", "constants", "KeyObject", "ecCurveAlgMap", "keyForCrypto", "alg", "key", "asymmetricKeyType", "asymmetricKeyDetails", "isJWK", "KeyObject", "options", "check_key_length_default", "hashAlgorithm", "mgf1HashAlgorithm", "saltLength", "length", "constants", "actual", "get_named_curve_default", "expected", "JOSENotSupported", "crypto", "promisify", "hmacDigest", "alg", "JOSENotSupported", "KeyObject", "createSecretKey", "getSignVerifyKey", "alg", "key", "usage", "invalid_key_input_default", "types", "createSecretKey", "KeyObject", "isCryptoKey", "checkSigCryptoKey", "isJWK", "oneShotSign", "promisify", "sign", "alg", "key", "data", "k", "getSignVerifyKey", "hmac", "hmacDigest", "dsaDigest", "keyForCrypto", "sign_default", "epoch_default", "date", "REGEX", "secs_default", "str", "matched", "value", "unit", "numericDate", "FlattenedSign", "payload", "__publicField", "<PERSON><PERSON><PERSON><PERSON>", "unprotectedHeader", "key", "options", "JWSInvalid", "is_disjoint_default", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "extensions", "validate_crit_default", "b64", "alg", "checkKeyTypeWithJwk", "encoder", "encode", "data", "concat", "signature", "sign_default", "jws", "decoder", "CompactSign", "payload", "__publicField", "FlattenedSign", "<PERSON><PERSON><PERSON><PERSON>", "key", "options", "jws", "validateInput", "label", "input", "ProduceJWT", "payload", "__publicField", "isObject", "issuer", "subject", "audience", "jwtId", "epoch_default", "secs_default", "SignJWT", "ProduceJWT", "__publicField", "<PERSON><PERSON><PERSON><PERSON>", "key", "options", "sig", "CompactSign", "encoder", "JWTInvalid", "createHmac", "createPrivateKey", "randomBytes", "existsSync", "readFileSync", "writeFileSync", "basename", "extname", "resolve", "isPromise", "extract", "<PERSON><PERSON><PERSON><PERSON>", "input", "saveBot", "medplum", "botConfig", "bot", "codePath", "code", "readFileContents", "sourceCode", "basename", "getCodeContentType", "updateResult", "deployBot", "deployResult", "createBot", "botName", "projectId", "sourceFile", "distFile", "runtimeVersion", "writeConfig", "body", "newBot", "addBotToConfig", "readBotConfigs", "regExBotName", "escapeRegex", "botConfigs", "readConfig", "b", "getConfigFileName", "tagName", "options", "parts", "configFileName", "config", "writeFileSync", "resolve", "fileName", "content", "readServerConfig", "path", "existsSync", "readFileSync", "str", "safeTarExtractor", "destinationDir", "fileCount", "totalSize", "extract", "_path", "entry", "getUnsupportedExtension", "filename", "ext", "extname", "ContentType", "saveProfile", "profileName", "storage", "FileSystemStorage", "optionsObject", "loadProfile", "jwtBearer<PERSON><PERSON><PERSON>", "medplum", "profile", "header", "currentTimestamp", "data", "encodedHeader", "encodeBase64", "encodedData", "token", "signature", "createHmac", "signedToken", "jwtAssertionLogin", "privateKey", "createPrivateKey", "readFileSync", "resolve", "jwt", "SignJWT", "randomBytes", "addSubcommand", "command", "subcommand", "MedplumCommand", "Command", "fn", "wrappedFn", "withMergedOptions", "option", "args", "expectedArgsCount", "actionArgs", "result", "isPromise", "agentStatusCommand", "MedplumCommand", "agentPingCommand", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "agentReloadConfigCommand", "agentUpgradeCommand", "agent", "addSubcommand", "Option", "agentIds", "options", "callAgentBulkOperation", "response", "statusEntry", "parseParameterValues", "ipOrDomain", "agentId", "medplum", "createMedplumClient", "agent<PERSON>ef", "resolveAgentReference", "count", "pingResult", "ContentType", "err", "deviceId", "message", "pushResult", "operation", "parseSuccessfulResponse", "normalized", "parseEitherIdsOrCriteria", "usedCriteria", "searchParams", "result", "url", "successfulResponses", "failedResponses", "responses", "parseAgentBulkOpBundle", "isOk", "successfulRows", "row", "failedRows", "issue", "usedId", "assertValidAgentCriteria", "bundle", "entry", "parseAgentBulkOpParameters", "params", "p", "paramNames", "map", "requiredParams", "optionalParams", "paramName", "paramsParam", "valueProp", "prop", "value", "extractValueFromParametersParameter", "id", "isUUID", "criteria", "invalidCriteriaMsg", "resourceType", "queryStr", "ContentType", "getDisplayString", "MEDPLUM_CLI_CLIENT_ID", "normalizeErrorString", "exec", "createServer", "platform", "clientId", "MEDPLUM_CLI_CLIENT_ID", "redirectUri", "login", "MedplumCommand", "whoami", "token", "options", "profileName", "profile", "saveProfile", "medplum", "createMedplumClient", "startLogin", "printMe", "medplumAuthorizationCodeLogin", "jwtBearer<PERSON><PERSON><PERSON>", "jwtAssertionLogin", "startWebServer", "server", "createServer", "req", "res", "url", "code", "ContentType", "getDisplayString", "err", "normalizeErrorString", "openBrowser", "os", "platform", "cmd", "resolve", "reject", "exec", "error", "_", "stderr", "loginState", "loginUrl", "RESET", "BOLD", "RED", "GREEN", "YELLOW", "BLUE", "color", "text", "processDescription", "desc", "_", "semver", "CloudFormationClient", "DescribeStackResourcesCommand", "DescribeStacksCommand", "paginateListStacks", "CloudFrontClient", "CreateInvalidationCommand", "ECSClient", "S3Client", "GetParameterCommand", "PutParameterCommand", "SSMClient", "GetCallerIdentityCommand", "STSClient", "normalizeErrorString", "fetch", "readdirSync", "readline", "terminal", "initTerminal", "closeTerminal", "print", "text", "header", "ask", "defaultValue", "resolve", "answer", "choose", "options", "str", "o", "chooseInt", "yesOrNo", "checkOk", "cloudFormationClient", "CloudFormationClient", "cloudFrontClient", "CloudFrontClient", "ecsClient", "ECSClient", "s3Client", "S3Client", "<PERSON><PERSON><PERSON>", "getAllStacks", "listResult", "paginator", "paginateListStacks", "page", "stack", "getStackByTag", "tag", "stackSummaries", "stackSummary", "stackName", "details", "getStackDetails", "result", "buildStackDetails", "client", "describeStacksCommand", "DescribeStacksCommand", "medplumTag", "stackResources", "DescribeStackResourcesCommand", "resource", "assignStackDetails", "printStackDetails", "getEcsServiceName", "createInvalidation", "distributionId", "response", "CreateInvalidationCommand", "getServerVersions", "from", "versions", "fetch", "release", "a", "b", "writeParameters", "region", "prefix", "params", "SSMClient", "key", "value", "name", "valueStr", "existingValue", "readParameter", "print", "checkOk", "writeParameter", "command", "GetParameterCommand", "err", "PutParameterCommand", "printConfigNotFound", "tagName", "options", "getConfigFileName", "entries", "files", "readdirSync", "f", "file", "printStackNotFound", "STSClient", "GetCallerIdentityCommand", "normalizeErrorString", "describeStacksCommand", "tag", "details", "getStackByTag", "printStackNotFound", "printStackDetails", "ACMClient", "ListCertificatesCommand", "RequestCertificateCommand", "CloudFrontClient", "CreatePublicKeyCommand", "GetCallerIdentityCommand", "STSClient", "normalizeErrorString", "generateKeyPairSync", "randomUUID", "existsSync", "getDomainSetting", "domain", "getDomainCertSetting", "initStackCommand", "config", "initTerminal", "header", "print", "currentAccountId", "getAccountId", "checkOk", "ask", "configFileName", "existsSync", "writeConfig", "defaultStackName", "supportEmail", "chooseInt", "yesOrNo", "latestVersion", "getServerVersions", "<PERSON><PERSON><PERSON>", "generateSigningKey", "allCerts", "listAllCertificates", "region", "certName", "arn", "processCert", "serverParams", "writeParameters", "serverConfigFileName", "getConfigFileName", "closeTerminal", "client", "STSClient", "command", "GetCallerIdentityCommand", "err", "result", "listCertificates", "usEast1Result", "ACMClient", "ListCertificatesCommand", "domainName", "existingCert", "cert", "requestCert", "validationMethod", "choose", "RequestCertificateCommand", "keyName", "passphrase", "randomUUID", "generateKeyPairSync", "CloudFrontClient", "CreatePublicKeyCommand", "normalizeErrorString", "listStacksCommand", "stackSummaries", "getAllStacks", "stackSummary", "stackName", "details", "getStackDetails", "printStackDetails", "PutObjectCommand", "ContentType", "fastGlob", "fetch", "createReadStream", "mkdtempSync", "readdirSync", "readFileSync", "rmSync", "writeFileSync", "tmpdir", "join", "sep", "pipeline", "updateAppCommand", "tag", "options", "config", "readConfig", "printConfigNotFound", "details", "getStackByTag", "printStackNotFound", "appBucket", "tmpDir", "version", "downloadNpmPackage", "replaceVariables", "uploadAppToS3", "createInvalidation", "getNpmPackageMetadata", "packageName", "url", "fetch", "tarballUrl", "mkdtempSync", "join", "tmpdir", "response", "extractor", "safeTarExtractor", "pipeline", "error", "rmSync", "folderName", "replacements", "item", "readdirSync", "itemPath", "replaceVariablesInFile", "fileName", "contents", "readFileSync", "placeholder", "replacement", "writeFileSync", "bucketName", "uploadPatterns", "ContentType", "uploadPattern", "uploadFolderToS3", "items", "fastGlob", "uploadFileToS3", "filePath", "fileStream", "createReadStream", "s3Key", "sep", "putObjectParams", "s3Client", "PutObjectCommand", "GetBucketPolicyCommand", "PutBucketPolicyCommand", "updateBucketPoliciesCommand", "tag", "options", "readConfig", "printConfigNotFound", "details", "getStackByTag", "printStackNotFound", "updateBucketPolicy", "friendlyName", "bucketResource", "distributionResource", "oaiResource", "bucketName", "oaiId", "bucketPolicy", "getPolicy", "policyHasStatement", "addPolicyStatement", "setPolicy", "createInvalidation", "policyResponse", "s3Client", "GetBucketPolicyCommand", "policy", "PutBucketPolicyCommand", "s", "updateConfigCommand", "tag", "options", "initTerminal", "infraConfig", "readConfig", "printConfigNotFound", "serverConfig", "readServerConfig", "serverConfigFileName", "getConfigFileName", "color", "yesOrNo", "checkConfigConflicts", "mergeConfigs", "print", "writeParameters", "closeTerminal", "checkConflict", "a", "b", "message", "isConflict", "semver", "spawnSync", "updateServerCommand", "tag", "options", "client", "createMedplumClient", "config", "readConfig", "getConfigFileName", "printConfigNotFound", "separatorIndex", "serverImagePrefix", "initialVersion", "getCurrentVersion", "updateVersion", "nextUpdateVersion", "deployServerUpdate", "medplum", "sep", "currentVersion", "targetVersion", "allVersions", "getServerVersions", "latestVersion", "v", "configFile", "writeConfig", "cmd", "deploy", "spawnSync", "buildAwsCommand", "aws", "MedplumCommand", "initStackCommand", "listStacksCommand", "describeStacksCommand", "processDescription", "color", "updateConfigCommand", "addSubcommand", "updateServerCommand", "updateAppCommand", "updateBucketPoliciesCommand", "botSaveCommand", "MedplumCommand", "botDeployCommand", "botCreateCommand", "bot", "addSubcommand", "saveBotDeprecate", "deployBotDeprecate", "createBotDeprecate", "botName", "options", "medplum", "createMedplumClient", "botWrapper", "projectId", "sourceFile", "distFile", "createBot", "deploy", "botConfigs", "readBotConfigs", "errors", "errored", "saved", "deployed", "botConfig", "saveBot", "deployBot", "err", "createReadStream", "writeFile", "resolve", "createInterface", "bulkExportCommand", "MedplumCommand", "bulkImportCommand", "bulk", "addSubcommand", "options", "exportLevel", "types", "since", "targetDirectory", "medplum", "createMedplumClient", "type", "url", "fileUrl", "data", "fileName", "path", "resolve", "writeFile", "numResourcesPerRequest", "addExtensionsForMissingValues", "importFile", "entries", "fileStream", "createReadStream", "rl", "createInterface", "line", "resource", "parseResource", "sendBatchEntries", "resultEntry", "<PERSON><PERSON><PERSON><PERSON>", "jsonString", "addExtensionsForMissingValuesResource", "addExtensionsForMissingValuesExplanationOfBenefits", "getUnsupportedExtension", "item", "formatHl7DateTime", "Hl7Message", "connect", "Hl7Message", "iconv", "net", "Hl7Base", "type", "listener", "options", "Hl7MessageEvent", "connection", "message", "Hl7ErrorEvent", "error", "Hl7CloseEvent", "Hl7Connection", "Hl7Base", "socket", "encoding", "enhancedMode", "data", "buffer", "contentBuffer", "contentString", "iconv", "Hl7Message", "err", "event", "next", "reply", "queueItem", "replyString", "replyB<PERSON>er", "outputBuffer", "msg", "resolve", "reject", "Hl7Client", "options", "connect", "Hl7Server", "handler", "port", "server", "net", "readFileSync", "send", "MedplumCommand", "host", "port", "body", "options", "generateSampleHl7Message", "readFileSync", "client", "u", "response", "Hl7Message", "listen", "f", "connection", "message", "hl7", "addSubcommand", "now", "formatHl7DateTime", "controlId", "readdirSync", "homedir", "resolve", "setProfile", "MedplumCommand", "removeProfile", "listProfiles", "describeProfile", "profile", "addSubcommand", "profileName", "options", "saveProfile", "FileSystemStorage", "dir", "resolve", "homedir", "files", "readdirSync", "allProfiles", "file", "fileName", "loadProfile", "Option", "projectListCommand", "MedplumCommand", "projectCurrentCommand", "projectSwitchCommand", "projectInviteCommand", "project", "addSubcommand", "options", "medplum", "createMedplumClient", "projectList", "projects", "login", "projectId", "switchProject", "Option", "firstName", "lastName", "email", "inviteBody", "inviteUser", "convertToTransactionBundle", "deleteObject", "MedplumCommand", "get", "patch", "post", "put", "url", "options", "medplum", "createMedplumClient", "<PERSON><PERSON><PERSON><PERSON>", "cleanUrl", "response", "convertToTransactionBundle", "body", "parseBody", "headers", "input", "p", "main", "argv", "index", "MedplumCommand", "Option", "MEDPLUM_VERSION", "addSubcommand", "login", "whoami", "token", "get", "post", "patch", "put", "deleteObject", "project", "bulk", "bot", "agent", "saveBotDeprecate", "deployBotDeprecate", "createBotDeprecate", "profile", "buildAwsCommand", "hl7", "err", "handleError", "exitCode", "should<PERSON><PERSON>t", "Commander<PERSON><PERSON><PERSON>", "writeErrorToStderr", "cause", "verbose", "normalizeErrorString", "run", "dotenv", "__require"]}