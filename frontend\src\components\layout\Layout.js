import React from 'react';
import { Outlet } from 'react-router-dom';
import Navbar from './Navbar';
import { useAuth } from '../../context/AuthContext';

const Layout = () => {
  const { currentUser } = useAuth();

  if (!currentUser) {
    return <Outlet />;
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <Navbar />
      <main className="pb-10">
        <Outlet />
      </main>
    </div>
  );
};

export default Layout;
