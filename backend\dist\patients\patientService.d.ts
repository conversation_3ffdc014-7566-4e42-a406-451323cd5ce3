/**
 * @file patientService.ts
 * @description This file contains the service layer for patient-related operations using FHIR's built-in fields
 */
import { MedplumClient } from '@medplum/core';
import { PatientCreate, PatientUpdate, PatientRead } from './patientSchemas';
/**
 * Create a new patient in Medplum
 */
export declare function createPatient(client: MedplumClient, patientIn: PatientCreate): Promise<PatientRead>;
/**
 * Get a patient by ID from Medplum
 */
export declare function getPatient(client: MedplumClient, patientId: string): Promise<PatientRead>;
/**
 * Update a patient in Medplum
 */
export declare function updatePatient(client: MedplumClient, patientId: string, patientIn: PatientUpdate): Promise<PatientRead>;
/**
 * Delete a patient from Medplum
 */
export declare function deletePatient(client: MedplumClient, patientId: string): Promise<void>;
/**
 * Search for patients with pagination
 */
export declare function searchPatients(client: MedplumClient, params?: {
    search?: string;
    limit?: number;
    offset?: number;
    gender?: string;
    active?: boolean;
}): Promise<{
    patients: PatientRead[];
    total: number;
    hasMore: boolean;
}>;
//# sourceMappingURL=patientService.d.ts.map