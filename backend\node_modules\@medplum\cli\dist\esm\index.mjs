#!/usr/bin/env node
var _s=Object.create;var pt=Object.defineProperty;var Us=Object.getOwnPropertyDescriptor;var js=Object.getOwnPropertyNames;var Fs=Object.getPrototypeOf,Bs=Object.prototype.hasOwnProperty;var Ws=(t,e,r)=>e in t?pt(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var mr=(t=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(t,{get:(e,r)=>(typeof require<"u"?require:e)[r]}):t)(function(t){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+t+'" is not supported')});var w=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports);var Ks=(t,e,r,o)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of js(e))!Bs.call(t,n)&&n!==r&&pt(t,n,{get:()=>e[n],enumerable:!(o=Us(e,n))||o.enumerable});return t};var fr=(t,e,r)=>(r=t!=null?_s(Fs(t)):{},Ks(e||!t||!t.__esModule?pt(r,"default",{value:t,enumerable:!0}):r,t));var v=(t,e,r)=>Ws(t,typeof e!="symbol"?e+"":e,r);var ge=w((zm,so)=>{"use strict";var Qi="2.0.0",ea=Number.MAX_SAFE_INTEGER||9007199254740991,ta=16,ra=250,oa=["major","premajor","minor","preminor","patch","prepatch","prerelease"];so.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:ta,MAX_SAFE_BUILD_LENGTH:ra,MAX_SAFE_INTEGER:ea,RELEASE_TYPES:oa,SEMVER_SPEC_VERSION:Qi,FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}});var ye=w((Ym,io)=>{"use strict";var na=typeof process=="object"&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...t)=>console.error("SEMVER",...t):()=>{};io.exports=na});var re=w((U,ao)=>{"use strict";var{MAX_SAFE_COMPONENT_LENGTH:Dt,MAX_SAFE_BUILD_LENGTH:sa,MAX_LENGTH:ia}=ge(),aa=ye();U=ao.exports={};var ca=U.re=[],la=U.safeRe=[],d=U.src=[],ua=U.safeSrc=[],m=U.t={},pa=0,Mt="[a-zA-Z0-9-]",da=[["\\s",1],["\\d",ia],[Mt,sa]],ma=t=>{for(let[e,r]of da)t=t.split(`${e}*`).join(`${e}{0,${r}}`).split(`${e}+`).join(`${e}{1,${r}}`);return t},E=(t,e,r)=>{let o=ma(e),n=pa++;aa(t,n,e),m[t]=n,d[n]=e,ua[n]=o,ca[n]=new RegExp(e,r?"g":void 0),la[n]=new RegExp(o,r?"g":void 0)};E("NUMERICIDENTIFIER","0|[1-9]\\d*");E("NUMERICIDENTIFIERLOOSE","\\d+");E("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${Mt}*`);E("MAINVERSION",`(${d[m.NUMERICIDENTIFIER]})\\.(${d[m.NUMERICIDENTIFIER]})\\.(${d[m.NUMERICIDENTIFIER]})`);E("MAINVERSIONLOOSE",`(${d[m.NUMERICIDENTIFIERLOOSE]})\\.(${d[m.NUMERICIDENTIFIERLOOSE]})\\.(${d[m.NUMERICIDENTIFIERLOOSE]})`);E("PRERELEASEIDENTIFIER",`(?:${d[m.NONNUMERICIDENTIFIER]}|${d[m.NUMERICIDENTIFIER]})`);E("PRERELEASEIDENTIFIERLOOSE",`(?:${d[m.NONNUMERICIDENTIFIER]}|${d[m.NUMERICIDENTIFIERLOOSE]})`);E("PRERELEASE",`(?:-(${d[m.PRERELEASEIDENTIFIER]}(?:\\.${d[m.PRERELEASEIDENTIFIER]})*))`);E("PRERELEASELOOSE",`(?:-?(${d[m.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${d[m.PRERELEASEIDENTIFIERLOOSE]})*))`);E("BUILDIDENTIFIER",`${Mt}+`);E("BUILD",`(?:\\+(${d[m.BUILDIDENTIFIER]}(?:\\.${d[m.BUILDIDENTIFIER]})*))`);E("FULLPLAIN",`v?${d[m.MAINVERSION]}${d[m.PRERELEASE]}?${d[m.BUILD]}?`);E("FULL",`^${d[m.FULLPLAIN]}$`);E("LOOSEPLAIN",`[v=\\s]*${d[m.MAINVERSIONLOOSE]}${d[m.PRERELEASELOOSE]}?${d[m.BUILD]}?`);E("LOOSE",`^${d[m.LOOSEPLAIN]}$`);E("GTLT","((?:<|>)?=?)");E("XRANGEIDENTIFIERLOOSE",`${d[m.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`);E("XRANGEIDENTIFIER",`${d[m.NUMERICIDENTIFIER]}|x|X|\\*`);E("XRANGEPLAIN",`[v=\\s]*(${d[m.XRANGEIDENTIFIER]})(?:\\.(${d[m.XRANGEIDENTIFIER]})(?:\\.(${d[m.XRANGEIDENTIFIER]})(?:${d[m.PRERELEASE]})?${d[m.BUILD]}?)?)?`);E("XRANGEPLAINLOOSE",`[v=\\s]*(${d[m.XRANGEIDENTIFIERLOOSE]})(?:\\.(${d[m.XRANGEIDENTIFIERLOOSE]})(?:\\.(${d[m.XRANGEIDENTIFIERLOOSE]})(?:${d[m.PRERELEASELOOSE]})?${d[m.BUILD]}?)?)?`);E("XRANGE",`^${d[m.GTLT]}\\s*${d[m.XRANGEPLAIN]}$`);E("XRANGELOOSE",`^${d[m.GTLT]}\\s*${d[m.XRANGEPLAINLOOSE]}$`);E("COERCEPLAIN",`(^|[^\\d])(\\d{1,${Dt}})(?:\\.(\\d{1,${Dt}}))?(?:\\.(\\d{1,${Dt}}))?`);E("COERCE",`${d[m.COERCEPLAIN]}(?:$|[^\\d])`);E("COERCEFULL",d[m.COERCEPLAIN]+`(?:${d[m.PRERELEASE]})?(?:${d[m.BUILD]})?(?:$|[^\\d])`);E("COERCERTL",d[m.COERCE],!0);E("COERCERTLFULL",d[m.COERCEFULL],!0);E("LONETILDE","(?:~>?)");E("TILDETRIM",`(\\s*)${d[m.LONETILDE]}\\s+`,!0);U.tildeTrimReplace="$1~";E("TILDE",`^${d[m.LONETILDE]}${d[m.XRANGEPLAIN]}$`);E("TILDELOOSE",`^${d[m.LONETILDE]}${d[m.XRANGEPLAINLOOSE]}$`);E("LONECARET","(?:\\^)");E("CARETTRIM",`(\\s*)${d[m.LONECARET]}\\s+`,!0);U.caretTrimReplace="$1^";E("CARET",`^${d[m.LONECARET]}${d[m.XRANGEPLAIN]}$`);E("CARETLOOSE",`^${d[m.LONECARET]}${d[m.XRANGEPLAINLOOSE]}$`);E("COMPARATORLOOSE",`^${d[m.GTLT]}\\s*(${d[m.LOOSEPLAIN]})$|^$`);E("COMPARATOR",`^${d[m.GTLT]}\\s*(${d[m.FULLPLAIN]})$|^$`);E("COMPARATORTRIM",`(\\s*)${d[m.GTLT]}\\s*(${d[m.LOOSEPLAIN]}|${d[m.XRANGEPLAIN]})`,!0);U.comparatorTrimReplace="$1$2$3";E("HYPHENRANGE",`^\\s*(${d[m.XRANGEPLAIN]})\\s+-\\s+(${d[m.XRANGEPLAIN]})\\s*$`);E("HYPHENRANGELOOSE",`^\\s*(${d[m.XRANGEPLAINLOOSE]})\\s+-\\s+(${d[m.XRANGEPLAINLOOSE]})\\s*$`);E("STAR","(<|>)?=?\\s*\\*");E("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$");E("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")});var _e=w((Zm,co)=>{"use strict";var fa=Object.freeze({loose:!0}),ha=Object.freeze({}),ga=t=>t?typeof t!="object"?fa:t:ha;co.exports=ga});var _t=w((Qm,po)=>{"use strict";var lo=/^[0-9]+$/,uo=(t,e)=>{let r=lo.test(t),o=lo.test(e);return r&&o&&(t=+t,e=+e),t===e?0:r&&!o?-1:o&&!r?1:t<e?-1:1},ya=(t,e)=>uo(e,t);po.exports={compareIdentifiers:uo,rcompareIdentifiers:ya}});var P=w((ef,fo)=>{"use strict";var Ue=ye(),{MAX_LENGTH:mo,MAX_SAFE_INTEGER:je}=ge(),{safeRe:Fe,t:Be}=re(),wa=_e(),{compareIdentifiers:oe}=_t(),Ut=class t{constructor(e,r){if(r=wa(r),e instanceof t){if(e.loose===!!r.loose&&e.includePrerelease===!!r.includePrerelease)return e;e=e.version}else if(typeof e!="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>mo)throw new TypeError(`version is longer than ${mo} characters`);Ue("SemVer",e,r),this.options=r,this.loose=!!r.loose,this.includePrerelease=!!r.includePrerelease;let o=e.trim().match(r.loose?Fe[Be.LOOSE]:Fe[Be.FULL]);if(!o)throw new TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+o[1],this.minor=+o[2],this.patch=+o[3],this.major>je||this.major<0)throw new TypeError("Invalid major version");if(this.minor>je||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>je||this.patch<0)throw new TypeError("Invalid patch version");o[4]?this.prerelease=o[4].split(".").map(n=>{if(/^[0-9]+$/.test(n)){let s=+n;if(s>=0&&s<je)return s}return n}):this.prerelease=[],this.build=o[5]?o[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(Ue("SemVer.compare",this.version,this.options,e),!(e instanceof t)){if(typeof e=="string"&&e===this.version)return 0;e=new t(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof t||(e=new t(e,this.options)),oe(this.major,e.major)||oe(this.minor,e.minor)||oe(this.patch,e.patch)}comparePre(e){if(e instanceof t||(e=new t(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let r=0;do{let o=this.prerelease[r],n=e.prerelease[r];if(Ue("prerelease compare",r,o,n),o===void 0&&n===void 0)return 0;if(n===void 0)return 1;if(o===void 0)return-1;if(o===n)continue;return oe(o,n)}while(++r)}compareBuild(e){e instanceof t||(e=new t(e,this.options));let r=0;do{let o=this.build[r],n=e.build[r];if(Ue("build compare",r,o,n),o===void 0&&n===void 0)return 0;if(n===void 0)return 1;if(o===void 0)return-1;if(o===n)continue;return oe(o,n)}while(++r)}inc(e,r,o){if(e.startsWith("pre")){if(!r&&o===!1)throw new Error("invalid increment argument: identifier is empty");if(r){let n=`-${r}`.match(this.options.loose?Fe[Be.PRERELEASELOOSE]:Fe[Be.PRERELEASE]);if(!n||n[1]!==r)throw new Error(`invalid identifier: ${r}`)}}switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",r,o);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",r,o);break;case"prepatch":this.prerelease.length=0,this.inc("patch",r,o),this.inc("pre",r,o);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",r,o),this.inc("pre",r,o);break;case"release":if(this.prerelease.length===0)throw new Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":{let n=Number(o)?1:0;if(this.prerelease.length===0)this.prerelease=[n];else{let s=this.prerelease.length;for(;--s>=0;)typeof this.prerelease[s]=="number"&&(this.prerelease[s]++,s=-2);if(s===-1){if(r===this.prerelease.join(".")&&o===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(n)}}if(r){let s=[r,n];o===!1&&(s=[r]),oe(this.prerelease[0],r)===0?isNaN(this.prerelease[1])&&(this.prerelease=s):this.prerelease=s}break}default:throw new Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}};fo.exports=Ut});var Q=w((tf,go)=>{"use strict";var ho=P(),Ea=(t,e,r=!1)=>{if(t instanceof ho)return t;try{return new ho(t,e)}catch(o){if(!r)return null;throw o}};go.exports=Ea});var wo=w((rf,yo)=>{"use strict";var Sa=Q(),Ra=(t,e)=>{let r=Sa(t,e);return r?r.version:null};yo.exports=Ra});var So=w((of,Eo)=>{"use strict";var Aa=Q(),Ia=(t,e)=>{let r=Aa(t.trim().replace(/^[=v]+/,""),e);return r?r.version:null};Eo.exports=Ia});var Io=w((nf,Ao)=>{"use strict";var Ro=P(),va=(t,e,r,o,n)=>{typeof r=="string"&&(n=o,o=r,r=void 0);try{return new Ro(t instanceof Ro?t.version:t,r).inc(e,o,n).version}catch{return null}};Ao.exports=va});var Po=w((sf,bo)=>{"use strict";var vo=Q(),ba=(t,e)=>{let r=vo(t,null,!0),o=vo(e,null,!0),n=r.compare(o);if(n===0)return null;let s=n>0,i=s?r:o,c=s?o:r,l=!!i.prerelease.length;if(!!c.prerelease.length&&!l){if(!c.patch&&!c.minor)return"major";if(c.compareMain(i)===0)return c.minor&&!c.patch?"minor":"patch"}let h=l?"pre":"";return r.major!==o.major?h+"major":r.minor!==o.minor?h+"minor":r.patch!==o.patch?h+"patch":"prerelease"};bo.exports=ba});var To=w((af,Co)=>{"use strict";var Pa=P(),Ca=(t,e)=>new Pa(t,e).major;Co.exports=Ca});var xo=w((cf,Oo)=>{"use strict";var Ta=P(),Oa=(t,e)=>new Ta(t,e).minor;Oo.exports=Oa});var No=w((lf,$o)=>{"use strict";var xa=P(),$a=(t,e)=>new xa(t,e).patch;$o.exports=$a});var ko=w((uf,Lo)=>{"use strict";var Na=Q(),La=(t,e)=>{let r=Na(t,e);return r&&r.prerelease.length?r.prerelease:null};Lo.exports=La});var N=w((pf,Mo)=>{"use strict";var Do=P(),ka=(t,e,r)=>new Do(t,r).compare(new Do(e,r));Mo.exports=ka});var Uo=w((df,_o)=>{"use strict";var Da=N(),Ma=(t,e,r)=>Da(e,t,r);_o.exports=Ma});var Fo=w((mf,jo)=>{"use strict";var _a=N(),Ua=(t,e)=>_a(t,e,!0);jo.exports=Ua});var We=w((ff,Wo)=>{"use strict";var Bo=P(),ja=(t,e,r)=>{let o=new Bo(t,r),n=new Bo(e,r);return o.compare(n)||o.compareBuild(n)};Wo.exports=ja});var qo=w((hf,Ko)=>{"use strict";var Fa=We(),Ba=(t,e)=>t.sort((r,o)=>Fa(r,o,e));Ko.exports=Ba});var Go=w((gf,Ho)=>{"use strict";var Wa=We(),Ka=(t,e)=>t.sort((r,o)=>Wa(o,r,e));Ho.exports=Ka});var we=w((yf,Vo)=>{"use strict";var qa=N(),Ha=(t,e,r)=>qa(t,e,r)>0;Vo.exports=Ha});var Ke=w((wf,Jo)=>{"use strict";var Ga=N(),Va=(t,e,r)=>Ga(t,e,r)<0;Jo.exports=Va});var jt=w((Ef,Xo)=>{"use strict";var Ja=N(),Xa=(t,e,r)=>Ja(t,e,r)===0;Xo.exports=Xa});var Ft=w((Sf,zo)=>{"use strict";var za=N(),Ya=(t,e,r)=>za(t,e,r)!==0;zo.exports=Ya});var qe=w((Rf,Yo)=>{"use strict";var Za=N(),Qa=(t,e,r)=>Za(t,e,r)>=0;Yo.exports=Qa});var He=w((Af,Zo)=>{"use strict";var ec=N(),tc=(t,e,r)=>ec(t,e,r)<=0;Zo.exports=tc});var Bt=w((If,Qo)=>{"use strict";var rc=jt(),oc=Ft(),nc=we(),sc=qe(),ic=Ke(),ac=He(),cc=(t,e,r,o)=>{switch(e){case"===":return typeof t=="object"&&(t=t.version),typeof r=="object"&&(r=r.version),t===r;case"!==":return typeof t=="object"&&(t=t.version),typeof r=="object"&&(r=r.version),t!==r;case"":case"=":case"==":return rc(t,r,o);case"!=":return oc(t,r,o);case">":return nc(t,r,o);case">=":return sc(t,r,o);case"<":return ic(t,r,o);case"<=":return ac(t,r,o);default:throw new TypeError(`Invalid operator: ${e}`)}};Qo.exports=cc});var tn=w((vf,en)=>{"use strict";var lc=P(),uc=Q(),{safeRe:Ge,t:Ve}=re(),pc=(t,e)=>{if(t instanceof lc)return t;if(typeof t=="number"&&(t=String(t)),typeof t!="string")return null;e=e||{};let r=null;if(!e.rtl)r=t.match(e.includePrerelease?Ge[Ve.COERCEFULL]:Ge[Ve.COERCE]);else{let l=e.includePrerelease?Ge[Ve.COERCERTLFULL]:Ge[Ve.COERCERTL],u;for(;(u=l.exec(t))&&(!r||r.index+r[0].length!==t.length);)(!r||u.index+u[0].length!==r.index+r[0].length)&&(r=u),l.lastIndex=u.index+u[1].length+u[2].length;l.lastIndex=-1}if(r===null)return null;let o=r[2],n=r[3]||"0",s=r[4]||"0",i=e.includePrerelease&&r[5]?`-${r[5]}`:"",c=e.includePrerelease&&r[6]?`+${r[6]}`:"";return uc(`${o}.${n}.${s}${i}${c}`,e)};en.exports=pc});var on=w((bf,rn)=>{"use strict";var Wt=class{constructor(){this.max=1e3,this.map=new Map}get(e){let r=this.map.get(e);if(r!==void 0)return this.map.delete(e),this.map.set(e,r),r}delete(e){return this.map.delete(e)}set(e,r){if(!this.delete(e)&&r!==void 0){if(this.map.size>=this.max){let n=this.map.keys().next().value;this.delete(n)}this.map.set(e,r)}return this}};rn.exports=Wt});var L=w((Pf,cn)=>{"use strict";var dc=/\s+/g,Kt=class t{constructor(e,r){if(r=fc(r),e instanceof t)return e.loose===!!r.loose&&e.includePrerelease===!!r.includePrerelease?e:new t(e.raw,r);if(e instanceof qt)return this.raw=e.value,this.set=[[e]],this.formatted=void 0,this;if(this.options=r,this.loose=!!r.loose,this.includePrerelease=!!r.includePrerelease,this.raw=e.trim().replace(dc," "),this.set=this.raw.split("||").map(o=>this.parseRange(o.trim())).filter(o=>o.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){let o=this.set[0];if(this.set=this.set.filter(n=>!sn(n[0])),this.set.length===0)this.set=[o];else if(this.set.length>1){for(let n of this.set)if(n.length===1&&Rc(n[0])){this.set=[n];break}}}this.formatted=void 0}get range(){if(this.formatted===void 0){this.formatted="";for(let e=0;e<this.set.length;e++){e>0&&(this.formatted+="||");let r=this.set[e];for(let o=0;o<r.length;o++)o>0&&(this.formatted+=" "),this.formatted+=r[o].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){let o=((this.options.includePrerelease&&Ec)|(this.options.loose&&Sc))+":"+e,n=nn.get(o);if(n)return n;let s=this.options.loose,i=s?O[C.HYPHENRANGELOOSE]:O[C.HYPHENRANGE];e=e.replace(i,$c(this.options.includePrerelease)),A("hyphen replace",e),e=e.replace(O[C.COMPARATORTRIM],gc),A("comparator trim",e),e=e.replace(O[C.TILDETRIM],yc),A("tilde trim",e),e=e.replace(O[C.CARETTRIM],wc),A("caret trim",e);let c=e.split(" ").map(f=>Ac(f,this.options)).join(" ").split(/\s+/).map(f=>xc(f,this.options));s&&(c=c.filter(f=>(A("loose invalid filter",f,this.options),!!f.match(O[C.COMPARATORLOOSE])))),A("range list",c);let l=new Map,u=c.map(f=>new qt(f,this.options));for(let f of u){if(sn(f))return[f];l.set(f.value,f)}l.size>1&&l.has("")&&l.delete("");let h=[...l.values()];return nn.set(o,h),h}intersects(e,r){if(!(e instanceof t))throw new TypeError("a Range is required");return this.set.some(o=>an(o,r)&&e.set.some(n=>an(n,r)&&o.every(s=>n.every(i=>s.intersects(i,r)))))}test(e){if(!e)return!1;if(typeof e=="string")try{e=new hc(e,this.options)}catch{return!1}for(let r=0;r<this.set.length;r++)if(Nc(this.set[r],e,this.options))return!0;return!1}};cn.exports=Kt;var mc=on(),nn=new mc,fc=_e(),qt=Ee(),A=ye(),hc=P(),{safeRe:O,t:C,comparatorTrimReplace:gc,tildeTrimReplace:yc,caretTrimReplace:wc}=re(),{FLAG_INCLUDE_PRERELEASE:Ec,FLAG_LOOSE:Sc}=ge(),sn=t=>t.value==="<0.0.0-0",Rc=t=>t.value==="",an=(t,e)=>{let r=!0,o=t.slice(),n=o.pop();for(;r&&o.length;)r=o.every(s=>n.intersects(s,e)),n=o.pop();return r},Ac=(t,e)=>(A("comp",t,e),t=bc(t,e),A("caret",t),t=Ic(t,e),A("tildes",t),t=Cc(t,e),A("xrange",t),t=Oc(t,e),A("stars",t),t),T=t=>!t||t.toLowerCase()==="x"||t==="*",Ic=(t,e)=>t.trim().split(/\s+/).map(r=>vc(r,e)).join(" "),vc=(t,e)=>{let r=e.loose?O[C.TILDELOOSE]:O[C.TILDE];return t.replace(r,(o,n,s,i,c)=>{A("tilde",t,o,n,s,i,c);let l;return T(n)?l="":T(s)?l=`>=${n}.0.0 <${+n+1}.0.0-0`:T(i)?l=`>=${n}.${s}.0 <${n}.${+s+1}.0-0`:c?(A("replaceTilde pr",c),l=`>=${n}.${s}.${i}-${c} <${n}.${+s+1}.0-0`):l=`>=${n}.${s}.${i} <${n}.${+s+1}.0-0`,A("tilde return",l),l})},bc=(t,e)=>t.trim().split(/\s+/).map(r=>Pc(r,e)).join(" "),Pc=(t,e)=>{A("caret",t,e);let r=e.loose?O[C.CARETLOOSE]:O[C.CARET],o=e.includePrerelease?"-0":"";return t.replace(r,(n,s,i,c,l)=>{A("caret",t,n,s,i,c,l);let u;return T(s)?u="":T(i)?u=`>=${s}.0.0${o} <${+s+1}.0.0-0`:T(c)?s==="0"?u=`>=${s}.${i}.0${o} <${s}.${+i+1}.0-0`:u=`>=${s}.${i}.0${o} <${+s+1}.0.0-0`:l?(A("replaceCaret pr",l),s==="0"?i==="0"?u=`>=${s}.${i}.${c}-${l} <${s}.${i}.${+c+1}-0`:u=`>=${s}.${i}.${c}-${l} <${s}.${+i+1}.0-0`:u=`>=${s}.${i}.${c}-${l} <${+s+1}.0.0-0`):(A("no pr"),s==="0"?i==="0"?u=`>=${s}.${i}.${c}${o} <${s}.${i}.${+c+1}-0`:u=`>=${s}.${i}.${c}${o} <${s}.${+i+1}.0-0`:u=`>=${s}.${i}.${c} <${+s+1}.0.0-0`),A("caret return",u),u})},Cc=(t,e)=>(A("replaceXRanges",t,e),t.split(/\s+/).map(r=>Tc(r,e)).join(" ")),Tc=(t,e)=>{t=t.trim();let r=e.loose?O[C.XRANGELOOSE]:O[C.XRANGE];return t.replace(r,(o,n,s,i,c,l)=>{A("xRange",t,o,n,s,i,c,l);let u=T(s),h=u||T(i),f=h||T(c),S=f;return n==="="&&S&&(n=""),l=e.includePrerelease?"-0":"",u?n===">"||n==="<"?o="<0.0.0-0":o="*":n&&S?(h&&(i=0),c=0,n===">"?(n=">=",h?(s=+s+1,i=0,c=0):(i=+i+1,c=0)):n==="<="&&(n="<",h?s=+s+1:i=+i+1),n==="<"&&(l="-0"),o=`${n+s}.${i}.${c}${l}`):h?o=`>=${s}.0.0${l} <${+s+1}.0.0-0`:f&&(o=`>=${s}.${i}.0${l} <${s}.${+i+1}.0-0`),A("xRange return",o),o})},Oc=(t,e)=>(A("replaceStars",t,e),t.trim().replace(O[C.STAR],"")),xc=(t,e)=>(A("replaceGTE0",t,e),t.trim().replace(O[e.includePrerelease?C.GTE0PRE:C.GTE0],"")),$c=t=>(e,r,o,n,s,i,c,l,u,h,f,S)=>(T(o)?r="":T(n)?r=`>=${o}.0.0${t?"-0":""}`:T(s)?r=`>=${o}.${n}.0${t?"-0":""}`:i?r=`>=${r}`:r=`>=${r}${t?"-0":""}`,T(u)?l="":T(h)?l=`<${+u+1}.0.0-0`:T(f)?l=`<${u}.${+h+1}.0-0`:S?l=`<=${u}.${h}.${f}-${S}`:t?l=`<${u}.${h}.${+f+1}-0`:l=`<=${l}`,`${r} ${l}`.trim()),Nc=(t,e,r)=>{for(let o=0;o<t.length;o++)if(!t[o].test(e))return!1;if(e.prerelease.length&&!r.includePrerelease){for(let o=0;o<t.length;o++)if(A(t[o].semver),t[o].semver!==qt.ANY&&t[o].semver.prerelease.length>0){let n=t[o].semver;if(n.major===e.major&&n.minor===e.minor&&n.patch===e.patch)return!0}return!1}return!0}});var Ee=w((Cf,fn)=>{"use strict";var Se=Symbol("SemVer ANY"),Vt=class t{static get ANY(){return Se}constructor(e,r){if(r=ln(r),e instanceof t){if(e.loose===!!r.loose)return e;e=e.value}e=e.trim().split(/\s+/).join(" "),Gt("comparator",e,r),this.options=r,this.loose=!!r.loose,this.parse(e),this.semver===Se?this.value="":this.value=this.operator+this.semver.version,Gt("comp",this)}parse(e){let r=this.options.loose?un[pn.COMPARATORLOOSE]:un[pn.COMPARATOR],o=e.match(r);if(!o)throw new TypeError(`Invalid comparator: ${e}`);this.operator=o[1]!==void 0?o[1]:"",this.operator==="="&&(this.operator=""),o[2]?this.semver=new dn(o[2],this.options.loose):this.semver=Se}toString(){return this.value}test(e){if(Gt("Comparator.test",e,this.options.loose),this.semver===Se||e===Se)return!0;if(typeof e=="string")try{e=new dn(e,this.options)}catch{return!1}return Ht(e,this.operator,this.semver,this.options)}intersects(e,r){if(!(e instanceof t))throw new TypeError("a Comparator is required");return this.operator===""?this.value===""?!0:new mn(e.value,r).test(this.value):e.operator===""?e.value===""?!0:new mn(this.value,r).test(e.semver):(r=ln(r),r.includePrerelease&&(this.value==="<0.0.0-0"||e.value==="<0.0.0-0")||!r.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0"))?!1:!!(this.operator.startsWith(">")&&e.operator.startsWith(">")||this.operator.startsWith("<")&&e.operator.startsWith("<")||this.semver.version===e.semver.version&&this.operator.includes("=")&&e.operator.includes("=")||Ht(this.semver,"<",e.semver,r)&&this.operator.startsWith(">")&&e.operator.startsWith("<")||Ht(this.semver,">",e.semver,r)&&this.operator.startsWith("<")&&e.operator.startsWith(">")))}};fn.exports=Vt;var ln=_e(),{safeRe:un,t:pn}=re(),Ht=Bt(),Gt=ye(),dn=P(),mn=L()});var Re=w((Tf,hn)=>{"use strict";var Lc=L(),kc=(t,e,r)=>{try{e=new Lc(e,r)}catch{return!1}return e.test(t)};hn.exports=kc});var yn=w((Of,gn)=>{"use strict";var Dc=L(),Mc=(t,e)=>new Dc(t,e).set.map(r=>r.map(o=>o.value).join(" ").trim().split(" "));gn.exports=Mc});var En=w((xf,wn)=>{"use strict";var _c=P(),Uc=L(),jc=(t,e,r)=>{let o=null,n=null,s=null;try{s=new Uc(e,r)}catch{return null}return t.forEach(i=>{s.test(i)&&(!o||n.compare(i)===-1)&&(o=i,n=new _c(o,r))}),o};wn.exports=jc});var Rn=w(($f,Sn)=>{"use strict";var Fc=P(),Bc=L(),Wc=(t,e,r)=>{let o=null,n=null,s=null;try{s=new Bc(e,r)}catch{return null}return t.forEach(i=>{s.test(i)&&(!o||n.compare(i)===1)&&(o=i,n=new Fc(o,r))}),o};Sn.exports=Wc});var vn=w((Nf,In)=>{"use strict";var Jt=P(),Kc=L(),An=we(),qc=(t,e)=>{t=new Kc(t,e);let r=new Jt("0.0.0");if(t.test(r)||(r=new Jt("0.0.0-0"),t.test(r)))return r;r=null;for(let o=0;o<t.set.length;++o){let n=t.set[o],s=null;n.forEach(i=>{let c=new Jt(i.semver.version);switch(i.operator){case">":c.prerelease.length===0?c.patch++:c.prerelease.push(0),c.raw=c.format();case"":case">=":(!s||An(c,s))&&(s=c);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${i.operator}`)}}),s&&(!r||An(r,s))&&(r=s)}return r&&t.test(r)?r:null};In.exports=qc});var Pn=w((Lf,bn)=>{"use strict";var Hc=L(),Gc=(t,e)=>{try{return new Hc(t,e).range||"*"}catch{return null}};bn.exports=Gc});var Je=w((kf,xn)=>{"use strict";var Vc=P(),On=Ee(),{ANY:Jc}=On,Xc=L(),zc=Re(),Cn=we(),Tn=Ke(),Yc=He(),Zc=qe(),Qc=(t,e,r,o)=>{t=new Vc(t,o),e=new Xc(e,o);let n,s,i,c,l;switch(r){case">":n=Cn,s=Yc,i=Tn,c=">",l=">=";break;case"<":n=Tn,s=Zc,i=Cn,c="<",l="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(zc(t,e,o))return!1;for(let u=0;u<e.set.length;++u){let h=e.set[u],f=null,S=null;if(h.forEach(p=>{p.semver===Jc&&(p=new On(">=0.0.0")),f=f||p,S=S||p,n(p.semver,f.semver,o)?f=p:i(p.semver,S.semver,o)&&(S=p)}),f.operator===c||f.operator===l||(!S.operator||S.operator===c)&&s(t,S.semver))return!1;if(S.operator===l&&i(t,S.semver))return!1}return!0};xn.exports=Qc});var Nn=w((Df,$n)=>{"use strict";var el=Je(),tl=(t,e,r)=>el(t,e,">",r);$n.exports=tl});var kn=w((Mf,Ln)=>{"use strict";var rl=Je(),ol=(t,e,r)=>rl(t,e,"<",r);Ln.exports=ol});var _n=w((_f,Mn)=>{"use strict";var Dn=L(),nl=(t,e,r)=>(t=new Dn(t,r),e=new Dn(e,r),t.intersects(e,r));Mn.exports=nl});var jn=w((Uf,Un)=>{"use strict";var sl=Re(),il=N();Un.exports=(t,e,r)=>{let o=[],n=null,s=null,i=t.sort((h,f)=>il(h,f,r));for(let h of i)sl(h,e,r)?(s=h,n||(n=h)):(s&&o.push([n,s]),s=null,n=null);n&&o.push([n,null]);let c=[];for(let[h,f]of o)h===f?c.push(h):!f&&h===i[0]?c.push("*"):f?h===i[0]?c.push(`<=${f}`):c.push(`${h} - ${f}`):c.push(`>=${h}`);let l=c.join(" || "),u=typeof e.raw=="string"?e.raw:String(e);return l.length<u.length?l:e}});var Hn=w((jf,qn)=>{"use strict";var Fn=L(),zt=Ee(),{ANY:Xt}=zt,Ae=Re(),Yt=N(),al=(t,e,r={})=>{if(t===e)return!0;t=new Fn(t,r),e=new Fn(e,r);let o=!1;e:for(let n of t.set){for(let s of e.set){let i=ll(n,s,r);if(o=o||i!==null,i)continue e}if(o)return!1}return!0},cl=[new zt(">=0.0.0-0")],Bn=[new zt(">=0.0.0")],ll=(t,e,r)=>{if(t===e)return!0;if(t.length===1&&t[0].semver===Xt){if(e.length===1&&e[0].semver===Xt)return!0;r.includePrerelease?t=cl:t=Bn}if(e.length===1&&e[0].semver===Xt){if(r.includePrerelease)return!0;e=Bn}let o=new Set,n,s;for(let p of t)p.operator===">"||p.operator===">="?n=Wn(n,p,r):p.operator==="<"||p.operator==="<="?s=Kn(s,p,r):o.add(p.semver);if(o.size>1)return null;let i;if(n&&s){if(i=Yt(n.semver,s.semver,r),i>0)return null;if(i===0&&(n.operator!==">="||s.operator!=="<="))return null}for(let p of o){if(n&&!Ae(p,String(n),r)||s&&!Ae(p,String(s),r))return null;for(let F of e)if(!Ae(p,String(F),r))return!1;return!0}let c,l,u,h,f=s&&!r.includePrerelease&&s.semver.prerelease.length?s.semver:!1,S=n&&!r.includePrerelease&&n.semver.prerelease.length?n.semver:!1;f&&f.prerelease.length===1&&s.operator==="<"&&f.prerelease[0]===0&&(f=!1);for(let p of e){if(h=h||p.operator===">"||p.operator===">=",u=u||p.operator==="<"||p.operator==="<=",n){if(S&&p.semver.prerelease&&p.semver.prerelease.length&&p.semver.major===S.major&&p.semver.minor===S.minor&&p.semver.patch===S.patch&&(S=!1),p.operator===">"||p.operator===">="){if(c=Wn(n,p,r),c===p&&c!==n)return!1}else if(n.operator===">="&&!Ae(n.semver,String(p),r))return!1}if(s){if(f&&p.semver.prerelease&&p.semver.prerelease.length&&p.semver.major===f.major&&p.semver.minor===f.minor&&p.semver.patch===f.patch&&(f=!1),p.operator==="<"||p.operator==="<="){if(l=Kn(s,p,r),l===p&&l!==s)return!1}else if(s.operator==="<="&&!Ae(s.semver,String(p),r))return!1}if(!p.operator&&(s||n)&&i!==0)return!1}return!(n&&u&&!s&&i!==0||s&&h&&!n&&i!==0||S||f)},Wn=(t,e,r)=>{if(!t)return e;let o=Yt(t.semver,e.semver,r);return o>0?t:o<0||e.operator===">"&&t.operator===">="?e:t},Kn=(t,e,r)=>{if(!t)return e;let o=Yt(t.semver,e.semver,r);return o<0?t:o>0||e.operator==="<"&&t.operator==="<="?e:t};qn.exports=al});var Qt=w((Ff,Jn)=>{"use strict";var Zt=re(),Gn=ge(),ul=P(),Vn=_t(),pl=Q(),dl=wo(),ml=So(),fl=Io(),hl=Po(),gl=To(),yl=xo(),wl=No(),El=ko(),Sl=N(),Rl=Uo(),Al=Fo(),Il=We(),vl=qo(),bl=Go(),Pl=we(),Cl=Ke(),Tl=jt(),Ol=Ft(),xl=qe(),$l=He(),Nl=Bt(),Ll=tn(),kl=Ee(),Dl=L(),Ml=Re(),_l=yn(),Ul=En(),jl=Rn(),Fl=vn(),Bl=Pn(),Wl=Je(),Kl=Nn(),ql=kn(),Hl=_n(),Gl=jn(),Vl=Hn();Jn.exports={parse:pl,valid:dl,clean:ml,inc:fl,diff:hl,major:gl,minor:yl,patch:wl,prerelease:El,compare:Sl,rcompare:Rl,compareLoose:Al,compareBuild:Il,sort:vl,rsort:bl,gt:Pl,lt:Cl,eq:Tl,neq:Ol,gte:xl,lte:$l,cmp:Nl,coerce:Ll,Comparator:kl,Range:Dl,satisfies:Ml,toComparators:_l,maxSatisfying:Ul,minSatisfying:jl,minVersion:Fl,validRange:Bl,outside:Wl,gtr:Kl,ltr:ql,intersects:Hl,simplifyRange:Gl,subset:Vl,SemVer:ul,re:Zt.re,src:Zt.src,tokens:Zt.t,SEMVER_SPEC_VERSION:Gn.SEMVER_SPEC_VERSION,RELEASE_TYPES:Gn.RELEASE_TYPES,compareIdentifiers:Vn.compareIdentifiers,rcompareIdentifiers:Vn.rcompareIdentifiers}});import{MEDPLUM_VERSION as $p,normalizeErrorString as dr}from"@medplum/core";import{CommanderError as Ds,Option as Np}from"commander";import Lp from"dotenv";import{ContentType as Xr,isOk as xi,isUUID as $i}from"@medplum/core";import{Option as Ot}from"commander";import{MedplumClient as Xs}from"@medplum/core";import{ClientStorage as qs}from"@medplum/core";import{existsSync as hr,mkdirSync as Hs,readFileSync as Gs,writeFileSync as Vs}from"node:fs";import{homedir as Js}from"node:os";import{resolve as gr}from"node:path";var D=class extends qs{constructor(e){super(),this.dirName=gr(Js(),".medplum"),this.fileName=gr(this.dirName,e+".json")}clear(){this.writeFile({})}getString(e){return this.readFile()?.[e]}setString(e,r){let o=this.readFile()??{};r?o[e]=r:delete o[e],this.writeFile(o)}getObject(e){let r=this.getString(e);return r?JSON.parse(r):void 0}setObject(e,r){this.setString(e,r?JSON.stringify(r):void 0)}readFile(){if(hr(this.fileName))return JSON.parse(Gs(this.fileName,"utf8"))}writeFile(e){hr(this.dirName)||Hs(this.dirName),Vs(this.fileName,JSON.stringify(e,null,2),"utf8")}};async function R(t,e=!0){let r=t.profile??"default",o=new D(r),n=o.getObject("options");if(r!=="default"&&!n)throw new Error(`Profile "${r}" does not exist`);let{baseUrl:s,fhirUrlPath:i,accessToken:c,tokenUrl:l,authorizeUrl:u,clientId:h,clientSecret:f}=zs(t,o),S=t.fetch??fetch,p=new Xs({fetch:S,baseUrl:s,tokenUrl:l,fhirUrlPath:i,authorizeUrl:u,storage:o,onUnauthenticated:Ys,verbose:t.verbose});return e&&(c?p.setAccessToken(c):h&&f&&(p.setBasicAuth(h,f),n?.authType!=="basic"&&await p.startClientLogin(h,f))),p}function zs(t,e){let r=e.getObject("options"),o=t.baseUrl??r?.baseUrl??process.env.MEDPLUM_BASE_URL??"https://api.medplum.com/",n=t.fhirUrlPath??r?.fhirUrlPath??process.env.MEDPLUM_FHIR_URL_PATH,s=t.accessToken??r?.accessToken??process.env.MEDPLUM_CLIENT_ACCESS_TOKEN,i=t.tokenUrl??r?.tokenUrl??process.env.MEDPLUM_TOKEN_URL,c=t.authorizeUrl??r?.authorizeUrl??process.env.MEDPLUM_AUTHORIZE_URL,l=t.clientId??r?.clientId??process.env.MEDPLUM_CLIENT_ID,u=t.clientSecret??r?.clientSecret??process.env.MEDPLUM_CLIENT_SECRET;return{baseUrl:o,fhirUrlPath:n,accessToken:s,tokenUrl:i,authorizeUrl:c,clientId:l,clientSecret:u}}function Ys(){console.log("Unauthenticated: run `npx medplum login` to sign in")}import{ContentType as It,encodeBase64 as jr}from"@medplum/core";import{Command as wi}from"commander";import{Buffer as Zs}from"node:buffer";var J=new TextEncoder,dt=new TextDecoder,Vp=2**32;function yr(...t){let e=t.reduce((n,{length:s})=>n+s,0),r=new Uint8Array(e),o=0;for(let n of t)r.set(n,o),o+=n.length;return r}var Ce=t=>Zs.from(t).toString("base64url");var X=class extends Error{constructor(r,o){super(r,o);v(this,"code","ERR_JOSE_GENERIC");this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}};v(X,"code","ERR_JOSE_GENERIC");var $=class extends X{constructor(){super(...arguments);v(this,"code","ERR_JOSE_NOT_SUPPORTED")}};v($,"code","ERR_JOSE_NOT_SUPPORTED");var B=class extends X{constructor(){super(...arguments);v(this,"code","ERR_JWS_INVALID")}};v(B,"code","ERR_JWS_INVALID");var ue=class extends X{constructor(){super(...arguments);v(this,"code","ERR_JWT_INVALID")}};v(ue,"code","ERR_JWT_INVALID");var wr,Er,mt=class extends(Er=X,wr=Symbol.asyncIterator,Er){constructor(r="multiple matching keys found in the JSON Web Key Set",o){super(r,o);v(this,wr);v(this,"code","ERR_JWKS_MULTIPLE_MATCHING_KEYS")}};v(mt,"code","ERR_JWKS_MULTIPLE_MATCHING_KEYS");import*as Sr from"node:util";var Te=t=>Sr.types.isKeyObject(t);import*as Rr from"node:crypto";import*as Ar from"node:util";var ei=Rr.webcrypto,Ir=ei,ee=t=>Ar.types.isCryptoKey(t);function M(t,e="algorithm.name"){return new TypeError(`CryptoKey does not support this operation, its ${e} must be ${t}`)}function pe(t,e){return t.name===e}function ft(t){return parseInt(t.name.slice(4),10)}function ti(t){switch(t){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw new Error("unreachable")}}function ri(t,e){if(e.length&&!e.some(r=>t.usages.includes(r))){let r="CryptoKey does not support this operation, its usages must include ";if(e.length>2){let o=e.pop();r+=`one of ${e.join(", ")}, or ${o}.`}else e.length===2?r+=`one of ${e[0]} or ${e[1]}.`:r+=`${e[0]}.`;throw new TypeError(r)}}function vr(t,e,...r){switch(e){case"HS256":case"HS384":case"HS512":{if(!pe(t.algorithm,"HMAC"))throw M("HMAC");let o=parseInt(e.slice(2),10);if(ft(t.algorithm.hash)!==o)throw M(`SHA-${o}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!pe(t.algorithm,"RSASSA-PKCS1-v1_5"))throw M("RSASSA-PKCS1-v1_5");let o=parseInt(e.slice(2),10);if(ft(t.algorithm.hash)!==o)throw M(`SHA-${o}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!pe(t.algorithm,"RSA-PSS"))throw M("RSA-PSS");let o=parseInt(e.slice(2),10);if(ft(t.algorithm.hash)!==o)throw M(`SHA-${o}`,"algorithm.hash");break}case"EdDSA":{if(t.algorithm.name!=="Ed25519"&&t.algorithm.name!=="Ed448")throw M("Ed25519 or Ed448");break}case"Ed25519":{if(!pe(t.algorithm,"Ed25519"))throw M("Ed25519");break}case"ES256":case"ES384":case"ES512":{if(!pe(t.algorithm,"ECDSA"))throw M("ECDSA");let o=ti(e);if(t.algorithm.namedCurve!==o)throw M(o,"algorithm.namedCurve");break}default:throw new TypeError("CryptoKey does not support this operation")}ri(t,r)}function br(t,e,...r){if(r=r.filter(Boolean),r.length>2){let o=r.pop();t+=`one of type ${r.join(", ")}, or ${o}.`}else r.length===2?t+=`one of type ${r[0]} or ${r[1]}.`:t+=`of type ${r[0]}.`;return e==null?t+=` Received ${e}`:typeof e=="function"&&e.name?t+=` Received function ${e.name}`:typeof e=="object"&&e!=null&&e.constructor?.name&&(t+=` Received an instance of ${e.constructor.name}`),t}var de=(t,...e)=>br("Key must be ",t,...e);function ht(t,e,...r){return br(`Key for the ${t} algorithm must be `,e,...r)}var gt=t=>Te(t)||ee(t),W=["KeyObject"];(globalThis.CryptoKey||Ir?.CryptoKey)&&W.push("CryptoKey");var oi=(...t)=>{let e=t.filter(Boolean);if(e.length===0||e.length===1)return!0;let r;for(let o of e){let n=Object.keys(o);if(!r||r.size===0){r=new Set(n);continue}for(let s of n){if(r.has(s))return!1;r.add(s)}}return!0},Pr=oi;function ni(t){return typeof t=="object"&&t!==null}function me(t){if(!ni(t)||Object.prototype.toString.call(t)!=="[object Object]")return!1;if(Object.getPrototypeOf(t)===null)return!0;let e=t;for(;Object.getPrototypeOf(e)!==null;)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e}import{KeyObject as si}from"node:crypto";function q(t){return me(t)&&typeof t.kty=="string"}function Cr(t){return t.kty!=="oct"&&typeof t.d=="string"}function Tr(t){return t.kty!=="oct"&&typeof t.d>"u"}function Or(t){return q(t)&&t.kty==="oct"&&typeof t.k=="string"}var ii=t=>{switch(t){case"prime256v1":return"P-256";case"secp384r1":return"P-384";case"secp521r1":return"P-521";case"secp256k1":return"secp256k1";default:throw new $("Unsupported key curve for this operation")}},ai=(t,e)=>{let r;if(ee(t))r=si.from(t);else if(Te(t))r=t;else{if(q(t))return t.crv;throw new TypeError(de(t,...W))}if(r.type==="secret")throw new TypeError('only "private" or "public" type keys can be used for this operation');switch(r.asymmetricKeyType){case"ed25519":case"ed448":return`Ed${r.asymmetricKeyType.slice(2)}`;case"x25519":case"x448":return`X${r.asymmetricKeyType.slice(1)}`;case"ec":{let o=r.asymmetricKeyDetails.namedCurve;return e?o:ii(o)}default:throw new TypeError("Invalid asymmetric key type for this operation")}},$r=ai;import{KeyObject as ci}from"node:crypto";var yt=(t,e)=>{let r;try{t instanceof ci?r=t.asymmetricKeyDetails?.modulusLength:r=Buffer.from(t.n,"base64url").byteLength<<3}catch{}if(typeof r!="number"||r<2048)throw new TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)};var te=t=>t?.[Symbol.toStringTag],wt=(t,e,r)=>{if(e.use!==void 0&&e.use!=="sig")throw new TypeError("Invalid key for this operation, when present its use must be sig");if(e.key_ops!==void 0&&e.key_ops.includes?.(r)!==!0)throw new TypeError(`Invalid key for this operation, when present its key_ops must include ${r}`);if(e.alg!==void 0&&e.alg!==t)throw new TypeError(`Invalid key for this operation, when present its alg must be ${t}`);return!0},li=(t,e,r,o)=>{if(!(e instanceof Uint8Array)){if(o&&q(e)){if(Or(e)&&wt(t,e,r))return;throw new TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!gt(e))throw new TypeError(ht(t,e,...W,"Uint8Array",o?"JSON Web Key":null));if(e.type!=="secret")throw new TypeError(`${te(e)} instances for symmetric algorithms must be of type "secret"`)}},ui=(t,e,r,o)=>{if(o&&q(e))switch(r){case"sign":if(Cr(e)&&wt(t,e,r))return;throw new TypeError("JSON Web Key for this operation be a private JWK");case"verify":if(Tr(e)&&wt(t,e,r))return;throw new TypeError("JSON Web Key for this operation be a public JWK")}if(!gt(e))throw new TypeError(ht(t,e,...W,o?"JSON Web Key":null));if(e.type==="secret")throw new TypeError(`${te(e)} instances for asymmetric algorithms must not be of type "secret"`);if(r==="sign"&&e.type==="public")throw new TypeError(`${te(e)} instances for asymmetric algorithm signing must be of type "private"`);if(r==="decrypt"&&e.type==="public")throw new TypeError(`${te(e)} instances for asymmetric algorithm decryption must be of type "private"`);if(e.algorithm&&r==="verify"&&e.type==="private")throw new TypeError(`${te(e)} instances for asymmetric algorithm verifying must be of type "public"`);if(e.algorithm&&r==="encrypt"&&e.type==="private")throw new TypeError(`${te(e)} instances for asymmetric algorithm encryption must be of type "public"`)};function Nr(t,e,r,o){e.startsWith("HS")||e==="dir"||e.startsWith("PBES2")||/^A\d{3}(?:GCM)?KW$/.test(e)?li(e,r,o,t):ui(e,r,o,t)}var Sd=Nr.bind(void 0,!1),Lr=Nr.bind(void 0,!0);function pi(t,e,r,o,n){if(n.crit!==void 0&&o?.crit===void 0)throw new t('"crit" (Critical) Header Parameter MUST be integrity protected');if(!o||o.crit===void 0)return new Set;if(!Array.isArray(o.crit)||o.crit.length===0||o.crit.some(i=>typeof i!="string"||i.length===0))throw new t('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');let s;r!==void 0?s=new Map([...Object.entries(r),...e.entries()]):s=e;for(let i of o.crit){if(!s.has(i))throw new $(`Extension Header Parameter "${i}" is not recognized`);if(n[i]===void 0)throw new t(`Extension Header Parameter "${i}" is missing`);if(s.get(i)&&o[i]===void 0)throw new t(`Extension Header Parameter "${i}" MUST be integrity protected`)}return new Set(o.crit)}var kr=pi;function Et(t){switch(t){case"PS256":case"RS256":case"ES256":case"ES256K":return"sha256";case"PS384":case"RS384":case"ES384":return"sha384";case"PS512":case"RS512":case"ES512":return"sha512";case"Ed25519":case"EdDSA":return;default:throw new $(`alg ${t} is not supported either by JOSE or your javascript runtime`)}}import{constants as Dr,KeyObject as di}from"node:crypto";var mi=new Map([["ES256","P-256"],["ES256K","secp256k1"],["ES384","P-384"],["ES512","P-521"]]);function St(t,e){let r,o,n;if(e instanceof di)r=e.asymmetricKeyType,o=e.asymmetricKeyDetails;else switch(n=!0,e.kty){case"RSA":r="rsa";break;case"EC":r="ec";break;case"OKP":{if(e.crv==="Ed25519"){r="ed25519";break}if(e.crv==="Ed448"){r="ed448";break}throw new TypeError("Invalid key for this operation, its crv must be Ed25519 or Ed448")}default:throw new TypeError("Invalid key for this operation, its kty must be RSA, OKP, or EC")}let s;switch(t){case"Ed25519":if(r!=="ed25519")throw new TypeError("Invalid key for this operation, its asymmetricKeyType must be ed25519");break;case"EdDSA":if(!["ed25519","ed448"].includes(r))throw new TypeError("Invalid key for this operation, its asymmetricKeyType must be ed25519 or ed448");break;case"RS256":case"RS384":case"RS512":if(r!=="rsa")throw new TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa");yt(e,t);break;case"PS256":case"PS384":case"PS512":if(r==="rsa-pss"){let{hashAlgorithm:i,mgf1HashAlgorithm:c,saltLength:l}=o,u=parseInt(t.slice(-3),10);if(i!==void 0&&(i!==`sha${u}`||c!==i))throw new TypeError(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of "alg" ${t}`);if(l!==void 0&&l>u>>3)throw new TypeError(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of "alg" ${t}`)}else if(r!=="rsa")throw new TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa or rsa-pss");yt(e,t),s={padding:Dr.RSA_PKCS1_PSS_PADDING,saltLength:Dr.RSA_PSS_SALTLEN_DIGEST};break;case"ES256":case"ES256K":case"ES384":case"ES512":{if(r!=="ec")throw new TypeError("Invalid key for this operation, its asymmetricKeyType must be ec");let i=$r(e),c=mi.get(t);if(i!==c)throw new TypeError(`Invalid key curve for the algorithm, its curve must be ${c}, got ${i}`);s={dsaEncoding:"ieee-p1363"};break}default:throw new $(`alg ${t} is not supported either by JOSE or your javascript runtime`)}return n?{format:"jwk",key:e,...s}:s?{...s,key:e}:e}import*as Oe from"node:crypto";import{promisify as fi}from"node:util";function Rt(t){switch(t){case"HS256":return"sha256";case"HS384":return"sha384";case"HS512":return"sha512";default:throw new $(`alg ${t} is not supported either by JOSE or your javascript runtime`)}}import{KeyObject as Mr,createSecretKey as _r}from"node:crypto";function At(t,e,r){if(e instanceof Uint8Array){if(!t.startsWith("HS"))throw new TypeError(de(e,...W));return _r(e)}if(e instanceof Mr)return e;if(ee(e))return vr(e,t,r),Mr.from(e);if(q(e))return t.startsWith("HS")?_r(Buffer.from(e.k,"base64url")):e;throw new TypeError(de(e,...W,"Uint8Array","JSON Web Key"))}var hi=fi(Oe.sign),gi=async(t,e,r)=>{let o=At(t,e,"sign");if(t.startsWith("HS")){let n=Oe.createHmac(Rt(t),o);return n.update(r),n.digest()}return hi(Et(t),r,St(t,o))},Ur=gi;var H=t=>Math.floor(t.getTime()/1e3);var yi=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,xe=t=>{let e=yi.exec(t);if(!e||e[4]&&e[1])throw new TypeError("Invalid time period format");let r=parseFloat(e[2]),o=e[3].toLowerCase(),n;switch(o){case"sec":case"secs":case"second":case"seconds":case"s":n=Math.round(r);break;case"minute":case"minutes":case"min":case"mins":case"m":n=Math.round(r*60);break;case"hour":case"hours":case"hr":case"hrs":case"h":n=Math.round(r*3600);break;case"day":case"days":case"d":n=Math.round(r*86400);break;case"week":case"weeks":case"w":n=Math.round(r*604800);break;default:n=Math.round(r*31557600);break}return e[1]==="-"||e[4]==="ago"?-n:n};var $e=class{constructor(e){v(this,"_payload");v(this,"_protectedHeader");v(this,"_unprotectedHeader");if(!(e instanceof Uint8Array))throw new TypeError("payload must be an instance of Uint8Array");this._payload=e}setProtectedHeader(e){if(this._protectedHeader)throw new TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setUnprotectedHeader(e){if(this._unprotectedHeader)throw new TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}async sign(e,r){if(!this._protectedHeader&&!this._unprotectedHeader)throw new B("either setProtectedHeader or setUnprotectedHeader must be called before #sign()");if(!Pr(this._protectedHeader,this._unprotectedHeader))throw new B("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let o={...this._protectedHeader,...this._unprotectedHeader},n=kr(B,new Map([["b64",!0]]),r?.crit,this._protectedHeader,o),s=!0;if(n.has("b64")&&(s=this._protectedHeader.b64,typeof s!="boolean"))throw new B('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:i}=o;if(typeof i!="string"||!i)throw new B('JWS "alg" (Algorithm) Header Parameter missing or invalid');Lr(i,e,"sign");let c=this._payload;s&&(c=J.encode(Ce(c)));let l;this._protectedHeader?l=J.encode(Ce(JSON.stringify(this._protectedHeader))):l=J.encode("");let u=yr(l,J.encode("."),c),h=await Ur(i,e,u),f={signature:Ce(h),payload:""};return s&&(f.payload=dt.decode(c)),this._unprotectedHeader&&(f.header=this._unprotectedHeader),this._protectedHeader&&(f.protected=dt.decode(l)),f}};var Ne=class{constructor(e){v(this,"_flattened");this._flattened=new $e(e)}setProtectedHeader(e){return this._flattened.setProtectedHeader(e),this}async sign(e,r){let o=await this._flattened.sign(e,r);if(o.payload===void 0)throw new TypeError("use the flattened module for creating JWS with b64: false");return`${o.protected}.${o.payload}.${o.signature}`}};function z(t,e){if(!Number.isFinite(e))throw new TypeError(`Invalid ${t} input`);return e}var Le=class{constructor(e={}){v(this,"_payload");if(!me(e))throw new TypeError("JWT Claims Set MUST be an object");this._payload=e}setIssuer(e){return this._payload={...this._payload,iss:e},this}setSubject(e){return this._payload={...this._payload,sub:e},this}setAudience(e){return this._payload={...this._payload,aud:e},this}setJti(e){return this._payload={...this._payload,jti:e},this}setNotBefore(e){return typeof e=="number"?this._payload={...this._payload,nbf:z("setNotBefore",e)}:e instanceof Date?this._payload={...this._payload,nbf:z("setNotBefore",H(e))}:this._payload={...this._payload,nbf:H(new Date)+xe(e)},this}setExpirationTime(e){return typeof e=="number"?this._payload={...this._payload,exp:z("setExpirationTime",e)}:e instanceof Date?this._payload={...this._payload,exp:z("setExpirationTime",H(e))}:this._payload={...this._payload,exp:H(new Date)+xe(e)},this}setIssuedAt(e){return typeof e>"u"?this._payload={...this._payload,iat:H(new Date)}:e instanceof Date?this._payload={...this._payload,iat:z("setIssuedAt",H(e))}:typeof e=="string"?this._payload={...this._payload,iat:z("setIssuedAt",H(new Date)+xe(e))}:this._payload={...this._payload,iat:z("setIssuedAt",e)},this}};var fe=class extends Le{constructor(){super(...arguments);v(this,"_protectedHeader")}setProtectedHeader(r){return this._protectedHeader=r,this}async sign(r,o){let n=new Ne(J.encode(JSON.stringify(this._payload)));if(n.setProtectedHeader(this._protectedHeader),Array.isArray(this._protectedHeader?.crit)&&this._protectedHeader.crit.includes("b64")&&this._protectedHeader.b64===!1)throw new ue("JWTs MUST NOT use unencoded payload");return n.sign(r,o)}};import{createHmac as Ei,createPrivateKey as Si,randomBytes as Ri}from"node:crypto";import{existsSync as Ai,readFileSync as Fr,writeFileSync as Br}from"node:fs";import{basename as Wr,extname as Ii,resolve as vt}from"node:path";import{isPromise as vi}from"node:util/types";import{extract as bi}from"tar";function K(t){console.log(JSON.stringify(t,null,2))}async function bt(t,e,r){let o=e.source,n=ke(o);if(!n)return;console.log("Saving source code...");let s=await t.createAttachment({data:n,filename:Wr(o),contentType:Ti(o)});console.log("Updating bot...");let i=await t.updateResource({...r,sourceCode:s});console.log("Success! New bot version: "+i.meta?.versionId)}async function Pt(t,e,r){let o=e.dist??e.source,n=ke(o);if(!n)return;console.log("Deploying bot...");let s=await t.post(t.fhirUrl("Bot",r.id,"$deploy"),{code:n,filename:Wr(o)});console.log("Deploy result: "+s.issue?.[0]?.details?.text)}async function Ct(t,e,r,o,n,s,i){let c={name:e,description:"",runtimeVersion:s},l=await t.post("admin/projects/"+r+"/bot",c),u=await t.readResource("Bot",l.id),h={name:e,id:l.id,source:o,dist:n};await bt(t,h,u),await Pt(t,h,u),console.log(`Success! Bot created: ${u.id}`),i&&Pi(h)}function Kr(t){let e=new RegExp("^"+Ci(t).replace(/\\\*/g,".*")+"$"),r=_()?.bots?.filter(o=>e.test(o.name));return r||[]}function k(t,e){if(e?.file)return e.file;let r=["medplum"];return t&&r.push(t),r.push("config"),e?.server&&r.push("server"),r.push("json"),r.join(".")}function b(t,e){Br(vt(t),JSON.stringify(e,void 0,2),"utf-8")}function _(t,e){let r=k(t,e),o=ke(r);if(o)return JSON.parse(o)}function qr(t){let e=ke(k(t,{server:!0}));if(e)return JSON.parse(e)}function ke(t){let e=vt(t);return Ai(e)?Fr(e,"utf8"):""}function Pi(t){let e=_()??{};e.bots||(e.bots=[]),e.bots.push(t),Br("medplum.config.json",JSON.stringify(e,null,2),"utf8"),console.log(`Bot added to config: ${t.id}`)}function Ci(t){return t.replace(/[/\-\\^$*+?.()|[\]{}]/g,"\\$&")}function Hr(t){let o=0,n=0;return bi({cwd:t,filter:(s,i)=>{if(o++,o>100)throw new Error("Tar extractor reached max number of files");if(n+=i.size,n>10485760)throw new Error("Tar extractor reached max size");return!0}})}function Tt(){return{url:"http://hl7.org/fhir/StructureDefinition/data-absent-reason",valueCode:"unsupported"}}function Ti(t){let e=Ii(t).toLowerCase();return[".cjs",".mjs",".js"].includes(e)?It.JAVASCRIPT:[".cts",".mts",".ts"].includes(e)?It.TYPESCRIPT:It.TEXT}function De(t,e){let r=new D(t),o={name:t,...e};return r.setObject("options",o),o}function Gr(t){return new D(t).getObject("options")}async function Vr(t,e){let r={typ:"JWT",alg:"HS256"},o=Math.floor(Date.now()/1e3),n={aud:`${e.baseUrl}${e.audience}`,iss:e.issuer,sub:e.subject,nbf:o,iat:o,exp:o+604800},s=jr(JSON.stringify(r)),i=jr(JSON.stringify(n)),c=`${s}.${i}`,l=Ei("sha256",e.clientSecret).update(c).digest("base64url"),u=`${c}.${l}`;await t.startJwtBearerLogin(e.clientId,u,e.scope??"")}async function Jr(t,e){let r=Si(Fr(vt(e.privateKeyPath))),o=await new fe({}).setProtectedHeader({alg:"RS384",typ:"JWT"}).setIssuer(e.clientId).setSubject(e.clientId).setAudience(`${e.baseUrl}${e.audience}`).setJti(Ri(16).toString("hex")).setIssuedAt().setExpirationTime("5m").sign(r);await t.startJwtAssertionLogin(o)}function y(t,e){e.configureHelp({showGlobalOptions:!0}),t.addCommand(e)}var g=class extends wi{action(e){let r=Oi(this,e);return super._actionHandler=r,this}resetOptionDefaults(){this._optionValues={};for(let e of this.options)e.defaultValue!==void 0&&(this._optionValues[e.attributeName()]=e.defaultValue)}};function Oi(t,e){return async r=>{let o=t.registeredArguments.length,n=r.slice(0,o);n[o]=t.optsWithGlobals();try{let s=e(...n);vi(s)&&await s}finally{t.resetOptionDefaults()}}}var zr=new g("status").aliases(["info","list","ls"]),Yr=new g("ping"),Zr=new g("push"),Qr=new g("reload-config"),eo=new g("upgrade"),Y=new g("agent");y(Y,zr);y(Y,Yr);y(Y,Zr);y(Y,Qr);y(Y,eo);zr.description("Get the status of a specified agent").argument("[agentIds...]","The ID(s) of the agent(s) to get the status of").option("--criteria <criteria>","An optional FHIR search criteria to resolve the agent to get the status of. Mutually exclusive with [agentIds...] arg").addOption(new Ot("--output <format>","An optional output format, defaults to table").choices(["table","json"]).default("table")).action(async(t,e)=>{await xt({operation:"$bulk-status",agentIds:t,options:e,parseSuccessfulResponse:r=>{let o=ki(r.result,{required:["status","version"],optional:["lastUpdated"]});return{id:r.agent.id,name:r.agent.name,enabledStatus:r.agent.status,version:o.version,connectionStatus:o.status,statusLastUpdated:o.lastUpdated??"N/A"}}})});Yr.description("Ping a host from a specified agent").argument("<ipOrDomain>","The IPv4 address or domain name to ping").argument("[agentId]","Conditionally optional ID of the agent to ping from. Mutually exclusive with --criteria <criteria> option").option("--count <count>","An optional amount of pings to issue before returning results","1").option("--criteria <criteria>","An optional FHIR search criteria to resolve the agent to ping from. Mutually exclusive with [agentId] arg").action(async(t,e,r)=>{let o=await R(r),n=await to(o,e,r),s=Number.parseInt(r.count,10);if(Number.isNaN(s))throw new Error("--count <count> must be an integer if specified");try{let i=await o.pushToAgent(n,t,`PING ${s}`,Xr.PING,!0,{maxRetries:0});console.info(i)}catch(i){throw new Error("Unexpected response from agent while pinging",{cause:i})}});Zr.description("Push a message to a target device via a specified agent").argument("<deviceId>","The ID of the device to push the message to").argument("<message>","The message to send to the destination device").argument("[agentId]","Conditionally optional ID of the agent to send the message from. Mutually exclusive with --criteria <criteria> option").option("--content-type <contentType>","The content type of the message",Xr.HL7_V2).option("--no-wait","Tells the server not to wait for a response from the destination device").option("--criteria <criteria>","An optional FHIR search criteria to resolve the agent to ping from. Mutually exclusive with [agentId] arg").action(async(t,e,r,o)=>{let n=await R(o),s=await to(n,r,o),i;try{i=await n.pushToAgent(s,{reference:`Device/${t}`},e,o.contentType,o.wait!==!1,{maxRetries:0})}catch(c){throw new Error("Unexpected response from agent while pushing message to agent",{cause:c})}console.info(i)});Qr.description("Reload the config for the specified agent(s)").argument("[agentIds...]","The ID(s) of the agent(s) for which the config should be reloaded. Mutually exclusive with --criteria <criteria> flag").option("--criteria <criteria>","An optional FHIR search criteria to resolve the agent(s) for which to notify to reload their config. Mutually exclusive with [agentIds...] arg").addOption(new Ot("--output <format>","An optional output format, defaults to table").choices(["table","json"]).default("table")).action(async(t,e)=>{await xt({operation:"$reload-config",agentIds:t,options:e,parseSuccessfulResponse:r=>({id:r.agent.id,name:r.agent.name})})});eo.description("Upgrade the version for the specified agent(s)").argument("[agentIds...]","The ID(s) of the agent(s) that should be upgraded. Mutually exclusive with --criteria <criteria> flag").option("--criteria <criteria>","An optional FHIR search criteria to resolve the agent(s) to upgrade. Mutually exclusive with [agentIds...] arg").option("--version <version>","An optional version to upgrade to. Defaults to the latest version if flag not included").addOption(new Ot("--output <format>","An optional output format, defaults to table").choices(["table","json"]).default("table")).action(async(t,e)=>{await xt({operation:"$upgrade",agentIds:t,options:e,parseSuccessfulResponse:r=>({id:r.agent.id,name:r.agent.name,version:e.version??"latest"})})});async function xt({operation:t,agentIds:e,options:r,parseSuccessfulResponse:o}){let n=Mi(e,r),s=await R(r),i=n.type==="criteria"?n.criteria:`Agent?_id=${n.ids.join(",")}`,c=new URLSearchParams(i.split("?")[1]),l;try{let p=s.fhirUrl("Agent",t);p.search=c.toString(),l=await s.get(p,{cache:"reload"})}catch(p){throw new Error(`Operation '${t}' failed`,{cause:p})}if(r.output==="json"){console.info(JSON.stringify(l,null,2));return}let u=[],h=[];switch(l.resourceType){case"Bundle":{let p=Ni(l);for(let F of p)F.result.resourceType==="Parameters"||xi(F.result)?u.push(F):h.push(F);break}case"Parameters":case"OperationOutcome":{let p=await s.searchOne("Agent",c,{cache:"reload"});if(!p)throw new Error("Agent not found");l.resourceType==="Parameters"?u.push({agent:p,result:l}):h.push({agent:p,result:l});break}default:throw new Error(`Invalid result received for '${t}' operation: ${JSON.stringify(l)}`)}let f=[];for(let p of u){let F=o(p);f.push(F)}let S=[];for(let p of h){let ut=p.result.issue?.[0],Ms={id:p.agent.id,name:p.agent.name,severity:ut.severity,code:ut.code,details:ut.details?.text??"No details to show"};S.push(Ms)}console.info(`
${f.length} successful response(s):
`),console.table(f.length?f:"No successful responses received"),console.info(),S.length&&(console.info(`${S.length} failed response(s):`),console.info(),console.table(S))}async function to(t,e,r){if(!(e||r.criteria))throw new Error("This command requires either an [agentId] or a --criteria <criteria> flag");if(e&&r.criteria)throw new Error("Ambiguous arguments and options combination; [agentId] arg and --criteria <criteria> flag are mutually exclusive");let o;if(e)o=e;else{ro(r.criteria);let n=await t.search("Agent",`${r.criteria.split("?")[1]}&_count=2`);if(!n?.entry?.length)throw new Error("Could not find an agent matching the provided criteria");if(n.entry.length!==1)throw new Error("Found more than one agent matching this criteria. This operation requires the criteria to resolve to exactly one agent");o=n.entry[0].resource?.id}return{reference:`Agent/${o}`}}function Ni(t){let e=[];for(let r of t.entry??[]){if(!r.resource)throw new Error("No Parameter resource found in entry");e.push(Li(r.resource))}return e}function Li(t){let e=t.parameter?.find(o=>o.name==="agent")?.resource;if(!e)throw new Error("Agent bulk operation response missing 'agent'");if(e.resourceType!=="Agent")throw new Error(`Agent bulk operation returned 'agent' with type '${e.resourceType}'`);let r=t.parameter?.find(o=>o.name==="result")?.resource;if(!r)throw new Error("Agent bulk operation response missing result'");if(!(r.resourceType==="Parameters"||r.resourceType==="OperationOutcome"))throw new Error(`Agent bulk operation returned 'result' with type '${r.resourceType}'`);return{agent:e,result:r}}function ki(t,e){let r={},o=e.required,n=e.optional;for(let s of o){let i=t.parameter?.find(l=>l.name===s);if(!i)throw new Error(`Failed to find parameter '${s}'`);let c;for(let l in i)if(l.startsWith("value")){if(c)throw new Error(`Found multiple values for parameter '${s}'`);c=l}if(!c)throw new Error(`Failed to find a value for parameter '${s}'`);r[s]=i[c]}if(n?.length)for(let s of n){let i=t.parameter?.find(l=>l.name===s);if(!i)continue;let c=Di(s,i);r[s]=c}return r}function Di(t,e){let r;for(let o in e)if(o.startsWith("value")){if(r)throw new Error(`Found multiple values for parameter '${t}'`);r=o}if(!r)throw new Error(`Failed to find a value for parameter '${t}'`);return e[r]}function Mi(t,e){if(!Array.isArray(t))throw new Error("Invalid agent IDs array");if(t.length){if(e.criteria)throw new Error("Ambiguous arguments and options combination; [agentIds...] arg and --criteria <criteria> flag are mutually exclusive");for(let r of t)if(!$i(r))throw new Error(`Input '${r}' is not a valid agentId`);return{type:"ids",ids:t}}if(e.criteria)return ro(e.criteria),{type:"criteria",criteria:e.criteria};throw new Error("Either an [agentId...] arg or a --criteria <criteria> flag is required")}function ro(t){let e="Criteria must be formatted as a string containing the resource type (Agent) followed by a '?' and valid URL search query params, eg. `Agent?name=Test Agent`";if(typeof t!="string")throw new Error(e);let[r,o]=t.split("?");if(r!=="Agent"||!o)throw new Error(e);try{new URLSearchParams(o)}catch(n){throw new Error(e,{cause:n})}if(!o.includes("="))throw new Error(e,{cause:new Error("Query string lacks at least one `=`")})}import{ContentType as Me,getDisplayString as _i,MEDPLUM_CLI_CLIENT_ID as Ui,normalizeErrorString as ji}from"@medplum/core";import{exec as Fi}from"node:child_process";import{createServer as Bi}from"node:http";import{platform as Wi}from"node:os";var oo=Ui,no="http://localhost:9615",$t=new g("login"),Nt=new g("whoami"),Lt=new g("token");$t.action(async t=>{let e=t.profile??"default",r=De(e,t),o=await R(t,!1);await Ki(o,r)});Nt.action(async t=>{let e=await R(t);Gi(e)});Lt.action(async t=>{let e=await R(t);await e.getProfileAsync();let r=e.getAccessToken();if(!r)throw new Error("Not logged in");console.log(r)});async function Ki(t,e){switch(e?.authType??"authorization-code"){case"authorization-code":await Vi(t);break;case"basic":t.setBasicAuth(e.clientId,e.clientSecret);break;case"client-credentials":t.setBasicAuth(e.clientId,e.clientSecret),await t.startClientLogin(e.clientId,e.clientSecret);break;case"jwt-bearer":await Vr(t,e);break;case"jwt-assertion":await Jr(t,e);break}}async function qi(t){let e=Bi(async(r,o)=>{let n=new URL(r.url,"http://localhost:9615"),s=n.searchParams.get("code");if(r.method==="OPTIONS"){o.writeHead(200,{Allow:"GET, POST","Content-Type":Me.TEXT}),o.end("OK");return}if(n.pathname==="/"&&s)try{let i=await t.processCode(s,{clientId:oo,redirectUri:no});o.writeHead(200,{"Content-Type":Me.TEXT}),o.end(`Signed in as ${_i(i)}. You may close this window.`)}catch(i){o.writeHead(400,{"Content-Type":Me.TEXT}),o.end(`Error: ${ji(i)}`)}finally{e.close()}else o.writeHead(404,{"Content-Type":Me.TEXT}),o.end("Not found")}).listen(9615)}async function Hi(t){let e=Wi(),r;switch(e){case"openbsd":case"linux":r=`xdg-open '${t}'`;break;case"darwin":r=`open '${t}'`;break;case"win32":r=`cmd /c start "" "${t}"`;break;default:throw new Error("Unsupported platform: "+e)}return new Promise((o,n)=>{Fi(r,(s,i,c)=>{if(s){n(s);return}if(c){n(new Error("Could not open browser: "+c));return}o()})})}function Gi(t){let e=t.getActiveLogin();e?(console.log(`Server:  ${t.getBaseUrl()}`),console.log(`Profile: ${e.profile.display} (${e.profile.reference})`),console.log(`Project: ${e.project.display} (${e.project.reference})`)):console.log("Not logged in")}async function Vi(t){await qi(t);let e=new URL(t.getAuthorizeUrl());e.searchParams.set("client_id",oo),e.searchParams.set("redirect_uri",no),e.searchParams.set("scope","openid"),e.searchParams.set("response_type","code"),e.searchParams.set("prompt","login"),await Hi(e.toString())}var he="\x1B[0m",Ji="\x1B[1m",Xi="\x1B[31m",zi="\x1B[32m",Yi="\x1B[33m",Zi="\x1B[34m",Z={red:t=>`${Xi}${t}${he}`,green:t=>`${zi}${t}${he}`,yellow:t=>`${Yi}${t}${he}`,blue:t=>`${Zi}${t}${he}`,bold:t=>`${Ji}${t}${he}`},kt=t=>t.replace(/\*\*(.*?)\*\*/g,(e,r)=>Z.bold(r));var Yn=fr(Qt());import{CloudFormationClient as zn,DescribeStackResourcesCommand as Xl,DescribeStacksCommand as zl,paginateListStacks as Yl}from"@aws-sdk/client-cloudformation";import{CloudFrontClient as Zl,CreateInvalidationCommand as Ql}from"@aws-sdk/client-cloudfront";import{ECSClient as eu}from"@aws-sdk/client-ecs";import{S3Client as tu}from"@aws-sdk/client-s3";import{GetParameterCommand as ru,PutParameterCommand as ou,SSMClient as nu}from"@aws-sdk/client-ssm";import{GetCallerIdentityCommand as su,STSClient as iu}from"@aws-sdk/client-sts";import{normalizeErrorString as au}from"@medplum/core";import cu from"node-fetch";import{readdirSync as lu}from"node:fs";import Jl from"node:readline";var Xe;function ze(){Xe=Jl.createInterface({input:process.stdin,output:process.stdout})}function Ye(){Xe.close()}function a(t){Xe.write(t+`
`)}function I(t){a(`
`+t+`
`)}function x(t,e=""){return new Promise(r=>{Xe.question(t+(e?" ("+e+")":"")+" ",o=>{r(o||e.toString())})})}async function Ze(t,e,r=""){let o=t+" ["+e.map(n=>n===r?"("+n+")":n).join("|")+"]";for(;;){let n=await x(o)||r;if(e.includes(n))return n;a("Please choose one of the following options: "+e.join(", "))}}async function ne(t,e,r){return parseInt(await Ze(t,e.map(o=>o.toString()),r.toString()),10)}async function G(t){return(await Ze(t,["y","n"])).toLowerCase()==="y"}async function Ie(t){if(!await G(t))throw a("Exiting..."),new Error("User cancelled")}var Qe=new zn({}),uu=new Zl({region:"us-east-1"}),oh=new eu({}),ve=new tu({}),pu="medplum:environment";async function er(){let t=[],e=Yl({client:Qe},{StackStatusFilter:["CREATE_COMPLETE","CREATE_FAILED","CREATE_IN_PROGRESS","DELETE_FAILED","DELETE_IN_PROGRESS","IMPORT_COMPLETE","IMPORT_IN_PROGRESS","IMPORT_ROLLBACK_COMPLETE","IMPORT_ROLLBACK_FAILED","IMPORT_ROLLBACK_IN_PROGRESS","REVIEW_IN_PROGRESS","ROLLBACK_COMPLETE","ROLLBACK_FAILED","ROLLBACK_IN_PROGRESS","UPDATE_COMPLETE","UPDATE_COMPLETE_CLEANUP_IN_PROGRESS","UPDATE_FAILED","UPDATE_IN_PROGRESS","UPDATE_ROLLBACK_COMPLETE","UPDATE_ROLLBACK_COMPLETE_CLEANUP_IN_PROGRESS","UPDATE_ROLLBACK_FAILED","UPDATE_ROLLBACK_IN_PROGRESS"]});for await(let r of e)if(r.StackSummaries)for(let o of r.StackSummaries)t.push(o);return t}async function se(t){let e=await er();for(let r of e){let o=r.StackName,n=await tr(o);if(n?.tag===t)return n}}async function tr(t){let e={};if(await Xn(Qe,t,e),await Qe.config.region()!=="us-east-1")try{await Xn(new zn({region:"us-east-1"}),t+"-us-east-1",e)}catch{}return e}async function Xn(t,e,r){let o=new zl({StackName:e}),s=(await t.send(o))?.Stacks?.[0],i=s?.Tags?.find(l=>l.Key===pu);if(!i)return;let c=await t.send(new Xl({StackName:e}));if(c.StackResources){t===Qe&&(r.stack=s,r.tag=i.Value);for(let l of c.StackResources)du(l,r)}}function du(t,e){t.ResourceType==="AWS::ECS::Cluster"?e.ecsCluster=t:t.ResourceType==="AWS::ECS::Service"?e.ecsService=t:t.ResourceType==="AWS::S3::Bucket"&&t.LogicalResourceId?.startsWith("FrontEndAppBucket")?e.appBucket=t:t.ResourceType==="AWS::CloudFront::Distribution"&&t.LogicalResourceId?.startsWith("FrontEndAppDistribution")?e.appDistribution=t:t.ResourceType==="AWS::CloudFront::CloudFrontOriginAccessIdentity"&&t.LogicalResourceId?.startsWith("FrontEndOriginAccessIdentity")?e.appOriginAccessIdentity=t:t.ResourceType==="AWS::S3::Bucket"&&t.LogicalResourceId?.startsWith("StorageStorageBucket")?e.storageBucket=t:t.ResourceType==="AWS::CloudFront::Distribution"&&t.LogicalResourceId?.startsWith("StorageStorageDistribution")?e.storageDistribution=t:t.ResourceType==="AWS::CloudFront::CloudFrontOriginAccessIdentity"&&t.LogicalResourceId?.startsWith("StorageOriginAccessIdentity")&&(e.storageOriginAccessIdentity=t)}function et(t){console.log(`Medplum Tag:           ${t.tag}`),console.log(`Stack Name:            ${t.stack?.StackName}`),console.log(`Stack ID:              ${t.stack?.StackId}`),console.log(`Status:                ${t.stack?.StackStatus}`),console.log(`ECS Cluster:           ${t.ecsCluster?.PhysicalResourceId}`),console.log(`ECS Service:           ${mu(t.ecsService)}`),console.log(`App Bucket:            ${t.appBucket?.PhysicalResourceId}`),console.log(`App Distribution:      ${t.appDistribution?.PhysicalResourceId}`),console.log(`App OAI:               ${t.appOriginAccessIdentity?.PhysicalResourceId}`),console.log(`Storage Bucket:        ${t.storageBucket?.PhysicalResourceId}`),console.log(`Storage Distribution:  ${t.storageDistribution?.PhysicalResourceId}`),console.log(`Storage OAI:           ${t.storageOriginAccessIdentity?.PhysicalResourceId}`)}function mu(t){return t?.PhysicalResourceId?.split("/")?.pop()||""}async function tt(t){let e=await uu.send(new Ql({DistributionId:t,InvalidationBatch:{CallerReference:`invalidate-all-${Date.now()}`,Paths:{Quantity:1,Items:["/*"]}}}));console.log(`Created invalidation with ID: ${e.Invalidation?.Id}`)}async function rt(t){let o=(await(await cu("https://api.github.com/repos/medplum/medplum/releases?per_page=100",{headers:{Accept:"application/vnd.github+json","X-GitHub-Api-Version":"2022-11-28"}})).json()).map(n=>n.tag_name.startsWith("v")?n.tag_name.slice(1):n.tag_name);return o.sort((n,s)=>Yn.compare(s,n)),t?o.slice(0,o.indexOf(t)):o}async function ot(t,e,r){let o=new nu({region:t});for(let[n,s]of Object.entries(r)){let i=e+n,c=s.toString(),l=await fu(o,i);l!==void 0&&l!==c&&(a(`Parameter "${i}" exists with different value.`),await Ie(`Do you want to overwrite "${i}"?`)),await hu(o,i,c)}}async function fu(t,e){let r=new ru({Name:e,WithDecryption:!0});try{return(await t.send(r)).Parameter?.Value}catch(o){if(o.name==="ParameterNotFound")return;throw o}}async function hu(t,e,r){let o=new ou({Name:e,Value:r,Type:"SecureString",Overwrite:!0});await t.send(o)}function V(t,e){if(console.log(`Config not found: ${t} (${k(t,e)})`),e){let o=Object.entries(e);if(o.length>0){console.log("Additional options:");for(let[n,s]of o)console.log(`  ${n}: ${s}`)}}console.log();let r=lu(".",{withFileTypes:!0});if(r=r.filter(o=>o.isFile()&&o.name.startsWith("medplum.")&&o.name.endsWith(".json")).map(o=>o.name),r.length===0)console.log("No configs found");else{console.log("Available configs:");for(let o of r)console.log(`  ${o.replaceAll("medplum.","").replaceAll(".config","").replaceAll(".server","").replaceAll(".json","").padEnd(40," ")} (${o})`)}}async function ie(t){console.log(`Stack not found: ${t}`),console.log();try{let e=new iu,r=new su({}),o=await e.send(r),n=await e.config.region();console.log("AWS Region:        ",n),console.log("AWS Account ID:    ",o.Account),console.log("AWS Account ARN:   ",o.Arn),console.log("AWS User ID:       ",o.UserId)}catch(e){console.log("Warning: Unable to get AWS account ID",au(e))}}async function Zn(t){let e=await se(t);if(!e)throw await ie(t),new Error(`Stack not found: ${t}`);et(e)}import{ACMClient as ts,ListCertificatesCommand as gu,RequestCertificateCommand as yu}from"@aws-sdk/client-acm";import{CloudFrontClient as wu,CreatePublicKeyCommand as Eu}from"@aws-sdk/client-cloudfront";import{GetCallerIdentityCommand as Su,STSClient as Ru}from"@aws-sdk/client-sts";import{normalizeErrorString as Au}from"@medplum/core";import{generateKeyPairSync as Iu,randomUUID as Qn}from"node:crypto";import{existsSync as vu}from"node:fs";var bu=t=>`${t}DomainName`,rs=t=>`${t}SslCertArn`;async function os(){let t={apiPort:8103,region:"us-east-1"};ze(),I("MEDPLUM"),a("This tool prepares the necessary prerequisites for deploying Medplum in your AWS account."),a(""),a("Most Medplum infrastructure is deployed using the AWS CDK."),a("However, some AWS resources must be created manually, such as email addresses and SSL certificates."),a("This tool will help you create those resources."),a(""),a("Upon completion, this tool will:"),a("  1. Generate a Medplum CDK config file (i.e., medplum.demo.config.json)"),a("  2. Optionally generate an AWS CloudFront signing key"),a("  3. Optionally request SSL certificates from AWS Certificate Manager"),a("  4. Optionally write server config settings to AWS Parameter Store"),a(""),a("The Medplum infra config file is an input to the Medplum CDK."),a("The Medplum CDK will create and manage the necessary AWS resources."),a(""),a("We will ask a series of questions to generate your infra config file."),a("Some questions have predefined options in [square brackets]."),a("Some questions have default values in (parentheses), which you can accept by pressing Enter."),a("Press Ctrl+C at any time to exit.");let e=await Pu(t.region);e||(a("It appears that you do not have AWS credentials configured."),a("AWS credentials are not strictly required, but will enable some additional features."),a("If you intend to use AWS credentials, please configure them now."),await Ie("Do you want to continue without AWS credentials?")),I("ENVIRONMENT NAME"),a('Medplum deployments have a short environment name such as "prod", "staging", "alice", or "demo".'),a("The environment name is used in multiple places:"),a("  1. As part of config file names (i.e., medplum.demo.config.json)"),a("  2. As the base of CloudFormation stack names (i.e., MedplumDemo)"),a("  3. AWS Parameter Store keys (i.e., /medplum/demo/...)"),t.name=await x("What is your environment name?","demo"),a('Using environment name "'+t.name+'"...'),I("CONFIG FILE"),a("Medplum Infrastructure will create a config file in the current directory.");let r=await x("What is the config file name?",`medplum.${t.name}.config.json`);vu(r)&&(a("Config file already exists."),await Ie("Do you want to overwrite the config file?")),a('Using config file "'+r+'"...'),b(r,t),I("AWS REGION"),a("Most Medplum resources will be created in a single AWS region."),t.region=await x("Enter your AWS region:","us-east-1"),b(r,t),I("AWS ACCOUNT NUMBER"),a("Medplum Infrastructure will use your AWS account number to create AWS resources."),e&&a("Using the AWS CLI, your current account ID is: "+e),t.accountNumber=await x("What is your AWS account number?",e),b(r,t),I("STACK NAME"),a("Medplum will create a CloudFormation stack to manage AWS resources."),a("AWS CloudFormation stack names ");let o="Medplum"+t.name.charAt(0).toUpperCase()+t.name.slice(1);for(t.stackName=await x("Enter your CloudFormation stack name?",o),b(r,t),I("BASE DOMAIN NAME"),a("Please enter the base domain name for your Medplum deployment."),a(""),a("Medplum deploys multiple subdomains for various services."),a(""),a('For example, "api." for the REST API and "app." for the web application.'),a("The base domain name is the common suffix for all subdomains."),a(""),a('For example, if your base domain name is "example.com",'),a('then the REST API will be "api.example.com".'),a(""),a('The base domain should include the TLD (i.e., ".com", ".org", ".net").'),a(""),a("Note that you must own the base domain, and it must use Route53 DNS.");!t.domainName;)t.domainName=await x("Enter your base domain name:");b(r,t),I("SUPPORT EMAIL"),a("Medplum sends transactional emails to users."),a("For example, emails to new users or for password reset."),a("Medplum will use the support email address to send these emails."),a("Note that you must verify the support email address in SES.");let n=await x("Enter your support email address:");I("API DOMAIN NAME"),a("Medplum deploys a REST API for the backend services."),t.apiDomainName=await x("Enter your REST API domain name:","api."+t.domainName),t.baseUrl=`https://${t.apiDomainName}/`,b(r,t),I("APP DOMAIN NAME"),a("Medplum deploys a web application for the user interface."),t.appDomainName=await x("Enter your web application domain name:","app."+t.domainName),b(r,t),I("STORAGE DOMAIN NAME"),a("Medplum deploys a storage service for file uploads."),t.storageDomainName=await x("Enter your storage domain name:","storage."+t.domainName),b(r,t),I("STORAGE BUCKET"),a("Medplum uses an S3 bucket to store binary content such as file uploads."),a("Medplum will create a the S3 bucket as part of the CloudFormation stack."),t.storageBucketName=await x("Enter your storage bucket name:",t.storageDomainName),b(r,t),I("MAX AVAILABILITY ZONES"),a("Medplum API servers can be deployed in multiple availability zones."),a("This provides redundancy and high availability."),a("However, it also increases the cost of the deployment."),a("If you want to use all availability zones, choose a large number such as 99."),a("If you want to restrict the number, for example to manage EIP limits,"),a("then choose a small number such as 2 or 3."),t.maxAzs=await ne("Enter the maximum number of availability zones:",[2,3,99],2),I("DATABASE INSTANCES"),a("Medplum uses a relational database to store data."),a("Medplum can create a new RDS database as part of the CloudFormation stack,"),a("or can set up your own database and enter the database name, username, and password."),await G("Do you want to create a new RDS database as part of the CloudFormation stack?")?(a("Medplum will create a new RDS database as part of the CloudFormation stack."),a(""),a("If you need high availability, you can choose multiple instances."),a("Use 1 for a single instance, or 2 for a primary and a standby."),t.rdsInstances=await ne("Enter the number of database instances:",[1,2],1)):(a("Medplum will not create a new RDS database."),a("Please create a new RDS database and enter the database name, username, and password."),a('Set the AWS Secrets Manager secret ARN in the config file in the "rdsSecretsArn" setting.'),t.rdsSecretsArn="TODO"),b(r,t),I("SERVER INSTANCES"),a("Medplum uses AWS Fargate to run the API servers."),a("Medplum will create a new Fargate cluster as part of the CloudFormation stack."),a("Fargate will automatically scale the number of servers up and down."),a("If you need high availability, you can choose multiple instances."),t.desiredServerCount=await ne("Enter the number of server instances:",[1,2,3,4,6,8],1),b(r,t),I("SERVER MEMORY"),a("You can choose the amount of memory for each server instance."),a("The default is 512 MB, which is sufficient for getting started."),a("Note that only certain CPU units are compatible with memory units."),a('Consult AWS Fargate "Task Definition Parameters" for more information.'),t.serverMemory=await ne("Enter the server memory (MB):",[512,1024,2048,4096,8192,16384],512),b(r,t),I("SERVER CPU"),a("You can choose the amount of CPU for each server instance."),a("CPU is expressed as an integer using AWS CPU units"),a("The default is 256, which is sufficient for getting started."),a("Note that only certain CPU units are compatible with memory units."),a('Consult AWS Fargate "Task Definition Parameters" for more information.'),t.serverCpu=await ne("Enter the server CPU:",[256,512,1024,2048,4096,8192,16384],256),b(r,t),I("SERVER IMAGE"),a("Medplum uses Docker images for the API servers."),a("You can choose the image to use for the servers."),a("Docker images can be loaded from either Docker Hub or AWS ECR."),a("The default is the latest Medplum release.");let s=(await rt())[0]??"latest";t.serverImage=await x("Enter the server image:",`medplum/medplum-server:${s}`),b(r,t),I("SIGNING KEY"),a("Medplum uses AWS CloudFront Presigned URLs for binary content such as file uploads.");let i=await xu(t.region,t.stackName+"SigningKey");i?(t.signingKeyId=i.keyId,t.storagePublicKey=i.publicKey,b(r,t)):(a("Unable to generate signing key."),a("Please manually create a signing key and enter the key ID and public key in the config file."),a('You must set the "signingKeyId", "signingKey", and "signingKeyPassphrase" settings.')),I("SSL CERTIFICATES"),a("Medplum will now check for existing SSL certificates for the subdomains.");let c=await Cu(t.region);a("Found "+c.length+" certificate(s).");for(let{region:u,certName:h}of[{region:t.region,certName:"api"},{region:"us-east-1",certName:"app"},{region:"us-east-1",certName:"storage"}]){a("");let f=await Tu(t,c,u,h);t[rs(h)]=f,b(r,t)}I("AWS PARAMETER STORE"),a("Medplum uses AWS Parameter Store to store sensitive configuration values."),a("These values will be encrypted at rest."),a(`The values will be stored in the "/medplum/${t.name}" path.`);let l={port:t.apiPort,baseUrl:t.baseUrl,appBaseUrl:`https://${t.appDomainName}/`,storageBaseUrl:`https://${t.storageDomainName}/binary/`,binaryStorage:`s3:${t.storageBucketName}`,supportEmail:n};if(i&&(l.signingKeyId=i.keyId,l.signingKey=i.privateKey,l.signingKeyPassphrase=i.passphrase),a(JSON.stringify({...l,signingKey:"****",signingKeyPassphrase:"****"},null,2)),await G("Do you want to store these values in AWS Parameter Store?"))await ot(t.region,`/medplum/${t.name}/`,l);else{let u=k(t.name,{server:!0});b(u,l),a("Skipping AWS Parameter Store."),a(`Writing values to local config file: ${u}`),a("Please add these values to AWS Parameter Store manually.")}I("DONE!"),a("Medplum configuration complete."),a("You can now proceed to deploying the Medplum infrastructure with CDK."),a("Run:"),a(""),a(`    npx cdk bootstrap -c config=${r}`),a(`    npx cdk synth -c config=${r}`),t.region==="us-east-1"?a(`    npx cdk deploy -c config=${r}`):a(`    npx cdk deploy -c config=${r} --all`),a(""),a("See Medplum documentation for more information:"),a(""),a("    https://www.medplum.com/docs/self-hosting/install-on-aws"),a(""),Ye()}async function Pu(t){try{let e=new Ru({region:t}),r=new Su({});return(await e.send(r)).Account}catch(e){console.log("Warning: Unable to get AWS account ID",e.message);return}}async function Cu(t){let e=await es(t);if(t!=="us-east-1"){let r=await es("us-east-1");e.push(...r)}return e}async function es(t){try{let e=new ts({region:t}),r=new gu({MaxItems:1e3});return(await e.send(r)).CertificateSummaryList}catch(e){return console.log("Warning: Unable to list certificates",e.message),[]}}async function Tu(t,e,r,o){let n=t[bu(o)],s=e.find(c=>c.CertificateArn?.includes(r)&&c.DomainName===n);if(s)return a(`Found existing certificate for "${n}" in "${r}.`),s.CertificateArn;if(a(`No existing certificate found for "${n}" in "${r}.`),!await G("Do you want to request a new certificate?"))return a(`Please add your certificate ARN to the config file in the "${rs(o)}" setting.`),"TODO";let i=await Ou(r,n);return a("Certificate ARN: "+i),i}async function Ou(t,e){try{let r=await Ze("Validate certificate using DNS or email validation?",["dns","email"],"dns"),o=new ts({region:t}),n=new yu({DomainName:e,ValidationMethod:r.toUpperCase()});return(await o.send(n)).CertificateArn}catch(r){return console.log("Error: Unable to request certificate",r.message),"TODO"}}async function xu(t,e){let r=Qn(),o=Iu("rsa",{modulusLength:2048,publicKeyEncoding:{type:"spki",format:"pem"},privateKeyEncoding:{type:"pkcs1",format:"pem",cipher:"aes-256-cbc",passphrase:r}});try{return{keyId:(await new wu({region:t}).send(new Eu({PublicKeyConfig:{Name:e,CallerReference:Qn(),EncodedKey:o.publicKey}}))).PublicKey?.Id,publicKey:o.publicKey,privateKey:o.privateKey,passphrase:r}}catch(n){console.log("Error: Unable to create signing key: ",Au(n));return}}async function ns(){let t=await er();for(let e of t){let r=e.StackName,o=await tr(r);o&&(et(o),console.log(""))}}import{PutObjectCommand as $u}from"@aws-sdk/client-s3";import{ContentType as j}from"@medplum/core";import Nu from"fast-glob";import ss from"node-fetch";import{createReadStream as Lu,mkdtempSync as ku,readdirSync as Du,readFileSync as Mu,rmSync as _u,writeFileSync as Uu}from"node:fs";import{tmpdir as ju}from"node:os";import{join as nt,sep as Fu}from"node:path";import{pipeline as Bu}from"node:stream/promises";async function is(t,e){let r=_(t,e);if(!r)throw V(t,e),new Error(`Config not found: ${t}`);let o=await se(t);if(!o)throw await ie(t),new Error(`Stack not found: ${t}`);let n=o.appBucket;if(!n)throw new Error(`App bucket not found for stack ${t}`);let s;if(e.tarPath)s=e.tarPath;else{let i=e?.toVersion??"latest";s=await Ku("@medplum/app",i)}as(s,{MEDPLUM_BASE_URL:r.baseUrl,MEDPLUM_CLIENT_ID:r.clientId??"",GOOGLE_CLIENT_ID:r.googleClientId??"",RECAPTCHA_SITE_KEY:r.recaptchaSiteKey??"",MEDPLUM_REGISTER_ENABLED:r.registerEnabled?"true":"false"}),await Hu(s,n.PhysicalResourceId,e),o.appDistribution?.PhysicalResourceId&&!e.dryrun&&await tt(o.appDistribution.PhysicalResourceId),console.log("Done")}async function Wu(t,e){let r=`https://registry.npmjs.org/${t}/${e}`;return(await ss(r)).json()}async function Ku(t,e){let o=(await Wu(t,e)).dist.tarball,n=ku(nt(ju(),"tarball-"));try{let s=await ss(o),i=Hr(n);return await Bu(s.body,i),nt(n,"package","dist")}catch(s){throw _u(n,{recursive:!0,force:!0}),s}}function as(t,e){for(let r of Du(t,{withFileTypes:!0})){let o=nt(t,r.name);r.isDirectory()?as(o,e):r.isFile()&&o.endsWith(".js")&&qu(o,e)}}function qu(t,e){let r=Mu(t,"utf-8");for(let[o,n]of Object.entries(e))r=r.replaceAll(`__${o}__`,n);Uu(t,r)}async function Hu(t,e,r){let o=[["assets/**/*.css",j.CSS,!0],["assets/**/*.css.map",j.JSON,!0],["assets/**/*.js",j.JAVASCRIPT,!0],["assets/**/*.js.map",j.JSON,!0],["assets/**/*.txt",j.TEXT,!0],["assets/**/*.ico",j.FAVICON,!0],["img/**/*.png",j.PNG,!0],["img/**/*.svg",j.SVG,!0],["robots.txt",j.TEXT,!0],["index.html",j.HTML,!1]];for(let n of o)await Gu({rootDir:t,bucketName:e,fileNamePattern:n[0],contentType:n[1],cached:n[2],dryrun:r.dryrun})}async function Gu(t){let e=Nu.sync(t.fileNamePattern,{cwd:t.rootDir});for(let r of e)await Vu(nt(t.rootDir,r),t)}async function Vu(t,e){let r=Lu(t),o=t.substring(e.rootDir.length+1).split(Fu).join("/"),n={Bucket:e.bucketName,Key:o,Body:r,ContentType:e.contentType,CacheControl:e.cached?"public, max-age=31536000":"no-cache, no-store, must-revalidate"};console.log(`Uploading ${o} to ${e.bucketName}...`),e.dryrun||await ve.send(new $u(n))}import{GetBucketPolicyCommand as Ju,PutBucketPolicyCommand as Xu}from"@aws-sdk/client-s3";async function ls(t,e){if(!_(t,e))throw V(t,e),new Error(`Config not found: ${t}`);let o=await se(t);if(!o)throw await ie(t),new Error(`Stack not found: ${t}`);await cs("App",o.appBucket,o.appDistribution,o.appOriginAccessIdentity,e),await cs("Storage",o.storageBucket,o.storageDistribution,o.storageOriginAccessIdentity,e),console.log("Done")}async function cs(t,e,r,o,n){if(!e?.PhysicalResourceId)throw new Error(`${t} bucket not found`);if(!r?.PhysicalResourceId)throw new Error(`${t} distribution not found`);if(!o?.PhysicalResourceId)throw new Error(`${t} OAI not found`);let s=e.PhysicalResourceId,i=o.PhysicalResourceId,c=await zu(s);if(Zu(c,s,i))throw new Error(`${t} bucket already has policy statement`);Qu(c,s,i),console.log(`${t} bucket policy:`),console.log(JSON.stringify(c,void 0,2)),n.dryrun?console.log("Dry run - skipping updates"):(console.log("Updating bucket policy..."),await Yu(s,c),console.log("Bucket policy updated"),console.log("Creating CloudFront invalidation..."),await tt(r.PhysicalResourceId),console.log("CloudFront invalidation created"),console.log(`${t} bucket policy updated`))}async function zu(t){let e=await ve.send(new Ju({Bucket:t}));return JSON.parse(e.Policy??"{}")}async function Yu(t,e){await ve.send(new Xu({Bucket:t,Policy:JSON.stringify(e)}))}function Zu(t,e,r){return!!t?.Statement?.some(o=>o?.Effect==="Allow"&&o?.Principal?.AWS===`arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity ${r}`&&Array.isArray(o?.Action)&&o?.Action?.includes("s3:GetObject*")&&o?.Action?.includes("s3:GetBucket*")&&o?.Action?.includes("s3:List*")&&Array.isArray(o?.Resource)&&o?.Resource?.includes(`arn:aws:s3:::${e}`)&&o?.Resource?.includes(`arn:aws:s3:::${e}/*`))}function Qu(t,e,r){t.Version||(t.Version="2012-10-17"),t.Statement||(t.Statement=[]),t.Statement.push({Effect:"Allow",Principal:{AWS:`arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity ${r}`},Action:["s3:GetObject*","s3:GetBucket*","s3:List*"],Resource:[`arn:aws:s3:::${e}`,`arn:aws:s3:::${e}/*`]})}async function us(t,e){try{ze();let r=_(t,e);if(!r)throw V(t,e),new Error(`Config not found: ${t}`);let o=qr(t)??{};if(!e.yes&&Object.keys(o).length===0){let n=k(t,{server:!0});if(console.log(Z.yellow(`Config file ${n} not found!`)),!await G("Do you want to proceed?")){console.log(Z.red(`Run Aborted, please ensure ${n} is present and try again.`));return}}ep(r,o),rp(r,o),a("Medplum uses AWS Parameter Store to store sensitive configuration values."),a("These values will be encrypted at rest."),a(`The values will be stored in the "/medplum/${r.name}" path.`),a(JSON.stringify({...o,signingKey:"****",signingKeyPassphrase:"****"},null,2)),e.dryrun?console.log(Z.yellow("Dry run - skipping updates!")):(e.yes||await G("Do you want to store these values in AWS Parameter Store?"))&&await ot(r.region,`/medplum/${r.name}/`,o)}finally{Ye()}}function ep(t,e){st(t.apiPort,e.port,`Infra "apiPort" (${t.apiPort}) does not match server "port" (${e.port})`),st(t.baseUrl,e.baseUrl,`Infra "baseUrl" (${t.baseUrl}) does not match server "baseUrl" (${e.baseUrl})`),st(t.appDomainName&&`https://${t.appDomainName}/`,e.appBaseUrl,`Infra "appDomainName" (${t.appDomainName}) does not match server "appBaseUrl" (${e.appBaseUrl})`),st(t.storageDomainName&&`https://${t.storageDomainName}/binary/`,e.storageBaseUrl,`Infra "storageDomainName" (${t.storageDomainName}) does not match server "storageBaseUrl" (${e.storageBaseUrl})`)}function st(t,e,r){if(tp(t,e))throw new Error(r)}function tp(t,e){return t!==void 0&&e!==void 0&&t!==e}function rp(t,e){t.apiPort&&(e.port=t.apiPort),t.baseUrl&&(e.baseUrl=t.baseUrl),t.appDomainName&&(e.appBaseUrl=`https://${t.appDomainName}/`),t.storageDomainName&&(e.storageBaseUrl=`https://${t.storageDomainName}/`)}var ae=fr(Qt());import{spawnSync as op}from"node:child_process";async function ds(t,e){let r=await R(e),o=_(t,e);if(!o)throw console.log(`Configuration file ${k(t)} not found`),V(t,e),new Error(`Config not found: ${t}`);let n=o.serverImage.lastIndexOf(":"),s=o.serverImage.slice(0,n),i=await np(r,o),c=await ps(i);for(;c;){if(e.toVersion&&ae.gt(c,e.toVersion)){console.log(`Skipping update to v${c}`);break}console.log(`Performing update to v${c}`),o.serverImage=`${s}:${c}`,sp(t,o),await r.startAsyncRequest("/admin/super/migrate"),c=await ps(c)}}async function np(t,e){let r=e.serverImage.lastIndexOf(":"),o=e.serverImage.slice(r+1);if(o==="latest"){o=(await t.get("/healthcheck")).version;let s=o.indexOf("-");s>-1&&(o=o.slice(0,s))}return o}async function ps(t,e){let r=await rt(t),o=r[0];return r.filter(n=>n===o||n===e||ae.gte(n,ae.inc(t,"minor"))).pop()}function sp(t,e){let r=k(t);b(r,e);let o=`npx cdk deploy -c config=${r}${e.region!=="us-east-1"?" --all":""}`;console.log("> "+o);let n=op(o,{stdio:"inherit"});if(n.status!==0)throw new Error(`Deploy of ${e.serverImage} failed (exit code ${n.status}): ${n.stderr}`);console.log(n.stdout)}function ms(){let t=new g("aws").description("Commands to manage AWS resources");return t.command("init").description("Initialize a new Medplum AWS CloudFormation stacks").action(os),t.command("list").description("List Medplum AWS CloudFormation stacks").action(ns),t.command("describe").description("Describe a Medplum AWS CloudFormation stack by tag").argument("<tag>","The Medplum stack tag").action(Zn),t.command("update-config").alias("deploy-config").summary("Update the AWS Parameter Store config values.").description(kt(`Update the AWS Parameter Store config values.

Configuration values come from a file named **medplum.<tag>.config.server.json** where **<tag>** is the Medplum stack tag.

`+Z.yellow("**Services must be restarted to apply changes.**"))).argument("<tag>","The Medplum stack tag").option("--file [file]",kt("File to provide overrides for **apiPort**, **baseUrl**, **appDomainName** and **storageDomainName** values that appear in the config file.")).option("--dryrun","Displays the operations that would be performed using the specified command without actually running them.").option("--yes","Automatically confirm the update").action(us),y(t,new g("update-server").alias("deploy-server").description("Update the server image").argument("<tag>","The Medplum stack tag").option("--file [file]","Specifies the config file to use. If not specified, the file is based on the tag.").option("--to-version [version]","Specifies the version of the configuration to update. If not specified, the latest version is updated.").action(ds)),t.command("update-app").alias("deploy-app").description("Update the app site").argument("<tag>","The Medplum stack tag").option("--file [file]","Specifies the config file to use. If not specified, the file is based on the tag.").option("--to-version [version]","Specifies the version of the configuration to update. If not specified, the latest version is updated.").option("--dryrun","Displays the operations that would be performed using the specified command without actually running them.").option("--tar-path [tarPath]","Specifies the path to the extracted tarball of the @medplum/app package.").action(is),t.command("update-bucket-policies").description("Update S3 bucket policies").argument("<tag>","The Medplum stack tag").option("--file [file]","Specifies the config file to use. If not specified, the file is based on the tag.").option("--dryrun","Displays the operations that would be performed using the specified command without actually running them.").action(ls),t}var fs=new g("save"),hs=new g("deploy"),gs=new g("create"),be=new g("bot");y(be,fs);y(be,hs);y(be,gs);var rr=new g("save-bot"),or=new g("deploy-bot"),nr=new g("create-bot");fs.description("Saving the bot").argument("<botName>").action(async(t,e)=>{let r=await R(e);await it(r,t)});hs.description("Deploy the app to AWS").argument("<botName>").action(async(t,e)=>{let r=await R(e);await it(r,t,!0)});gs.arguments("<botName> <projectId> <sourceFile> <distFile>").description("Creating a bot").option("--runtime-version <runtimeVersion>","Runtime version (awslambda, vmcontext)").option("--no-write-config","Do not write bot to config").action(async(t,e,r,o,n)=>{let s=await R(n);await Ct(s,t,e,r,o,n.runtimeVersion,!!n.writeConfig)});async function it(t,e,r=!1){let o=Kr(e),n=[],s=[],i=0,c=0;for(let l of o)try{let u=await t.readResource("Bot",l.id);await bt(t,l,u),i++,r&&(await Pt(t,l,u),c++)}catch(u){n.push(u),s.push(`${l.name} [${l.id}]`)}if(console.log(`Number of bots saved: ${i}`),console.log(`Number of bots deployed: ${c}`),console.log(`Number of errors: ${n.length}`),n.length)throw new Error(`${n.length} bot(s) had failures. Bots with failures:

    ${s.join(`
    `)}`,{cause:n})}rr.description("Saves the bot").argument("<botName>").action(async(t,e)=>{let r=await R(e);await it(r,t)});or.description("Deploy the bot to AWS").argument("<botName>").action(async(t,e)=>{let r=await R(e);await it(r,t,!0)});nr.arguments("<botName> <projectId> <sourceFile> <distFile>").description("Creates and saves the bot").action(async(t,e,r,o,n)=>{let s=await R(n);await Ct(s,t,e,r,o)});import{createReadStream as ip,writeFile as ap}from"node:fs";import{resolve as ws}from"node:path";import{createInterface as cp}from"node:readline";var Es=new g("export"),Ss=new g("import"),at=new g("bulk");y(at,Es);y(at,Ss);Es.option("-e, --export-level <exportLevel>",'Optional export level. Defaults to system level export. "Group/:id" - Group of Patients, "Patient" - All Patients.').option("-t, --types <types>","optional resource types to export").option("-s, --since <since>","optional Resources will be included in the response if their state has changed after the supplied time (e.g. if Resource.meta.lastUpdated is later than the supplied _since time).").option("-d, --target-directory <targetDirectory>","optional target directory to save files from the bulk export operations.").action(async t=>{let{exportLevel:e,types:r,since:o,targetDirectory:n}=t,s=await R(t);(await s.bulkExport(e,r,o,{pollStatusOnAccepted:!0})).output?.forEach(async({type:c,url:l})=>{let u=new URL(l),h=await s.download(l),f=`${c}_${u.pathname}`.replace(/[^a-zA-Z0-9]+/g,"_")+".ndjson",S=ws(n??"",f);ap(`${S}`,await h.text(),()=>{console.log(`${S} is created`)})})});Ss.argument("<filename>","File Name").option("--num-resources-per-request <numResourcesPerRequest>","optional number of resources to import per batch request. Defaults to 25.","25").option("--add-extensions-for-missing-values","optional flag to add extensions for missing values in a resource",!1).option("-d, --target-directory <targetDirectory>","optional target directory of file to be imported").action(async(t,e)=>{let{numResourcesPerRequest:r,addExtensionsForMissingValues:o,targetDirectory:n}=e,s=ws(n??process.cwd(),t),i=await R(e);await lp(s,Number.parseInt(r,10),i,o)});async function lp(t,e,r,o){let n=[],s=ip(t),i=cp({input:s});for await(let c of i){let l=up(c,o);n.push({resource:l,request:{method:"POST",url:l.resourceType}}),n.length%e===0&&(await ys(n,r),n=[])}n.length>0&&await ys(n,r)}async function ys(t,e){(await e.executeBatch({resourceType:"Bundle",type:"transaction",entry:t})).entry?.forEach(o=>{K(o.response)})}function up(t,e){let r=JSON.parse(t);return e?pp(r):r}function pp(t){return t.resourceType==="ExplanationOfBenefit"?dp(t):t}function dp(t){return t.provider||(t.provider=Tt()),t.item?.forEach(e=>{e?.productOrService||(e.productOrService=Tt())}),t}import{formatHl7DateTime as yp,Hl7Message as wp}from"@medplum/core";import{connect as mp}from"node:net";import{Hl7Message as fp}from"@medplum/core";import Rs from"iconv-lite";import gp from"node:net";var As=class extends EventTarget{addEventListener(t,e,r){super.addEventListener(t,e,r)}removeEventListener(t,e,r){super.removeEventListener(t,e,r)}};var hp=class extends Event{constructor(t,e){super("message"),this.connection=t,this.message=e}},ct=class extends Event{constructor(t){super("error"),this.error=t}},Is=class extends Event{constructor(){super("close")}},vs=class extends As{constructor(t,e="utf-8",r=!1){super(),this.chunks=[],this.messageQueue=[],this.socket=t,this.encoding=e,this.enhancedMode=r,t.on("data",o=>{try{if(this.appendData(o),o.at(-2)===28&&o.at(-1)===13){let n=Buffer.concat(this.chunks),s=n.subarray(1,n.length-2),i=Rs.decode(s,this.encoding),c=fp.parse(i);this.dispatchEvent(new hp(this,c)),this.resetBuffer()}}catch(n){this.dispatchEvent(new ct(n))}}),t.on("error",o=>{this.resetBuffer(),this.dispatchEvent(new ct(o))}),t.on("end",()=>{this.close()}),this.addEventListener("message",o=>{r&&this.send(o.message.buildAck({ackCode:"CA"}));let n=this.messageQueue.shift();if(!n){this.dispatchEvent(new ct(new Error(`Received a message when no pending messages were in the queue. Message: ${o.message}`)));return}n.resolve?.(o.message)})}sendImpl(t,e){this.messageQueue.push(e);let r=t.toString(),o=Rs.encode(r,this.encoding),n=Buffer.alloc(o.length+3);n.writeInt8(11,0),o.copy(n,1),n.writeInt8(28,o.length+1),n.writeInt8(13,o.length+2),this.socket.write(n)}send(t){this.sendImpl(t,{message:t})}async sendAndWait(t){return new Promise((e,r)=>{let o={message:t,resolve:e,reject:r};this.sendImpl(t,o)})}close(){this.socket.end(),this.socket.destroy(),this.dispatchEvent(new Is)}appendData(t){this.chunks.push(t)}resetBuffer(){this.chunks=[]}},bs=class extends As{constructor(t){super(),this.options=t,this.host=this.options.host,this.port=this.options.port,this.encoding=this.options.encoding,this.keepAlive=this.options.keepAlive??!1,this.connectTimeout=this.options.connectTimeout??3e4}connect(){return this.connection?Promise.resolve(this.connection):(this.socket&&(this.socket.removeAllListeners(),this.socket.destroy(),this.socket=void 0),new Promise((t,e)=>{this.socket=mp({host:this.host,port:this.port,keepAlive:this.keepAlive}),this.connectTimeout>0&&(this.socket.setTimeout(this.connectTimeout),this.socket.on("timeout",()=>{let r=new Error(`Connection timeout after ${this.connectTimeout}ms`);this.socket&&(this.socket.destroy(),this.socket=void 0),e(r)})),this.socket.on("connect",()=>{if(!this.socket)return;let r;this.connection=r=new vs(this.socket,this.encoding),this.socket.setTimeout(0),r.addEventListener("close",()=>{this.socket=void 0,this.dispatchEvent(new Is)}),r.addEventListener("error",o=>{this.dispatchEvent(new ct(o.error))}),t(this.connection)}),this.socket.on("error",r=>{this.socket&&(this.socket.destroy(),this.socket=void 0),e(r)})}))}async send(t){return(await this.connect()).send(t)}async sendAndWait(t){return(await this.connect()).sendAndWait(t)}close(){this.socket&&(this.socket.removeAllListeners(),this.socket.destroy(),this.socket=void 0),this.connection&&(this.connection.close(),delete this.connection)}},Ps=class{constructor(t){this.handler=t}start(t,e,r=!1){let o=gp.createServer(n=>{let s=new vs(n,e,r);this.handler(s)});o.listen(t),this.server=o}async stop(){return new Promise((t,e)=>{if(!this.server){e(new Error("Stop was called but there is no server running"));return}this.server.close(r=>{if(r){e(r);return}t()}),this.server=void 0})}};import{readFileSync as Ep}from"node:fs";var Sp=new g("send").description("Send an HL7 v2 message via MLLP").argument("<host>","The destination host name or IP address").argument("<port>","The destination port number").argument("[body]","Optional HL7 message body").option("--generate-example","Generate a sample HL7 message").option("--file <file>","Read the HL7 message from a file").option("--encoding <encoding>","The encoding to use").action(async(t,e,r,o)=>{if(o.generateExample?r=Ap():o.file&&(r=Ep(o.file,"utf8")),!r)throw new Error("Missing HL7 message body");let n=new bs({host:t,port:Number.parseInt(e,10),encoding:o.encoding});try{let s=await n.sendAndWait(wp.parse(r));console.log(s.toString().replaceAll("\r",`
`))}finally{n.close()}}),Rp=new g("listen").description("Starts an HL7 v2 MLLP server").argument("<port>").option("--encoding <encoding>","The encoding to use").action(async(t,e)=>{new Ps(o=>{o.addEventListener("message",({message:n})=>{console.log(n.toString().replaceAll("\r",`
`)),o.send(n.buildAck())})}).start(Number.parseInt(t,10),e.encoding),console.log("Listening on port "+t)}),lt=new g("hl7");y(lt,Sp);y(lt,Rp);function Ap(){let t=yp(new Date),e=Date.now().toString();return`MSH|^~\\&|ADTSYS|HOSPITAL|RECEIVER|DEST|${t}||ADT^A01|${e}|P|2.5|
EVN|A01|${t}||
PID|1|12345|12345^^^HOSP^MR|123456|DOE^JOHN^MIDDLE^SUFFIX|19800101|M|||123 STREET^APT 4B^CITY^ST^12345-6789||555-555-5555||S|
PV1|1|I|2000^2012^01||||12345^DOCTOR^DOC||||||||||1234567^DOCTOR^DOC||AMB|||||||||||||||||||||||||202309280900|`}import{readdirSync as Ip}from"node:fs";import{homedir as vp}from"node:os";import{resolve as bp}from"node:path";var Cs=new g("set"),Ts=new g("remove"),Os=new g("list"),xs=new g("describe"),ce=new g("profile");y(ce,Cs);y(ce,Ts);y(ce,Os);y(ce,xs);Cs.argument("<profileName>","Name of the profile").description("Create a new profile or replace it with the given name and its associated properties").action(async(t,e)=>{De(t,e)});Ts.argument("<profileName>","Name of the profile").description("Remove a profile by name").action(async t=>{new D(t).setObject("options",void 0),console.log(`${t} profile removed`)});Os.description("List all profiles saved").action(async()=>{let t=bp(vp(),".medplum"),e=Ip(t),r=[];e.forEach(o=>{let n=o.split(".")[0],i=new D(n).getObject("options");i&&r.push({profileName:n,profile:i})}),console.log(r)});xs.argument("<profileName>","Name of the profile").description("Describes a profile").action(async t=>{let e=Gr(t);console.log(e)});import{Option as Pp}from"commander";var $s=new g("list"),Ns=new g("current"),Ls=new g("switch"),ks=new g("invite"),le=new g("project");y(le,$s);y(le,Ns);y(le,Ls);y(le,ks);$s.description("List of current projects").action(async t=>{let e=await R(t);Cp(e)});function Cp(t){let r=t.getLogins().map(o=>`${o.project.display} (${o.project.reference})`).join(`

`);console.log(r)}Ns.description("Project you are currently on").action(async t=>{let r=(await R(t)).getActiveLogin();if(!r)throw new Error("Unauthenticated: run `npx medplum login` to login");console.log(`${r.project.display} (${r.project.reference})`)});Ls.description("Switching to another project from the current one").argument("<projectId>").action(async(t,e)=>{let r=await R(e);await Tp(r,t)});ks.description("Invite a member to your current project (run npx medplum project current to confirm)").arguments("<firstName> <lastName> <email>").option("--send-email","If you want to send the email when inviting the user").option("--admin","If the user you are inviting is an admin").addOption(new Pp("-r, --role <role>","Role of user").choices(["Practitioner","Patient","RelatedPerson"]).default("Practitioner")).action(async(t,e,r,o)=>{let n=await R(o),s=n.getActiveLogin();if(!s)throw new Error("Unauthenticated: run `npx medplum login` to login");if(!s?.project?.reference)throw new Error("No current project to invite user to");let i=s.project.reference.split("/")[1],c={resourceType:o.role,firstName:t,lastName:e,email:r,sendEmail:!!o.sendEmail,admin:!!o.admin};await Op(i,c,n)});async function Tp(t,e){let o=t.getLogins().find(n=>n.project.reference?.includes(e));if(!o)throw new Error(`Project ${e} not found. Make sure you are added as a user to this project`);await t.setActiveLogin(o),console.log(`Switched to project ${e}
`)}async function Op(t,e,r){await r.invite(t,e),e.sendEmail&&console.log("Email sent"),console.log("See your users at https://app.medplum.com/admin/users")}import{convertToTransactionBundle as xp}from"@medplum/core";var sr=new g("delete"),ir=new g("get"),ar=new g("patch"),cr=new g("post"),lr=new g("put");sr.argument("<url>","Resource/$id").action(async(t,e)=>{let r=await R(e);K(await r.delete(Pe(r,t)))});ir.argument("<url>","Resource/$id").option("--as-transaction","Print out the bundle as a transaction type").action(async(t,e)=>{let r=await R(e),o=await r.get(Pe(r,t));e.asTransaction?K(xp(o)):K(o)});ar.arguments("<url> <body>").action(async(t,e,r)=>{let o=await R(r);K(await o.patch(Pe(o,t),ur(e)))});cr.arguments("<url> <body>").option("--prefer-async",'Sets the Prefer header to "respond-async"').action(async(t,e,r)=>{let o=await R(r),n=r.preferAsync?{Prefer:"respond-async"}:void 0;K(await o.post(Pe(o,t),ur(e),void 0,{headers:n}))});lr.arguments("<url> <body>").action(async(t,e,r)=>{let o=await R(r);K(await o.put(Pe(o,t),ur(e)))});function ur(t){if(t)try{return JSON.parse(t)}catch{return t}}function Pe(t,e){return["admin/","auth/","fhir/R4"].some(o=>e.startsWith(o))?e:t.fhirUrl(e).toString()}async function kp(t){let e=new g("medplum").description("Command to access Medplum CLI").option("--client-id <clientId>","FHIR server client id").option("--client-secret <clientSecret>","FHIR server client secret").option("--base-url <baseUrl>","FHIR server base URL, must be absolute").option("--token-url <tokenUrl>","FHIR server token URL, absolute or relative to base URL").option("--authorize-url <authorizeUrl>","FHIR server authorize URL, absolute or relative to base URL").option("--fhir-url, --fhir-url-path <fhirUrlPath>","FHIR server URL, absolute or relative to base URL").option("--scope <scope>","JWT scope").option("--access-token <accessToken>","Access token for token exchange authentication").option("--callback-url <callbackUrl>","Callback URL for authorization code flow").option("--subject <subject>","Subject for JWT authentication").option("--audience <audience>","Audience for JWT authentication").option("--issuer <issuer>","Issuer for JWT authentication").option("--private-key-path <privateKeyPath>","Private key path for JWT assertion").option("-p, --profile <profile>","Profile name").option("-v --verbose","Verbose output").addOption(new Np("--auth-type <authType>","Type of authentication").choices(["basic","client-credentials","authorization-code","jwt-bearer","token-exchange","jwt-assertion"])).on("option:verbose",()=>{process.env.VERBOSE="1"});e.exitOverride(),e.version($p),e.configureHelp({showGlobalOptions:!0}),y(e,$t),y(e,Nt),y(e,Lt),y(e,ir),y(e,cr),y(e,ar),y(e,lr),y(e,sr),y(e,le),y(e,at),y(e,be),y(e,Y),y(e,rr),y(e,or),y(e,nr),y(e,ce),y(e,ms()),y(e,lt);try{await e.parseAsync(t)}catch(r){Dp(r)}}function Dp(t){let e=1,r=!0;if(t instanceof Ds&&(process.env.VERBOSE||(r=!1),e=t.exitCode),e!==0&&r){pr(t,!!process.env.VERBOSE);let o=t.cause;if(process.env.VERBOSE)if(Array.isArray(o))for(let n of o)pr(n,!0);else o instanceof Error&&pr(o,!0)}process.exit(e)}function pr(t,e=!1){if(e){console.error(t);return}t instanceof Ds?process.stderr.write(`${dr(t)}
`):process.stderr.write(`Error: ${dr(t)}
`)}async function Mp(){Lp.config(),await kp(process.argv)}mr.main===module&&Mp().catch(t=>{console.error("Unhandled error:",dr(t)),process.exit(1)});export{Dp as handleError,kp as main,Mp as run};
//# sourceMappingURL=index.mjs.map
