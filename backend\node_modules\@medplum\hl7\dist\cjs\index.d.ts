import { Hl7Message } from '@medplum/core';
import net from 'node:net';

/**
 * CR (Carriage Return) character.
 *
 * In HL7 messages, this character is used to indicate the end of a message.
 */
export declare const CR = 13;

/**
 * FS (File Separator) character.
 *
 * In HL7 messages, this character is used to separate fields.
 */
export declare const FS = 28;

export declare abstract class Hl7Base extends EventTarget {
    addEventListener<K extends keyof Hl7EventMap>(type: K, listener: ((event: Hl7EventMap[K]) => void) | EventListenerObject | null, options?: boolean | AddEventListenerOptions): void;
    removeEventListener<K extends keyof Hl7EventMap>(type: K, listener: ((event: Hl7EventMap[K]) => void) | EventListenerObject | null, options?: boolean | AddEventListenerOptions): void;
}

export declare class Hl7Client extends Hl7Base {
    options: Hl7ClientOptions;
    host: string;
    port: number;
    encoding?: string;
    connection?: Hl7Connection;
    keepAlive: boolean;
    private socket?;
    private connectTimeout;
    constructor(options: Hl7ClientOptions);
    connect(): Promise<Hl7Connection>;
    send(msg: Hl7Message): Promise<void>;
    sendAndWait(msg: Hl7Message): Promise<Hl7Message>;
    close(): void;
}

export declare interface Hl7ClientOptions {
    host: string;
    port: number;
    encoding?: string;
    keepAlive?: boolean;
    connectTimeout?: number;
}

export declare class Hl7CloseEvent extends Event {
    constructor();
}

export declare class Hl7Connection extends Hl7Base {
    readonly socket: net.Socket;
    readonly encoding: string;
    readonly enhancedMode: boolean;
    private chunks;
    private readonly messageQueue;
    constructor(socket: net.Socket, encoding?: string, enhancedMode?: boolean);
    private sendImpl;
    send(reply: Hl7Message): void;
    sendAndWait(msg: Hl7Message): Promise<Hl7Message>;
    close(): void;
    private appendData;
    private resetBuffer;
}

export declare class Hl7ErrorEvent extends Event {
    readonly error: Error;
    constructor(error: Error);
}

export declare interface Hl7EventMap {
    message: Hl7MessageEvent;
    error: Hl7ErrorEvent;
    close: Hl7CloseEvent;
}

export declare class Hl7MessageEvent extends Event {
    readonly connection: Hl7Connection;
    readonly message: Hl7Message;
    constructor(connection: Hl7Connection, message: Hl7Message);
}

export declare type Hl7MessageQueueItem = {
    message: Hl7Message;
    resolve?: (reply: Hl7Message) => void;
    reject?: (err: Error) => void;
};

export declare class Hl7Server {
    readonly handler: (connection: Hl7Connection) => void;
    server?: net.Server;
    constructor(handler: (connection: Hl7Connection) => void);
    start(port: number, encoding?: string, enhancedMode?: boolean): void;
    stop(): Promise<void>;
}

/**
 * VT (Vertical Tab) character.
 *
 * In HL7 messages, this character is used to indicate the start of a message.
 */
export declare const VT = 11;

export { }
