import React, { useState, useEffect } from 'react';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { Link } from 'react-router-dom';
import { FaUserCircle, FaHeartbeat } from 'react-icons/fa';

// Setup the localizer for react-big-calendar
const localizer = momentLocalizer(moment);

const AppointmentCalendar = ({ appointments, userRole }) => {
  const [calendarEvents, setCalendarEvents] = useState([]);
  
  useEffect(() => {
    // Convert appointments to calendar events format
    const events = appointments.map(appointment => ({
      id: appointment.id,
      title: `${appointment.patientName} - ${appointment.appointmentType}`,
      start: new Date(appointment.start),
      end: new Date(appointment.end),
      resource: appointment
    }));
    
    setCalendarEvents(events);
  }, [appointments]);
  
  // Custom event component to display in the calendar
  const EventComponent = ({ event }) => {
    const appointment = event.resource;
    const statusColors = {
      scheduled: 'bg-blue-100 border-blue-500',
      arrived: 'bg-green-100 border-green-500',
      cancelled: 'bg-red-100 border-red-500',
      completed: 'bg-gray-100 border-gray-500'
    };
    
    return (
      <div className={`p-1 overflow-hidden text-xs rounded border-l-4 ${statusColors[appointment.status.toLowerCase()] || 'bg-gray-100 border-gray-500'}`}>
        <div className="font-medium truncate">{appointment.patientName}</div>
        <div className="truncate">{appointment.appointmentType}</div>
        <div className="truncate text-gray-600">{appointment.location}</div>
      </div>
    );
  };
  
  // Custom toolbar to add more controls
  const CustomToolbar = ({ label, onNavigate, onView, view }) => {
    return (
      <div className="flex justify-between items-center mb-4">
        <div>
          <button
            type="button"
            onClick={() => onNavigate('TODAY')}
            className="mr-2 px-3 py-1 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Today
          </button>
          <button
            type="button"
            onClick={() => onNavigate('PREV')}
            className="mr-2 px-3 py-1 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Back
          </button>
          <button
            type="button"
            onClick={() => onNavigate('NEXT')}
            className="px-3 py-1 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Next
          </button>
        </div>
        
        <div className="text-lg font-semibold">{label}</div>
        
        <div>
          <button
            type="button"
            onClick={() => onView('month')}
            className={`mr-2 px-3 py-1 border rounded-md shadow-sm text-sm font-medium ${
              view === 'month' 
                ? 'border-primary bg-primary text-white' 
                : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
            } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary`}
          >
            Month
          </button>
          <button
            type="button"
            onClick={() => onView('week')}
            className={`mr-2 px-3 py-1 border rounded-md shadow-sm text-sm font-medium ${
              view === 'week' 
                ? 'border-primary bg-primary text-white' 
                : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
            } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary`}
          >
            Week
          </button>
          <button
            type="button"
            onClick={() => onView('day')}
            className={`px-3 py-1 border rounded-md shadow-sm text-sm font-medium ${
              view === 'day' 
                ? 'border-primary bg-primary text-white' 
                : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
            } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary`}
          >
            Day
          </button>
        </div>
      </div>
    );
  };
  
  // Custom popup when clicking on an event
  const EventPopup = ({ event }) => {
    const appointment = event.resource;
    
    return (
      <div className="bg-white shadow-lg rounded-md p-4 max-w-sm">
        <h3 className="text-lg font-medium text-gray-900">{appointment.appointmentType}</h3>
        <div className="mt-2">
          <p className="text-sm text-gray-500">
            <span className="font-medium">Patient:</span> {appointment.patientName}
          </p>
          <p className="text-sm text-gray-500">
            <span className="font-medium">Provider:</span> {appointment.practitionerName}
          </p>
          <p className="text-sm text-gray-500">
            <span className="font-medium">Time:</span> {moment(appointment.start).format('h:mm A')} - {moment(appointment.end).format('h:mm A')}
          </p>
          <p className="text-sm text-gray-500">
            <span className="font-medium">Location:</span> {appointment.location}
          </p>
          {appointment.notes && (
            <p className="text-sm text-gray-500">
              <span className="font-medium">Notes:</span> {appointment.notes}
            </p>
          )}
        </div>
        <div className="mt-4 flex space-x-2">
          {userRole === 'nurse' && appointment.status === 'scheduled' && (
            <Link
              to={`/sessions/${appointment.id}`}
              className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <FaHeartbeat className="mr-1" />
              Start Session
            </Link>
          )}
          <Link
            to={`/patients/${appointment.patientId}`}
            className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <FaUserCircle className="mr-1" />
            View Patient
          </Link>
        </div>
      </div>
    );
  };
  
  const handleSelectEvent = (event) => {
    console.log('Selected appointment:', event.resource);
  };
  
  return (
    <div className="h-[600px]">
      <Calendar
        localizer={localizer}
        events={calendarEvents}
        startAccessor="start"
        endAccessor="end"
        defaultView="week"
        views={['month', 'week', 'day']}
        step={30}
        timeslots={2}
        components={{
          event: EventComponent,
          toolbar: CustomToolbar
        }}
        onSelectEvent={handleSelectEvent}
        popup
        popupOffset={30}
        eventPropGetter={(event) => {
          const appointment = event.resource;
          const backgroundColor = appointment.status === 'scheduled' ? '#EBF5FF' : 
                                 appointment.status === 'arrived' ? '#ECFDF5' : 
                                 appointment.status === 'cancelled' ? '#FEF2F2' : '#F3F4F6';
          return { style: { backgroundColor } };
        }}
        className="rounded-lg shadow bg-white p-4"
      />
    </div>
  );
};

export default AppointmentCalendar;
