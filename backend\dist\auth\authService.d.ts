/**
 * @file Implements the authentication service.
 */
import { LoginRequest, LoginResponse } from './authSchema';
/**
 * AuthService class provides methods for user authentication.
 */
export declare class AuthService {
    private medplumClient;
    constructor();
    /**
     * Handles user login.
     * @param credentials - An object containing the user's email and password.
     * @returns A Promise that resolves to a LoginResponse on success, or rejects with an error.
     */
    login(credentials: LoginRequest): Promise<LoginResponse>;
}
//# sourceMappingURL=authService.d.ts.map