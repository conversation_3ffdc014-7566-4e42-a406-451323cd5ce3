{"name": "@medplum/definitions", "version": "4.1.12", "description": "Medplum Data Definitions", "keywords": ["medplum", "fhir", "healthcare", "interoperability", "json", "serialization", "hl7", "standards", "clinical", "dstu2", "stu3", "r4", "normative"], "homepage": "https://www.medplum.com/", "bugs": {"url": "https://github.com/medplum/medplum/issues"}, "repository": {"type": "git", "url": "git+https://github.com/medplum/medplum.git", "directory": "packages/definitions"}, "license": "Apache-2.0", "author": "Medplum <<EMAIL>>", "sideEffects": false, "main": "dist/index.js", "files": ["dist"], "scripts": {"build": "npm run clean && tsc --project tsconfig.build.json", "clean": "rimraf dist/index.js dist/index.d.ts", "lint": "eslint .", "test": "jest"}, "engines": {"node": ">=20.0.0"}}