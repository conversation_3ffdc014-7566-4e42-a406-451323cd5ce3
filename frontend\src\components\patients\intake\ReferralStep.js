import React from 'react';
import { FaUserMd, FaIdCard } from 'react-icons/fa';

const ReferralStep = ({ patientData, handleInputChange }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900">Referral & Insurance Information</h3>
        <p className="mt-1 text-sm text-gray-500">
          Enter the patient's referral and insurance details
        </p>
      </div>

      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h4 className="text-md font-medium text-gray-900 flex items-center mb-4">
          <FaUserMd className="mr-2 text-primary" />
          Referral Information
        </h4>
        
        <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
          {/* New Referral Source Field */}
          <div className="sm:col-span-3">
            <label htmlFor="referralSource" className="block text-sm font-medium text-gray-700">
              Referral Source
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="referralSource"
                id="referralSource"
                value={patientData.referralSource}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="e.g., Cardiologist, Clinic Website, Word of Mouth"
              />
            </div>
          </div>

          <div className="sm:col-span-3">
            <label htmlFor="referringPhysician" className="block text-sm font-medium text-gray-700">
              Referring Physician (if applicable)
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="referringPhysician"
                id="referringPhysician"
                value={patientData.referringPhysician}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="Dr. Jane Smith"
              />
            </div>
          </div>

          <div className="sm:col-span-3">
            <label htmlFor="referralDate" className="block text-sm font-medium text-gray-700">
              Referral Date
            </label>
            <div className="mt-1">
              <input
                type="date"
                name="referralDate"
                id="referralDate"
                value={patientData.referralDate}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
          </div>
          
          {/* New Walk-in Indicator Checkbox */}
          <div className="sm:col-span-3 flex items-center pt-5">
             <div className="flex items-center h-5">
               <input
                 id="isWalkIn"
                 name="isWalkIn"
                 type="checkbox"
                 checked={patientData.isWalkIn}
                 onChange={handleInputChange} // Uses the updated handler
                 className="focus:ring-primary h-4 w-4 text-primary border-gray-300 rounded"
               />
             </div>
             <div className="ml-3 text-sm">
               <label htmlFor="isWalkIn" className="font-medium text-gray-700">
                 Patient is a Walk-in
               </label>
             </div>
           </div>
        </div>
      </div>

      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h4 className="text-md font-medium text-gray-900 flex items-center mb-4">
          <FaIdCard className="mr-2 text-primary" />
          Insurance Information
        </h4>
        
        <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
          <div className="sm:col-span-3">
            <label htmlFor="insuranceProvider" className="block text-sm font-medium text-gray-700">
              Insurance Provider
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="insuranceProvider"
                id="insuranceProvider"
                value={patientData.insuranceProvider}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="Blue Cross Blue Shield"
              />
            </div>
          </div>

          <div className="sm:col-span-3">
            <label htmlFor="insuranceId" className="block text-sm font-medium text-gray-700">
              Insurance ID
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="insuranceId"
                id="insuranceId"
                value={patientData.insuranceId}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="XYZ123456789"
              />
            </div>
          </div>

          <div className="sm:col-span-3">
            <label htmlFor="insuranceGroup" className="block text-sm font-medium text-gray-700">
              Group Number
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="insuranceGroup"
                id="insuranceGroup"
                value={patientData.insuranceGroup}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="G12345"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="bg-blue-50 p-4 rounded-md border border-blue-200">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3 flex-1 md:flex md:justify-between">
            <p className="text-sm text-blue-700">
              For EECP therapy, most insurance providers require a referral from a cardiologist.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReferralStep;
