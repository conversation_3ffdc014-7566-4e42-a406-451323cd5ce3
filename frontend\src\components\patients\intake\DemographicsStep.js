import React from 'react';

const DemographicsStep = ({ patientData, handleInputChange }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900">Patient Demographics</h3>
        <p className="mt-1 text-sm text-gray-500">
          Enter the patient's personal information
        </p>
      </div>

      <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
        <div className="sm:col-span-3">
          <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
            First name
          </label>
          <div className="mt-1">
            <input
              type="text"
              name="firstName"
              id="firstName"
              value={patientData.firstName}
              onChange={handleInputChange}
              className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
            />
          </div>
        </div>

        <div className="sm:col-span-3">
          <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
            Last name
          </label>
          <div className="mt-1">
            <input
              type="text"
              name="lastName"
              id="lastName"
              value={patientData.lastName}
              onChange={handleInputChange}
              className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
            />
          </div>
        </div>

        <div className="sm:col-span-3">
          <label htmlFor="dateOfBirth" className="block text-sm font-medium text-gray-700">
            Date of birth
          </label>
          <div className="mt-1">
            <input
              type="date"
              name="dateOfBirth"
              id="dateOfBirth"
              value={patientData.dateOfBirth}
              onChange={handleInputChange}
              className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
            />
          </div>
        </div>

        <div className="sm:col-span-3">
          <label htmlFor="gender" className="block text-sm font-medium text-gray-700">
            Gender
          </label>
          <div className="mt-1">
            <select
              id="gender"
              name="gender"
              value={patientData.gender}
              onChange={handleInputChange}
              className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
            >
              <option value="">Select gender</option>
              <option value="Male">Male</option>
              <option value="Female">Female</option>
              <option value="Other">Other</option>
              <option value="Prefer not to say">Prefer not to say</option>
            </select>
          </div>
        </div>

        <div className="sm:col-span-6">
          <label htmlFor="address" className="block text-sm font-medium text-gray-700">
            Street address
          </label>
          <div className="mt-1">
            <input
              type="text"
              name="address"
              id="address"
              value={patientData.address}
              onChange={handleInputChange}
              className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
            />
          </div>
        </div>

        <div className="sm:col-span-2">
          <label htmlFor="city" className="block text-sm font-medium text-gray-700">
            City
          </label>
          <div className="mt-1">
            <input
              type="text"
              name="city"
              id="city"
              value={patientData.city}
              onChange={handleInputChange}
              className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
            />
          </div>
        </div>

        <div className="sm:col-span-2">
          <label htmlFor="state" className="block text-sm font-medium text-gray-700">
            State / Province
          </label>
          <div className="mt-1">
            <input
              type="text"
              name="state"
              id="state"
              value={patientData.state}
              onChange={handleInputChange}
              className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
            />
          </div>
        </div>

        <div className="sm:col-span-2">
          <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700">
            ZIP / Postal code
          </label>
          <div className="mt-1">
            <input
              type="text"
              name="zipCode"
              id="zipCode"
              value={patientData.zipCode}
              onChange={handleInputChange}
              className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
            />
          </div>
        </div>

        <div className="sm:col-span-3">
          <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700">
            Phone number
          </label>
          <div className="mt-1">
            <input
              type="tel"
              name="phoneNumber"
              id="phoneNumber"
              value={patientData.phoneNumber}
              onChange={handleInputChange}
              className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
              placeholder="(*************"
            />
          </div>
        </div>

        <div className="sm:col-span-3">
          <label htmlFor="email" className="block text-sm font-medium text-gray-700">
            Email address
          </label>
          <div className="mt-1">
            <input
              type="email"
              name="email"
              id="email"
              value={patientData.email}
              onChange={handleInputChange}
              className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
              placeholder="<EMAIL>"
            />
          </div>
        </div>

        {/* New Occupation Field */}
        <div className="sm:col-span-3">
          <label htmlFor="occupation" className="block text-sm font-medium text-gray-700">
            Occupation
          </label>
          <div className="mt-1">
            <input
              type="text"
              name="occupation"
              id="occupation"
              value={patientData.occupation}
              onChange={handleInputChange}
              className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
            />
          </div>
        </div>

        {/* Unique Patient ID (Display Only) */}
        <div className="sm:col-span-3">
          <label htmlFor="uniquePatientId" className="block text-sm font-medium text-gray-700">
            Patient ID
          </label>
          <div className="mt-1">
            <input
              type="text"
              name="uniquePatientId"
              id="uniquePatientId"
              value={patientData.uniquePatientId}
              readOnly // Make it read-only as it's auto-generated
              className="shadow-sm block w-full sm:text-sm border-gray-300 rounded-md bg-gray-50 text-gray-500"
            />
          </div>
        </div>
      </div>

      {/* Emergency Contact Section */}
      <div className="pt-6">
        <h3 className="text-lg font-medium text-gray-900">Emergency Contact</h3>
        <p className="mt-1 text-sm text-gray-500">
          Provide information for an emergency contact person.
        </p>
        <div className="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
          <div className="sm:col-span-3">
            <label htmlFor="emergencyContactName" className="block text-sm font-medium text-gray-700">
              Contact Name
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="emergencyContact.name" // Use dot notation for nested state
                id="emergencyContactName"
                value={patientData.emergencyContact.name}
                onChange={handleInputChange} // Need to update handleInputChange to support nested state
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
          </div>

          <div className="sm:col-span-3">
            <label htmlFor="emergencyContactRelationship" className="block text-sm font-medium text-gray-700">
              Relationship
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="emergencyContact.relationship" // Use dot notation
                id="emergencyContactRelationship"
                value={patientData.emergencyContact.relationship}
                onChange={handleInputChange} // Need to update handleInputChange
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="e.g., Spouse, Sibling, Friend"
              />
            </div>
          </div>

          <div className="sm:col-span-3">
            <label htmlFor="emergencyContactPhone" className="block text-sm font-medium text-gray-700">
              Contact Phone
            </label>
            <div className="mt-1">
              <input
                type="tel"
                name="emergencyContact.phone" // Use dot notation
                id="emergencyContactPhone"
                value={patientData.emergencyContact.phone}
                onChange={handleInputChange} // Need to update handleInputChange
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="(*************"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DemographicsStep;
