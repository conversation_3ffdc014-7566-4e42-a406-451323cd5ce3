import React, { useRef, useState, forwardRef, useImperativeHandle } from 'react';
import { Printer } from 'lucide-react';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

// Add print styles
const printStyles = `
  @media print {
    @page {
      margin: 1.5cm;
      size: portrait;
    }
    
    body {
      -webkit-print-color-adjust: exact !important;
      print-color-adjust: exact !important;
      background: white !important;
    }

    .no-print {
      display: none !important;
    }

    .print-section {
      break-inside: avoid;
      page-break-inside: avoid;
      margin-bottom: 1.5rem;
    }

    .print-header {
      margin-bottom: 1.5rem;
    }

    table {
      width: 100% !important;
      break-inside: avoid;
      page-break-inside: avoid;
    }

    /* Hide navigation and other UI elements */
    nav, button, .tabs, header {
      display: none !important;
    }

    /* Reset background colors for better printing */
    .bg-gray-50 {
      background-color: transparent !important;
    }

    /* Ensure text is black for better printing */
    * {
      color: black !important;
    }

    /* Ensure borders are visible */
    .border {
      border: 1px solid #ddd !important;
    }
  }
`;

const MedicalHistoryTab = forwardRef(({ patient }, ref) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const contentRef = useRef(null);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const generatePDF = async () => {
    if (isGenerating) return;
    setIsGenerating(true);

    try {
      const content = contentRef.current;
      const canvas = await html2canvas(content, {
        scale: 2,
        useCORS: true,
        logging: false,
        backgroundColor: '#ffffff'
      });

      const imgWidth = 210; // A4 width in mm
      const pageHeight = 297; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      
      const pdf = new jsPDF('p', 'mm', 'a4');
      pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, 0, imgWidth, imgHeight);

      // Add more pages if content exceeds one page
      let heightLeft = imgHeight - pageHeight;
      let position = -pageHeight;

      while (heightLeft >= 0) {
        position = position - imgHeight;
        pdf.addPage();
        pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      // Open PDF in new window and print
      const pdfBlob = pdf.output('blob');
      const pdfUrl = URL.createObjectURL(pdfBlob);
      const printWindow = window.open(pdfUrl);
      
      printWindow.onload = () => {
        printWindow.print();
        URL.revokeObjectURL(pdfUrl);
      };
    } catch (error) {
      console.error('Error generating PDF:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  useImperativeHandle(ref, () => ({
    generatePDF
  }));

  // Safely access medical history data with fallbacks
  const medicalHistory = patient?.medicalHistory || {};
  const cardiovascular = medicalHistory?.cardiovascular || {};
  const riskFactors = medicalHistory?.riskFactors || {};
  const medications = medicalHistory?.medications || [];
  const allergies = medicalHistory?.allergies || [];
  const familyHistory = medicalHistory?.familyHistory || {};

  return (
    <div className="space-y-6">
      <style>{printStyles}</style>
      <div ref={contentRef} className="space-y-8">
        {/* Print Header */}
        <div className="hidden print:block print-header">
          <h1 className="text-2xl font-bold mb-2">{patient?.name} - Medical History</h1>
          <p className="text-gray-600">Printed on: {formatDate(new Date().toISOString())}</p>
        </div>

        {/* Primary Diagnosis */}
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-6 py-5">
            <h3 className="text-xl font-medium text-gray-900">Primary Diagnosis</h3>
          </div>
          <div className="border-t border-gray-200">
            <dl className="grid grid-cols-1 gap-x-6 gap-y-6 sm:grid-cols-2 p-6">
              <div>
                <dt className="text-sm font-medium text-gray-600">Primary Condition</dt>
                <dd className="mt-2 text-sm text-gray-900">{cardiovascular?.primaryDiagnosis || 'Not specified'}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-600">CAD Status</dt>
                <dd className="mt-2 text-sm text-gray-900">{cardiovascular?.cadStatus?.vessels || 'N/A'}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-600">Previous Interventions</dt>
                <dd className="mt-2 text-sm text-gray-900">
                  {cardiovascular?.previousInterventions?.length > 0 ? (
                    cardiovascular.previousInterventions.map((intervention, index) => (
                      <div key={index}>{intervention.type} ({formatDate(intervention.date)})</div>
                    ))
                  ) : (
                    'No previous interventions'
                  )}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-600">NYHA Class</dt>
                <dd className="mt-2 text-sm text-gray-900">{cardiovascular?.congestiveHeartFailure?.nyhaClass || 'N/A'}</dd>
              </div>
            </dl>
          </div>
        </div>

        {/* Personal Information */}
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-6 py-5">
            <h3 className="text-xl font-medium text-gray-900">Personal Information</h3>
          </div>
          <div className="border-t border-gray-200">
            <dl className="grid grid-cols-1 gap-x-6 gap-y-6 sm:grid-cols-3 p-6">
              <div>
                <dt className="text-sm font-medium text-gray-600">Age</dt>
                <dd className="mt-2 text-sm text-gray-900">{patient?.age || 'N/A'} years</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-600">Gender</dt>
                <dd className="mt-2 text-sm text-gray-900">{patient?.gender || 'N/A'}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-600">Status</dt>
                <dd className="mt-2 text-sm text-gray-900">{patient?.status || 'N/A'}</dd>
              </div>
            </dl>
          </div>
        </div>

        {/* Risk Factors */}
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-6 py-5">
            <h3 className="text-xl font-medium text-gray-900">Risk Factors</h3>
          </div>
          <div className="border-t border-gray-200">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Factor</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Duration</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Control</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Details</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {Object.entries(riskFactors).length > 0 ? (
                  Object.entries(riskFactors).map(([factor, info], index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {factor.charAt(0).toUpperCase() + factor.slice(1)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {typeof info.status === 'boolean' ? (info.status ? 'Yes' : 'No') : info.status}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{info.duration || 'N/A'}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{info.control || 'N/A'}</td>
                      <td className="px-6 py-4 text-sm text-gray-900">{info.details || 'N/A'}</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="5" className="px-6 py-4 text-center text-sm text-gray-500">
                      No risk factors recorded
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Medications */}
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-6 py-5">
            <h3 className="text-xl font-medium text-gray-900">Medication History</h3>
          </div>
          <div className="border-t border-gray-200">
            <div className="p-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {medications.length > 0 ? (
                  medications.map((medication, index) => (
                    <div key={index} className={`p-3 rounded-lg border ${medication.status === 'active' ? 'bg-white' : 'bg-gray-50'}`}>
                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-gray-900">{medication.name}</h4>
                          <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                            medication.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                          }`}>
                            {medication.status === 'active' ? 'Active' : 'Discontinued'}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600">
                          {medication.dosage} • {medication.frequency}
                        </p>
                        <p className="text-sm text-gray-500">{medication.class}</p>
                        <div className="text-xs text-gray-500">
                          Started: {formatDate(medication.startDate)}
                          {medication.endDate && (
                            <>
                              <br />
                              Ended: {formatDate(medication.endDate)}
                              {medication.reason && (
                                <p className="mt-1 italic">
                                  Reason: {medication.reason}
                                </p>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="col-span-full text-center text-sm text-gray-500">
                    No medications recorded
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Allergies */}
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-6 py-5">
            <h3 className="text-xl font-medium text-gray-900">Allergies</h3>
          </div>
          <div className="border-t border-gray-200">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Allergen</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Reaction</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Severity</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {allergies.length > 0 ? (
                  allergies.map((allergy, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{allergy.substance}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{allergy.reaction}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          allergy.severity.toLowerCase() === 'high' ? 'bg-red-100 text-red-800' :
                          allergy.severity.toLowerCase() === 'moderate' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {allergy.severity}
                        </span>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="3" className="px-6 py-4 text-center text-sm text-gray-500">
                      No allergies recorded
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Family History */}
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-6 py-5">
            <h3 className="text-xl font-medium text-gray-900">Family History</h3>
          </div>
          <div className="border-t border-gray-200">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Relation</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Condition</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Age of Onset</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {Object.entries(familyHistory).length > 0 ? (
                  Object.entries(familyHistory)
                    .filter(([_, value]) => value.status)
                    .map(([relation, info], index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {relation.charAt(0).toUpperCase() + relation.slice(1)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{info.condition}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{info.ageOfOnset}</td>
                      </tr>
                    ))
                ) : (
                  <tr>
                    <td colSpan="3" className="px-6 py-4 text-center text-sm text-gray-500">
                      No family history recorded
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
});

MedicalHistoryTab.displayName = 'MedicalHistoryTab';

export default MedicalHistoryTab; 