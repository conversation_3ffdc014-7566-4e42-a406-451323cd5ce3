import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { FaUserPlus, FaArrowLeft, FaArrowRight, FaCheck, FaSave, FaExclamationTriangle, FaInfoCircle, FaMicrophone, FaMicrophoneSlash, FaRobot, FaKeyboard, FaSpinner } from 'react-icons/fa';

// Import steps
import DemographicsStep from './intake/DemographicsStep';
import ReferralStep from './intake/ReferralStep';
import MedicalHistoryStep from './intake/MedicalHistoryStep';
import VitalsStep from './intake/VitalsStep';
import SuccessStep from './intake/SuccessStep';

const PatientIntakeWizard = () => {
  const navigate = useNavigate();
  const { medplum } = useAuth();
  
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [entryMode, setEntryMode] = useState('manual'); // 'manual' or 'ai-scribe'
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const timerRef = useRef(null);
  
  
  // Define fields/topics for each step for AI Scribe guidance
  const aiStepTopics = useMemo(() => ({
    1: { name: 'Demographics & Basic Info', fields: ['Full Name', 'Date of Birth', 'Gender', 'Occupation', 'Address (Street, City, State/Province, ZIP/Postal Code)', 'Phone Number', 'Email', 'Emergency Contact Name', 'Emergency Contact Relationship', 'Emergency Contact Phone'] },
    2: { name: 'Referral & Administrative', fields: ['Referral Source', 'Referring Physician', 'Referral Date', 'Referral Reason', 'Walk-in (Yes/No)', 'Insurance Provider', 'Insurance ID', 'Insurance Group'] },
    3: { name: 'Medical History', fields: ['Diagnoses', 'Medications', 'Allergies', 'Past Surgeries', 'Family History', 'Cardiovascular History (MI, CAD, HF, Arrhythmia, HTN, etc.)'] },
    4: { name: 'Vitals & Diagnostics', fields: ['Height', 'Weight', 'Blood Pressure', 'Heart Rate', 'O2 Saturation', 'LVEF', 'BNP', '6-Minute Walk Distance', 'ECG/Stress Test Findings'] }
  }), []);

  const [patientData, setPatientData] = useState({
    // Demographics
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    gender: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    phoneNumber: '',
    email: '',
    occupation: '', // New field from specs
    uniquePatientId: `SYN-${Date.now().toString().slice(-6)}`, // Auto-generated placeholder from specs
    emergencyContact: { // New structure for emergency contact from specs
      name: '',
      relationship: '',
      phone: ''
    },
    
    // Referral/Insurance
    referralSource: '', // New field from specs
    isWalkIn: false, // New field (Walk-in Indicator) from specs
    referringPhysician: '',
    referralDate: '',
    referralReason: '',
    insuranceProvider: '',
    insuranceId: '',
    insuranceGroup: '',
    
    // Medical History
    diagnoses: [],
    medications: [],
    allergies: [],
    pastSurgeries: [],
    familyHistory: '',
    cardiovascularHistory: {
      myocardialInfarction: false,
      myocardialInfarctionDate: '',
      coronaryArteryDisease: false,
      heartFailure: false,
      nyhaClass: '',
      valvularDisease: false,
      arrhythmia: false,
      arrhythmiaType: '',
      hypertension: false,
      hyperlipidemia: false,
      diabetes: false,
      peripheralVascularDisease: false,
      stroke: false,
      strokeDate: '',
    },
    
    // Vitals & Diagnostics
    height: '',
    weight: '',
    bmi: '',
    bloodPressure: '',
    heartRate: '',
    respiratoryRate: '',
    oxygenSaturation: '',
    temperature: '',
    lvef: '',
    bnp: '',
    sixMinuteWalkDistance: '',
    ecgFindings: '',
    stressTestResults: '',
  });
  
  const [contraindications, setContraindications] = useState([]);
  const [autoSaveStatus, setAutoSaveStatus] = useState('saved'); // 'saved', 'saving', 'error'
  const [validationErrors, setValidationErrors] = useState({});
  const [fieldsToClarify, setFieldsToClarify] = useState([]); // State to track missing/invalid fields
  // Remove state related to individual questions
  const [formStateDebugLog, setFormStateDebugLog] = useState([]);

  const totalSteps = 5;

  // Define required fields for each step (used for clarification prompts)
  const requiredFieldsByStep = useMemo(() => ({
    1: ['firstName', 'lastName', 'dateOfBirth', 'gender', 'emergencyContact.name', 'emergencyContact.phone'], // Added emergency contact fields from specs
    2: ['referralSource', 'referralDate'], // Changed referringPhysician to referralSource as potentially more general, kept referralDate
    3: [], 
    4: ['lvef'] 
  }), []);
  
  // Create a ref to store the latest patient data
  const latestPatientDataRef = useRef(patientData);
  
  // Create a debounced function for saving form state
  const debouncedSaveRef = useRef(null);
  const fieldCommitTimeoutRef = useRef(null);
  
  // Auto-save functionality
  const autoSave = useCallback(async () => {
    const dataToSave = latestPatientDataRef.current; // Use the up-to-date version
  
    if (currentStep < totalSteps) {
      setAutoSaveStatus('saving');
      try {
        if (medplum && typeof medplum.isSignedIn === 'function' && medplum.isSignedIn()) {
          console.log('Saving patient data to Medplum:', dataToSave);
        } else {
          console.log('Saving patient data locally:', dataToSave);
          localStorage.setItem('syncore_patient_data', JSON.stringify(dataToSave));
        }
        await new Promise(resolve => setTimeout(resolve, 800));
        setAutoSaveStatus('saved');
      } catch (error) {
        console.error('Auto-save error:', error);
        setAutoSaveStatus('error');
      }
    }
  }, [currentStep, totalSteps, medplum]);
  
  
  // Debug logging function
  const logFormStateTransition = useCallback((action, data = {}) => {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      action,
      // currentQuestion, // Removed
      ...data
    };
    console.debug('Form State Transition:', logEntry);
    setFormStateDebugLog(prev => [...prev, logEntry]);
  }, []); // Removed currentQuestion dependency
  
  // Debounce function implementation
  function debounce(func, wait) {
    let timeout;
    
    const debounced = function(...args) {
      const context = this;
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(context, args), wait);
    };
    
    debounced.cancel = function() {
      clearTimeout(timeout);
    };
    
    return debounced;
  }
  
  // Keep the ref in sync with the state
  useEffect(() => {
    latestPatientDataRef.current = patientData;
  }, [patientData]);
  
  // Debug actual updates to patientData
  useEffect(() => {
    console.log('Patient data updated (from useEffect):', patientData);
  }, [patientData]);
  
  
  // Check for contraindications
  useEffect(() => {
    const checkContraindications = () => {
      const newContraindications = [];
      
      // Check LVEF
      if (patientData.lvef && parseFloat(patientData.lvef) < 30) {
        newContraindications.push({
          severity: 'high',
          message: 'LVEF < 30% is a potential contraindication for EECP therapy',
          detail: 'Patients with severely reduced ejection fraction may require additional cardiac evaluation before proceeding'
        });
      }
      
      // Check for recent MI
      if (patientData.cardiovascularHistory.myocardialInfarction && patientData.cardiovascularHistory.myocardialInfarctionDate) {
        const miDate = new Date(patientData.cardiovascularHistory.myocardialInfarctionDate);
        const threeMonthsAgo = new Date();
        threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
        
        if (miDate > threeMonthsAgo) {
          newContraindications.push({
            severity: 'high',
            message: 'Recent myocardial infarction (within 3 months) is a contraindication for EECP therapy',
            detail: 'Consider delaying treatment until at least 3 months post-MI'
          });
        }
      }
      
      // Check for arrhythmia
      if (patientData.cardiovascularHistory.arrhythmia) {
        newContraindications.push({
          severity: 'medium',
          message: 'Arrhythmia detected',
          detail: 'Certain arrhythmias may affect EECP timing. Additional monitoring may be required.'
        });
      }
      
      // Check BNP levels
      if (patientData.bnp && parseFloat(patientData.bnp) > 500) {
        newContraindications.push({
          severity: 'medium',
          message: 'Elevated BNP levels (>500 pg/mL)',
          detail: 'May indicate decompensated heart failure. Consider additional evaluation.'
        });
      }
      
      setContraindications(newContraindications);
    };
    
    checkContraindications();
  }, [patientData]);
  
  // Validate form data
  const validateStep = (step) => {
    const errors = {};
    
    switch (step) {
      case 1: // Demographics
        if (!patientData.firstName.trim()) errors.firstName = 'First name is required';
        if (!patientData.lastName.trim()) errors.lastName = 'Last name is required';
        if (!patientData.dateOfBirth) errors.dateOfBirth = 'Date of birth is required';
        if (!patientData.gender) errors.gender = 'Gender is required';
        break;
        
      case 2: // Referral
        if (!patientData.referringPhysician.trim()) errors.referringPhysician = 'Referring physician is required';
        if (!patientData.referralDate) errors.referralDate = 'Referral date is required';
        break;
        
      case 3: // Medical History
        // No required fields for medical history
        break;
        
      case 4: // Vitals
        if (patientData.height && !patientData.weight) errors.weight = 'Weight is required when height is provided';
        if (!patientData.height && patientData.weight) errors.height = 'Height is required when weight is provided';
        if (patientData.lvef && (parseFloat(patientData.lvef) < 0 || parseFloat(patientData.lvef) > 100)) {
          errors.lvef = 'LVEF must be between 0 and 100';
        }
        break;
        
      default:
        break;
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    // Handle nested state updates (e.g., emergencyContact.name)
    if (name.includes('.')) {
      const [parentKey, childKey] = name.split('.');
      setPatientData(prevData => ({
        ...prevData,
        [parentKey]: {
          ...prevData[parentKey],
          [childKey]: value
        }
      }));
    } else if (type === 'checkbox') {
      // Handle checkboxes (like isWalkIn)
       setPatientData(prevData => ({
         ...prevData,
         [name]: checked
       }));
    } else {
      // Handle regular top-level fields
      setPatientData(prevData => ({
        ...prevData,
        [name]: value
      }));
    }
  };

  const handleArrayItemAdd = (field, item) => {
    setPatientData({
      ...patientData,
      [field]: [...patientData[field], item]
    });
  };

  const handleArrayItemRemove = (field, index) => {
    setPatientData({
      ...patientData,
      [field]: patientData[field].filter((_, i) => i !== index)
    });
  };

  //Fix BMI calculation loop in VitalsStep using useCallback without
  // The original was doing an infinite loop
  const calculateBMI = useCallback(() => {
  if (patientData.height && patientData.weight) {
    // Height in meters (assuming input is in cm)
    const heightInM = patientData.height / 100;
    // Weight in kg
    const weightInKg = parseFloat(patientData.weight);

    if (heightInM > 0 && weightInKg > 0) {
      const bmi = (weightInKg / (heightInM * heightInM)).toFixed(1);
      setPatientData(prev => ({
        ...prev,
        bmi,
      }));
    }
  }
  }, [patientData.height, patientData.weight]);


  const handleNext = () => {
    if (validateStep(currentStep) && currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
      window.scrollTo(0, 0);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      window.scrollTo(0, 0);
    }
  };

  // Start recording for AI Scribe
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      audioChunksRef.current = [];
      
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };
      
      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        // Pass the current step info to processAudio
        await processAudio(audioBlob, currentStep); 
      };
      
      // Start recording
      mediaRecorder.start(1000);
      setIsRecording(true);
      
      // Start timer
      setRecordingTime(0);
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
      
    } catch (error) {
      console.error('Error starting recording:', error);
      alert('Could not access microphone. Please check permissions.');
    }
  };
  
  // Stop recording
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
      setIsRecording(false);
      
      // Stop timer
      clearInterval(timerRef.current);
    }
  };
  
  // Transformation functions for field values
  const transformValue = (value, transformType) => {
    switch (transformType) {
      case 'normalizeDateFormat':
        // Try to parse various date formats and convert to YYYY-MM-DD
        try {
          // Check if it's already in ISO format
          if (/^\d{4}-\d{2}-\d{2}$/.test(value)) {
            return value;
          }
          
          // Try to parse MM/DD/YYYY
          const mmddyyyyMatch = value.match(/(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})/);
          if (mmddyyyyMatch) {
            const month = mmddyyyyMatch[1].padStart(2, '0');
            const day = mmddyyyyMatch[2].padStart(2, '0');
            const year = mmddyyyyMatch[3];
            return `${year}-${month}-${day}`;
          }
          
          // Try to extract date from text like "born on January 15, 1980"
          const months = {
            'january': '01', 'february': '02', 'march': '03', 'april': '04',
            'may': '05', 'june': '06', 'july': '07', 'august': '08',
            'september': '09', 'october': '10', 'november': '11', 'december': '12'
          };
          
          for (const [monthName, monthNum] of Object.entries(months)) {
            const regex = new RegExp(`${monthName}\\s+(\\d{1,2})(?:\\w{2})?,?\\s+(\\d{4})`, 'i');
            const match = value.match(regex);
            if (match) {
              const day = match[1].padStart(2, '0');
              const year = match[2];
              return `${year}-${monthNum}-${day}`;
            }
          }
          
          // Try to extract a year
          const yearMatch = value.match(/\b(19\d{2}|20\d{2})\b/);
          if (yearMatch) {
            // If we only have a year, default to January 1
            return `${yearMatch[1]}-01-01`;
          }
          
          // If all else fails, return the original value
          return value;
        } catch (e) {
          console.error('Date normalization error:', e);
          return value;
        }
        
      case 'capitalize':
        // Capitalize first letter of each word
        if (typeof value !== 'string') return value;
        return value.replace(/\b\w/g, l => l.toUpperCase());
        
      case 'toTitleCase':
        // Convert to title case
        if (typeof value !== 'string') return value;
        return value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
        
      default:
        return value;
    }
  };
  
  // Process audio with Whisper and update fields using conversational AI
  const processAudio = async (audioBlob, step) => {
    setIsProcessing(true);
    // Read OpenAI API key from environment variables
    const apiKey = process.env.REACT_APP_OPENAI_API_KEY; 

    if (!apiKey) {
      alert('OpenAI API key not found. Please ensure REACT_APP_OPENAI_API_KEY is set in your .env file.');
      setIsProcessing(false);
      return;
    }

    try {
      // 1. Transcribe Audio using Whisper
      const formData = new FormData();
      formData.append('file', audioBlob, 'recording.wav');
      formData.append('model', 'whisper-1');

      console.log('Starting API call to Whisper...');
      const whisperResponse = await fetch('https://api.openai.com/v1/audio/transcriptions', {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${apiKey}` },
        body: formData
      });

      if (!whisperResponse.ok) {
        const errorText = await whisperResponse.text();
        console.error('Whisper API error:', whisperResponse.status, errorText);
        throw new Error(`Whisper API error: ${whisperResponse.status} ${errorText}`);
      }

      const whisperData = await whisperResponse.json();
      const transcriptionText = whisperData.text;
      console.log('Raw transcription:', transcriptionText);

      // 2. Extract Structured Data using GPT-4o-mini
      const stepInfo = aiStepTopics[step];
      if (!stepInfo) {
        console.error(`No AI topics defined for step ${step}`);
        throw new Error(`Configuration error: No AI topics for step ${step}`);
      }

      // Define the fields expected for the current step based on patientData structure
      let relevantFields = [];
      switch (step) {
        case 1: // Demographics & Basic Info
          relevantFields = ['firstName', 'lastName', 'dateOfBirth', 'gender', 'occupation', 'address', 'city', 'state', 'zipCode', 'phoneNumber', 'email', 'emergencyContact']; // Added occupation, emergencyContact (as object)
          break;
        case 2: // Referral & Administrative
          relevantFields = ['referralSource', 'referringPhysician', 'referralDate', 'referralReason', 'isWalkIn', 'insuranceProvider', 'insuranceId', 'insuranceGroup']; // Added referralSource, isWalkIn
          break;
        case 3: // Medical History
          relevantFields = ['diagnoses', 'medications', 'allergies', 'pastSurgeries', 'familyHistory', 'cardiovascularHistory']; 
          break;
        case 4: // Vitals & Diagnostics
          relevantFields = ['height', 'weight', 'bmi', 'bloodPressure', 'heartRate', 'respiratoryRate', 'oxygenSaturation', 'temperature', 'lvef', 'bnp', 'sixMinuteWalkDistance', 'ecgFindings', 'stressTestResults'];
          break;
        default:
          console.warn(`No specific fields defined for extraction in step ${step}`);
      }

      const systemPrompt = `You are an AI assistant processing a patient intake conversation for an EECP clinic (Syncore EHR).
The current intake step is Step ${step}: ${stepInfo.name}.
Analyze the following transcription and extract information relevant to these fields: ${relevantFields.join(', ')}.
Structure the output as a valid JSON object containing ONLY the extracted data. Use the exact field names as listed.
For 'cardiovascularHistory', return an object with relevant sub-fields.
For 'emergencyContact', return an object with 'name', 'relationship', and 'phone'.
For list fields like 'diagnoses', 'medications', 'allergies', 'pastSurgeries', return an array of strings.
For 'isWalkIn', return a boolean (true/false).
Normalize dates to YYYY-MM-DD format if possible.
If a field is not mentioned or unclear in the transcription, omit it entirely from the JSON response. Do not guess or add fields that are not present.
Your response MUST be only the JSON object, without any introductory text or explanations.`;

      console.log('Starting API call to GPT-4o-mini...');
      const chatResponse = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: "gpt-4o-mini", // Using the specified model
          messages: [
            { role: "system", content: systemPrompt },
            { role: "user", content: transcriptionText }
          ],
          response_format: { type: "json_object" }, // Ensure JSON output
          temperature: 0.2 // Lower temperature for more deterministic extraction
        })
      });

      if (!chatResponse.ok) {
        const errorText = await chatResponse.text();
        console.error('GPT-4o-mini API error:', chatResponse.status, errorText);
        throw new Error(`GPT-4o-mini API error: ${chatResponse.status} ${errorText}`);
      }

      const chatData = await chatResponse.json();
      console.log('GPT-4o-mini API response:', chatData);

      if (!chatData.choices || !chatData.choices[0] || !chatData.choices[0].message || !chatData.choices[0].message.content) {
        console.error('Unexpected response format from GPT-4o-mini:', chatData);
        throw new Error('Unexpected response format from OpenAI Chat API');
      }

      // 3. Parse and Process Extracted Data
      let extractedData = {};
      try {
        const contentStr = chatData.choices[0].message.content;
        extractedData = JSON.parse(contentStr);
        console.log('Parsed extracted data:', extractedData);
      } catch (parseError) {
        console.error('Error parsing JSON from GPT-4o-mini response:', parseError);
        // Attempt to clean the string if it's wrapped in markdown ```json ... ```
        const jsonMatch = chatData.choices[0].message.content.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch && jsonMatch[1]) {
          try {
            extractedData = JSON.parse(jsonMatch[1]);
            console.log('Parsed extracted data after cleaning:', extractedData);
          } catch (nestedParseError) {
             console.error('Still failed parsing JSON after cleaning:', nestedParseError);
             throw new Error('Failed to parse structured data from OpenAI response even after cleaning.');
          }
        } else {
          throw new Error('Failed to parse structured data from OpenAI response.');
        }
      }

      // 4. Process, Validate, and Merge Extracted Data
      const newData = JSON.parse(JSON.stringify(patientData)); // Deep copy
      const newlyInvalidFields = []; // Track fields failing validation in this run

      // Define fields to treat as numeric
      const numericFields = ['height', 'weight', 'lvef', 'bnp', 'sixMinuteWalkDistance', 'temperature', 'heartRate', 'respiratoryRate', 'oxygenSaturation'];
      
      // Define Canadian Provinces/Territories for validation (based on image context)
      // NOTE: Assuming Canadian context based on image (Calgary, D2Y 0V2). Adjust if context is different (e.g., India).
      const canadianProvincesTerritories = [
        'AB', 'BC', 'MB', 'NB', 'NL', 'NS', 'NT', 'NU', 'ON', 'PE', 'QC', 'SK', 'YT'
      ];
      const canadianPostalCodeRegex = /^[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d$/;


      for (const key in extractedData) {
        if (!newData.hasOwnProperty(key)) continue; // Only process keys that exist in our state structure

        let value = extractedData[key];

        // --- Data Type Conversion & Basic Cleaning ---
        if (numericFields.includes(key)) {
          const num = parseFloat(value);
          value = isNaN(num) ? '' : num; // Keep as number or empty string if invalid
          if (value === '' && extractedData[key]) { // Log if conversion failed
             console.warn(`Failed to parse numeric value for ${key}:`, extractedData[key]);
             newlyInvalidFields.push({ field: key, reason: 'Invalid number format' });
          }
        } else if (key === 'dateOfBirth' || key === 'referralDate' || key === 'myocardialInfarctionDate') {
          value = transformValue(value, 'normalizeDateFormat');
        } else if (typeof value === 'string') {
           value = value.trim(); // Trim whitespace
        }

        // --- Validation ---
        let isValid = true;
        let validationReason = '';
        if (key === 'state') { // Assuming 'state' maps to Province/Territory
           // Simple validation: check if uppercase version is in the list
           if (value && !canadianProvincesTerritories.includes(value.toUpperCase())) {
             isValid = false;
             validationReason = 'Invalid province/territory code';
             console.warn(`Invalid province/territory detected: ${value}`);
           } else if (value) {
             value = value.toUpperCase(); // Standardize to uppercase
           }
        } else if (key === 'zipCode') { // Assuming 'zipCode' maps to Postal Code
           if (value && !canadianPostalCodeRegex.test(value)) {
             isValid = false;
             validationReason = 'Invalid postal code format';
             console.warn(`Invalid postal code format detected: ${value}`);
           } else if (value) {
             // Standardize format (e.g., uppercase, add space if missing)
             value = value.toUpperCase().replace(/([A-Z]\d[A-Z])(\d[A-Z]\d)/, '$1 $2');
           }
        } else if (key === 'gender') {
            // Example: Validate against expected values if using a dropdown
            const validGenders = ['Male', 'Female', 'Other', 'Prefer not to say']; // Match dropdown options
            if (value && !validGenders.some(g => g.toLowerCase() === value.toLowerCase())) {
                isValid = false;
                validationReason = 'Invalid gender value';
                console.warn(`Invalid gender detected: ${value}`);
            } else if (value) {
                 // Standardize casing to match dropdown options (e.g., Title Case)
                 value = value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
            }
        }
        // Add more validations as needed (e.g., email format, phone format)


        // --- Merging ---
        if (!isValid) {
          newlyInvalidFields.push({ field: key, reason: validationReason });
          // Do not update newData[key] if validation failed
          continue;
        }

        if (key === 'cardiovascularHistory' && typeof value === 'object' && value !== null) {
          newData.cardiovascularHistory = { ...newData.cardiovascularHistory, ...value };
        } else if (key === 'emergencyContact' && typeof value === 'object' && value !== null) {
          // Merge emergencyContact object, ensuring sub-fields exist
          newData.emergencyContact = { 
             name: value.name || newData.emergencyContact.name || '',
             relationship: value.relationship || newData.emergencyContact.relationship || '',
             phone: value.phone || newData.emergencyContact.phone || ''
          };
        } else if (key === 'isWalkIn') {
           // Handle boolean specifically, default to false if not clearly true
           newData[key] = typeof value === 'boolean' ? value : (typeof value === 'string' && value.toLowerCase() === 'true');
        } else if (Array.isArray(value) && Array.isArray(newData[key])) {
          // Append unique items only to avoid duplicates
          const existingItems = new Set(newData[key]);
          const itemsToAdd = value.filter(item => typeof item === 'string' && item.trim() !== '' && !existingItems.has(item.trim())); // Ensure items are non-empty strings
          newData[key] = [...newData[key], ...itemsToAdd];
        } else {
          // Handle simple value overwrite for other fields
          newData[key] = value;
        }
      }

      // Recalculate BMI if height/weight changed and are valid numbers
      const heightNum = typeof newData.height === 'number' ? newData.height : parseFloat(newData.height);
      const weightNum = typeof newData.weight === 'number' ? newData.weight : parseFloat(newData.weight);
      if (!isNaN(heightNum) && heightNum > 0 && !isNaN(weightNum) && weightNum > 0) {
        const heightInM = heightNum / 100;
        newData.bmi = (weightNum / (heightInM * heightInM)).toFixed(1);
      } else {
        newData.bmi = ''; // Clear BMI if height/weight are invalid or missing
      }

      // 5. Update State and Identify Fields for Clarification
      console.log('Updating patient data with validated/processed data:', newData);
      setPatientData(newData);
      latestPatientDataRef.current = newData;
      autoSave();

      // Check for missing required fields *after* update, handling nested fields like emergencyContact.name
      const missingRequired = (requiredFieldsByStep[step] || []).filter(fieldPath => {
         let value;
         // Handle nested paths like 'emergencyContact.name'
         if (fieldPath.includes('.')) {
           const parts = fieldPath.split('.');
           value = newData[parts[0]] ? newData[parts[0]][parts[1]] : undefined;
         } else {
           value = newData[fieldPath];
         }
         return value === null || value === undefined || value === '' || (Array.isArray(value) && value.length === 0);
      });

      const clarificationNeeded = [
          ...newlyInvalidFields,
          ...missingRequired.map(field => ({ field, reason: 'Missing required information' }))
      ];

      // Remove duplicates by field name, prioritizing validation errors
      const uniqueClarifications = Array.from(new Map(clarificationNeeded.map(item => [item.field, item])).values());

      setFieldsToClarify(uniqueClarifications);
      logFormStateTransition('clarificationCheck', { needed: uniqueClarifications });


      // Highlight updated fields (optional visual feedback) - Keep this part
      setTimeout(() => {
        // Highlight fields that were successfully updated by the AI
        Object.keys(extractedData).forEach(key => {
          // Only highlight if it wasn't marked as invalid
          if (!newlyInvalidFields.some(f => f.field === key)) {
            const inputs = document.querySelectorAll(`[name="${key}"]`);
            inputs.forEach(input => {
              if (input) {
                input.style.backgroundColor = '#e0f2fe'; // Lighter blue
                input.style.transition = 'background-color 0.5s ease';
                setTimeout(() => {
                  input.style.backgroundColor = ''; // Fade back
                }, 1500);
              }
            });
          }
        });
      }, 100);

    } catch (error) {
      console.error('Error in processAudio pipeline:', error);
      alert(`Error processing audio: ${error.message}. Please try again or use manual entry.`);
      // Optionally fallback to simulation or just stop
      // simulateWhisperResponse(); // Keep simulation logic if needed for fallback/demo
    } finally {
      setIsProcessing(false);
    }
  };
  
  
  // Toggle recording
  const toggleRecording = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };
  
  // Format seconds to MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60).toString().padStart(2, '0');
    const secs = (seconds % 60).toString().padStart(2, '0');
    return `${mins}:${secs}`;
  };
  
  // Switch between manual and AI Scribe modes
  const toggleEntryMode = () => {
    const newMode = entryMode === 'manual' ? 'ai-scribe' : 'manual';
    setEntryMode(newMode);
  };

  // Function for handling the submission of the patient data. 
  const handleSubmit = async () => {
    setLoading(true);
 
    try {
      // Format the patient data for storage, MAY REQUIRE CHANGES as data structure is not accurate.
      const formattedPatient = {
        id: patientData.uniquePatientId,
        name: `${patientData.firstName} ${patientData.lastName}`,
        age: calculateAge(patientData.dateOfBirth),
        gender: patientData.gender,
        status: 'Active',
        diagnosis: patientData.diagnoses[0] || 'Not specified',
        lastVisit: new Date().toISOString(),
        nextAppointment: null,
        lvef: patientData.lvef || null,
        bnp: patientData.bnp || null,
        medicalHistory: {
          cardiovascular: {
            primaryDiagnosis: patientData.diagnoses[0] || 'Not specified',
            cadStatus: {
              vessels: patientData.cardiovascularHistory?.coronaryArteryDisease ? 'Present' : 'Not specified',
              severity: 'Not specified'
            },
            previousInterventions: patientData.pastSurgeries || [],
            congestiveHeartFailure: {
              nyhaClass: patientData.cardiovascularHistory?.nyhaClass || 'Not specified'
            }
          },
          riskFactors: {
            hypertension: patientData.cardiovascularHistory?.hypertension || false,
            diabetes: patientData.cardiovascularHistory?.diabetes || false,
            smoking: false,
            hyperlipidemia: patientData.cardiovascularHistory?.hyperlipidemia || false
          },
          medications: patientData.medications || [],
          allergies: patientData.allergies || [],
          familyHistory: {
            cardiovascular: patientData.familyHistory?.includes('cardiovascular') || false,
            diabetes: patientData.familyHistory?.includes('diabetes') || false
          }
        },
        notes: [],
        outcomeMeasures: {
          baseline: {
            date: new Date().toISOString(),
            bloodPressure: {
              systolic: patientData.bloodPressure?.split('/')?.[0] || '',
              diastolic: patientData.bloodPressure?.split('/')?.[1] || ''
            },
            anginaClass: 'Not specified',
            lvef: patientData.lvef || '',
            walkDistance: patientData.sixMinuteWalkDistance || '',
            qualityOfLife: ''
          },
          current: {
            date: new Date().toISOString(),
            bloodPressure: {
              systolic: patientData.bloodPressure?.split('/')?.[0] || '',
              diastolic: patientData.bloodPressure?.split('/')?.[1] || ''
            },
            anginaClass: 'Not specified',
            lvef: patientData.lvef || '',
            walkDistance: patientData.sixMinuteWalkDistance || '',
            qualityOfLife: ''
          },
          improvement: {
            bloodPressure: '',
            anginaClass: '',
            lvef: '',
            walkDistance: '',
            qualityOfLife: ''
          }
        }
      };

      // Create post request to json server.
      fetch('http://localhost:4000/patients',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(formattedPatient)
        }
      ).then(()=> {
        console.log("Patient data saved successfully")
      })

      
      // Move to success step
      setCurrentStep(totalSteps);
      
    } catch (error) {
      console.error('Error saving patient:', error);
      alert('Failed to save patient. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Helper function to calculate age from date of birth
  const calculateAge = (dateOfBirth) => {
    if (!dateOfBirth) return null;
    const birthDate = new Date(dateOfBirth);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  const handleFinish = () => {
    // Navigate back to the dashboard based on user role
    navigate(-1);
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <DemographicsStep 
            patientData={patientData} 
            handleInputChange={handleInputChange} 
          />
        );
      case 2:
        return (
          <ReferralStep 
            patientData={patientData} 
            handleInputChange={handleInputChange} 
          />
        );
      case 3:
        return (
          <MedicalHistoryStep 
            patientData={patientData} 
            handleInputChange={handleInputChange}
            handleArrayItemAdd={handleArrayItemAdd}
            handleArrayItemRemove={handleArrayItemRemove}
          />
        );
      case 4:
        return (
          <VitalsStep 
            patientData={patientData} 
            handleInputChange={handleInputChange}
            calculateBMI={calculateBMI}
          />
        );
      case 5:
        return (
          <SuccessStep 
            patientData={patientData} 
            handleFinish={handleFinish}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <header className="mb-6">
        <div className="flex items-center">
          <FaUserPlus className="h-8 w-8 text-primary mr-3" />
          <h1 className="text-3xl font-bold text-gray-900">Patient Intake</h1>
        </div>
        <p className="mt-1 text-sm text-gray-500">
          Register a new patient for EECP therapy
        </p>
      </header>

      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {Array.from({ length: totalSteps }, (_, i) => (
            <div 
              key={i} 
              className={`flex flex-col items-center ${
                i + 1 < currentStep 
                  ? 'text-primary' 
                  : i + 1 === currentStep 
                    ? 'text-primary' 
                    : 'text-gray-400'
              }`}
            >
              <div className={`flex items-center justify-center h-8 w-8 rounded-full border-2 ${
                i + 1 < currentStep 
                  ? 'border-primary bg-primary text-white' 
                  : i + 1 === currentStep 
                    ? 'border-primary text-primary' 
                    : 'border-gray-300 text-gray-400'
              }`}>
                {i + 1 < currentStep ? (
                  <FaCheck className="h-4 w-4" />
                ) : (
                  <span>{i + 1}</span>
                )}
              </div>
              <div className="mt-2 text-xs font-medium">
                {i === 0 ? 'Demographics' : 
                 i === 1 ? 'Referral' : 
                 i === 2 ? 'Medical History' : 
                 i === 3 ? 'Vitals' : 'Complete'}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Entry Mode Toggle */}
      {currentStep < totalSteps && (
        <div className="mb-6">
          <div className="flex items-center justify-end">
            <span className="mr-3 text-sm text-gray-500">
              {entryMode === 'manual' ? 'Manual Entry' : 'AI-Guided Entry'}
            </span>
            <button
              type="button"
              onClick={toggleEntryMode}
              className={`relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary ${
                entryMode === 'ai-scribe' ? 'bg-primary' : 'bg-gray-200'
              }`}
            >
              <span className="sr-only">Toggle entry mode</span>
              <span
                className={`pointer-events-none relative inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200 ${
                  entryMode === 'ai-scribe' ? 'translate-x-5' : 'translate-x-0'
                }`}
              >
                <span
                  className={`absolute inset-0 h-full w-full flex items-center justify-center transition-opacity ${
                    entryMode === 'ai-scribe' ? 'opacity-0 ease-out duration-100' : 'opacity-100 ease-in duration-200'
                  }`}
                >
                  <FaKeyboard className="h-3 w-3 text-gray-400" />
                </span>
                <span
                  className={`absolute inset-0 h-full w-full flex items-center justify-center transition-opacity ${
                    entryMode === 'ai-scribe' ? 'opacity-100 ease-in duration-200' : 'opacity-0 ease-out duration-100'
                  }`}
                >
                  <FaRobot className="h-3 w-3 text-primary" />
                </span>
              </span>
            </button>
          </div>
        </div>
      )}

      {/* AI Scribe Interface */}
      {entryMode === 'ai-scribe' && currentStep < totalSteps && (
        <div className="mb-6 bg-blue-50 p-4 rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <FaRobot className="h-5 w-5 text-primary mr-2" />
              <h3 className="text-lg font-medium text-gray-900">AI-Scribe Assistant</h3>
            </div>
            {/* Record button always available in AI mode for the current step */}
            {/* {currentQuestion && ( */}
              <button
                type="button"
                onClick={toggleRecording}
                disabled={isProcessing}
                className={`inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm ${
                  isRecording
                    ? 'text-white bg-red-600 hover:bg-red-700'
                    : 'text-white bg-primary hover:bg-blue-700'
                } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary`}
              >
                {isRecording ? (
                  <>
                    <FaMicrophoneSlash className="mr-1.5 -ml-0.5 h-4 w-4" />
                    Stop Recording ({formatTime(recordingTime)})
                  </>
                ) : (
                  <>
                    <FaMicrophone className="mr-1.5 -ml-0.5 h-4 w-4" />
                    Start Recording
                  </>
                )}
              </button>
            {/* )} */}
          </div>
          
          {isProcessing ? (
            <div className="flex items-center justify-center py-4">
              <FaSpinner className="animate-spin h-5 w-5 text-primary mr-3" />
              <p className="text-gray-700">Processing audio...</p>
            </div>
          ) : (
            // Wrap the guidance and clarification sections in a fragment
            <>
              {/* Guidance and Clarification Prompts */}
              <div className="bg-white p-4 rounded-md shadow-sm space-y-4">
                {/* Step Guidance */}
                <div>
                {aiStepTopics[currentStep] ? (
                  <>
                    <p className="text-gray-700 mb-2">
                      For <span className="font-semibold">{aiStepTopics[currentStep].name}</span>, please discuss the following points:
                    </p>
                    <ul className="list-disc list-inside text-sm text-gray-600 space-y-1 mb-3">
                      {aiStepTopics[currentStep].fields.map(field => <li key={field}>{field}</li>)}
                    </ul>
                    <p className="text-sm text-gray-500">
                      Click "Start Recording" to capture the conversation. The AI will attempt to fill the fields automatically.
                    </p>
                  </>
                ) : (
                  <p className="text-gray-700">AI Scribe not configured for this step.</p>
                )}
              </div>

              {/* Clarification Prompt Area */}
              {fieldsToClarify.length > 0 && (
                <div className="border-t border-gray-200 pt-4">
                   <div className="flex items-start">
                     <div className="flex-shrink-0">
                       <FaInfoCircle className="h-5 w-5 text-blue-400" aria-hidden="true" />
                     </div>
                     <div className="ml-3 flex-1 md:flex md:justify-between">
                       <div>
                         <p className="text-sm font-medium text-blue-800">
                           Please clarify the following information:
                         </p>
                         <ul className="list-disc list-inside text-sm text-blue-700 mt-1 space-y-0.5">
                           {fieldsToClarify.map(({ field, reason }) => (
                             <li key={field}>
                               <span className="font-semibold">{field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}:</span> {reason}
                             </li>
                           ))}
                         </ul>
                         <p className="text-xs text-gray-500 mt-2">
                            You can address these points in your next recording or fill them manually below.
                         </p>
                       </div>
                     </div>
                   </div>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      )}

      {/* Contraindications Alert */}
      {contraindications.length > 0 && (
        <div className="mb-6">
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <FaExclamationTriangle className="h-5 w-5 text-yellow-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">
                  Potential contraindications detected ({contraindications.length})
                </h3>
                <div className="mt-2 text-sm text-yellow-700">
                  <ul className="list-disc pl-5 space-y-1">
                    {contraindications.map((item, index) => (
                      <li key={index}>
                        <span className={`font-medium ${
                          item.severity === 'high' ? 'text-red-600' : 'text-yellow-600'
                        }`}>
                          {item.message}
                        </span>
                        <p className="text-xs mt-0.5">{item.detail}</p>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Auto-save Status */}
      {currentStep < totalSteps && (
        <div className="mb-4 flex justify-end">
          <div className="flex items-center text-sm">
            {autoSaveStatus === 'saving' ? (
              <>
                <FaSpinner className="animate-spin h-4 w-4 text-gray-400 mr-1.5" />
                <span className="text-gray-500">Saving...</span>
              </>
            ) : autoSaveStatus === 'saved' ? (
              <>
                <FaCheck className="h-4 w-4 text-green-500 mr-1.5" />
                <span className="text-gray-500">Changes saved</span>
              </>
            ) : (
              <>
                <FaExclamationTriangle className="h-4 w-4 text-red-500 mr-1.5" />
                <span className="text-red-500">Error saving changes</span>
              </>
            )}
          </div>
        </div>
      )}

      {/* Form Content */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          {renderStep()}
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="mt-8 flex justify-between">
        <button
          type="button"
          onClick={handleBack}
          disabled={currentStep === 1}
          className={`inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary ${
            currentStep === 1 ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          <FaArrowLeft className="mr-2 -ml-1 h-5 w-5" />
          Back
        </button>
        
        {currentStep < totalSteps - 1 ? (
          <button
            type="button"
            onClick={handleNext}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Next
            <FaArrowRight className="ml-2 -mr-1 h-5 w-5" />
          </button>
        ) : currentStep === totalSteps - 1 ? (
          <button
            type="button"
            onClick={handleSubmit}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            {loading ? 'Saving...' : 'Submit'}
            <FaCheck className="ml-2 -mr-1 h-5 w-5" />
          </button>
        ) : null}
      </div>
    </div>
  );
};

export default PatientIntakeWizard;
