import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CloudFrontClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../CloudFrontClient";
import {
  UpdateVpcOriginRequest,
  UpdateVpcOriginResult,
} from "../models/models_2";
export { __MetadataBearer };
export { $Command };
export interface UpdateVpcOriginCommandInput extends UpdateVpcOriginRequest {}
export interface UpdateVpcOriginCommandOutput
  extends UpdateVpcOriginResult,
    __MetadataBearer {}
declare const UpdateVpcOriginCommand_base: {
  new (
    input: UpdateVpcOriginCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateVpcOriginCommandInput,
    UpdateVpcOriginCommandOutput,
    CloudFrontClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateVpcOriginCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateVpcOriginCommandInput,
    UpdateVpcOriginCommandOutput,
    CloudFrontClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateVpcOriginCommand extends UpdateVpcOriginCommand_base {
  protected static __types: {
    api: {
      input: UpdateVpcOriginRequest;
      output: UpdateVpcOriginResult;
    };
    sdk: {
      input: UpdateVpcOriginCommandInput;
      output: UpdateVpcOriginCommandOutput;
    };
  };
}
