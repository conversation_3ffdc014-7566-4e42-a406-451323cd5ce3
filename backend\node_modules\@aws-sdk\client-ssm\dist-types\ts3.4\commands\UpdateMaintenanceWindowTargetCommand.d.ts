import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateMaintenanceWindowTargetRequest,
  UpdateMaintenanceWindowTargetResult,
} from "../models/models_2";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SSMClientResolvedConfig,
} from "../SSMClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateMaintenanceWindowTargetCommandInput
  extends UpdateMaintenanceWindowTargetRequest {}
export interface UpdateMaintenanceWindowTargetCommandOutput
  extends UpdateMaintenanceWindowTargetResult,
    __MetadataBearer {}
declare const UpdateMaintenanceWindowTargetCommand_base: {
  new (
    input: UpdateMaintenanceWindowTargetCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateMaintenanceWindowTargetCommandInput,
    UpdateMaintenanceWindowTargetCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateMaintenanceWindowTargetCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateMaintenanceWindowTargetCommandInput,
    UpdateMaintenanceWindowTargetCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateMaintenanceWindowTargetCommand extends UpdateMaintenanceWindowTargetCommand_base {
  protected static __types: {
    api: {
      input: UpdateMaintenanceWindowTargetRequest;
      output: UpdateMaintenanceWindowTargetResult;
    };
    sdk: {
      input: UpdateMaintenanceWindowTargetCommandInput;
      output: UpdateMaintenanceWindowTargetCommandOutput;
    };
  };
}
