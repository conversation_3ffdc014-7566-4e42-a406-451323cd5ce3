import OpenAI from "openai";

/**
 * Dashboard_AI class
 * 
 * Provides methods for interacting with the Syncore AI assistant,
 * including fetching patient and appointment data from local storage,
 * managing message history, and communicating with the OpenAI API.
 */
export default class Dashboard_AI {

    constructor() {
        this.key = "********************************************************************************************************************************************************************";
        
        /**
         * Storage keys for various data types in localStorage.
         * @type {Object} 
         */
        this.STORAGE_KEYS = {
            PATIENTS: 'syncore_patients',
            APPOINTMENTS: 'syncore_appointments',
            PRACTITIONERS: 'syncore_practitioners',
            EVALUATIONS: 'syncore_evaluations',
            SESSIONS: 'syncore_sessions',
            USER: 'syncore_user',
            USER_ROLE: 'syncore_user_role'
        };

        /**
         * Message history for the AI conversation.
         * @type {Array}
         */
        this.messages_history = [
            { role: "system", content: "Your name is SynCore AI. You are built by SynCore. You are a polite assistant to a Medical Doctor, who responds in a conversational manner." },
            { role: "user", content: "ALL PATIENTS JSON LIST: " + this.#getAllPatients() },
            { role: "user", content: "ALL APPOINTMENTS JSON LIST: " + this.#getAllAppointments() },
        ];
    }

    /**
     * Retrieves details for a specific patient by name from local storage.
     * @param {string} name - The name of the patient.
     * @returns {string} JSON string of the patient details, or "..." if not found.
     */
    #getPatientDetails(name) {
        let raw = this.#getAllPatients() == "NO_PATIENT_RECORDS_FOUND" ? [] : JSON.parse(this.#getAllPatients());
        let patient = raw.find(p => p.name.toLowerCase() == name.toLowerCase());
        if (!patient || patient === 'undefined') {
            return "...";
        }
        return JSON.stringify(patient);
    }

    /**
     * Retrieves all patients from local storage.
     * @returns {string} JSON string of patients or "NO_PATIENT_RECORDS_FOUND".
     */
    #getAllPatients() {
        const patients = localStorage.getItem(this.STORAGE_KEYS.PATIENTS);
        return patients && patients !== 'undefined' ? patients : "NO_PATIENT_RECORDS_FOUND";
    }

    /**
     * Retrieves all appointments from local storage.
     * @returns {string} JSON string of appointments or "NO_APPOINTMENT_RECORDS_FOUND".
     */
    #getAllAppointments() {
        const appointments = localStorage.getItem(this.STORAGE_KEYS.APPOINTMENTS);
        return appointments && appointments !== 'undefined' ? appointments : "NO_APPOINTMENT_RECORDS_FOUND";
    }

    /**
     * Calls the OpenAI API with the given prompt or the current message history.
     * @param {string} [prompt="USE_MESSAGE_HISTORY"] - The prompt to send to the AI.
     * @returns {Promise<string>} The AI's response.
     */
    async #llm_call(prompt = "USE_MESSAGE_HISTORY") {
        const open_ai_api = this.key;
        const openai = new OpenAI({
            apiKey: open_ai_api,
            dangerouslyAllowBrowser: true,
        });

        if (prompt === "USE_MESSAGE_HISTORY") {
            const response = await openai.chat.completions.create({
                model: "gpt-4o-mini",
                messages: this.messages_history,
            });
            const content = response.choices?.[0]?.message?.content ?? "";
            return content;
        }

        const response = await openai.chat.completions.create({
            model: "gpt-4o-mini",
            messages: [
                { role: "system", content: "Your name is SynCore AI. You are built by SynCore. You are a polite assistant to a Medical Doctor." },
                { role: "user", content: prompt }
            ]
        });

        const content = response.choices?.[0]?.message?.content ?? "";
        return content;
    }

    /**
     * Gets a response from the AI based on the user's prompt.
     * Includes patient details if a patient is referenced in the prompt.
     * @param {string} prompt - The user's prompt.
     * @returns {Promise<string>} The AI's response.
     */
    async get_response(prompt) {
        const name = await this.#llm_call("Take the following Prompt, and then get me the name of the patient. Only return the name of the Patient, and nothing else. Prompt : " + prompt);
        const patient_details = this.#getPatientDetails(name);

        const instructions = [
            "1) Prompt by User: " + prompt,
            "2) Specific Patient Details (If Needed by Doctor): " + patient_details,
            "3) You are given the prompt by the user and then the patient details. Analyze the prompt and find out what the user is asking.",
            "Then, analyze the patient details in depth to find relevant pieces of infornation - if that is what is needed by the Doctor.",
            "Then return the answer in a concise way. Stay to the point and give a medium length answer - unless explicitly asked to explain.",
            "If there are no Patient details found, ask how else you can assist them. If The user does not exists, then tell them that the user does not exist in their records.",
            "Answer in a converstaional tone, not simple giving the answer provided in database in a raw form.",
            "If the doctor asks a general question, do not state that he has not provided patient details. Simply answer his question."
        ];
        const prompt_2 = instructions.join("\n");
        this.messages_history.push({ role: "user", content: prompt_2 });

        const response = await this.#llm_call();
        this.messages_history.push({ role: "assistant", content: response });

        return response
    }
}