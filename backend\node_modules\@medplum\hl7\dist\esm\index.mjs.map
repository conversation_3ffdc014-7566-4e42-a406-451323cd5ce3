{"version": 3, "sources": ["../../src/base.ts", "../../src/client.ts", "../../src/connection.ts", "../../src/constants.ts", "../../src/events.ts", "../../src/server.ts"], "sourcesContent": ["import { Hl7CloseEvent, Hl7ErrorEvent, Hl7MessageEvent } from './events';\n\nexport interface Hl7EventMap {\n  message: Hl7MessageEvent;\n  error: Hl7ErrorEvent;\n  close: Hl7CloseEvent;\n}\n\nexport abstract class Hl7Base extends EventTarget {\n  addEventListener<K extends keyof Hl7EventMap>(\n    type: K,\n    listener: ((event: Hl7EventMap[K]) => void) | EventListenerObject | null,\n    options?: boolean | AddEventListenerOptions\n  ): void;\n\n  addEventListener(\n    type: string,\n    listener: EventListenerOrEventListenerObject | null,\n    options?: boolean | AddEventListenerOptions\n  ): void {\n    super.addEventListener(type, listener, options);\n  }\n  removeEventListener<K extends keyof Hl7EventMap>(\n    type: K,\n    listener: ((event: Hl7EventMap[K]) => void) | EventListenerObject | null,\n    options?: boolean | AddEventListenerOptions\n  ): void;\n\n  removeEventListener(\n    type: string,\n    listener: EventListenerOrEventListenerObject | null,\n    options?: boolean | AddEventListenerOptions\n  ): void {\n    super.removeEventListener(type, listener, options);\n  }\n}\n", "import { Hl7Message } from '@medplum/core';\nimport { connect, Socket } from 'node:net';\nimport { Hl7Base } from './base';\nimport { Hl7Connection } from './connection';\nimport { Hl7CloseEvent, Hl7ErrorEvent } from './events';\n\nexport interface Hl7ClientOptions {\n  host: string;\n  port: number;\n  encoding?: string;\n  keepAlive?: boolean;\n  connectTimeout?: number; // Add timeout option\n}\n\nexport class Hl7Client extends Hl7Base {\n  options: Hl7ClientOptions;\n  host: string;\n  port: number;\n  encoding?: string;\n  connection?: Hl7Connection;\n  keepAlive: boolean;\n  private socket?: Socket;\n  private connectTimeout: number;\n\n  constructor(options: Hl7ClientOptions) {\n    super();\n    this.options = options;\n    this.host = this.options.host;\n    this.port = this.options.port;\n    this.encoding = this.options.encoding;\n    this.keepAlive = this.options.keepAlive ?? false;\n    this.connectTimeout = this.options.connectTimeout ?? 30000; // Default 30 seconds\n  }\n\n  connect(): Promise<Hl7Connection> {\n    // If we already have a connection, use it\n    if (this.connection) {\n      return Promise.resolve(this.connection);\n    }\n\n    // If there's an ongoing connection attempt, destroy it\n    if (this.socket) {\n      this.socket.removeAllListeners();\n      this.socket.destroy();\n      this.socket = undefined;\n    }\n\n    return new Promise((resolve, reject) => {\n      // Create the socket\n      this.socket = connect({\n        host: this.host,\n        port: this.port,\n        keepAlive: this.keepAlive,\n      });\n\n      // Set timeout if specified\n      if (this.connectTimeout > 0) {\n        this.socket.setTimeout(this.connectTimeout);\n\n        // Handle timeout event\n        this.socket.on('timeout', () => {\n          const error = new Error(`Connection timeout after ${this.connectTimeout}ms`);\n          if (this.socket) {\n            this.socket.destroy();\n            this.socket = undefined;\n          }\n          reject(error);\n        });\n      }\n\n      // Handle successful connection\n      this.socket.on('connect', () => {\n        if (!this.socket) {\n          return; // Socket was already destroyed\n        }\n\n        // Create the HL7 connection\n        let connection: Hl7Connection;\n        this.connection = connection = new Hl7Connection(this.socket, this.encoding);\n\n        // Remove the timeout listener as we're now connected\n        this.socket.setTimeout(0);\n\n        // Set up event handlers\n        connection.addEventListener('close', () => {\n          this.socket = undefined;\n          this.dispatchEvent(new Hl7CloseEvent());\n        });\n\n        connection.addEventListener('error', (event) => {\n          this.dispatchEvent(new Hl7ErrorEvent(event.error));\n        });\n\n        resolve(this.connection);\n      });\n\n      // Handle connection errors\n      this.socket.on('error', (err) => {\n        if (this.socket) {\n          this.socket.destroy();\n          this.socket = undefined;\n        }\n        reject(err);\n      });\n    });\n  }\n\n  async send(msg: Hl7Message): Promise<void> {\n    return (await this.connect()).send(msg);\n  }\n\n  async sendAndWait(msg: Hl7Message): Promise<Hl7Message> {\n    return (await this.connect()).sendAndWait(msg);\n  }\n\n  close(): void {\n    // Close the socket if it exists\n    if (this.socket) {\n      this.socket.removeAllListeners();\n      this.socket.destroy();\n      this.socket = undefined;\n    }\n\n    // Close established connection if it exists\n    if (this.connection) {\n      this.connection.close();\n      delete this.connection;\n    }\n  }\n}\n", "import { Hl7Message } from '@medplum/core';\nimport iconv from 'iconv-lite';\nimport net from 'node:net';\nimport { Hl7Base } from './base';\nimport { CR, FS, VT } from './constants';\nimport { Hl7CloseEvent, Hl7ErrorEvent, Hl7MessageEvent } from './events';\n\n// iconv-lite docs have great examples and explanations for how to use Buffers with iconv-lite:\n// See: https://github.com/ashtuchkin/iconv-lite/wiki/Use-Buffers-when-decoding\n\nexport type Hl7MessageQueueItem = {\n  message: Hl7Message;\n  resolve?: (reply: Hl7Message) => void;\n  reject?: (err: Error) => void;\n};\n\nexport class Hl7Connection extends Hl7Base {\n  readonly socket: net.Socket;\n  readonly encoding: string;\n  readonly enhancedMode: boolean;\n  private chunks: Buffer[] = [];\n  private readonly messageQueue: Hl7MessageQueueItem[] = [];\n\n  constructor(socket: net.Socket, encoding: string = 'utf-8', enhancedMode = false) {\n    super();\n\n    this.socket = socket;\n    this.encoding = encoding;\n    this.enhancedMode = enhancedMode;\n\n    socket.on('data', (data: Buffer) => {\n      try {\n        this.appendData(data);\n        if (data.at(-2) === FS && data.at(-1) === CR) {\n          const buffer = Buffer.concat(this.chunks);\n          const contentBuffer = buffer.subarray(1, buffer.length - 2);\n          const contentString = iconv.decode(contentBuffer, this.encoding);\n          const message = Hl7Message.parse(contentString);\n          this.dispatchEvent(new Hl7MessageEvent(this, message));\n          this.resetBuffer();\n        }\n      } catch (err) {\n        this.dispatchEvent(new Hl7ErrorEvent(err as Error));\n      }\n    });\n\n    socket.on('error', (err) => {\n      this.resetBuffer();\n      this.dispatchEvent(new Hl7ErrorEvent(err));\n    });\n\n    socket.on('end', () => {\n      this.close();\n    });\n\n    this.addEventListener('message', (event) => {\n      if (enhancedMode) {\n        this.send(event.message.buildAck({ ackCode: 'CA' }));\n      }\n      // Get the queue item at the head of the queue\n      const next = this.messageQueue.shift();\n      // If there isn't an item, then throw an error\n      if (!next) {\n        this.dispatchEvent(\n          new Hl7ErrorEvent(\n            new Error(`Received a message when no pending messages were in the queue. Message: ${event.message}`)\n          )\n        );\n        return;\n      }\n      // Resolve the promise if there is one pending for this message\n      next.resolve?.(event.message);\n    });\n  }\n\n  private sendImpl(reply: Hl7Message, queueItem: Hl7MessageQueueItem): void {\n    this.messageQueue.push(queueItem);\n    const replyString = reply.toString();\n    const replyBuffer = iconv.encode(replyString, this.encoding);\n    const outputBuffer = Buffer.alloc(replyBuffer.length + 3);\n    outputBuffer.writeInt8(VT, 0);\n    replyBuffer.copy(outputBuffer, 1);\n    outputBuffer.writeInt8(FS, replyBuffer.length + 1);\n    outputBuffer.writeInt8(CR, replyBuffer.length + 2);\n    this.socket.write(outputBuffer);\n  }\n\n  send(reply: Hl7Message): void {\n    this.sendImpl(reply, { message: reply });\n  }\n\n  async sendAndWait(msg: Hl7Message): Promise<Hl7Message> {\n    return new Promise<Hl7Message>((resolve, reject) => {\n      const queueItem = { message: msg, resolve, reject };\n      this.sendImpl(msg, queueItem);\n    });\n  }\n\n  close(): void {\n    this.socket.end();\n    this.socket.destroy();\n    this.dispatchEvent(new Hl7CloseEvent());\n  }\n\n  private appendData(data: Buffer): void {\n    this.chunks.push(data);\n  }\n\n  private resetBuffer(): void {\n    this.chunks = [];\n  }\n}\n", "/**\n * VT (Vertical Tab) character.\n *\n * In HL7 messages, this character is used to indicate the start of a message.\n */\nexport const VT = 0x0b;\n\n/**\n * CR (Carriage Return) character.\n *\n * In HL7 messages, this character is used to indicate the end of a message.\n */\nexport const CR = 0x0d;\n\n/**\n * FS (File Separator) character.\n *\n * In HL7 messages, this character is used to separate fields.\n */\nexport const FS = 0x1c;\n", "import { Hl7Message } from '@medplum/core';\nimport { Hl7Connection } from './connection';\n\nexport class Hl7MessageEvent extends Event {\n  readonly connection: Hl7Connection;\n  readonly message: Hl7Message;\n\n  constructor(connection: Hl7Connection, message: Hl7Message) {\n    super('message');\n    this.connection = connection;\n    this.message = message;\n  }\n}\n\nexport class Hl7ErrorEvent extends Event {\n  readonly error: Error;\n\n  constructor(error: Error) {\n    super('error');\n    this.error = error;\n  }\n}\n\nexport class Hl7CloseEvent extends Event {\n  constructor() {\n    super('close');\n  }\n}\n", "import net from 'node:net';\nimport { Hl7Connection } from './connection';\n\nexport class Hl7Server {\n  readonly handler: (connection: Hl7Connection) => void;\n  server?: net.Server;\n\n  constructor(handler: (connection: Hl7Connection) => void) {\n    this.handler = handler;\n  }\n\n  start(port: number, encoding?: string, enhancedMode = false): void {\n    const server = net.createServer((socket) => {\n      const connection = new Hl7Connection(socket, encoding, enhancedMode);\n      this.handler(connection);\n    });\n\n    server.listen(port);\n    this.server = server;\n  }\n\n  async stop(): Promise<void> {\n    return new Promise<void>((resolve, reject) => {\n      if (!this.server) {\n        reject(new Error('Stop was called but there is no server running'));\n        return;\n      }\n      this.server.close((err) => {\n        if (err) {\n          reject(err);\n          return;\n        }\n        resolve();\n      });\n      this.server = undefined;\n    });\n  }\n}\n"], "mappings": "AAQO,IAAeA,EAAf,cAA+B,WAAY,CAOhD,iBACEC,EACAC,EACAC,EACM,CACN,MAAM,iBAAiBF,EAAMC,EAAUC,CAAO,CAChD,CAOA,oBACEF,EACAC,EACAC,EACM,CACN,MAAM,oBAAoBF,EAAMC,EAAUC,CAAO,CACnD,CACF,EClCA,OAAS,WAAAC,MAAuB,WCDhC,OAAS,cAAAC,MAAkB,gBAC3B,OAAOC,MAAW,aCIX,IAAMC,EAAK,GAOLC,EAAK,GAOLC,EAAK,GChBX,IAAMC,EAAN,cAA8B,KAAM,CAIzC,YAAYC,EAA2BC,EAAqB,CAC1D,MAAM,SAAS,EACf,KAAK,WAAaD,EAClB,KAAK,QAAUC,CACjB,CACF,EAEaC,EAAN,cAA4B,KAAM,CAGvC,YAAYC,EAAc,CACxB,MAAM,OAAO,EACb,KAAK,MAAQA,CACf,CACF,EAEaC,EAAN,cAA4B,KAAM,CACvC,aAAc,CACZ,MAAM,OAAO,CACf,CACF,EFXO,IAAMC,EAAN,cAA4BC,CAAQ,CAOzC,YAAYC,EAAoBC,EAAmB,QAASC,EAAe,GAAO,CAChF,MAAM,EAJR,KAAQ,OAAmB,CAAC,EAC5B,KAAiB,aAAsC,CAAC,EAKtD,KAAK,OAASF,EACd,KAAK,SAAWC,EAChB,KAAK,aAAeC,EAEpBF,EAAO,GAAG,OAASG,GAAiB,CAClC,GAAI,CAEF,GADA,KAAK,WAAWA,CAAI,EAChBA,EAAK,GAAG,EAAE,IAAM,IAAMA,EAAK,GAAG,EAAE,IAAM,GAAI,CAC5C,IAAMC,EAAS,OAAO,OAAO,KAAK,MAAM,EAClCC,EAAgBD,EAAO,SAAS,EAAGA,EAAO,OAAS,CAAC,EACpDE,EAAgBC,EAAM,OAAOF,EAAe,KAAK,QAAQ,EACzDG,EAAUC,EAAW,MAAMH,CAAa,EAC9C,KAAK,cAAc,IAAII,EAAgB,KAAMF,CAAO,CAAC,EACrD,KAAK,YAAY,CACnB,CACF,OAASG,EAAK,CACZ,KAAK,cAAc,IAAIC,EAAcD,CAAY,CAAC,CACpD,CACF,CAAC,EAEDX,EAAO,GAAG,QAAUW,GAAQ,CAC1B,KAAK,YAAY,EACjB,KAAK,cAAc,IAAIC,EAAcD,CAAG,CAAC,CAC3C,CAAC,EAEDX,EAAO,GAAG,MAAO,IAAM,CACrB,KAAK,MAAM,CACb,CAAC,EAED,KAAK,iBAAiB,UAAYa,GAAU,CACtCX,GACF,KAAK,KAAKW,EAAM,QAAQ,SAAS,CAAE,QAAS,IAAK,CAAC,CAAC,EAGrD,IAAMC,EAAO,KAAK,aAAa,MAAM,EAErC,GAAI,CAACA,EAAM,CACT,KAAK,cACH,IAAIF,EACF,IAAI,MAAM,2EAA2EC,EAAM,OAAO,EAAE,CACtG,CACF,EACA,MACF,CAEAC,EAAK,UAAUD,EAAM,OAAO,CAC9B,CAAC,CACH,CAEQ,SAASE,EAAmBC,EAAsC,CACxE,KAAK,aAAa,KAAKA,CAAS,EAChC,IAAMC,EAAcF,EAAM,SAAS,EAC7BG,EAAcX,EAAM,OAAOU,EAAa,KAAK,QAAQ,EACrDE,EAAe,OAAO,MAAMD,EAAY,OAAS,CAAC,EACxDC,EAAa,UAAU,GAAI,CAAC,EAC5BD,EAAY,KAAKC,EAAc,CAAC,EAChCA,EAAa,UAAU,GAAID,EAAY,OAAS,CAAC,EACjDC,EAAa,UAAU,GAAID,EAAY,OAAS,CAAC,EACjD,KAAK,OAAO,MAAMC,CAAY,CAChC,CAEA,KAAKJ,EAAyB,CAC5B,KAAK,SAASA,EAAO,CAAE,QAASA,CAAM,CAAC,CACzC,CAEA,MAAM,YAAYK,EAAsC,CACtD,OAAO,IAAI,QAAoB,CAACC,EAASC,IAAW,CAClD,IAAMN,EAAY,CAAE,QAASI,EAAK,QAAAC,EAAS,OAAAC,CAAO,EAClD,KAAK,SAASF,EAAKJ,CAAS,CAC9B,CAAC,CACH,CAEA,OAAc,CACZ,KAAK,OAAO,IAAI,EAChB,KAAK,OAAO,QAAQ,EACpB,KAAK,cAAc,IAAIO,CAAe,CACxC,CAEQ,WAAWpB,EAAoB,CACrC,KAAK,OAAO,KAAKA,CAAI,CACvB,CAEQ,aAAoB,CAC1B,KAAK,OAAS,CAAC,CACjB,CACF,EDjGO,IAAMqB,EAAN,cAAwBC,CAAQ,CAUrC,YAAYC,EAA2B,CACrC,MAAM,EACN,KAAK,QAAUA,EACf,KAAK,KAAO,KAAK,QAAQ,KACzB,KAAK,KAAO,KAAK,QAAQ,KACzB,KAAK,SAAW,KAAK,QAAQ,SAC7B,KAAK,UAAY,KAAK,QAAQ,WAAa,GAC3C,KAAK,eAAiB,KAAK,QAAQ,gBAAkB,GACvD,CAEA,SAAkC,CAEhC,OAAI,KAAK,WACA,QAAQ,QAAQ,KAAK,UAAU,GAIpC,KAAK,SACP,KAAK,OAAO,mBAAmB,EAC/B,KAAK,OAAO,QAAQ,EACpB,KAAK,OAAS,QAGT,IAAI,QAAQ,CAACC,EAASC,IAAW,CAEtC,KAAK,OAASC,EAAQ,CACpB,KAAM,KAAK,KACX,KAAM,KAAK,KACX,UAAW,KAAK,SAClB,CAAC,EAGG,KAAK,eAAiB,IACxB,KAAK,OAAO,WAAW,KAAK,cAAc,EAG1C,KAAK,OAAO,GAAG,UAAW,IAAM,CAC9B,IAAMC,EAAQ,IAAI,MAAM,4BAA4B,KAAK,cAAc,IAAI,EACvE,KAAK,SACP,KAAK,OAAO,QAAQ,EACpB,KAAK,OAAS,QAEhBF,EAAOE,CAAK,CACd,CAAC,GAIH,KAAK,OAAO,GAAG,UAAW,IAAM,CAC9B,GAAI,CAAC,KAAK,OACR,OAIF,IAAIC,EACJ,KAAK,WAAaA,EAAa,IAAIC,EAAc,KAAK,OAAQ,KAAK,QAAQ,EAG3E,KAAK,OAAO,WAAW,CAAC,EAGxBD,EAAW,iBAAiB,QAAS,IAAM,CACzC,KAAK,OAAS,OACd,KAAK,cAAc,IAAIE,CAAe,CACxC,CAAC,EAEDF,EAAW,iBAAiB,QAAUG,GAAU,CAC9C,KAAK,cAAc,IAAIC,EAAcD,EAAM,KAAK,CAAC,CACnD,CAAC,EAEDP,EAAQ,KAAK,UAAU,CACzB,CAAC,EAGD,KAAK,OAAO,GAAG,QAAUS,GAAQ,CAC3B,KAAK,SACP,KAAK,OAAO,QAAQ,EACpB,KAAK,OAAS,QAEhBR,EAAOQ,CAAG,CACZ,CAAC,CACH,CAAC,EACH,CAEA,MAAM,KAAKC,EAAgC,CACzC,OAAQ,MAAM,KAAK,QAAQ,GAAG,KAAKA,CAAG,CACxC,CAEA,MAAM,YAAYA,EAAsC,CACtD,OAAQ,MAAM,KAAK,QAAQ,GAAG,YAAYA,CAAG,CAC/C,CAEA,OAAc,CAER,KAAK,SACP,KAAK,OAAO,mBAAmB,EAC/B,KAAK,OAAO,QAAQ,EACpB,KAAK,OAAS,QAIZ,KAAK,aACP,KAAK,WAAW,MAAM,EACtB,OAAO,KAAK,WAEhB,CACF,EIjIA,OAAOC,MAAS,WAGT,IAAMC,EAAN,KAAgB,CAIrB,YAAYC,EAA8C,CACxD,KAAK,QAAUA,CACjB,CAEA,MAAMC,EAAcC,EAAmBC,EAAe,GAAa,CACjE,IAAMC,EAASC,EAAI,aAAcC,GAAW,CAC1C,IAAMC,EAAa,IAAIC,EAAcF,EAAQJ,EAAUC,CAAY,EACnE,KAAK,QAAQI,CAAU,CACzB,CAAC,EAEDH,EAAO,OAAOH,CAAI,EAClB,KAAK,OAASG,CAChB,CAEA,MAAM,MAAsB,CAC1B,OAAO,IAAI,QAAc,CAACK,EAASC,IAAW,CAC5C,GAAI,CAAC,KAAK,OAAQ,CAChBA,EAAO,IAAI,MAAM,gDAAgD,CAAC,EAClE,MACF,CACA,KAAK,OAAO,MAAOC,GAAQ,CACzB,GAAIA,EAAK,CACPD,EAAOC,CAAG,EACV,MACF,CACAF,EAAQ,CACV,CAAC,EACD,KAAK,OAAS,MAChB,CAAC,CACH,CACF", "names": ["Hl7Base", "type", "listener", "options", "connect", "Hl7Message", "iconv", "VT", "CR", "FS", "Hl7MessageEvent", "connection", "message", "Hl7ErrorEvent", "error", "Hl7CloseEvent", "Hl7Connection", "Hl7Base", "socket", "encoding", "enhancedMode", "data", "buffer", "contentBuffer", "contentString", "iconv", "message", "Hl7Message", "Hl7MessageEvent", "err", "Hl7ErrorEvent", "event", "next", "reply", "queueItem", "replyString", "replyB<PERSON>er", "outputBuffer", "msg", "resolve", "reject", "Hl7CloseEvent", "Hl7Client", "Hl7Base", "options", "resolve", "reject", "connect", "error", "connection", "Hl7Connection", "Hl7CloseEvent", "event", "Hl7ErrorEvent", "err", "msg", "net", "Hl7Server", "handler", "port", "encoding", "enhancedMode", "server", "net", "socket", "connection", "Hl7Connection", "resolve", "reject", "err"]}