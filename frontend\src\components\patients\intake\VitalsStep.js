import React, { useEffect } from 'react';
import { FaRuler, FaWeight, FaHeartbeat, FaLungs, FaThermometerHalf, FaWalking, FaFileAlt, FaChartLine } from 'react-icons/fa';

const VitalsStep = ({ patientData, handleInputChange, calculateBMI }) => {
  // Calculate BMI when height or weight changes
  useEffect(() => {
    calculateBMI();
  }, [patientData.height, patientData.weight, calculateBMI]);

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900">Baseline Vitals</h3>
        <p className="mt-1 text-sm text-gray-500">
          Record the patient's baseline vital signs and cardiac metrics
        </p>
      </div>

      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h4 className="text-md font-medium text-gray-900 flex items-center mb-4">
          <FaRuler className="mr-2 text-primary" />
          <FaWeight className="mr-2 text-primary" />
          Height, Weight & BMI
        </h4>
        
        <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
          <div className="sm:col-span-2">
            <label htmlFor="height" className="block text-sm font-medium text-gray-700">
              Height (cm)
            </label>
            <div className="mt-1">
              <input
                type="number"
                name="height"
                id="height"
                value={patientData.height}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="175"
              />
            </div>
          </div>

          <div className="sm:col-span-2">
            <label htmlFor="weight" className="block text-sm font-medium text-gray-700">
              Weight (kg)
            </label>
            <div className="mt-1">
              <input
                type="number"
                name="weight"
                id="weight"
                value={patientData.weight}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="70"
              />
            </div>
          </div>

          <div className="sm:col-span-2">
            <label htmlFor="bmi" className="block text-sm font-medium text-gray-700">
              BMI
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="bmi"
                id="bmi"
                value={patientData.bmi}
                readOnly
                className="bg-gray-100 shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="Calculated"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h4 className="text-md font-medium text-gray-900 flex items-center mb-4">
          <FaHeartbeat className="mr-2 text-primary" />
          <FaLungs className="mr-2 text-primary" />
          <FaThermometerHalf className="mr-2 text-primary" />
          Vital Signs
        </h4>
        
        <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
          <div className="sm:col-span-3">
            <label htmlFor="bloodPressure" className="block text-sm font-medium text-gray-700">
              Blood Pressure (mmHg)
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="bloodPressure"
                id="bloodPressure"
                value={patientData.bloodPressure}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="120/80"
              />
            </div>
          </div>

          <div className="sm:col-span-3">
            <label htmlFor="heartRate" className="block text-sm font-medium text-gray-700">
              Heart Rate (bpm)
            </label>
            <div className="mt-1">
              <input
                type="number"
                name="heartRate"
                id="heartRate"
                value={patientData.heartRate}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="72"
              />
            </div>
          </div>

          <div className="sm:col-span-2">
            <label htmlFor="respiratoryRate" className="block text-sm font-medium text-gray-700">
              Respiratory Rate (breaths/min)
            </label>
            <div className="mt-1">
              <input
                type="number"
                name="respiratoryRate"
                id="respiratoryRate"
                value={patientData.respiratoryRate}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="16"
              />
            </div>
          </div>

          <div className="sm:col-span-2">
            <label htmlFor="oxygenSaturation" className="block text-sm font-medium text-gray-700">
              Oxygen Saturation (%)
            </label>
            <div className="mt-1">
              <input
                type="number"
                name="oxygenSaturation"
                id="oxygenSaturation"
                value={patientData.oxygenSaturation}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="98"
              />
            </div>
          </div>

          <div className="sm:col-span-2">
            <label htmlFor="temperature" className="block text-sm font-medium text-gray-700">
              Temperature (°C)
            </label>
            <div className="mt-1">
              <input
                type="number"
                name="temperature"
                id="temperature"
                value={patientData.temperature}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="37.0"
                step="0.1"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h4 className="text-md font-medium text-gray-900 flex items-center mb-4">
          <FaHeartbeat className="mr-2 text-primary" />
          Cardiac Metrics
        </h4>
        
        <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
          <div className="sm:col-span-3">
            <label htmlFor="lvef" className="block text-sm font-medium text-gray-700">
              Left Ventricular Ejection Fraction (%)
            </label>
            <div className="mt-1">
              <input
                type="number"
                name="lvef"
                id="lvef"
                value={patientData.lvef}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="55"
              />
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Normal range: 55-70%. Values below 40% indicate heart failure.
            </p>
          </div>

          <div className="sm:col-span-3">
            <label htmlFor="bnp" className="block text-sm font-medium text-gray-700">
              B-type Natriuretic Peptide (pg/mL)
            </label>
            <div className="mt-1">
              <input
                type="number"
                name="bnp"
                id="bnp"
                value={patientData.bnp}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="100"
              />
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Normal range: &lt;100 pg/mL. Values &gt;400 pg/mL suggest heart failure.
            </p>
          </div>
        </div>
      </div>

      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h4 className="text-md font-medium text-gray-900 flex items-center mb-4">
          <FaFileAlt className="mr-2 text-primary" />
          Diagnostic Tests
        </h4>
        
        <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
          <div className="sm:col-span-3">
            <label htmlFor="sixMinuteWalkDistance" className="block text-sm font-medium text-gray-700 flex items-center">
              <FaWalking className="mr-1 text-gray-500" />
              Six-Minute Walk Distance (meters)
            </label>
            <div className="mt-1">
              <input
                type="number"
                name="sixMinuteWalkDistance"
                id="sixMinuteWalkDistance"
                value={patientData.sixMinuteWalkDistance}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="350"
              />
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Normal range: 400-700m. Lower values indicate reduced functional capacity.
            </p>
          </div>

          <div className="sm:col-span-3">
            <label htmlFor="ecgFindings" className="block text-sm font-medium text-gray-700 flex items-center">
              <FaHeartbeat className="mr-1 text-gray-500" />
              ECG Findings
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="ecgFindings"
                id="ecgFindings"
                value={patientData.ecgFindings}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="e.g., Normal sinus rhythm"
              />
            </div>
          </div>

          <div className="sm:col-span-6">
            <label htmlFor="stressTestResults" className="block text-sm font-medium text-gray-700 flex items-center">
              <FaChartLine className="mr-1 text-gray-500" />
              Stress Test Results
            </label>
            <div className="mt-1">
              <textarea
                name="stressTestResults"
                id="stressTestResults"
                rows="3"
                value={patientData.stressTestResults}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="Enter stress test findings, including exercise capacity, symptoms, and any ischemic changes"
              ></textarea>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-yellow-50 p-4 rounded-md border border-yellow-200">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">Important Note</h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>
                EECP therapy is typically indicated for patients with:
              </p>
              <ul className="list-disc pl-5 mt-1 space-y-1">
                <li>Angina (CCS class III-IV) despite optimal medical therapy</li>
                <li>Heart failure (NYHA class II-III) with reduced ejection fraction</li>
                <li>Patients who are not candidates for revascularization procedures</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VitalsStep;
