import React from 'react';
import { 
  X, 
  FileText, 
  Calendar, 
  User, 
  Heart, 
  Activity, 
  BarChart2, 
  Star, 
  TrendingUp 
} from 'lucide-react';

const EvaluationDetailsModal = ({ evaluation, onClose }) => {
  if (!evaluation) return null;

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Helper function to render star rating
  const renderStarRating = (value) => {
    return (
      <div className="flex">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`h-4 w-4 ${
              i < parseInt(value || 0)
                ? 'text-yellow-400 fill-yellow-400'
                : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            {/* Header */}
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center">
                <FileText className="h-6 w-6 text-blue-600 mr-2" />
                <h3 className="text-lg font-medium text-gray-900">
                  Post-Treatment Evaluation Details
                </h3>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* Evaluation Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Info */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-500 mb-3">Evaluation Information</h4>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-sm text-gray-600">
                      Date: {formatDate(evaluation.date)}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <User className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-sm text-gray-600">
                      Evaluated by: {evaluation.author}
                    </span>
                  </div>
                </div>
              </div>

              {/* Summary */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-500 mb-3">Summary</h4>
                <p className="text-sm text-gray-600">{evaluation.summary}</p>
              </div>
            </div>

            {/* Vitals Section */}
            <div className="mt-6">
              <h4 className="text-sm font-medium text-gray-500 mb-3">Vitals</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-white border rounded-lg p-4">
                  <div className="flex items-center">
                    <Activity className="h-4 w-4 text-blue-600 mr-2" />
                    <span className="text-sm font-medium text-gray-900">Blood Pressure</span>
                  </div>
                  <div className="mt-2 text-2xl font-semibold text-gray-900">
                    {evaluation.data.vitals.systolic || '--'}/{evaluation.data.vitals.diastolic || '--'} mmHg
                  </div>
                </div>
                <div className="bg-white border rounded-lg p-4">
                  <div className="flex items-center">
                    <Heart className="h-4 w-4 text-red-600 mr-2" />
                    <span className="text-sm font-medium text-gray-900">Heart Rate</span>
                  </div>
                  <div className="mt-2 text-2xl font-semibold text-gray-900">
                    {evaluation.data.vitals.heartRate || '--'} bpm
                  </div>
                </div>
                <div className="bg-white border rounded-lg p-4">
                  <div className="flex items-center">
                    <BarChart2 className="h-4 w-4 text-green-600 mr-2" />
                    <span className="text-sm font-medium text-gray-900">BMI</span>
                  </div>
                  <div className="mt-2 text-2xl font-semibold text-gray-900">
                    {evaluation.data.vitals.bmi || '--'}
                  </div>
                </div>
              </div>
            </div>

            {/* Functional Tests */}
            <div className="mt-6">
              <h4 className="text-sm font-medium text-gray-500 mb-3">Functional Tests</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-white border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-900">CCS Angina Class</span>
                    <div className="flex items-center">
                      <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                      <span className="text-sm text-green-600">Improved</span>
                    </div>
                  </div>
                  <div className="mt-2 text-2xl font-semibold text-gray-900">
                    Class {evaluation.data.functionalTests.ccsClass || '--'}
                  </div>
                </div>
                <div className="bg-white border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-900">LVEF</span>
                    <div className="flex items-center">
                      <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                      <span className="text-sm text-green-600">Improved</span>
                    </div>
                  </div>
                  <div className="mt-2 text-2xl font-semibold text-gray-900">
                    {evaluation.data.functionalTests.lvef || '--'}%
                  </div>
                </div>
              </div>
            </div>

            {/* Quality of Life */}
            <div className="mt-6">
              <h4 className="text-sm font-medium text-gray-500 mb-3">Quality of Life</h4>
              <div className="bg-white border rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-900">Energy Level</span>
                      {renderStarRating(evaluation.data.qualityOfLife.energyLevel)}
                    </div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-900">Sleep Quality</span>
                      {renderStarRating(evaluation.data.qualityOfLife.sleepQuality)}
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-900">Mood</span>
                      {renderStarRating(evaluation.data.qualityOfLife.mood)}
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900">Social Activity</span>
                      {renderStarRating(evaluation.data.qualityOfLife.socialActivity)}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Notes */}
            {evaluation.data.vitals.notes && (
              <div className="mt-6">
                <h4 className="text-sm font-medium text-gray-500 mb-3">Notes</h4>
                <div className="bg-white border rounded-lg p-4">
                  <p className="text-sm text-gray-600">{evaluation.data.vitals.notes}</p>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              onClick={onClose}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EvaluationDetailsModal; 