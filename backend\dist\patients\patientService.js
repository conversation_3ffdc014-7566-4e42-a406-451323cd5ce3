/**
 * @file patientService.ts
 * @description This file contains the service layer for patient-related operations using FHIR's built-in fields
 */
import { validatePatientCreate } from './patientSchemas';
/**
 * Convert our schema to FHIR Patient resource
 */
function toFHIRPatient(patient) {
    const fhirPatient = {
        resourceType: 'Patient',
        active: true,
        name: [{
                use: 'official',
                family: patient.family,
                given: patient.given
            }]
    };
    if (patient.gender) {
        fhirPatient.gender = patient.gender;
    }
    if (patient.birthDate) {
        fhirPatient.birthDate = patient.birthDate;
    }
    // Store intake reason in FHIR extension field
    if (patient.intakeReason) {
        fhirPatient.extension = fhirPatient.extension || [];
        fhirPatient.extension.push({
            url: 'http://syncore.com/fhir/StructureDefinition/intake-reason',
            valueString: patient.intakeReason
        });
    }
    return fhirPatient;
}
/**
 * Convert FHIR Patient resource to our simple schema
 */
function fromFHIRPatient(fhirPatient) {
    const patient = {
        id: fhirPatient.id,
        resourceType: 'Patient',
        family: '',
        given: []
    };
    // Extract name from FHIR format
    if (fhirPatient.name && fhirPatient.name.length > 0) {
        const officialName = fhirPatient.name.find((n) => n.use === 'official') || fhirPatient.name[0];
        if (officialName.family) {
            patient.family = officialName.family;
        }
        if (officialName.given) {
            patient.given = officialName.given;
        }
    }
    if (fhirPatient.gender) {
        patient.gender = fhirPatient.gender;
    }
    if (fhirPatient.birthDate) {
        patient.birthDate = fhirPatient.birthDate;
    }
    // Extract intake reason from extensions
    if (fhirPatient.extension && fhirPatient.extension.length > 0) {
        const intakeExtension = fhirPatient.extension.find((ext) => ext.url === 'http://syncore.com/fhir/StructureDefinition/intake-reason');
        if (intakeExtension && intakeExtension.valueString) {
            patient.intakeReason = intakeExtension.valueString;
        }
    }
    // Include FHIR metadata
    if (fhirPatient.meta) {
        patient.meta = fhirPatient.meta;
    }
    return patient;
}
/**
 * Create a new patient in Medplum
 */
export async function createPatient(client, patientIn) {
    try {
        // Validate input
        const validationErrors = validatePatientCreate(patientIn);
        if (validationErrors.length > 0) {
            throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
        }
        console.log('Creating patient:', {
            family: patientIn.family,
            given: patientIn.given.join(' '),
            gender: patientIn.gender,
            birthDate: patientIn.birthDate,
            intakeReason: patientIn.intakeReason
        });
        // Convert to FHIR format
        const fhirPatient = toFHIRPatient(patientIn);
        // Create in Medplum
        const createdPatient = await client.createResource(fhirPatient);
        console.log('Patient created successfully:', createdPatient.id);
        // Convert back to our format
        return fromFHIRPatient(createdPatient);
    }
    catch (error) {
        console.error('Error in createPatient:', error);
        if (error.message.includes('Validation failed')) {
            throw error;
        }
        // Handle Medplum-specific errors
        if (error.response?.status === 400) {
            throw new Error(`Invalid patient data: ${error.response.data?.issue?.[0]?.diagnostics || error.message}`);
        }
        else if (error.response?.status === 401) {
            throw new Error('Authentication failed. Please check Medplum credentials.');
        }
        else if (error.response?.status === 403) {
            throw new Error('Permission denied. Insufficient access rights.');
        }
        else {
            throw new Error(`Failed to create patient: ${error.message}`);
        }
    }
}
/**
 * Get a patient by ID from Medplum
 */
export async function getPatient(client, patientId) {
    try {
        if (!patientId || patientId.trim().length === 0) {
            throw new Error('Patient ID is required');
        }
        console.log('Retrieving patient:', patientId);
        // Get from Medplum
        const fhirPatient = await client.readResource('Patient', patientId);
        if (!fhirPatient) {
            throw new Error(`Patient not found: ${patientId}`);
        }
        console.log('Patient retrieved successfully:', patientId);
        // Convert to our format
        return fromFHIRPatient(fhirPatient);
    }
    catch (error) {
        console.error('Error in getPatient:', error);
        if (error.response?.status === 404) {
            throw new Error(`Patient not found: ${patientId}`);
        }
        else if (error.response?.status === 401) {
            throw new Error('Authentication failed. Please check Medplum credentials.');
        }
        else if (error.response?.status === 403) {
            throw new Error('Permission denied. Insufficient access rights.');
        }
        else {
            throw new Error(`Failed to retrieve patient: ${error.message}`);
        }
    }
}
/**
 * Update a patient in Medplum
 */
export async function updatePatient(client, patientId, patientIn) {
    try {
        if (!patientId || patientId.trim().length === 0) {
            throw new Error('Patient ID is required');
        }
        console.log('Updating patient:', patientId, patientIn);
        // First, get the existing patient
        const existingPatient = await client.readResource('Patient', patientId);
        if (!existingPatient) {
            throw new Error(`Patient not found: ${patientId}`);
        }
        // Update only the provided fields
        const updatedPatient = { ...existingPatient };
        if (patientIn.gender !== undefined) {
            updatedPatient.gender = patientIn.gender;
        }
        if (patientIn.birthDate !== undefined) {
            updatedPatient.birthDate = patientIn.birthDate;
        }
        if (patientIn.intakeReason !== undefined) {
            // Update intake reason in extensions
            if (!updatedPatient.extension) {
                updatedPatient.extension = [];
            }
            // Find existing intake reason extension
            const intakeExtensionIndex = updatedPatient.extension.findIndex((ext) => ext.url === 'http://syncore.com/fhir/StructureDefinition/intake-reason');
            const newExtension = {
                url: 'http://syncore.com/fhir/StructureDefinition/intake-reason',
                valueString: patientIn.intakeReason
            };
            if (intakeExtensionIndex >= 0) {
                // Update existing extension
                updatedPatient.extension[intakeExtensionIndex] = newExtension;
            }
            else {
                // Add new extension
                updatedPatient.extension.push(newExtension);
            }
        }
        // Update in Medplum
        const result = await client.updateResource(updatedPatient);
        console.log('Patient updated successfully:', patientId);
        // Convert back to our format
        return fromFHIRPatient(result);
    }
    catch (error) {
        console.error('Error in updatePatient:', error);
        if (error.response?.status === 404) {
            throw new Error(`Patient not found: ${patientId}`);
        }
        else if (error.response?.status === 400) {
            throw new Error(`Invalid update data: ${error.response.data?.issue?.[0]?.diagnostics || error.message}`);
        }
        else if (error.response?.status === 401) {
            throw new Error('Authentication failed. Please check Medplum credentials.');
        }
        else if (error.response?.status === 403) {
            throw new Error('Permission denied. Insufficient access rights.');
        }
        else {
            throw new Error(`Failed to update patient: ${error.message}`);
        }
    }
}
/**
 * Delete a patient from Medplum
 */
export async function deletePatient(client, patientId) {
    try {
        if (!patientId || patientId.trim().length === 0) {
            throw new Error('Patient ID is required');
        }
        console.log('Deleting patient:', patientId);
        // Check if patient exists first
        const existingPatient = await client.readResource('Patient', patientId);
        if (!existingPatient) {
            throw new Error(`Patient not found: ${patientId}`);
        }
        // Delete from Medplum
        await client.deleteResource('Patient', patientId);
        console.log('Patient deleted successfully:', patientId);
    }
    catch (error) {
        console.error('Error in deletePatient:', error);
        if (error.response?.status === 404) {
            throw new Error(`Patient not found: ${patientId}`);
        }
        else if (error.response?.status === 401) {
            throw new Error('Authentication failed. Please check Medplum credentials.');
        }
        else if (error.response?.status === 403) {
            throw new Error('Permission denied. Insufficient access rights.');
        }
        else {
            throw new Error(`Failed to delete patient: ${error.message}`);
        }
    }
}
/**
 * Search for patients with pagination
 */
export async function searchPatients(client, params = {}) {
    try {
        const { search, limit = 20, offset = 0, gender, active } = params;
        console.log('Searching patients:', params);
        // Build search parameters for FHIR search
        const searchParams = {
            _count: Math.min(limit, 100).toString(),
            _offset: offset.toString(),
        };
        if (search) {
            searchParams.name = search;
        }
        if (gender) {
            searchParams.gender = gender;
        }
        if (active !== undefined) {
            searchParams.active = active.toString();
        }
        // Search in Medplum
        const searchResult = await client.search('Patient', searchParams);
        // Convert results to our format
        const patients = [];
        if (searchResult.entry) {
            for (const entry of searchResult.entry) {
                if (entry.resource && entry.resource.resourceType === 'Patient') {
                    patients.push(fromFHIRPatient(entry.resource));
                }
            }
        }
        const total = searchResult.total || patients.length;
        const hasMore = total > (offset + patients.length);
        console.log(`Found ${patients.length} patients (${total} total)`);
        return {
            patients,
            total,
            hasMore
        };
    }
    catch (error) {
        console.error('Error in searchPatients:', error);
        if (error.response?.status === 401) {
            throw new Error('Authentication failed. Please check Medplum credentials.');
        }
        else if (error.response?.status === 403) {
            throw new Error('Permission denied. Insufficient access rights.');
        }
        else {
            throw new Error(`Failed to search patients: ${error.message}`);
        }
    }
}
//# sourceMappingURL=patientService.js.map