import { PATIENTS } from '../data/mockData';


/**
 *  Service to handle patient-related operations
 *  swap to Medplum later—just change the implementation here.
 *  @returns {Object} An object containing methods to interact with patient data.
 */
export const patientService = {
  getPatientsByDoctor: (doctorId) => {
    // For now, filter mock data
    return PATIENTS.filter(p => p.doctorId === doctorId);
  },
  // Add more methods as needed
};