/**
 * MedplumService provides methods to authenticate and perform CRUD operations
 * on FHIR resources via the Medplum API.
 */
export declare class MedplumService {
    private clientId;
    private clientSecret;
    private baseUrl;
    private token;
    private client;
    /**
     * Initializes the MedplumService with credentials and base URL.
     * @param clientId - OAuth2 client ID (default: from environment variable)
     * @param clientSecret - OAuth2 client secret (default: from environment variable)
     * @param baseUrl - Medplum FHIR API base URL
     */
    constructor(clientId?: string, clientSecret?: string, baseUrl?: string);
    /**
     * Retrieves and caches an OAuth2 access token for authenticating API requests.
     * @returns The access token string.
     */
    private getAccessToken;
    /**
     * Creates a new FHIR resource of the specified type.
     * @param resourceType - The FHIR resource type (e.g., 'Patient').
     * @param data - The resource data to create.
     * @returns The created resource object.
     */
    create(resourceType: string, data: any): Promise<any>;
    /**
     * Reads a FHIR resource by type and ID.
     * @param resourceType - The FHIR resource type (e.g., 'Patient').
     * @param id - The ID of the resource to read.
     * @returns The resource object.
     */
    read(resourceType: string, id: string): Promise<any>;
    /**
     * Updates an existing FHIR resource.
     * @param resourceType - The FHIR resource type (e.g., 'Patient').
     * @param id - The ID of the resource to update.
     * @param data - The updated resource data.
     * @returns The updated resource object.
     */
    update(resourceType: string, id: string, data: any): Promise<any>;
    /**
     * Deletes a FHIR resource by type and ID.
     * @param resourceType - The FHIR resource type (e.g., 'Patient').
     * @param id - The ID of the resource to delete.
     * @returns True if deletion was successful, false otherwise.
     */
    delete(resourceType: string, id: string): Promise<boolean>;
    /**
    * Performs user login using Medplum's auth endpoint
    * @param email - User's email
    * @param password - User's password
    * @returns Login response with access token and profile
    */
    login(email: string, password: string): Promise<{
        login: string | null;
        profile: any;
    }>;
    /**
      * Gets the current access token
      * @returns The current access token
      */
    getToken(): string | null;
}
//# sourceMappingURL=medplum_client.d.ts.map