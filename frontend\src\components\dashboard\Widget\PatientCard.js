import React, { useState } from 'react';

const PatientCard = ({ patient, onClose, onStartEECP, onFollowUpTest, onGenerateReport, isPanel = false }) => {
  const [activeTab, setActiveTab] = useState('overview');

  if (!patient) return null;

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      case 'completed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'pending':
        return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'discontinued':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-slate-100 text-slate-800 border-slate-200';
    }
  };

  const getProgressColor = (progress) => {
    if (progress >= 80) return 'bg-emerald-500';
    if (progress >= 60) return 'bg-blue-500';
    if (progress >= 40) return 'bg-amber-500';
    return 'bg-slate-400';
  };

  const tabs = [
    { id: 'demographics', name: 'Demographics', icon: '👤' },
    { id: 'medical-history', name: 'Medical History', icon: '🏥' },
    { id: 'baseline', name: 'Baseline Assessment', icon: '📊' },
    { id: 'medications', name: 'Medications', icon: '💊' },
    { id: 'eecp-sessions', name: 'EECP Sessions', icon: '💓' },
    { id: 'eligibility', name: 'Eligibility & Consent', icon: '✅' },
    { id: 'therapy-plan', name: 'Therapy Plan', icon: '📋' },
    { id: 'follow-up', name: 'Follow-up', icon: '🔄' }
  ];

  const renderDemographics = () => (
    <div className="space-y-6">
      {/* Basic Information */}
      <div className="bg-gradient-to-r from-blue-50/80 to-indigo-50/80 rounded-xl p-6 border border-blue-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Basic Information</h3>
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Full Name</label>
              <p className="text-slate-900 font-medium">{patient.name || 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Date of Birth</label>
              <p className="text-slate-900">{patient.dateOfBirth || 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Age</label>
              <p className="text-slate-900">{patient.age || 'Not specified'} years</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Gender</label>
              <p className="text-slate-900">{patient.gender || 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Ethnicity</label>
              <p className="text-slate-900">{patient.ethnicity || 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Occupation</label>
              <p className="text-slate-900">{patient.occupation || 'Not specified'}</p>
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Unique Patient Identifier</label>
              <p className="text-slate-900 font-medium">{patient.id}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Phone</label>
              <p className="text-slate-900">{patient.phone || 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Email</label>
              <p className="text-slate-900">{patient.email || 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Address</label>
              <p className="text-slate-900">{patient.address || 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Residence Type</label>
              <p className="text-slate-900">{patient.residenceType || 'Not specified'}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Administrative Information */}
      <div className="bg-gradient-to-r from-emerald-50/80 to-cyan-50/80 rounded-xl p-6 border border-emerald-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Administrative Information</h3>
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Referral Source</label>
              <p className="text-slate-900">{patient.referralSource || 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Walk-in Indicator</label>
              <p className="text-slate-900">{patient.walkIn ? 'Yes' : 'No'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Insurance Provider</label>
              <p className="text-slate-900">{patient.insurance || 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Insurance Policy Number</label>
              <p className="text-slate-900">{patient.insurancePolicyNumber || 'Not specified'}</p>
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Emergency Contact Name</label>
              <p className="text-slate-900">{patient.emergencyContactName || 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Emergency Contact Phone</label>
              <p className="text-slate-900">{patient.emergencyContactPhone || 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Emergency Contact Relationship</label>
              <p className="text-slate-900">{patient.emergencyContactRelationship || 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Attending Physician</label>
              <p className="text-slate-900">{patient.physician || 'Not specified'}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Activities of Daily Living & Hobbies */}
      <div className="bg-gradient-to-r from-purple-50/80 to-pink-50/80 rounded-xl p-6 border border-purple-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Lifestyle Information</h3>
        <div className="grid grid-cols-2 gap-6">
          <div>
            <label className="text-sm font-medium text-slate-600">Activities of Daily Living (ADLs)</label>
            <p className="text-slate-900">{patient.adls || 'Independent in all ADLs'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-slate-600">Hobbies & Interests</label>
            <p className="text-slate-900">{patient.hobbies || 'Not specified'}</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderMedicalHistory = () => (
    <div className="space-y-6">
      {/* Primary Diagnosis & Cardiovascular History */}
      <div className="bg-gradient-to-r from-red-50/80 to-pink-50/80 rounded-xl p-6 border border-red-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Cardiovascular History</h3>
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Primary Diagnosis</label>
              <p className="text-slate-900 font-medium">{patient.diagnosis || 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">CAD Status</label>
              <p className="text-slate-900">{patient.cadStatus || 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Number of Diseased Vessels</label>
              <p className="text-slate-900">{patient.diseasedVessels || 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Left Main Disease</label>
              <p className="text-slate-900">{patient.leftMainDisease ? 'Yes' : 'No'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">History of MI</label>
              <p className="text-slate-900">{patient.historyMI || 'No'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">History of CHF</label>
              <p className="text-slate-900">{patient.historyCHF || 'No'}</p>
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Previous CABG</label>
              <p className="text-slate-900">{patient.previousCABG || 'None'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Previous PCI/Stents</label>
              <p className="text-slate-900">{patient.previousPCI || 'None'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">History of Arrhythmias</label>
              <p className="text-slate-900">{patient.historyArrhythmias || 'No'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Valvular Disease</label>
              <p className="text-slate-900">{patient.valvularDisease || 'No'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">History of Stroke/TIA</label>
              <p className="text-slate-900">{patient.historyStroke || 'No'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Peripheral Vascular Disease</label>
              <p className="text-slate-900">{patient.pvd || 'No'}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Risk Factors & Co-morbidities */}
      <div className="bg-gradient-to-r from-orange-50/80 to-yellow-50/80 rounded-xl p-6 border border-orange-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Risk Factors & Co-morbidities</h3>
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Hypertension</label>
              <p className="text-slate-900">{patient.hypertension || 'No'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Diabetes Mellitus</label>
              <p className="text-slate-900">{patient.diabetes || 'No'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Hyperlipidemia</label>
              <p className="text-slate-900">{patient.hyperlipidemia || 'No'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Smoking History</label>
              <p className="text-slate-900">{patient.smokingHistory || 'Never'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Family History of Premature CAD</label>
              <p className="text-slate-900">{patient.familyHistoryCAD ? 'Yes' : 'No'}</p>
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Kidney Disease</label>
              <p className="text-slate-900">{patient.kidneyDisease || 'No'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Creatinine Level</label>
              <p className="text-slate-900">{patient.creatinine || 'Not measured'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">eGFR</label>
              <p className="text-slate-900">{patient.egfr || 'Not measured'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Bleeding Diathesis</label>
              <p className="text-slate-900">{patient.bleedingDisorder || 'No'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Anticoagulation Use</label>
              <p className="text-slate-900">{patient.anticoagulation || 'No'}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Vascular History */}
      <div className="bg-gradient-to-r from-purple-50/80 to-indigo-50/80 rounded-xl p-6 border border-purple-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Vascular History</h3>
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">ABI (Ankle-Brachial Index)</label>
              <p className="text-slate-900">{patient.abi || 'Not measured'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">History of DVT</label>
              <p className="text-slate-900">{patient.historyDVT || 'No'}</p>
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">History of PE</label>
              <p className="text-slate-900">{patient.historyPE || 'No'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Phlebitis/Stasis Ulcer</label>
              <p className="text-slate-900">{patient.phlebitis || 'No'}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Allergies */}
      <div className="bg-gradient-to-r from-amber-50/80 to-orange-50/80 rounded-xl p-6 border border-amber-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Allergies</h3>
        <div className="space-y-2">
          {(patient.allergies || ['No known allergies']).map((allergy, index) => (
            <div key={index} className="flex items-center p-3 bg-amber-50/50 rounded-lg border border-amber-200/50">
              <div className="h-2 w-2 bg-amber-500 rounded-full mr-3"></div>
              <span className="text-slate-900">{allergy}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderBaseline = () => (
    <div className="space-y-6">
      {/* Physical Measurements */}
      <div className="bg-gradient-to-r from-green-50/80 to-emerald-50/80 rounded-xl p-6 border border-green-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Physical Measurements</h3>
        <div className="grid grid-cols-3 gap-6">
          <div className="text-center p-4 bg-green-50/50 rounded-xl border border-green-200/50">
            <div className="text-2xl font-bold text-green-600">{patient.height || 'N/A'}</div>
            <div className="text-sm text-slate-600">Height</div>
            <div className="text-xs text-slate-500 mt-1">cm</div>
          </div>
          <div className="text-center p-4 bg-blue-50/50 rounded-xl border border-blue-200/50">
            <div className="text-2xl font-bold text-blue-600">{patient.weight || 'N/A'}</div>
            <div className="text-sm text-slate-600">Weight</div>
            <div className="text-xs text-slate-500 mt-1">kg</div>
          </div>
          <div className="text-center p-4 bg-purple-50/50 rounded-xl border border-purple-200/50">
            <div className="text-2xl font-bold text-purple-600">{patient.bmi || 'N/A'}</div>
            <div className="text-sm text-slate-600">BMI</div>
            <div className="text-xs text-slate-500 mt-1">kg/m²</div>
          </div>
        </div>
      </div>

      {/* Vital Signs */}
      <div className="bg-gradient-to-r from-red-50/80 to-pink-50/80 rounded-xl p-6 border border-red-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Baseline Vital Signs</h3>
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Blood Pressure (Sitting)</label>
              <p className="text-slate-900 font-medium">{patient.bpSitting || 'Not measured'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Blood Pressure (Standing)</label>
              <p className="text-slate-900">{patient.bpStanding || 'Not measured'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Heart Rate</label>
              <p className="text-slate-900">{patient.heartRate || 'Not measured'} bpm</p>
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Oxygen Saturation</label>
              <p className="text-slate-900">{patient.oxygenSaturation || 'Not measured'}%</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">NYHA Functional Class</label>
              <p className="text-slate-900 font-medium">{patient.nyhaClass || 'Not assessed'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">CCS Angina Class</label>
              <p className="text-slate-900 font-medium">{patient.ccsClass || 'Not assessed'}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Cardiac Function */}
      <div className="bg-gradient-to-r from-blue-50/80 to-indigo-50/80 rounded-xl p-6 border border-blue-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Cardiac Function</h3>
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">LVEF (%)</label>
              <p className="text-slate-900 font-medium">{patient.lvef?.before || 'Not measured'}%</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">6-Minute Walk Distance</label>
              <p className="text-slate-900">{patient.sixMinuteWalk || 'Not tested'} meters</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Angina Frequency</label>
              <p className="text-slate-900">{patient.anginaFrequency || 'Not reported'}</p>
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Nitroglycerin Usage</label>
              <p className="text-slate-900">{patient.nitroglycerinUsage || 'Not reported'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Quality of Life Score</label>
              <p className="text-slate-900">{patient.qolScore || 'Not assessed'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Rose Dyspnea Scale</label>
              <p className="text-slate-900">{patient.roseScale || 'Not assessed'}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Exercise Testing */}
      <div className="bg-gradient-to-r from-orange-50/80 to-yellow-50/80 rounded-xl p-6 border border-orange-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Exercise Treadmill Test Results</h3>
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Exercise Duration</label>
              <p className="text-slate-900">{patient.exerciseDuration || 'Not tested'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Time to ST Depression</label>
              <p className="text-slate-900">{patient.timeToSTDepression || 'Not applicable'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Peak Heart Rate</label>
              <p className="text-slate-900">{patient.peakHeartRate || 'Not measured'} bpm</p>
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Reason for Stopping</label>
              <p className="text-slate-900">{patient.reasonForStopping || 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Peak Angina Rating</label>
              <p className="text-slate-900">{patient.peakAnginaRating || 'Not rated'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">RPE Score</label>
              <p className="text-slate-900">{patient.rpeScore || 'Not assessed'}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Biomarkers */}
      <div className="bg-gradient-to-r from-purple-50/80 to-pink-50/80 rounded-xl p-6 border border-purple-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Baseline Biomarkers</h3>
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">BNP/NT-pro-BNP</label>
              <p className="text-slate-900">{patient.bnp || 'Not measured'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Total Cholesterol</label>
              <p className="text-slate-900">{patient.totalCholesterol || 'Not measured'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">LDL Cholesterol</label>
              <p className="text-slate-900">{patient.ldlCholesterol || 'Not measured'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">HDL Cholesterol</label>
              <p className="text-slate-900">{patient.hdlCholesterol || 'Not measured'}</p>
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Triglycerides</label>
              <p className="text-slate-900">{patient.triglycerides || 'Not measured'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Blood Glucose</label>
              <p className="text-slate-900">{patient.bloodGlucose || 'Not measured'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">HbA1c</label>
              <p className="text-slate-900">{patient.hba1c || 'Not measured'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">hsCRP</label>
              <p className="text-slate-900">{patient.hsCRP || 'Not measured'}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderMedications = () => (
    <div className="space-y-6">
      {/* Current Medications */}
      <div className="bg-gradient-to-r from-blue-50/80 to-indigo-50/80 rounded-xl p-6 border border-blue-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Current Medications</h3>
        <div className="space-y-3">
          {(patient.medications || ['No medications listed']).map((medication, index) => (
            <div key={index} className="flex items-center justify-between p-4 bg-blue-50/50 rounded-lg border border-blue-200/50">
              <div className="flex items-center">
                <div className="h-3 w-3 bg-blue-500 rounded-full mr-4"></div>
                <div>
                  <span className="text-slate-900 font-medium">{medication.name || medication}</span>
                  {medication.dosage && <span className="text-slate-600 ml-2">- {medication.dosage}</span>}
                </div>
              </div>
              <div className="text-right">
                {medication.frequency && <div className="text-sm text-slate-600">{medication.frequency}</div>}
                {medication.form && <div className="text-xs text-slate-500">{medication.form}</div>}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Cardiovascular Medications */}
      <div className="bg-gradient-to-r from-red-50/80 to-pink-50/80 rounded-xl p-6 border border-red-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Cardiovascular Medications</h3>
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Beta-blockers</label>
              <p className="text-slate-900">{patient.betaBlockers || 'None'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">ACE Inhibitors</label>
              <p className="text-slate-900">{patient.aceInhibitors || 'None'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Calcium Channel Blockers</label>
              <p className="text-slate-900">{patient.calciumChannelBlockers || 'None'}</p>
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Nitrates</label>
              <p className="text-slate-900">{patient.nitrates || 'None'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Statins</label>
              <p className="text-slate-900">{patient.statins || 'None'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Antiplatelets</label>
              <p className="text-slate-900">{patient.antiplatelets || 'None'}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderEECPSessions = () => (
    <div className="space-y-6">
      {/* Treatment Progress */}
      <div className="bg-gradient-to-r from-emerald-50/80 to-cyan-50/80 rounded-xl p-6 border border-emerald-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Treatment Progress</h3>
        <div className="grid grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-emerald-600">{patient.sessions || 0}</div>
            <div className="text-sm text-slate-600">Sessions Completed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{(patient.totalSessions || 35) - (patient.sessions || 0)}</div>
            <div className="text-sm text-slate-600">Sessions Remaining</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{patient.progress || 0}%</div>
            <div className="text-sm text-slate-600">Progress</div>
          </div>
        </div>

        <div className="mt-4">
          <div className="flex justify-between text-sm text-slate-600 mb-2">
            <span>Treatment Progress</span>
            <span>{patient.sessions || 0}/{patient.totalSessions || 35} sessions</span>
          </div>
          <div className="w-full bg-slate-200 rounded-full h-3">
            <div
              className={`h-3 rounded-full ${getProgressColor(patient.progress || 0)} transition-all duration-300`}
              style={{ width: `${patient.progress || 0}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Session Data Tracking */}
      <div className="bg-gradient-to-r from-blue-50/80 to-indigo-50/80 rounded-xl p-6 border border-blue-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">EECP Session Data Points</h3>
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <h4 className="font-medium text-slate-700">Pre-EECP Data</h4>
            <div className="space-y-2 text-sm">
              <div>• O2 in liters</div>
              <div>• Weight</div>
              <div>• Blood Pressure</div>
              <div>• Pulse Rate</div>
              <div>• SPO2</div>
            </div>

            <h4 className="font-medium text-slate-700 mt-4">15-min EECP Data</h4>
            <div className="space-y-2 text-sm">
              <div>• D/S Peak (15 min)</div>
              <div>• D/S Area (15 min)</div>
              <div>• Pulse Rate (15 min)</div>
              <div>• Applied Pressure</div>
            </div>
          </div>
          <div className="space-y-4">
            <h4 className="font-medium text-slate-700">45-min EECP Data</h4>
            <div className="space-y-2 text-sm">
              <div>• D/S Peak (45 min)</div>
              <div>• D/S Area (45 min)</div>
              <div>• Pulse Rate (45 min)</div>
              <div>• Applied Pressure</div>
            </div>

            <h4 className="font-medium text-slate-700 mt-4">Post-EECP Data</h4>
            <div className="space-y-2 text-sm">
              <div>• BP Systolic/Diastolic</div>
              <div>• Pulse Rate</div>
              <div>• SPO2</div>
              <div>• Skin Condition</div>
              <div>• Electrode Artifacts</div>
              <div>• Therapist/Physician Names</div>
            </div>
          </div>
        </div>
      </div>

      {/* LVEF Improvement */}
      <div className="bg-gradient-to-r from-green-50/80 to-emerald-50/80 rounded-xl p-6 border border-green-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">LVEF Improvement</h3>
        <div className="grid grid-cols-2 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{patient.lvef?.before || 'N/A'}%</div>
            <div className="text-sm text-slate-600">Before Treatment</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{patient.lvef?.current || 'N/A'}%</div>
            <div className="text-sm text-slate-600">Current</div>
          </div>
        </div>
        {patient.lvef?.before && patient.lvef?.current && (
          <div className="mt-4 text-center">
            <div className="text-lg font-semibold text-emerald-600">
              +{patient.lvef.current - patient.lvef.before}% Improvement
            </div>
          </div>
        )}
      </div>
    </div>
  );

  const renderEligibility = () => (
    <div className="space-y-6">
      {/* Eligibility Assessment */}
      <div className="bg-gradient-to-r from-green-50/80 to-emerald-50/80 rounded-xl p-6 border border-green-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Eligibility Assessment</h3>
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Indications Confirmed</label>
              <p className="text-slate-900">{patient.indicationsConfirmed ? 'Yes' : 'Pending'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Operability Assessment</label>
              <p className="text-slate-900">{patient.operabilityAssessment || 'Not assessed'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Contraindications Checked</label>
              <p className="text-slate-900">{patient.contraindicationsChecked ? 'Yes' : 'No'}</p>
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Final Eligibility</label>
              <p className={`font-medium ${patient.eligible ? 'text-green-600' : 'text-red-600'}`}>
                {patient.eligible ? 'Eligible' : 'Ineligible'}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Assessing Doctor</label>
              <p className="text-slate-900">{patient.assessingDoctor || 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Assessment Date</label>
              <p className="text-slate-900">{patient.assessmentDate || 'Not specified'}</p>
            </div>
          </div>
        </div>
        {patient.doctorRationale && (
          <div className="mt-4">
            <label className="text-sm font-medium text-slate-600">Doctor's Rationale</label>
            <p className="text-slate-900 mt-1">{patient.doctorRationale}</p>
          </div>
        )}
      </div>

      {/* Consent & Education */}
      <div className="bg-gradient-to-r from-blue-50/80 to-indigo-50/80 rounded-xl p-6 border border-blue-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Consent & Education</h3>
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Educational Materials Provided</label>
              <p className="text-slate-900">{patient.educationProvided ? 'Yes' : 'No'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Patient Questions Answered</label>
              <p className="text-slate-900">{patient.questionsAnswered ? 'Yes' : 'No'}</p>
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Informed Consent Obtained</label>
              <p className="text-slate-900">{patient.consentObtained ? 'Yes' : 'No'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Consent Date</label>
              <p className="text-slate-900">{patient.consentDate || 'Not specified'}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderTherapyPlan = () => (
    <div className="space-y-6">
      {/* Therapy Plan */}
      <div className="bg-gradient-to-r from-purple-50/80 to-pink-50/80 rounded-xl p-6 border border-purple-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Therapy Plan</h3>
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Prescribed Sessions</label>
              <p className="text-slate-900 font-medium">{patient.totalSessions || 35} sessions</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Session Duration</label>
              <p className="text-slate-900">{patient.sessionDuration || '1 hour'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Frequency</label>
              <p className="text-slate-900">{patient.frequency || '5 days/week'}</p>
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Target Inflation Pressure</label>
              <p className="text-slate-900">{patient.targetPressure || 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Plan Approved By</label>
              <p className="text-slate-900">{patient.planApprovedBy || 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Approval Date</label>
              <p className="text-slate-900">{patient.planApprovalDate || 'Not specified'}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Scheduling */}
      <div className="bg-gradient-to-r from-orange-50/80 to-yellow-50/80 rounded-xl p-6 border border-orange-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Scheduling Information</h3>
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Assigned Technician</label>
              <p className="text-slate-900">{patient.assignedTechnician || 'Not assigned'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Clinic Location</label>
              <p className="text-slate-900">{patient.clinicLocation || 'Main clinic'}</p>
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Next Appointment</label>
              <p className="text-slate-900">{patient.nextAppointment || 'Not scheduled'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Reminder Settings</label>
              <p className="text-slate-900">{patient.reminderSettings || 'Standard'}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderFollowUp = () => (
    <div className="space-y-6">
      {/* Follow-up Schedule */}
      <div className="bg-gradient-to-r from-indigo-50/80 to-purple-50/80 rounded-xl p-6 border border-indigo-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Follow-up Schedule</h3>
        <div className="space-y-4">
          <div className="flex items-center p-4 bg-emerald-50/50 rounded-lg border border-emerald-200/50">
            <div className="h-3 w-3 bg-emerald-500 rounded-full mr-4"></div>
            <div>
              <div className="font-medium text-slate-900">3-Month Follow-up</div>
              <div className="text-sm text-slate-600">{patient.threeMonthFollowUp || 'Not scheduled'}</div>
            </div>
          </div>
          <div className="flex items-center p-4 bg-blue-50/50 rounded-lg border border-blue-200/50">
            <div className="h-3 w-3 bg-blue-500 rounded-full mr-4"></div>
            <div>
              <div className="font-medium text-slate-900">6-Month Review</div>
              <div className="text-sm text-slate-600">{patient.sixMonthReview || 'Not scheduled'}</div>
            </div>
          </div>
          <div className="flex items-center p-4 bg-purple-50/50 rounded-lg border border-purple-200/50">
            <div className="h-3 w-3 bg-purple-500 rounded-full mr-4"></div>
            <div>
              <div className="font-medium text-slate-900">Annual Follow-up</div>
              <div className="text-sm text-slate-600">{patient.annualFollowUp || 'Not scheduled'}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Long-term Tracking */}
      <div className="bg-gradient-to-r from-green-50/80 to-emerald-50/80 rounded-xl p-6 border border-green-200/50">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Long-term Tracking</h3>
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Hospitalizations Since Treatment</label>
              <p className="text-slate-900">{patient.hospitalizationsSince || 'None reported'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Subsequent CV Events</label>
              <p className="text-slate-900">{patient.subsequentEvents || 'None reported'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Patient Compliance</label>
              <p className="text-slate-900">{patient.compliance || 'Good'}</p>
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">Long-term QoL Score</label>
              <p className="text-slate-900">{patient.longTermQoL || 'Not assessed'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Patient Satisfaction</label>
              <p className="text-slate-900">{patient.satisfaction || 'Not assessed'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-slate-600">Mortality Status</label>
              <p className="text-slate-900">{patient.mortalityStatus || 'Alive'}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const containerClasses = isPanel
    ? "h-full flex flex-col"
    : "fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4";

  const cardClasses = isPanel
    ? "bg-white/90 backdrop-blur-xl h-full flex flex-col"
    : "bg-white/90 backdrop-blur-xl rounded-2xl shadow-2xl border border-slate-200/50 w-full max-w-4xl max-h-[90vh] overflow-hidden";

  return (
    <div className={containerClasses}>
      <div className={cardClasses}>
        {/* Header */}
        <div className="px-6 py-4 border-b border-slate-200/50 bg-gradient-to-r from-slate-50/80 to-slate-100/80">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-slate-100 to-slate-200 text-slate-700 flex items-center justify-center font-bold text-lg mr-4">
                {patient.name.split(' ').map(n => n[0]).join('')}
              </div>
              <div>
                <h2 className="text-xl font-bold text-slate-900">{patient.name}</h2>
                <div className="flex items-center space-x-4 text-sm text-slate-600">
                  <span>{patient.id}</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(patient.status)}`}>
                    {patient.status}
                  </span>
                </div>
              </div>
            </div>

            <button
              onClick={onClose}
              className="p-2 rounded-xl hover:bg-slate-100/50 text-slate-500 hover:text-slate-700 transition-colors"
            >
              <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="px-6 py-3 border-b border-slate-200/50 bg-white/50">
          <div className="flex space-x-1">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-emerald-100 text-emerald-700 border border-emerald-200'
                    : 'text-slate-600 hover:text-slate-900 hover:bg-slate-50'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.name}
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {activeTab === 'demographics' && renderDemographics()}
          {activeTab === 'medical-history' && renderMedicalHistory()}
          {activeTab === 'baseline' && renderBaseline()}
          {activeTab === 'medications' && renderMedications()}
          {activeTab === 'eecp-sessions' && renderEECPSessions()}
          {activeTab === 'eligibility' && renderEligibility()}
          {activeTab === 'therapy-plan' && renderTherapyPlan()}
          {activeTab === 'follow-up' && renderFollowUp()}
        </div>

        {/* Actions */}
        <div className="px-6 py-4 border-t border-slate-200/50 bg-gradient-to-r from-slate-50/80 to-slate-100/80">
          <div className="flex items-center justify-between">
            <div className="flex space-x-3">
              <button
                onClick={() => onStartEECP(patient)}
                className="px-4 py-2 bg-gradient-to-r from-emerald-500 to-green-500 text-white font-medium rounded-xl hover:from-emerald-600 hover:to-green-600 transition-colors shadow-lg shadow-emerald-500/25"
              >
                Start EECP Session
              </button>
              <button
                onClick={() => onFollowUpTest(patient)}
                className="px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-500 text-white font-medium rounded-xl hover:from-blue-600 hover:to-indigo-600 transition-colors shadow-lg shadow-blue-500/25"
              >
                Record Follow-up Test
              </button>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => onGenerateReport(patient, 'progress')}
                className="px-4 py-2 border border-purple-500 text-purple-600 font-medium rounded-xl hover:bg-purple-50 transition-colors"
              >
                Progress Report
              </button>
              <button
                onClick={() => onGenerateReport(patient, 'discharge')}
                className="px-4 py-2 border border-amber-500 text-amber-600 font-medium rounded-xl hover:bg-amber-50 transition-colors"
              >
                Discharge Report
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientCard;