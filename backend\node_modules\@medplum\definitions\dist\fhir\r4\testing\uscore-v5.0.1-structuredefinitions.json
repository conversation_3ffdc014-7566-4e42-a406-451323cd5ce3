[{"resourceType": "StructureDefinition", "id": "us-core-patient", "url": "http://hl7.org/fhir/us/core/StructureDefinition/us-core-patient", "version": "5.0.1", "name": "USCorePatientProfile", "title": "US Core Patient Profile", "status": "active", "experimental": false, "date": "2022-04-20T15:02:49-07:00", "publisher": "HL7 International - Cross-Group Projects", "contact": [{"name": "HL7 International - Cross-Group Projects", "telecom": [{"system": "url", "value": "http://www.hl7.org/Special/committees/cgp"}, {"system": "email", "value": "<EMAIL>"}]}], "description": "The US Core Patient Profile meets the U.S. Core Data for Interoperability (USCDI) v2 'Patient Demographics' requirements. This profile sets minimum expectations for the Patient resource to record, search, and fetch basic demographics and other administrative information about an individual patient. It identifies which core elements, extensions, vocabularies and value sets **SHALL** be present in the resource when using this profile to promote interoperability and adoption through common implementation.  It identifies which core elements, extensions, vocabularies and value sets **SHALL** be present in the resource when using this profile.  It provides the floor for standards development for specific uses cases.", "jurisdiction": [{"coding": [{"system": "urn:iso:std:iso:3166", "code": "US"}]}], "copyright": "Used by permission of HL7 International, all rights reserved Creative Commons License", "fhirVersion": "4.0.1", "kind": "resource", "abstract": false, "type": "Patient", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/Patient", "derivation": "constraint", "snapshot": {"element": [{"id": "Patient", "path": "Patient", "short": "Information about an individual or animal receiving health care services", "definition": "\\-", "comment": "\\-", "alias": ["SubjectOfCare Client Resident"], "min": 0, "max": "*", "base": {"path": "Patient", "min": 0, "max": "*"}, "constraint": [{"key": "dom-2", "severity": "error", "human": "If the resource is contained in another resource, it SHALL NOT contain nested Resources", "expression": "contained.contained.empty()", "xpath": "not(parent::f:contained and f:contained)", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"key": "dom-3", "severity": "error", "human": "If the resource is contained in another resource, it SHALL be referred to from elsewhere in the resource or SHALL refer to the containing resource", "expression": "contained.where((('#'+id in (%resource.descendants().reference | %resource.descendants().as(canonical) | %resource.descendants().as(uri) | %resource.descendants().as(url))) or descendants().where(reference = '#').exists() or descendants().where(as(canonical) = '#').exists() or descendants().where(as(canonical) = '#').exists()).not()).trace('unmatched', id).empty()", "xpath": "not(exists(for $id in f:contained/*/f:id/@value return $contained[not(parent::*/descendant::f:reference/@value=concat('#', $contained/*/id/@value) or descendant::f:reference[@value='#'])]))", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"key": "dom-4", "severity": "error", "human": "If a resource is contained in another resource, it SHALL NOT have a meta.versionId or a meta.lastUpdated", "expression": "contained.meta.versionId.empty() and contained.meta.lastUpdated.empty()", "xpath": "not(exists(f:contained/*/f:meta/f:versionId)) and not(exists(f:contained/*/f:meta/f:lastUpdated))", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"key": "dom-5", "severity": "error", "human": "If a resource is contained in another resource, it SHALL NOT have a security label", "expression": "contained.meta.security.empty()", "xpath": "not(exists(f:contained/*/f:meta/f:security))", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bestpractice", "valueBoolean": true}, {"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bestpractice-explanation", "valueMarkdown": "When a resource has no narrative, only systems that fully understand the data can display the resource to a human safely. Including a human readable representation in the resource makes for a much more robust eco-system and cheaper handling of resources by intermediary systems. Some ecosystems restrict distribution of resources to only those systems that do fully understand the resources, and as a consequence implementers may believe that the narrative is superfluous. However experience shows that such eco-systems often open up to new participants over time."}], "key": "dom-6", "severity": "warning", "human": "A resource should have narrative for robust management", "expression": "text.`div`.exists()", "xpath": "exists(f:text/h:div)", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"key": "us-core-6", "severity": "error", "human": "Either Patient.name.given and/or Patient.name.family SHALL be present or a Data Absent Reason Extension SHALL be present.", "expression": "(name.family.exists() or name.given.exists()) xor extension.where(url='http://hl7.org/fhir/StructureDefinition/data-absent-reason').exists()", "xpath": "(/f:name/f:extension/@url='http://hl7.org/fhir/StructureDefinition/data-absent-reason' and not(/f:name/f:family or /f:name/f:given)) or (not(/f:name/f:extension/@url='http://hl7.org/fhir/StructureDefinition/data-absent-reason') and (/f:name/f:family or /f:name/f:given))"}], "mustSupport": false, "isModifier": false, "isSummary": false}, {"id": "Patient.id", "path": "Patient.id", "short": "Logical id of this artifact", "definition": "The logical id of the resource, as used in the URL for the resource. Once assigned, this value never changes.", "comment": "The only time that a resource does not have an id is when it is being submitted to the server using a create operation.", "min": 0, "max": "1", "base": {"path": "Resource.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": true}, {"id": "Patient.meta", "path": "Patient.meta", "short": "Metadata about the resource", "definition": "The metadata about the resource. This is content that is maintained by the infrastructure. Changes to the content might not always be associated with version changes to the resource.", "min": 0, "max": "1", "base": {"path": "Resource.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Patient.implicitRules", "path": "Patient.implicitRules", "short": "A set of rules under which this content was created", "definition": "A reference to a set of rules that were followed when the resource was constructed, and which must be understood when processing the content. Often, this is a reference to an implementation guide that defines the special rules along with other profiles etc.", "comment": "Asserting this rule set restricts the content to be only understood by a limited set of trading partners. This inherently limits the usefulness of the data in the long term. However, the existing health eco-system is highly fractured, and not yet ready to define, collect, and exchange data in a generally computable sense. Wherever possible, implementers and/or specification writers should avoid using this element. Often, when used, the URL is a reference to an implementation guide that defines these special rules as part of it's narrative along with other profiles, value sets, etc.", "min": 0, "max": "1", "base": {"path": "Resource.implicitRules", "min": 0, "max": "1"}, "type": [{"code": "uri"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": true, "isModifierReason": "This element is labeled as a modifier because the implicit rules may provide additional knowledge about the resource that modifies it's meaning or interpretation", "isSummary": true}, {"id": "Patient.language", "path": "Patient.language", "short": "Language of the resource content", "definition": "The base language in which the resource is written.", "comment": "Language is provided to support indexing and accessibility (typically, services such as text to speech use the language tag). The html language tag in the narrative applies  to the narrative. The language tag on the resource may be used to specify the language of other presentations generated from the data in the resource. Not all the content has to be in the base language. The Resource.language should not be assumed to apply to the narrative automatically. If a language is specified, it should it also be specified on the div element in the html (see rules in HTML5 for information about the relationship between xml:lang and the html lang attribute).", "min": 0, "max": "1", "base": {"path": "Resource.language", "min": 0, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-maxValueSet", "valueCanonical": "http://hl7.org/fhir/ValueSet/all-languages"}, {"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "Language"}, {"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-isCommonBinding", "valueBoolean": true}], "strength": "preferred", "description": "A human language.", "valueSet": "http://hl7.org/fhir/ValueSet/languages"}}, {"id": "Patient.text", "path": "Patient.text", "short": "Text summary of the resource, for human interpretation", "definition": "A human-readable narrative that contains a summary of the resource and can be used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative. Resource definitions may define what content should be represented in the narrative to ensure clinical safety.", "comment": "Contained resources do not have narrative. Resources that are not contained SHOULD have a narrative. In some cases, a resource may only have text with little or no additional discrete data (as long as all minOccurs=1 elements are satisfied).  This may be necessary for data from legacy systems where information is captured as a \"text blob\" or where text is additionally entered raw or narrated and encoded information is added later.", "alias": ["narrative", "html", "xhtml", "display"], "min": 0, "max": "1", "base": {"path": "DomainResource.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Patient.contained", "path": "Patient.contained", "short": "Contained, inline Resources", "definition": "These resources do not have an independent existence apart from the resource that contains them - they cannot be identified independently, and nor can they have their own independent transaction scope.", "comment": "This should never be done when the content can be identified properly, as once identification is lost, it is extremely difficult (and context dependent) to restore it again. Contained resources may have profiles and tags In their meta elements, but SHALL NOT have security labels.", "alias": ["inline resources", "anonymous resources", "contained resources"], "min": 0, "max": "*", "base": {"path": "DomainResource.contained", "min": 0, "max": "*"}, "type": [{"code": "Resource"}], "isModifier": false, "isSummary": false}, {"id": "Patient.extension", "path": "Patient.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "ordered": false, "rules": "open"}, "short": "Extension", "definition": "An Extension", "min": 0, "max": "*", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Patient.extension:race", "path": "Patient.extension", "sliceName": "race", "short": "US Core Race Extension", "definition": "Concepts classifying the person into a named category of humans sharing common history, traits, geographical origin or nationality.  The race codes used to represent these concepts are based upon the [CDC Race and Ethnicity Code Set Version 1.0](http://www.cdc.gov/phin/resources/vocabulary/index.html) which includes over 900 concepts for representing race and ethnicity of which 921 reference race.  The race concepts are grouped by and pre-mapped to the 5 OMB race categories:\n\n   - American Indian or Alaska Native\n   - Asian\n   - Black or African American\n   - Native Hawaiian or Other Pacific Islander\n   - White.", "min": 0, "max": "1", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension", "profile": ["http://hl7.org/fhir/us/core/StructureDefinition/us-core-race"]}], "condition": ["ele-1"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), 'value')])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "mustSupport": false, "isModifier": false}, {"id": "Patient.extension:ethnicity", "path": "Patient.extension", "sliceName": "ethnicity", "short": "US Core ethnicity Extension", "definition": "Concepts classifying the person into a named category of humans sharing common history, traits, geographical origin or nationality.  The ethnicity codes used to represent these concepts are based upon the [CDC ethnicity and Ethnicity Code Set Version 1.0](http://www.cdc.gov/phin/resources/vocabulary/index.html) which includes over 900 concepts for representing race and ethnicity of which 43 reference ethnicity.  The ethnicity concepts are grouped by and pre-mapped to the 2 OMB ethnicity categories: - Hispanic or Latino - Not Hispanic or Latino.", "min": 0, "max": "1", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension", "profile": ["http://hl7.org/fhir/us/core/StructureDefinition/us-core-ethnicity"]}], "condition": ["ele-1"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), 'value')])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "mustSupport": false, "isModifier": false}, {"id": "Patient.extension:birthsex", "path": "Patient.extension", "sliceName": "birthsex", "short": "Extension", "definition": "A code classifying the person's sex assigned at birth  as specified by the [Office of the National Coordinator for Health IT (ONC)](https://www.healthit.gov/newsroom/about-onc).", "comment": "The codes required are intended to present birth sex (i.e., the sex recorded on the patient’s birth certificate) and not gender identity or reassigned sex.", "min": 0, "max": "1", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension", "profile": ["http://hl7.org/fhir/us/core/StructureDefinition/us-core-birthsex"]}], "condition": ["ele-1"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), 'value')])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "mustSupport": false, "isModifier": false}, {"id": "Patient.extension:genderIdentity", "path": "Patient.extension", "sliceName": "genderIdentity", "short": "Extension", "definition": "An Extension", "min": 0, "max": "1", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension", "profile": ["http://hl7.org/fhir/us/core/StructureDefinition/us-core-genderIdentity"]}], "condition": ["ele-1"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), 'value')])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "mustSupport": false, "isModifier": false}, {"id": "Patient.modifierExtension", "path": "Patient.modifierExtension", "short": "Extensions that cannot be ignored", "definition": "May be used to represent additional information that is not part of the basic definition of the resource and that modifies the understanding of the element that contains it and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer is allowed to define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R4/extensibility.html#modifierExtension).", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "DomainResource.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the resource that contains them", "isSummary": false}, {"id": "Patient.identifier", "path": "Patient.identifier", "short": "An identifier for this patient", "definition": "An identifier for this patient.", "requirements": "Patients are almost always assigned specific numerical identifiers.", "min": 1, "max": "*", "base": {"path": "Patient.identifier", "min": 0, "max": "*"}, "type": [{"code": "Identifier"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Patient.identifier.id", "path": "Patient.identifier.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Patient.identifier.extension", "path": "Patient.identifier.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Patient.identifier.use", "path": "Patient.identifier.use", "short": "usual | official | temp | secondary | old (If known)", "definition": "The purpose of this identifier.", "comment": "Applications can assume that an identifier is permanent unless it explicitly says that it is temporary.", "requirements": "Allows the appropriate identifier for a particular context of use to be selected from among a set of identifiers.", "min": 0, "max": "1", "base": {"path": "Identifier.use", "min": 0, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": true, "isModifierReason": "This is labeled as \"Is Modifier\" because applications should not mistake a temporary id for a permanent one.", "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "IdentifierUse"}], "strength": "required", "description": "Identifies the purpose for this identifier, if known .", "valueSet": "http://hl7.org/fhir/ValueSet/identifier-use|4.0.1"}}, {"id": "Patient.identifier.type", "path": "Patient.identifier.type", "short": "Description of identifier", "definition": "A coded type for the identifier that can be used to determine which identifier to use for a specific purpose.", "comment": "This element deals only with general categories of identifiers.  It SHOULD not be used for codes that correspond 1..1 with the Identifier.system. Some identifiers may fall into multiple categories due to common usage.   Where the system is known, a type is unnecessary because the type is always part of the system definition. However systems often need to handle identifiers where the system is not known. There is not a 1:1 relationship between type and system, since many different systems have the same type.", "requirements": "Allows users to make use of identifiers when the identifier system is not known.", "min": 0, "max": "1", "base": {"path": "Identifier.type", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "IdentifierType"}, {"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-isCommonBinding", "valueBoolean": true}], "strength": "extensible", "description": "A coded type for an identifier that can be used to determine which identifier to use for a specific purpose.", "valueSet": "http://hl7.org/fhir/ValueSet/identifier-type"}}, {"id": "Patient.identifier.system", "path": "Patient.identifier.system", "short": "The namespace for the identifier value", "definition": "Establishes the namespace for the value - that is, a URL that describes a set values that are unique.", "comment": "Identifier.system is always case sensitive.", "requirements": "There are many sets  of identifiers.  To perform matching of two identifiers, we need to know what set we're dealing with. The system identifies a particular set of unique identifiers.", "min": 1, "max": "1", "base": {"path": "Identifier.system", "min": 0, "max": "1"}, "type": [{"code": "uri"}], "example": [{"label": "General", "valueUri": "http://www.acme.com/identifiers/patient"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Patient.identifier.value", "path": "Patient.identifier.value", "short": "The value that is unique within the system.", "definition": "The portion of the identifier typically relevant to the user and which is unique within the context of the system.", "comment": "If the value is a full URI, then the system SHALL be urn:ietf:rfc:3986.  The value's primary purpose is computational mapping.  As a result, it may be normalized for comparison purposes (e.g. removing non-significant whitespace, dashes, etc.)  A value formatted for human display can be conveyed using the [Rendered Value extension](http://hl7.org/fhir/R4/extension-rendered-value.html). Identifier.value is to be treated as case sensitive unless knowledge of the Identifier.system allows the processer to be confident that non-case-sensitive processing is safe.", "min": 1, "max": "1", "base": {"path": "Identifier.value", "min": 0, "max": "1"}, "type": [{"code": "string"}], "example": [{"label": "General", "valueString": "123456"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Patient.identifier.period", "path": "Patient.identifier.period", "short": "Time period when id is/was valid for use", "definition": "Time period during which identifier is/was valid for use.", "min": 0, "max": "1", "base": {"path": "Identifier.period", "min": 0, "max": "1"}, "type": [{"code": "Period"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Patient.identifier.assigner", "path": "Patient.identifier.assigner", "short": "Organization that issued id (may be just text)", "definition": "Organization that issued/manages the identifier.", "comment": "The Identifier.assigner may omit the .reference element and only contain a .display element reflecting the name or other textual information about the assigning organization.", "min": 0, "max": "1", "base": {"path": "Identifier.assigner", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Organization"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Patient.active", "path": "Patient.active", "short": "Whether this patient's record is in active use", "definition": "Whether this patient record is in active use. \nMany systems use this property to mark as non-current patients, such as those that have not been seen for a period of time based on an organization's business rules.\n\nIt is often used to filter patient lists to exclude inactive patients\n\nDeceased patients may also be marked as inactive for the same reasons, but may be active for some time after death.", "comment": "If a record is inactive, and linked to an active record, then future patient/record updates should occur on the other patient.", "requirements": "Need to be able to mark a patient record as not to be used because it was created in error.", "min": 0, "max": "1", "base": {"path": "Patient.active", "min": 0, "max": "1"}, "type": [{"code": "boolean"}], "meaningWhenMissing": "This resource is generally assumed to be active if no value is provided for the active element", "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": true, "isModifierReason": "This element is labelled as a modifier because it is a status element that can indicate that a record should not be treated as valid", "isSummary": true}, {"id": "Patient.name", "path": "Patient.name", "short": "A name associated with the patient", "definition": "A name associated with the individual.", "comment": "A patient may have multiple names with different uses or applicable periods. For animals, the name is a \"HumanName\" in the sense that is assigned and used by humans and has the same patterns.", "requirements": "Need to be able to track the patient by multiple names. Examples are your official name and a partner name.", "min": 1, "max": "*", "base": {"path": "Patient.name", "min": 0, "max": "*"}, "type": [{"code": "HumanName"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Patient.name.id", "path": "Patient.name.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Patient.name.extension", "path": "Patient.name.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Patient.name.use", "path": "Patient.name.use", "short": "usual | official | temp | nickname | anonymous | old | maiden", "definition": "Identifies the purpose for this name.", "comment": "Applications can assume that a name is current unless it explicitly says that it is temporary or old.", "requirements": "Allows the appropriate name for a particular context of use to be selected from among a set of names.", "min": 0, "max": "1", "base": {"path": "HumanName.use", "min": 0, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": true, "isModifierReason": "This is labeled as \"Is Modifier\" because applications should not mistake a temporary or old name etc.for a current/permanent one", "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "NameUse"}], "strength": "required", "description": "The use of a human name.", "valueSet": "http://hl7.org/fhir/ValueSet/name-use|4.0.1"}}, {"id": "Patient.name.text", "path": "Patient.name.text", "short": "Text representation of the full name", "definition": "Specifies the entire name as it should be displayed e.g. on an application UI. This may be provided instead of or as well as the specific parts.", "comment": "Can provide both a text representation and parts. Applications updating a name SHALL ensure that when both text and parts are present,  no content is included in the text that isn't found in a part.", "requirements": "A renderable, unencoded form.", "min": 0, "max": "1", "base": {"path": "HumanName.text", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Patient.name.family", "path": "Patient.name.family", "short": "Family name (often called 'Surname')", "definition": "The part of a name that links to the genealogy. In some cultures (e.g. Eritrea) the family name of a son is the first name of his father.", "comment": "Family Name may be decomposed into specific parts using extensions (de, nl, es related cultures).", "alias": ["surname"], "min": 0, "max": "1", "base": {"path": "HumanName.family", "min": 0, "max": "1"}, "type": [{"code": "string"}], "condition": ["us-core-6"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Patient.name.given", "path": "Patient.name.given", "short": "Given names (not always 'first'). Includes middle names", "definition": "Given name.", "comment": "If only initials are recorded, they may be used in place of the full name parts. Initials may be separated into multiple given names but often aren't due to paractical limitations.  This element is not called \"first name\" since given names do not always come first.", "alias": ["first name", "middle name"], "min": 0, "max": "*", "base": {"path": "HumanName.given", "min": 0, "max": "*"}, "type": [{"code": "string"}], "orderMeaning": "Given Names appear in the correct order for presenting the name", "condition": ["us-core-6"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Patient.name.prefix", "path": "Patient.name.prefix", "short": "Parts that come before the name", "definition": "Part of the name that is acquired as a title due to academic, legal, employment or nobility status, etc. and that appears at the start of the name.", "min": 0, "max": "*", "base": {"path": "HumanName.prefix", "min": 0, "max": "*"}, "type": [{"code": "string"}], "orderMeaning": "Prefixes appear in the correct order for presenting the name", "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Patient.name.suffix", "path": "Patient.name.suffix", "short": "Parts that come after the name", "definition": "Part of the name that is acquired as a title due to academic, legal, employment or nobility status, etc. and that appears at the end of the name.", "min": 0, "max": "*", "base": {"path": "HumanName.suffix", "min": 0, "max": "*"}, "type": [{"code": "string"}], "orderMeaning": "Suffixes appear in the correct order for presenting the name", "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": false, "isModifier": false, "isSummary": true}, {"id": "Patient.name.period", "path": "Patient.name.period", "short": "Time period when name was/is in use", "definition": "Indicates the period of time when this name was valid for the named person.", "requirements": "Allows names to be placed in historical context.", "min": 0, "max": "1", "base": {"path": "HumanName.period", "min": 0, "max": "1"}, "type": [{"code": "Period"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": false, "isModifier": false, "isSummary": true}, {"id": "Patient.telecom", "path": "Patient.telecom", "short": "A contact detail for the individual", "definition": "A contact detail (e.g. a telephone number or an email address) by which the individual may be contacted.", "comment": "A Patient may have multiple ways to be contacted with different uses or applicable periods.  May need to have options for contacting the person urgently and also to help with identification. The address might not go directly to the individual, but may reach another party that is able to proxy for the patient (i.e. home phone, or pet owner's phone).", "requirements": "People have (primary) ways to contact them in some way such as phone, email.", "min": 0, "max": "*", "base": {"path": "Patient.telecom", "min": 0, "max": "*"}, "type": [{"code": "ContactPoint"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": false, "isModifier": false, "isSummary": true}, {"id": "Patient.telecom.id", "path": "Patient.telecom.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Patient.telecom.extension", "path": "Patient.telecom.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Patient.telecom.system", "path": "Patient.telecom.system", "short": "phone | fax | email | pager | url | sms | other", "definition": "Telecommunications form for contact point - what communications system is required to make use of the contact.", "min": 1, "max": "1", "base": {"path": "ContactPoint.system", "min": 0, "max": "1"}, "type": [{"code": "code"}], "condition": ["cpt-2"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true, "binding": {"strength": "required", "description": "Telecommunications form for contact point.", "valueSet": "http://hl7.org/fhir/ValueSet/contact-point-system"}}, {"id": "Patient.telecom.value", "path": "Patient.telecom.value", "short": "The actual contact point details", "definition": "The actual contact point details, in a form that is meaningful to the designated communication system (i.e. phone number or email address).", "comment": "Additional text data such as phone extension numbers, or notes about use of the contact are sometimes included in the value.", "requirements": "Need to support legacy numbers that are not in a tightly controlled format.", "min": 1, "max": "1", "base": {"path": "ContactPoint.value", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Patient.telecom.use", "path": "Patient.telecom.use", "short": "home | work | temp | old | mobile - purpose of this contact point", "definition": "Identifies the purpose for the contact point.", "comment": "Applications can assume that a contact is current unless it explicitly says that it is temporary or old.", "requirements": "Need to track the way a person uses this contact, so a user can choose which is appropriate for their purpose.", "min": 0, "max": "1", "base": {"path": "ContactPoint.use", "min": 0, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": true, "isModifierReason": "This is labeled as \"Is Modifier\" because applications should not mistake a temporary or old contact etc.for a current/permanent one", "isSummary": true, "binding": {"strength": "required", "valueSet": "http://hl7.org/fhir/ValueSet/contact-point-use"}}, {"id": "Patient.telecom.rank", "path": "Patient.telecom.rank", "short": "Specify preferred order of use (1 = highest)", "definition": "Specifies a preferred order in which to use a set of contacts. ContactPoints with lower rank values are more preferred than those with higher rank values.", "comment": "Note that rank does not necessarily follow the order in which the contacts are represented in the instance.", "min": 0, "max": "1", "base": {"path": "ContactPoint.rank", "min": 0, "max": "1"}, "type": [{"code": "positiveInt"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Patient.telecom.period", "path": "Patient.telecom.period", "short": "Time period when the contact point was/is in use", "definition": "Time period when the contact point was/is in use.", "min": 0, "max": "1", "base": {"path": "ContactPoint.period", "min": 0, "max": "1"}, "type": [{"code": "Period"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Patient.gender", "path": "Patient.gender", "short": "male | female | other | unknown", "definition": "Administrative Gender - the gender that the patient is considered to have for administration and record keeping purposes.", "comment": "The gender might not match the biological sex as determined by genetics or the individual's preferred identification. Note that for both humans and particularly animals, there are other legitimate possibilities than male and female, though the vast majority of systems and contexts only support male and female.  Systems providing decision support or enforcing business rules should ideally do this on the basis of Observations dealing with the specific sex or gender aspect of interest (anatomical, chromosomal, social, etc.)  However, because these observations are infrequently recorded, defaulting to the administrative gender is common practice.  Where such defaulting occurs, rule enforcement should allow for the variation between administrative and biological, chromosomal and other gender aspects.  For example, an alert about a hysterectomy on a male should be handled as a warning or overridable error, not a \"hard\" error.  See the Patient Gender and Sex section for additional information about communicating patient gender and sex.", "requirements": "Needed for identification of the individual, in combination with (at least) name and birth date.", "min": 1, "max": "1", "base": {"path": "Patient.gender", "min": 0, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true, "binding": {"strength": "required", "valueSet": "http://hl7.org/fhir/ValueSet/administrative-gender"}}, {"id": "Patient.birthDate", "path": "Patient.birthDate", "short": "The date of birth for the individual", "definition": "The date of birth for the individual.", "comment": "At least an estimated year should be provided as a guess if the real DOB is unknown  There is a standard extension \"patient-birthTime\" available that should be used where Time is required (such as in maternity/infant care systems).", "requirements": "Age of the individual drives many clinical processes.", "min": 0, "max": "1", "base": {"path": "Patient.birthDate", "min": 0, "max": "1"}, "type": [{"code": "date"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Patient.deceased[x]", "path": "Patient.deceased[x]", "short": "Indicates if the individual is deceased or not", "definition": "Indicates if the individual is deceased or not.", "comment": "If there's no value in the instance, it means there is no statement on whether or not the individual is deceased. Most systems will interpret the absence of a value as a sign of the person being alive.", "requirements": "The fact that a patient is deceased influences the clinical process. Also, in human communication and relation management it is necessary to know whether the person is alive.", "min": 0, "max": "1", "base": {"path": "Patient.deceased[x]", "min": 0, "max": "1"}, "type": [{"code": "boolean"}, {"code": "dateTime"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": true, "isModifierReason": "This element is labeled as a modifier because once a patient is marked as deceased, the actions that are appropriate to perform on the patient may be significantly different.", "isSummary": true}, {"id": "Patient.address", "path": "Patient.address", "short": "An address for the individual", "definition": "An address for the individual.", "comment": "Patient may have multiple addresses with different uses or applicable periods.", "requirements": "May need to keep track of patient addresses for contacting, billing or reporting requirements and also to help with identification.", "min": 0, "max": "*", "base": {"path": "Patient.address", "min": 0, "max": "*"}, "type": [{"code": "Address"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Patient.address.id", "path": "Patient.address.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Patient.address.extension", "path": "Patient.address.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Patient.address.use", "path": "Patient.address.use", "short": "home | work | temp | old | billing - purpose of this address", "definition": "The purpose of this address.", "comment": "Applications can assume that an address is current unless it explicitly says that it is temporary or old.", "requirements": "Allows an appropriate address to be chosen from a list of many.", "min": 0, "max": "1", "base": {"path": "Address.use", "min": 0, "max": "1"}, "type": [{"code": "code"}], "example": [{"label": "General", "valueCode": "home"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": true, "isModifierReason": "This is labeled as \"Is Modifier\" because applications should not mistake a temporary or old address etc.for a current/permanent one", "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "AddressUse"}], "strength": "required", "description": "The use of an address.", "valueSet": "http://hl7.org/fhir/ValueSet/address-use|4.0.1"}}, {"id": "Patient.address.type", "path": "Patient.address.type", "short": "postal | physical | both", "definition": "Distinguishes between physical addresses (those you can visit) and mailing addresses (e.g. PO Boxes and care-of addresses). Most addresses are both.", "comment": "The definition of Address states that \"address is intended to describe postal addresses, not physical locations\". However, many applications track whether an address has a dual purpose of being a location that can be visited as well as being a valid delivery destination, and Postal addresses are often used as proxies for physical locations (also see the [Location](http://hl7.org/fhir/R4/location.html#) resource).", "min": 0, "max": "1", "base": {"path": "Address.type", "min": 0, "max": "1"}, "type": [{"code": "code"}], "example": [{"label": "General", "valueCode": "both"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "AddressType"}], "strength": "required", "description": "The type of an address (physical / postal).", "valueSet": "http://hl7.org/fhir/ValueSet/address-type|4.0.1"}}, {"id": "Patient.address.text", "path": "Patient.address.text", "short": "Text representation of the address", "definition": "Specifies the entire address as it should be displayed e.g. on a postal label. This may be provided instead of or as well as the specific parts.", "comment": "Can provide both a text representation and parts. Applications updating an address SHALL ensure that  when both text and parts are present,  no content is included in the text that isn't found in a part.", "requirements": "A renderable, unencoded form.", "min": 0, "max": "1", "base": {"path": "Address.text", "min": 0, "max": "1"}, "type": [{"code": "string"}], "example": [{"label": "General", "valueString": "137 Nowhere Street, Erewhon 9132"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Patient.address.line", "path": "Patient.address.line", "short": "Street name, number, direction & P.O. Box etc.", "definition": "This component contains the house number, apartment number, street name, street direction,  P.O. Box number, delivery hints, and similar address information.", "min": 0, "max": "*", "base": {"path": "Address.line", "min": 0, "max": "*"}, "type": [{"code": "string"}], "orderMeaning": "The order in which lines should appear in an address label", "example": [{"label": "General", "valueString": "137 Nowhere Street"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Patient.address.city", "path": "Patient.address.city", "short": "Name of city, town etc.", "definition": "The name of the city, town, suburb, village or other community or delivery center.", "alias": ["Municpality"], "min": 0, "max": "1", "base": {"path": "Address.city", "min": 0, "max": "1"}, "type": [{"code": "string"}], "example": [{"label": "General", "valueString": "<PERSON><PERSON><PERSON>"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Patient.address.district", "path": "Patient.address.district", "short": "District name (aka county)", "definition": "The name of the administrative area (county).", "comment": "District is sometimes known as county, but in some regions 'county' is used in place of city (municipality), so county name should be conveyed in city instead.", "alias": ["County"], "min": 0, "max": "1", "base": {"path": "Address.district", "min": 0, "max": "1"}, "type": [{"code": "string"}], "example": [{"label": "General", "valueString": "Madison"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Patient.address.state", "path": "Patient.address.state", "short": "Sub-unit of country (abbreviations ok)", "definition": "Sub-unit of a country with limited sovereignty in a federally organized country. A code may be used if codes are in common use (e.g. US 2 letter state codes).", "alias": ["Province", "Territory"], "min": 0, "max": "1", "base": {"path": "Address.state", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true, "binding": {"strength": "extensible", "description": "Two Letter USPS alphabetic codes.", "valueSet": "http://hl7.org/fhir/us/core/ValueSet/us-core-usps-state"}}, {"id": "Patient.address.postalCode", "path": "Patient.address.postalCode", "short": "US Zip Codes", "definition": "A postal code designating a region defined by the postal service.", "alias": ["Zip", "Zip Code"], "min": 0, "max": "1", "base": {"path": "Address.postalCode", "min": 0, "max": "1"}, "type": [{"code": "string"}], "example": [{"label": "General", "valueString": "9132"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Patient.address.country", "path": "Patient.address.country", "short": "Country (e.g. can be ISO 3166 2 or 3 letter code)", "definition": "Country - a nation as commonly understood or generally accepted.", "comment": "ISO 3166 3 letter codes can be used in place of a human readable country name.", "min": 0, "max": "1", "base": {"path": "Address.country", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Patient.address.period", "path": "Patient.address.period", "short": "Time period when address was/is in use", "definition": "Time period when address was/is in use.", "requirements": "Allows addresses to be placed in historical context.", "min": 0, "max": "1", "base": {"path": "Address.period", "min": 0, "max": "1"}, "type": [{"code": "Period"}], "example": [{"label": "General", "valuePeriod": {"start": "2010-03-23", "end": "2010-07-01"}}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Patient.maritalStatus", "path": "Patient.maritalStatus", "short": "Marital (civil) status of a patient", "definition": "This field contains a patient's most recent marital (civil) status.", "requirements": "Most, if not all systems capture it.", "min": 0, "max": "1", "base": {"path": "Patient.maritalStatus", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "MaritalStatus"}, {"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-isCommonBinding", "valueBoolean": true}], "strength": "extensible", "description": "The domestic partnership status of a person.", "valueSet": "http://hl7.org/fhir/ValueSet/marital-status"}}, {"id": "Patient.multipleBirth[x]", "path": "Patient.multipleBirth[x]", "short": "Whether patient is part of a multiple birth", "definition": "Indicates whether the patient is part of a multiple (boolean) or indicates the actual birth order (integer).", "comment": "Where the valueInteger is provided, the number is the birth number in the sequence. E.g. The middle birth in triplets would be valueInteger=2 and the third born would have valueInteger=3 If a boolean value was provided for this triplets example, then all 3 patient records would have valueBoolean=true (the ordering is not indicated).", "requirements": "For disambiguation of multiple-birth children, especially relevant where the care provider doesn't meet the patient, such as labs.", "min": 0, "max": "1", "base": {"path": "Patient.multipleBirth[x]", "min": 0, "max": "1"}, "type": [{"code": "boolean"}, {"code": "integer"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Patient.photo", "path": "Patient.photo", "short": "Image of the patient", "definition": "Image of the patient.", "comment": "Guidelines:\n* Use id photos, not clinical photos.\n* Limit dimensions to thumbnail.\n* Keep byte count low to ease resource updates.", "requirements": "Many EHR systems have the capability to capture an image of the patient. Fits with newer social media usage too.", "min": 0, "max": "*", "base": {"path": "Patient.photo", "min": 0, "max": "*"}, "type": [{"code": "Attachment"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Patient.contact", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-explicit-type-name", "valueString": "Contact"}], "path": "Patient.contact", "short": "A contact party (e.g. guardian, partner, friend) for the patient", "definition": "A contact party (e.g. guardian, partner, friend) for the patient.", "comment": "Contact covers all kinds of contact parties: family members, business contacts, guardians, caregivers. Not applicable to register pedigree and family ties beyond use of having contact.", "requirements": "Need to track people you can contact about the patient.", "min": 0, "max": "*", "base": {"path": "Patient.contact", "min": 0, "max": "*"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "pat-1", "severity": "error", "human": "SHALL at least contain a contact's details or a reference to an organization", "expression": "name.exists() or telecom.exists() or address.exists() or organization.exists()", "xpath": "exists(f:name) or exists(f:telecom) or exists(f:address) or exists(f:organization)", "source": "http://hl7.org/fhir/StructureDefinition/Patient"}], "isModifier": false, "isSummary": false}, {"id": "Patient.contact.id", "path": "Patient.contact.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Patient.contact.extension", "path": "Patient.contact.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Patient.contact.modifierExtension", "path": "Patient.contact.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R4/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true}, {"id": "Patient.contact.relationship", "path": "Patient.contact.relationship", "short": "The kind of relationship", "definition": "The nature of the relationship between the patient and the contact person.", "requirements": "Used to determine which contact person is the most relevant to approach, depending on circumstances.", "min": 0, "max": "*", "base": {"path": "Patient.contact.relationship", "min": 0, "max": "*"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "ContactRelationship"}], "strength": "extensible", "description": "The nature of the relationship between a patient and a contact person for that patient.", "valueSet": "http://hl7.org/fhir/ValueSet/patient-contactrelationship"}}, {"id": "Patient.contact.name", "path": "Patient.contact.name", "short": "A name associated with the contact person", "definition": "A name associated with the contact person.", "requirements": "Contact persons need to be identified by name, but it is uncommon to need details about multiple other names for that contact person.", "min": 0, "max": "1", "base": {"path": "Patient.contact.name", "min": 0, "max": "1"}, "type": [{"code": "HumanName"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Patient.contact.telecom", "path": "Patient.contact.telecom", "short": "A contact detail for the person", "definition": "A contact detail for the person, e.g. a telephone number or an email address.", "comment": "Contact may have multiple ways to be contacted with different uses or applicable periods.  May need to have options for contacting the person urgently, and also to help with identification.", "requirements": "People have (primary) ways to contact them in some way such as phone, email.", "min": 0, "max": "*", "base": {"path": "Patient.contact.telecom", "min": 0, "max": "*"}, "type": [{"code": "ContactPoint"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Patient.contact.address", "path": "Patient.contact.address", "short": "Address for the contact person", "definition": "Address for the contact person.", "requirements": "Need to keep track where the contact person can be contacted per postal mail or visited.", "min": 0, "max": "1", "base": {"path": "Patient.contact.address", "min": 0, "max": "1"}, "type": [{"code": "Address"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Patient.contact.gender", "path": "Patient.contact.gender", "short": "male | female | other | unknown", "definition": "Administrative Gender - the gender that the contact person is considered to have for administration and record keeping purposes.", "requirements": "Needed to address the person correctly.", "min": 0, "max": "1", "base": {"path": "Patient.contact.gender", "min": 0, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "AdministrativeGender"}, {"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-isCommonBinding", "valueBoolean": true}], "strength": "required", "description": "The gender of a person used for administrative purposes.", "valueSet": "http://hl7.org/fhir/ValueSet/administrative-gender|4.0.1"}}, {"id": "Patient.contact.organization", "path": "Patient.contact.organization", "short": "Organization that is associated with the contact", "definition": "Organization on behalf of which the contact is acting or for which the contact is working.", "requirements": "For guardians or business related contacts, the organization is relevant.", "min": 0, "max": "1", "base": {"path": "Patient.contact.organization", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Organization"]}], "condition": ["pat-1"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Patient.contact.period", "path": "Patient.contact.period", "short": "The period during which this contact person or organization is valid to be contacted relating to this patient", "definition": "The period during which this contact person or organization is valid to be contacted relating to this patient.", "min": 0, "max": "1", "base": {"path": "Patient.contact.period", "min": 0, "max": "1"}, "type": [{"code": "Period"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Patient.communication", "path": "Patient.communication", "short": "A language which may be used to communicate with the patient about his or her health", "definition": "A language which may be used to communicate with the patient about his or her health.", "comment": "If no language is specified, this *implies* that the default local language is spoken.  If you need to convey proficiency for multiple modes, then you need multiple Patient.Communication associations.   For animals, language is not a relevant field, and should be absent from the instance. If the Patient does not speak the default local language, then the Interpreter Required Standard can be used to explicitly declare that an interpreter is required.", "requirements": "If a patient does not speak the local language, interpreters may be required, so languages spoken and proficiency are important things to keep track of both for patient and other persons of interest.", "min": 0, "max": "*", "base": {"path": "Patient.communication", "min": 0, "max": "*"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": false, "isModifier": false, "isSummary": false}, {"id": "Patient.communication.id", "path": "Patient.communication.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Patient.communication.extension", "path": "Patient.communication.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Patient.communication.modifierExtension", "path": "Patient.communication.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R4/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true}, {"id": "Patient.communication.language", "path": "Patient.communication.language", "short": "The language which can be used to communicate with the patient about his or her health", "definition": "The ISO-639-1 alpha 2 code in lower case for the language, optionally followed by a hyphen and the ISO-3166-1 alpha 2 code for the region in upper case; e.g. \"en\" for English, or \"en-US\" for American English versus \"en-EN\" for England English.", "comment": "The structure aa-BB with this exact casing is one the most widely used notations for locale. However not all systems actually code this but instead have it as free text. Hence CodeableConcept instead of code as the data type.", "requirements": "Most systems in multilingual countries will want to convey language. Not all systems actually need the regional dialect.", "min": 1, "max": "1", "base": {"path": "Patient.communication.language", "min": 1, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false, "binding": {"strength": "extensible", "valueSet": "http://hl7.org/fhir/us/core/ValueSet/simple-language"}}, {"id": "Patient.communication.preferred", "path": "Patient.communication.preferred", "short": "Language preference indicator", "definition": "Indicates whether or not the patient prefers this language (over other languages he masters up a certain level).", "comment": "This language is specifically identified for communicating healthcare information.", "requirements": "People that master multiple languages up to certain level may prefer one or more, i.e. feel more confident in communicating in a particular language making other languages sort of a fall back method.", "min": 0, "max": "1", "base": {"path": "Patient.communication.preferred", "min": 0, "max": "1"}, "type": [{"code": "boolean"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Patient.generalPractitioner", "path": "Patient.generalPractitioner", "short": "Patient's nominated primary care provider", "definition": "Patient's nominated care provider.", "comment": "This may be the primary care provider (in a GP context), or it may be a patient nominated care manager in a community/disability setting, or even organization that will provide people to perform the care provider roles.  It is not to be used to record Care Teams, these should be in a CareTeam resource that may be linked to the CarePlan or EpisodeOfCare resources.\nMultiple GPs may be recorded against the patient for various reasons, such as a student that has his home GP listed along with the GP at university during the school semesters, or a \"fly-in/fly-out\" worker that has the onsite GP also included with his home GP to remain aware of medical issues.\n\nJurisdictions may decide that they can profile this down to 1 if desired, or 1 per type.", "alias": ["careProvider"], "min": 0, "max": "*", "base": {"path": "Patient.generalPractitioner", "min": 0, "max": "*"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Organization", "http://hl7.org/fhir/StructureDefinition/Practitioner", "http://hl7.org/fhir/StructureDefinition/PractitionerRole"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Patient.managingOrganization", "path": "Patient.managingOrganization", "short": "Organization that is the custodian of the patient record", "definition": "Organization that is the custodian of the patient record.", "comment": "There is only one managing organization for a specific patient record. Other organizations will have their own Patient record, and may use the Link property to join the records together (or a Person resource which can include confidence ratings for the association).", "requirements": "Need to know who recognizes this patient record, manages and updates it.", "min": 0, "max": "1", "base": {"path": "Patient.managingOrganization", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Organization"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Patient.link", "path": "Patient.link", "short": "Link to another patient resource that concerns the same actual person", "definition": "Link to another patient resource that concerns the same actual patient.", "comment": "There is no assumption that linked patient records have mutual links.", "requirements": "There are multiple use cases:   \n\n* Duplicate patient records due to the clerical errors associated with the difficulties of identifying humans consistently, and \n* Distribution of patient information across multiple servers.", "min": 0, "max": "*", "base": {"path": "Patient.link", "min": 0, "max": "*"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": true, "isModifierReason": "This element is labeled as a modifier because it might not be the main Patient resource, and the referenced patient should be used instead of this Patient record. This is when the link.type value is 'replaced-by'", "isSummary": true}, {"id": "Patient.link.id", "path": "Patient.link.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Patient.link.extension", "path": "Patient.link.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Patient.link.modifierExtension", "path": "Patient.link.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R4/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true}, {"id": "Patient.link.other", "path": "Patient.link.other", "short": "The other patient or related person resource that the link refers to", "definition": "The other patient resource that the link refers to.", "comment": "Referencing a <PERSON><PERSON><PERSON><PERSON> here removes the need to use a Person record to associate a <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> as the same individual.", "min": 1, "max": "1", "base": {"path": "Patient.link.other", "min": 1, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-hierarchy", "valueBoolean": false}], "code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Patient", "http://hl7.org/fhir/StructureDefinition/RelatedPerson"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Patient.link.type", "path": "Patient.link.type", "short": "replaced-by | replaces | refer | seealso", "definition": "The type of link between this patient resource and another patient resource.", "min": 1, "max": "1", "base": {"path": "Patient.link.type", "min": 1, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "LinkType"}], "strength": "required", "description": "The type of link between this patient resource and another patient resource.", "valueSet": "http://hl7.org/fhir/ValueSet/link-type|4.0.1"}}]}}, {"resourceType": "StructureDefinition", "id": "us-core-race", "url": "http://hl7.org/fhir/us/core/StructureDefinition/us-core-race", "version": "5.0.1", "name": "USCoreRaceExtension", "title": "US Core Race Extension", "status": "active", "date": "2019-05-21", "publisher": "HL7 International - Cross-Group Projects", "contact": [{"name": "HL7 International - Cross-Group Projects", "telecom": [{"system": "url", "value": "http://www.hl7.org/Special/committees/cgp"}, {"system": "email", "value": "<EMAIL>"}]}], "description": "Concepts classifying the person into a named category of humans sharing common history, traits, geographical origin or nationality.  The race codes used to represent these concepts are based upon the [CDC Race and Ethnicity Code Set Version 1.0](http://www.cdc.gov/phin/resources/vocabulary/index.html) which includes over 900 concepts for representing race and ethnicity of which 921 reference race.  The race concepts are grouped by and pre-mapped to the 5 OMB race categories:\n\n - American Indian or Alaska Native\n - Asian\n - Black or African American\n - Native Hawaiian or Other Pacific Islander\n - White.", "jurisdiction": [{"coding": [{"system": "urn:iso:std:iso:3166", "code": "US"}]}], "purpose": "Complies with 2015 Edition Common Clinical Data Set for patient race.", "copyright": "Used by permission of HL7 International, all rights reserved Creative Commons License", "fhirVersion": "4.0.1", "kind": "complex-type", "abstract": false, "context": [{"type": "element", "expression": "Patient"}, {"type": "element", "expression": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "element", "expression": "Person"}, {"type": "element", "expression": "Practitioner"}, {"type": "element", "expression": "FamilyMemberHistory"}], "type": "Extension", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/Extension", "derivation": "constraint", "snapshot": {"element": [{"id": "Extension", "path": "Extension", "short": "US Core Race Extension", "definition": "Concepts classifying the person into a named category of humans sharing common history, traits, geographical origin or nationality.  The race codes used to represent these concepts are based upon the [CDC Race and Ethnicity Code Set Version 1.0](http://www.cdc.gov/phin/resources/vocabulary/index.html) which includes over 900 concepts for representing race and ethnicity of which 921 reference race.  The race concepts are grouped by and pre-mapped to the 5 OMB race categories:\n\n   - American Indian or Alaska Native\n   - Asian\n   - Black or African American\n   - Native Hawaiian or Other Pacific Islander\n   - White.", "min": 0, "max": "1", "base": {"path": "Extension", "min": 0, "max": "*"}, "condition": ["ele-1"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), 'value')])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false}, {"id": "Extension.id", "path": "Extension.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Extension.extension", "path": "Extension.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Extension.extension:ombCategory", "path": "Extension.extension", "sliceName": "ombCategory", "short": "American Indian or Alaska Native|Asian|Black or African American|Native Hawaiian or Other Pacific Islander|White", "definition": "The 5 race category codes according to the [OMB Standards for Maintaining, Collecting, and Presenting Federal Data on Race and Ethnicity, Statistical Policy Directive No. 15, as revised, October 30, 1997](https://www.govinfo.gov/content/pkg/FR-1997-10-30/pdf/97-28653.pdf).", "min": 0, "max": "5", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "mustSupport": true, "isModifier": false, "isSummary": false}, {"id": "Extension.extension:ombCategory.id", "path": "Extension.extension.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Extension.extension:ombCategory.extension", "path": "Extension.extension.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Extension.extension:ombCategory.url", "path": "Extension.extension.url", "representation": ["xmlAttr"], "short": "identifies the meaning of the extension", "definition": "Source of the definition for the extension code - a logical name or a URL.", "comment": "The definition may point directly to a computable or human-readable definition of the extensibility codes, or it may be a logical URI as declared in some other specification. The definition SHALL be a URI for the Structure Definition defining the extension.", "min": 1, "max": "1", "base": {"path": "Extension.url", "min": 1, "max": "1"}, "type": [{"code": "uri"}], "fixedUri": "ombCategory", "isModifier": false, "isSummary": false}, {"id": "Extension.extension:ombCategory.value[x]", "path": "Extension.extension.value[x]", "short": "Value of extension", "definition": "Value of extension - must be one of a constrained set of the data types (see [Extensibility](http://hl7.org/fhir/R4/extensibility.html) for a list).", "min": 1, "max": "1", "base": {"path": "Extension.value[x]", "min": 0, "max": "1"}, "type": [{"code": "Coding"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"strength": "required", "description": "The 5 race category codes according to the [OMB Standards for Maintaining, Collecting, and Presenting Federal Data on Race and Ethnicity, Statistical Policy Directive No. 15, as revised, October 30, 1997](https://www.govinfo.gov/content/pkg/FR-1997-10-30/pdf/97-28653.pdf).", "valueSet": "http://hl7.org/fhir/us/core/ValueSet/omb-race-category"}}, {"id": "Extension.extension:detailed", "path": "Extension.extension", "sliceName": "detailed", "short": "Extended race codes", "definition": "The 900+ CDC race codes that are grouped under one of the 5 OMB race category codes:.", "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Extension.extension:detailed.id", "path": "Extension.extension.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Extension.extension:detailed.extension", "path": "Extension.extension.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Extension.extension:detailed.url", "path": "Extension.extension.url", "representation": ["xmlAttr"], "short": "identifies the meaning of the extension", "definition": "Source of the definition for the extension code - a logical name or a URL.", "comment": "The definition may point directly to a computable or human-readable definition of the extensibility codes, or it may be a logical URI as declared in some other specification. The definition SHALL be a URI for the Structure Definition defining the extension.", "min": 1, "max": "1", "base": {"path": "Extension.url", "min": 1, "max": "1"}, "type": [{"code": "uri"}], "fixedUri": "detailed", "isModifier": false, "isSummary": false}, {"id": "Extension.extension:detailed.value[x]", "path": "Extension.extension.value[x]", "short": "Value of extension", "definition": "Value of extension - must be one of a constrained set of the data types (see [Extensibility](http://hl7.org/fhir/R4/extensibility.html) for a list).", "min": 1, "max": "1", "base": {"path": "Extension.value[x]", "min": 0, "max": "1"}, "type": [{"code": "Coding"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"strength": "required", "valueSet": "http://hl7.org/fhir/us/core/ValueSet/detailed-race"}}, {"id": "Extension.extension:text", "path": "Extension.extension", "sliceName": "text", "short": "Race Text", "definition": "Plain text representation of the race concept(s).", "min": 1, "max": "1", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "mustSupport": true, "isModifier": false, "isSummary": false}, {"id": "Extension.extension:text.id", "path": "Extension.extension.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Extension.extension:text.extension", "path": "Extension.extension.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Extension.extension:text.url", "path": "Extension.extension.url", "representation": ["xmlAttr"], "short": "identifies the meaning of the extension", "definition": "Source of the definition for the extension code - a logical name or a URL.", "comment": "The definition may point directly to a computable or human-readable definition of the extensibility codes, or it may be a logical URI as declared in some other specification. The definition SHALL be a URI for the Structure Definition defining the extension.", "min": 1, "max": "1", "base": {"path": "Extension.url", "min": 1, "max": "1"}, "type": [{"code": "uri"}], "fixedUri": "text", "isModifier": false, "isSummary": false}, {"id": "Extension.extension:text.value[x]", "path": "Extension.extension.value[x]", "short": "Value of extension", "definition": "Value of extension - must be one of a constrained set of the data types (see [Extensibility](http://hl7.org/fhir/R4/extensibility.html) for a list).", "min": 1, "max": "1", "base": {"path": "Extension.value[x]", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Extension.url", "path": "Extension.url", "representation": ["xmlAttr"], "short": "identifies the meaning of the extension", "definition": "Source of the definition for the extension code - a logical name or a URL.", "comment": "The definition may point directly to a computable or human-readable definition of the extensibility codes, or it may be a logical URI as declared in some other specification. The definition SHALL be a URI for the Structure Definition defining the extension.", "min": 1, "max": "1", "base": {"path": "Extension.url", "min": 1, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "uri"}], "code": "http://hl7.org/fhirpath/System.String"}], "fixedUri": "http://hl7.org/fhir/us/core/StructureDefinition/us-core-race", "isModifier": false, "isSummary": false}, {"id": "Extension.value[x]", "path": "Extension.value[x]", "short": "Value of extension", "definition": "Value of extension - must be one of a constrained set of the data types (see [Extensibility](http://hl7.org/fhir/R4/extensibility.html) for a list).", "min": 0, "max": "0", "base": {"path": "Extension.value[x]", "min": 0, "max": "1"}, "type": [{"code": "base64Binary"}, {"code": "boolean"}, {"code": "canonical"}, {"code": "code"}, {"code": "date"}, {"code": "dateTime"}, {"code": "decimal"}, {"code": "id"}, {"code": "instant"}, {"code": "integer"}, {"code": "markdown"}, {"code": "oid"}, {"code": "positiveInt"}, {"code": "string"}, {"code": "time"}, {"code": "unsignedInt"}, {"code": "uri"}, {"code": "url"}, {"code": "uuid"}, {"code": "Address"}, {"code": "Age"}, {"code": "Annotation"}, {"code": "Attachment"}, {"code": "CodeableConcept"}, {"code": "Coding"}, {"code": "ContactPoint"}, {"code": "Count"}, {"code": "Distance"}, {"code": "Duration"}, {"code": "HumanName"}, {"code": "Identifier"}, {"code": "Money"}, {"code": "Period"}, {"code": "Quantity"}, {"code": "Range"}, {"code": "<PERSON><PERSON>"}, {"code": "Reference"}, {"code": "SampledData"}, {"code": "Signature"}, {"code": "Timing"}, {"code": "ContactDetail"}, {"code": "Contributor"}, {"code": "DataRequirement"}, {"code": "Expression"}, {"code": "ParameterDefinition"}, {"code": "RelatedArtifact"}, {"code": "TriggerDefinition"}, {"code": "UsageContext"}, {"code": "Dosage"}, {"code": "Meta"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}]}}, {"resourceType": "StructureDefinition", "id": "us-core-ethnicity", "url": "http://hl7.org/fhir/us/core/StructureDefinition/us-core-ethnicity", "version": "5.0.1", "name": "USCoreEthnicityExtension", "title": "US Core Ethnicity Extension", "status": "active", "date": "2019-05-21T00:00:00-04:00", "publisher": "HL7 International - Cross-Group Projects", "contact": [{"name": "HL7 International - Cross-Group Projects", "telecom": [{"system": "url", "value": "http://www.hl7.org/Special/committees/cgp"}, {"system": "email", "value": "<EMAIL>"}]}], "description": "Concepts classifying the person into a named category of humans sharing common history, traits, geographical origin or nationality.  The ethnicity codes used to represent these concepts are based upon the [CDC ethnicity and Ethnicity Code Set Version 1.0](http://www.cdc.gov/phin/resources/vocabulary/index.html) which includes over 900 concepts for representing race and ethnicity of which 43 reference ethnicity.  The ethnicity concepts are grouped by and pre-mapped to the 2 OMB ethnicity categories: - Hispanic or Latino - Not Hispanic or Latino.", "jurisdiction": [{"coding": [{"system": "urn:iso:std:iso:3166", "code": "US"}]}], "purpose": "Complies with 2015 Edition Common Clinical Data Set for patient race.", "copyright": "Used by permission of HL7 International, all rights reserved Creative Commons License", "fhirVersion": "4.0.1", "kind": "complex-type", "abstract": false, "context": [{"type": "element", "expression": "Patient"}, {"type": "element", "expression": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "element", "expression": "Person"}, {"type": "element", "expression": "Practitioner"}, {"type": "element", "expression": "FamilyMemberHistory"}], "type": "Extension", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/Extension", "derivation": "constraint", "snapshot": {"element": [{"id": "Extension", "path": "Extension", "short": "US Core ethnicity Extension", "definition": "Concepts classifying the person into a named category of humans sharing common history, traits, geographical origin or nationality.  The ethnicity codes used to represent these concepts are based upon the [CDC ethnicity and Ethnicity Code Set Version 1.0](http://www.cdc.gov/phin/resources/vocabulary/index.html) which includes over 900 concepts for representing race and ethnicity of which 43 reference ethnicity.  The ethnicity concepts are grouped by and pre-mapped to the 2 OMB ethnicity categories: - Hispanic or Latino - Not Hispanic or Latino.", "min": 0, "max": "1", "base": {"path": "Extension", "min": 0, "max": "*"}, "condition": ["ele-1"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), 'value')])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false}, {"id": "Extension.id", "path": "Extension.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Extension.extension", "path": "Extension.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Extension.extension:ombCategory", "path": "Extension.extension", "sliceName": "ombCategory", "short": "Hispanic or Latino|Not Hispanic or Latino", "definition": "The 2 ethnicity category codes according to the [OMB Standards for Maintaining, Collecting, and Presenting Federal Data on Race and Ethnicity, Statistical Policy Directive No. 15, as revised, October 30, 1997](https://www.govinfo.gov/content/pkg/FR-1997-10-30/pdf/97-28653.pdf).", "min": 0, "max": "1", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "mustSupport": true, "isModifier": false, "isSummary": false}, {"id": "Extension.extension:ombCategory.id", "path": "Extension.extension.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Extension.extension:ombCategory.extension", "path": "Extension.extension.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Extension.extension:ombCategory.url", "path": "Extension.extension.url", "representation": ["xmlAttr"], "short": "identifies the meaning of the extension", "definition": "Source of the definition for the extension code - a logical name or a URL.", "comment": "The definition may point directly to a computable or human-readable definition of the extensibility codes, or it may be a logical URI as declared in some other specification. The definition SHALL be a URI for the Structure Definition defining the extension.", "min": 1, "max": "1", "base": {"path": "Extension.url", "min": 1, "max": "1"}, "type": [{"code": "uri"}], "fixedUri": "ombCategory", "isModifier": false, "isSummary": false}, {"id": "Extension.extension:ombCategory.value[x]", "path": "Extension.extension.value[x]", "short": "Value of extension", "definition": "Value of extension - must be one of a constrained set of the data types (see [Extensibility](http://hl7.org/fhir/R4/extensibility.html) for a list).", "min": 1, "max": "1", "base": {"path": "Extension.value[x]", "min": 0, "max": "1"}, "type": [{"code": "Coding"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"strength": "required", "valueSet": "http://hl7.org/fhir/us/core/ValueSet/omb-ethnicity-category"}}, {"id": "Extension.extension:detailed", "path": "Extension.extension", "sliceName": "detailed", "short": "Extended ethnicity codes", "definition": "The 41 CDC ethnicity codes that are grouped under one of the 2 OMB ethnicity category codes.", "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Extension.extension:detailed.id", "path": "Extension.extension.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Extension.extension:detailed.extension", "path": "Extension.extension.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Extension.extension:detailed.url", "path": "Extension.extension.url", "representation": ["xmlAttr"], "short": "identifies the meaning of the extension", "definition": "Source of the definition for the extension code - a logical name or a URL.", "comment": "The definition may point directly to a computable or human-readable definition of the extensibility codes, or it may be a logical URI as declared in some other specification. The definition SHALL be a URI for the Structure Definition defining the extension.", "min": 1, "max": "1", "base": {"path": "Extension.url", "min": 1, "max": "1"}, "type": [{"code": "uri"}], "fixedUri": "detailed", "isModifier": false, "isSummary": false}, {"id": "Extension.extension:detailed.value[x]", "path": "Extension.extension.value[x]", "short": "Value of extension", "definition": "Value of extension - must be one of a constrained set of the data types (see [Extensibility](http://hl7.org/fhir/R4/extensibility.html) for a list).", "min": 1, "max": "1", "base": {"path": "Extension.value[x]", "min": 0, "max": "1"}, "type": [{"code": "Coding"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"strength": "required", "valueSet": "http://hl7.org/fhir/us/core/ValueSet/detailed-ethnicity"}}, {"id": "Extension.extension:text", "path": "Extension.extension", "sliceName": "text", "short": "ethnicity Text", "definition": "Plain text representation of the ethnicity concept(s).", "min": 1, "max": "1", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "mustSupport": true, "isModifier": false, "isSummary": false}, {"id": "Extension.extension:text.id", "path": "Extension.extension.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Extension.extension:text.extension", "path": "Extension.extension.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Extension.extension:text.url", "path": "Extension.extension.url", "representation": ["xmlAttr"], "short": "identifies the meaning of the extension", "definition": "Source of the definition for the extension code - a logical name or a URL.", "comment": "The definition may point directly to a computable or human-readable definition of the extensibility codes, or it may be a logical URI as declared in some other specification. The definition SHALL be a URI for the Structure Definition defining the extension.", "min": 1, "max": "1", "base": {"path": "Extension.url", "min": 1, "max": "1"}, "type": [{"code": "uri"}], "fixedUri": "text", "isModifier": false, "isSummary": false}, {"id": "Extension.extension:text.value[x]", "path": "Extension.extension.value[x]", "short": "Value of extension", "definition": "Value of extension - must be one of a constrained set of the data types (see [Extensibility](http://hl7.org/fhir/R4/extensibility.html) for a list).", "min": 1, "max": "1", "base": {"path": "Extension.value[x]", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Extension.url", "path": "Extension.url", "representation": ["xmlAttr"], "short": "identifies the meaning of the extension", "definition": "Source of the definition for the extension code - a logical name or a URL.", "comment": "The definition may point directly to a computable or human-readable definition of the extensibility codes, or it may be a logical URI as declared in some other specification. The definition SHALL be a URI for the Structure Definition defining the extension.", "min": 1, "max": "1", "base": {"path": "Extension.url", "min": 1, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "uri"}], "code": "http://hl7.org/fhirpath/System.String"}], "fixedUri": "http://hl7.org/fhir/us/core/StructureDefinition/us-core-ethnicity", "isModifier": false, "isSummary": false}, {"id": "Extension.value[x]", "path": "Extension.value[x]", "short": "Value of extension", "definition": "Value of extension - must be one of a constrained set of the data types (see [Extensibility](http://hl7.org/fhir/R4/extensibility.html) for a list).", "min": 0, "max": "0", "base": {"path": "Extension.value[x]", "min": 0, "max": "1"}, "type": [{"code": "base64Binary"}, {"code": "boolean"}, {"code": "canonical"}, {"code": "code"}, {"code": "date"}, {"code": "dateTime"}, {"code": "decimal"}, {"code": "id"}, {"code": "instant"}, {"code": "integer"}, {"code": "markdown"}, {"code": "oid"}, {"code": "positiveInt"}, {"code": "string"}, {"code": "time"}, {"code": "unsignedInt"}, {"code": "uri"}, {"code": "url"}, {"code": "uuid"}, {"code": "Address"}, {"code": "Age"}, {"code": "Annotation"}, {"code": "Attachment"}, {"code": "CodeableConcept"}, {"code": "Coding"}, {"code": "ContactPoint"}, {"code": "Count"}, {"code": "Distance"}, {"code": "Duration"}, {"code": "HumanName"}, {"code": "Identifier"}, {"code": "Money"}, {"code": "Period"}, {"code": "Quantity"}, {"code": "Range"}, {"code": "<PERSON><PERSON>"}, {"code": "Reference"}, {"code": "SampledData"}, {"code": "Signature"}, {"code": "Timing"}, {"code": "ContactDetail"}, {"code": "Contributor"}, {"code": "DataRequirement"}, {"code": "Expression"}, {"code": "ParameterDefinition"}, {"code": "RelatedArtifact"}, {"code": "TriggerDefinition"}, {"code": "UsageContext"}, {"code": "Dosage"}, {"code": "Meta"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}]}}, {"resourceType": "StructureDefinition", "id": "us-core-birthsex", "url": "http://hl7.org/fhir/us/core/StructureDefinition/us-core-birthsex", "version": "5.0.1", "name": "USCoreBirthSexExtension", "title": "US Core Birth Sex Extension", "status": "active", "date": "2019-05-21", "publisher": "HL7 International - Cross-Group Projects", "contact": [{"name": "HL7 International - Cross-Group Projects", "telecom": [{"system": "url", "value": "http://www.hl7.org/Special/committees/cgp"}, {"system": "email", "value": "<EMAIL>"}]}], "description": "A code classifying the person's sex assigned at birth  as specified by the [Office of the National Coordinator for Health IT (ONC)](https://www.healthit.gov/newsroom/about-onc). This extension aligns with the C-CDA Birth Sex Observation (LOINC 76689-9).", "jurisdiction": [{"coding": [{"system": "urn:iso:std:iso:3166", "code": "US"}]}], "copyright": "Used by permission of HL7 International, all rights reserved Creative Commons License", "fhirVersion": "4.0.1", "kind": "complex-type", "abstract": false, "context": [{"type": "element", "expression": "Patient"}], "type": "Extension", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/Extension", "derivation": "constraint", "snapshot": {"element": [{"id": "Extension", "path": "Extension", "short": "Extension", "definition": "A code classifying the person's sex assigned at birth  as specified by the [Office of the National Coordinator for Health IT (ONC)](https://www.healthit.gov/newsroom/about-onc).", "comment": "The codes required are intended to present birth sex (i.e., the sex recorded on the patient’s birth certificate) and not gender identity or reassigned sex.", "min": 0, "max": "1", "base": {"path": "Extension", "min": 0, "max": "*"}, "condition": ["ele-1"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), 'value')])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false}, {"id": "Extension.id", "path": "Extension.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Extension.extension", "path": "Extension.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Extension.url", "path": "Extension.url", "representation": ["xmlAttr"], "short": "identifies the meaning of the extension", "definition": "Source of the definition for the extension code - a logical name or a URL.", "comment": "The definition may point directly to a computable or human-readable definition of the extensibility codes, or it may be a logical URI as declared in some other specification. The definition SHALL be a URI for the Structure Definition defining the extension.", "min": 1, "max": "1", "base": {"path": "Extension.url", "min": 1, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "uri"}], "code": "http://hl7.org/fhirpath/System.String"}], "fixedUri": "http://hl7.org/fhir/us/core/StructureDefinition/us-core-birthsex", "isModifier": false, "isSummary": false}, {"id": "Extension.value[x]", "path": "Extension.value[x]", "short": "Value of extension", "definition": "Value of extension - must be one of a constrained set of the data types (see [Extensibility](http://hl7.org/fhir/R4/extensibility.html) for a list).", "min": 1, "max": "1", "base": {"path": "Extension.value[x]", "min": 0, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"strength": "required", "description": "Code for sex assigned at birth", "valueSet": "http://hl7.org/fhir/us/core/ValueSet/birthsex"}}]}}, {"resourceType": "StructureDefinition", "id": "us-core-genderIdentity", "url": "http://hl7.org/fhir/us/core/StructureDefinition/us-core-genderIdentity", "version": "5.0.1", "name": "USCoreGenderIdentityExtension", "title": "US Core Gender Identity Extension", "status": "active", "date": "2022-01-22", "publisher": "HL7 International - Cross-Group Projects", "contact": [{"name": "HL7 International - Cross-Group Projects", "telecom": [{"system": "url", "value": "http://www.hl7.org/Special/committees/cgp"}, {"system": "email", "value": "<EMAIL>"}]}], "description": "This extension provides concepts to describe the gender a person identifies as.", "jurisdiction": [{"coding": [{"system": "urn:iso:std:iso:3166", "code": "US"}]}], "purpose": "Complies with USCDI v2", "copyright": "Used by permission of HL7 International, all rights reserved Creative Commons License", "fhirVersion": "4.0.1", "kind": "complex-type", "abstract": false, "context": [{"type": "element", "expression": "Patient"}, {"type": "element", "expression": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "element", "expression": "Person"}, {"type": "element", "expression": "Practitioner"}], "type": "Extension", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/patient-genderIdentity", "derivation": "constraint", "snapshot": {"element": [{"id": "Extension", "path": "Extension", "short": "Extension", "definition": "An Extension", "min": 0, "max": "1", "base": {"path": "Extension", "min": 0, "max": "*"}, "condition": ["ele-1"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), 'value')])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false}, {"id": "Extension.id", "path": "Extension.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Extension.extension", "path": "Extension.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Extension", "definition": "An Extension", "min": 0, "max": "0", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Extension.url", "path": "Extension.url", "representation": ["xmlAttr"], "short": "identifies the meaning of the extension", "definition": "Source of the definition for the extension code - a logical name or a URL.", "comment": "The definition may point directly to a computable or human-readable definition of the extensibility codes, or it may be a logical URI as declared in some other specification. The definition SHALL be a URI for the Structure Definition defining the extension.", "min": 1, "max": "1", "base": {"path": "Extension.url", "min": 1, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "uri"}], "code": "http://hl7.org/fhirpath/System.String"}], "fixedUri": "http://hl7.org/fhir/us/core/StructureDefinition/us-core-genderIdentity", "isModifier": false, "isSummary": false}, {"id": "Extension.value[x]", "path": "Extension.value[x]", "short": "Value of extension", "definition": "Value of extension - must be one of a constrained set of the data types (see [Extensibility](http://hl7.org/fhir/extensibility.html) for a list).", "min": 1, "max": "1", "base": {"path": "Extension.value[x]", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"strength": "extensible", "valueSet": "http://cts.nlm.nih.gov/fhir/ValueSet/2.16.840.1.113762.1.4.1021.32"}}]}}, {"resourceType": "StructureDefinition", "id": "us-core-implantable-device", "url": "http://hl7.org/fhir/us/core/StructureDefinition/us-core-implantable-device", "version": "5.0.1", "name": "USCoreImplantableDeviceProfile", "title": "US Core Implantable Device Profile", "status": "active", "experimental": false, "date": "2022-04-20", "publisher": "HL7 International - Cross-Group Projects", "contact": [{"name": "HL7 International - Cross-Group Projects", "telecom": [{"system": "url", "value": "http://www.hl7.org/Special/committees/cgp"}, {"system": "email", "value": "<EMAIL>"}]}], "description": "The US Core Implantable Device Profile is based upon the core FHIR Device Resource and meets the U.S. Core Data for Interoperability (USCDI) v2 ‘Unique Device Identifier(s) for a Patient’s Implantable Device(s)’ requirements. To promote interoperability and adoption through common implementation, this profile sets minimum expectations for the Device resource to record, search, and fetch UDI information associated with a patient's implantable device(s). It identifies which core elements, extensions, vocabularies and value sets **SHALL** be present in the resource when using this profile. It provides the floor for standards development for specific uses cases.", "jurisdiction": [{"coding": [{"system": "urn:iso:std:iso:3166", "code": "US"}]}], "copyright": "Used by permission of HL7 International, all rights reserved Creative Commons License", "fhirVersion": "4.0.1", "kind": "resource", "abstract": false, "type": "<PERSON><PERSON>", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/Device", "derivation": "constraint", "snapshot": {"element": [{"id": "<PERSON><PERSON>", "path": "<PERSON><PERSON>", "short": "Item used in healthcare", "definition": "\\-", "comment": "\\-", "min": 0, "max": "*", "base": {"path": "<PERSON><PERSON>", "min": 0, "max": "*"}, "constraint": [{"key": "dom-2", "severity": "error", "human": "If the resource is contained in another resource, it SHALL NOT contain nested Resources", "expression": "contained.contained.empty()", "xpath": "not(parent::f:contained and f:contained)", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"key": "dom-3", "severity": "error", "human": "If the resource is contained in another resource, it SHALL be referred to from elsewhere in the resource or SHALL refer to the containing resource", "expression": "contained.where((('#'+id in (%resource.descendants().reference | %resource.descendants().as(canonical) | %resource.descendants().as(uri) | %resource.descendants().as(url))) or descendants().where(reference = '#').exists() or descendants().where(as(canonical) = '#').exists() or descendants().where(as(canonical) = '#').exists()).not()).trace('unmatched', id).empty()", "xpath": "not(exists(for $id in f:contained/*/f:id/@value return $contained[not(parent::*/descendant::f:reference/@value=concat('#', $contained/*/id/@value) or descendant::f:reference[@value='#'])]))", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"key": "dom-4", "severity": "error", "human": "If a resource is contained in another resource, it SHALL NOT have a meta.versionId or a meta.lastUpdated", "expression": "contained.meta.versionId.empty() and contained.meta.lastUpdated.empty()", "xpath": "not(exists(f:contained/*/f:meta/f:versionId)) and not(exists(f:contained/*/f:meta/f:lastUpdated))", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"key": "dom-5", "severity": "error", "human": "If a resource is contained in another resource, it SHALL NOT have a security label", "expression": "contained.meta.security.empty()", "xpath": "not(exists(f:contained/*/f:meta/f:security))", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bestpractice", "valueBoolean": true}, {"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bestpractice-explanation", "valueMarkdown": "When a resource has no narrative, only systems that fully understand the data can display the resource to a human safely. Including a human readable representation in the resource makes for a much more robust eco-system and cheaper handling of resources by intermediary systems. Some ecosystems restrict distribution of resources to only those systems that do fully understand the resources, and as a consequence implementers may believe that the narrative is superfluous. However experience shows that such eco-systems often open up to new participants over time."}], "key": "dom-6", "severity": "warning", "human": "A resource should have narrative for robust management", "expression": "text.`div`.exists()", "xpath": "exists(f:text/h:div)", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}], "mustSupport": false, "isModifier": false, "isSummary": false}, {"id": "Device.id", "path": "Device.id", "short": "Logical id of this artifact", "definition": "The logical id of the resource, as used in the URL for the resource. Once assigned, this value never changes.", "comment": "The only time that a resource does not have an id is when it is being submitted to the server using a create operation.", "min": 0, "max": "1", "base": {"path": "Resource.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": true}, {"id": "Device.meta", "path": "Device.meta", "short": "Metadata about the resource", "definition": "The metadata about the resource. This is content that is maintained by the infrastructure. Changes to the content might not always be associated with version changes to the resource.", "min": 0, "max": "1", "base": {"path": "Resource.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Device.implicitRules", "path": "Device.implicitRules", "short": "A set of rules under which this content was created", "definition": "A reference to a set of rules that were followed when the resource was constructed, and which must be understood when processing the content. Often, this is a reference to an implementation guide that defines the special rules along with other profiles etc.", "comment": "Asserting this rule set restricts the content to be only understood by a limited set of trading partners. This inherently limits the usefulness of the data in the long term. However, the existing health eco-system is highly fractured, and not yet ready to define, collect, and exchange data in a generally computable sense. Wherever possible, implementers and/or specification writers should avoid using this element. Often, when used, the URL is a reference to an implementation guide that defines these special rules as part of it's narrative along with other profiles, value sets, etc.", "min": 0, "max": "1", "base": {"path": "Resource.implicitRules", "min": 0, "max": "1"}, "type": [{"code": "uri"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": true, "isModifierReason": "This element is labeled as a modifier because the implicit rules may provide additional knowledge about the resource that modifies it's meaning or interpretation", "isSummary": true}, {"id": "Device.language", "path": "Device.language", "short": "Language of the resource content", "definition": "The base language in which the resource is written.", "comment": "Language is provided to support indexing and accessibility (typically, services such as text to speech use the language tag). The html language tag in the narrative applies  to the narrative. The language tag on the resource may be used to specify the language of other presentations generated from the data in the resource. Not all the content has to be in the base language. The Resource.language should not be assumed to apply to the narrative automatically. If a language is specified, it should it also be specified on the div element in the html (see rules in HTML5 for information about the relationship between xml:lang and the html lang attribute).", "min": 0, "max": "1", "base": {"path": "Resource.language", "min": 0, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-maxValueSet", "valueCanonical": "http://hl7.org/fhir/ValueSet/all-languages"}, {"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "Language"}, {"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-isCommonBinding", "valueBoolean": true}], "strength": "preferred", "description": "A human language.", "valueSet": "http://hl7.org/fhir/ValueSet/languages"}}, {"id": "Device.text", "path": "Device.text", "short": "Text summary of the resource, for human interpretation", "definition": "A human-readable narrative that contains a summary of the resource and can be used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative. Resource definitions may define what content should be represented in the narrative to ensure clinical safety.", "comment": "Contained resources do not have narrative. Resources that are not contained SHOULD have a narrative. In some cases, a resource may only have text with little or no additional discrete data (as long as all minOccurs=1 elements are satisfied).  This may be necessary for data from legacy systems where information is captured as a \"text blob\" or where text is additionally entered raw or narrated and encoded information is added later.", "alias": ["narrative", "html", "xhtml", "display"], "min": 0, "max": "1", "base": {"path": "DomainResource.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.contained", "path": "Device.contained", "short": "Contained, inline Resources", "definition": "These resources do not have an independent existence apart from the resource that contains them - they cannot be identified independently, and nor can they have their own independent transaction scope.", "comment": "This should never be done when the content can be identified properly, as once identification is lost, it is extremely difficult (and context dependent) to restore it again. Contained resources may have profiles and tags In their meta elements, but SHALL NOT have security labels.", "alias": ["inline resources", "anonymous resources", "contained resources"], "min": 0, "max": "*", "base": {"path": "DomainResource.contained", "min": 0, "max": "*"}, "type": [{"code": "Resource"}], "isModifier": false, "isSummary": false}, {"id": "Device.extension", "path": "Device.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the resource. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Device.modifierExtension", "path": "Device.modifierExtension", "short": "Extensions that cannot be ignored", "definition": "May be used to represent additional information that is not part of the basic definition of the resource and that modifies the understanding of the element that contains it and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer is allowed to define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R4/extensibility.html#modifierExtension).", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "DomainResource.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the resource that contains them", "isSummary": false}, {"id": "Device.identifier", "path": "Device.identifier", "short": "Instance identifier", "definition": "Unique instance identifiers assigned to a device by manufacturers other organizations or owners.", "comment": "The barcode string from a barcode present on a device label or package may identify the instance, include names given to the device in local usage, or may identify the type of device. If the identifier identifies the type of device, Device.type element should be used.", "min": 0, "max": "*", "base": {"path": "Device.identifier", "min": 0, "max": "*"}, "type": [{"code": "Identifier"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.definition", "path": "Device.definition", "short": "The reference to the definition for the device", "definition": "The reference to the definition for the device.", "min": 0, "max": "1", "base": {"path": "Device.definition", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/DeviceDefinition"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.udiCarrier", "path": "Device.udiCarrier", "short": "Unique Device Identifier (UDI) Barcode string", "definition": "Unique device identifier (UDI) assigned to device label or package.  Note that the Device may include multiple udiCarriers as it either may include just the udiCarrier for the jurisdiction it is sold, or for multiple jurisdictions it could have been sold.", "comment": "Some devices may not have UDI information (for example. historical data or patient reported data).", "min": 0, "max": "1", "base": {"path": "Device.udiCarrier", "min": 0, "max": "*"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Device.udiCarrier.id", "path": "Device.udiCarrier.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Device.udiCarrier.extension", "path": "Device.udiCarrier.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Device.udiCarrier.modifierExtension", "path": "Device.udiCarrier.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R4/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true}, {"id": "Device.udiCarrier.deviceIdentifier", "path": "Device.udiCarrier.deviceIdentifier", "short": "Mandatory fixed portion of UDI", "definition": "The device identifier (DI) is a mandatory, fixed portion of a UDI that identifies the labeler and the specific version or model of a device.", "alias": ["DI"], "min": 1, "max": "1", "base": {"path": "Device.udiCarrier.deviceIdentifier", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Device.udiCarrier.issuer", "path": "Device.udiCarrier.issuer", "short": "UDI Issuing Organization", "definition": "Organization that is charged with issuing UDIs for devices.  For example, the US FDA issuers include :\n1) GS1: \nhttp://hl7.org/fhir/NamingSystem/gs1-di, \n2) HIBCC:\nhttp://hl7.org/fhir/NamingSystem/hibcc-dI, \n3) ICCBBA for blood containers:\nhttp://hl7.org/fhir/NamingSystem/iccbba-blood-di, \n4) ICCBA for other devices:\nhttp://hl7.org/fhir/NamingSystem/iccbba-other-di.", "alias": ["Barcode System"], "min": 0, "max": "1", "base": {"path": "Device.udiCarrier.issuer", "min": 0, "max": "1"}, "type": [{"code": "uri"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.udiCarrier.jurisdiction", "path": "Device.udiCarrier.jurisdiction", "short": "Regional UDI authority", "definition": "The identity of the authoritative source for UDI generation within a  jurisdiction.  All UDIs are globally unique within a single namespace with the appropriate repository uri as the system.  For example,  UDIs of devices managed in the U.S. by the FDA, the value is  http://hl7.org/fhir/NamingSystem/fda-udi.", "requirements": "Allows a recipient of a UDI to know which database will contain the UDI-associated metadata.", "min": 0, "max": "1", "base": {"path": "Device.udiCarrier.jurisdiction", "min": 0, "max": "1"}, "type": [{"code": "uri"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.udiCarrier.carrierAIDC", "path": "Device.udiCarrier.carrierAIDC", "short": "UDI Machine Readable Barcode String", "definition": "The full UDI carrier of the Automatic Identification and Data Capture (AIDC) technology representation of the barcode string as printed on the packaging of the device - e.g., a barcode or RFID.   Because of limitations on character sets in XML and the need to round-trip JSON data through XML, AIDC Formats *SHALL* be base64 encoded.", "comment": "The AIDC form of UDIs should be scanned or otherwise used for the identification of the device whenever possible to minimize errors in records resulting from manual transcriptions. If separate barcodes for DI and PI are present, concatenate the string with DI first and in order of human readable expression on label.", "alias": ["Automatic Identification and Data Capture"], "min": 0, "max": "1", "base": {"path": "Device.udiCarrier.carrierAIDC", "min": 0, "max": "1"}, "type": [{"code": "base64Binary"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Device.udiCarrier.carrierHRF", "path": "Device.udiCarrier.carrierHRF", "short": "UDI Human Readable Barcode String", "definition": "The full UDI carrier as the human readable form (HRF) representation of the barcode string as printed on the packaging of the device.", "comment": "If separate barcodes for DI and PI are present, concatenate the string with DI first and in order of human readable expression on label.", "alias": ["Human Readable Form", "UDI", "Barcode String"], "min": 0, "max": "1", "base": {"path": "Device.udiCarrier.carrierHRF", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Device.udiCarrier.entryType", "path": "Device.udiCarrier.entryType", "short": "barcode | rfid | manual +", "definition": "A coded entry to indicate how the data was entered.", "requirements": "Supports a way to distinguish hand entered from machine read data.", "min": 0, "max": "1", "base": {"path": "Device.udiCarrier.entryType", "min": 0, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "UDIEntryType"}], "strength": "required", "description": "Codes to identify how UDI data was entered.", "valueSet": "http://hl7.org/fhir/ValueSet/udi-entry-type|4.0.1"}}, {"id": "Device.status", "path": "Device.status", "short": "active | inactive | entered-in-error | unknown", "definition": "Status of the Device availability.", "comment": "This element is labeled as a modifier because the status contains the codes inactive and entered-in-error that mark the device (record)as not currently valid.", "min": 0, "max": "1", "base": {"path": "Device.status", "min": 0, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": true, "isModifierReason": "This element is labelled as a modifier because it is a status element that contains status entered-in-error which means that the resource should not be treated as valid", "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "FHIRDeviceStatus"}], "strength": "required", "description": "The availability status of the device.", "valueSet": "http://hl7.org/fhir/ValueSet/device-status|4.0.1"}}, {"id": "Device.statusReason", "path": "Device.statusReason", "short": "online | paused | standby | offline | not-ready | transduc-discon | hw-discon | off", "definition": "Reason for the dtatus of the Device availability.", "min": 0, "max": "*", "base": {"path": "Device.statusReason", "min": 0, "max": "*"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "FHIRDeviceStatusReason"}], "strength": "extensible", "description": "The availability status reason of the device.", "valueSet": "http://hl7.org/fhir/ValueSet/device-status-reason"}}, {"id": "Device.distinctIdentifier", "path": "Device.distinctIdentifier", "short": "The distinct identification string", "definition": "The distinct identification string as required by regulation for a human cell, tissue, or cellular and tissue-based product.", "comment": "For example, this applies to devices in the United States regulated under *Code of Federal Regulation 21CFR§1271.290(c)*.", "alias": ["Distinct Identification Code (DIC)"], "min": 0, "max": "1", "base": {"path": "Device.distinctIdentifier", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false}, {"id": "Device.manufacturer", "path": "Device.manufacturer", "short": "Name of device manufacturer", "definition": "A name of the manufacturer.", "min": 0, "max": "1", "base": {"path": "Device.manufacturer", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.manufactureDate", "path": "Device.manufactureDate", "short": "Date when the device was made", "definition": "The date and time when the device was manufactured.", "min": 0, "max": "1", "base": {"path": "Device.manufactureDate", "min": 0, "max": "1"}, "type": [{"code": "dateTime"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false}, {"id": "Device.expirationDate", "path": "Device.expirationDate", "short": "Date and time of expiry of this device (if applicable)", "definition": "The date and time beyond which this device is no longer valid or should not be used (if applicable).", "min": 0, "max": "1", "base": {"path": "Device.expirationDate", "min": 0, "max": "1"}, "type": [{"code": "dateTime"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false}, {"id": "Device.lotNumber", "path": "Device.lotNumber", "short": "Lot number of manufacture", "definition": "Lot number assigned by the manufacturer.", "min": 0, "max": "1", "base": {"path": "Device.lotNumber", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false}, {"id": "Device.serialNumber", "path": "Device.serialNumber", "short": "Serial number assigned by the manufacturer", "definition": "The serial number assigned by the organization when the device was manufactured.", "comment": "Alphanumeric Maximum 20.", "min": 0, "max": "1", "base": {"path": "Device.serialNumber", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false}, {"id": "Device.deviceName", "path": "Device.deviceName", "short": "The name of the device as given by the manufacturer", "definition": "This represents the manufacturer's name of the device as provided by the device, from a UDI label, or by a person describing the Device.  This typically would be used when a person provides the name(s) or when the device represents one of the names available from DeviceDefinition.", "min": 0, "max": "*", "base": {"path": "Device.deviceName", "min": 0, "max": "*"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.deviceName.id", "path": "Device.deviceName.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Device.deviceName.extension", "path": "Device.deviceName.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Device.deviceName.modifierExtension", "path": "Device.deviceName.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R4/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true}, {"id": "Device.deviceName.name", "path": "Device.deviceName.name", "short": "The name of the device", "definition": "The name of the device.", "alias": ["Σ"], "min": 1, "max": "1", "base": {"path": "Device.deviceName.name", "min": 1, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.deviceName.type", "path": "Device.deviceName.type", "short": "udi-label-name | user-friendly-name | patient-reported-name | manufacturer-name | model-name | other", "definition": "The type of deviceName.\nUDILabelName | UserFriendlyName | PatientReportedName | ManufactureDeviceName | ModelName.", "min": 1, "max": "1", "base": {"path": "Device.deviceName.type", "min": 1, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "DeviceNameType"}], "strength": "required", "description": "The type of name the device is referred by.", "valueSet": "http://hl7.org/fhir/ValueSet/device-nametype|4.0.1"}}, {"id": "Device.modelNumber", "path": "Device.modelNumber", "short": "The model number for the device", "definition": "The model number for the device.", "min": 0, "max": "1", "base": {"path": "Device.modelNumber", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.partNumber", "path": "Device.partNumber", "short": "The part number of the device", "definition": "The part number of the device.", "comment": "Alphanumeric Maximum 20.", "min": 0, "max": "1", "base": {"path": "Device.partNumber", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.type", "path": "Device.type", "short": "The kind or type of device", "definition": "The kind or type of device.", "min": 1, "max": "1", "base": {"path": "Device.type", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false, "binding": {"strength": "extensible", "description": "Codes to identify medical devices", "valueSet": "http://hl7.org/fhir/ValueSet/device-kind"}}, {"id": "Device.specialization", "path": "Device.specialization", "short": "The capabilities supported on a  device, the standards to which the device conforms for a particular purpose, and used for the communication", "definition": "The capabilities supported on a  device, the standards to which the device conforms for a particular purpose, and used for the communication.", "min": 0, "max": "*", "base": {"path": "Device.specialization", "min": 0, "max": "*"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.specialization.id", "path": "Device.specialization.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Device.specialization.extension", "path": "Device.specialization.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Device.specialization.modifierExtension", "path": "Device.specialization.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R4/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true}, {"id": "Device.specialization.systemType", "path": "Device.specialization.systemType", "short": "The standard that is used to operate and communicate", "definition": "The standard that is used to operate and communicate.", "alias": ["Σ"], "min": 1, "max": "1", "base": {"path": "Device.specialization.systemType", "min": 1, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.specialization.version", "path": "Device.specialization.version", "short": "The version of the standard that is used to operate and communicate", "definition": "The version of the standard that is used to operate and communicate.", "min": 0, "max": "1", "base": {"path": "Device.specialization.version", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.version", "path": "Device.version", "short": "The actual design of the device or software version running on the device", "definition": "The actual design of the device or software version running on the device.", "min": 0, "max": "*", "base": {"path": "Device.version", "min": 0, "max": "*"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.version.id", "path": "Device.version.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Device.version.extension", "path": "Device.version.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Device.version.modifierExtension", "path": "Device.version.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R4/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true}, {"id": "Device.version.type", "path": "Device.version.type", "short": "The type of the device version", "definition": "The type of the device version.", "alias": ["Σ"], "min": 0, "max": "1", "base": {"path": "Device.version.type", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.version.component", "path": "Device.version.component", "short": "A single component of the device version", "definition": "A single component of the device version.", "min": 0, "max": "1", "base": {"path": "Device.version.component", "min": 0, "max": "1"}, "type": [{"code": "Identifier"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.version.value", "path": "Device.version.value", "short": "The version text", "definition": "The version text.", "min": 1, "max": "1", "base": {"path": "Device.version.value", "min": 1, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.property", "path": "Device.property", "short": "The actual configuration settings of a device as it actually operates, e.g., regulation status, time properties", "definition": "The actual configuration settings of a device as it actually operates, e.g., regulation status, time properties.", "min": 0, "max": "*", "base": {"path": "Device.property", "min": 0, "max": "*"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.property.id", "path": "Device.property.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Device.property.extension", "path": "Device.property.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Device.property.modifierExtension", "path": "Device.property.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R4/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true}, {"id": "Device.property.type", "path": "Device.property.type", "short": "Code that specifies the property DeviceDefinitionPropetyCode (Extensible)", "definition": "Code that specifies the property DeviceDefinitionPropetyCode (Extensible).", "min": 1, "max": "1", "base": {"path": "Device.property.type", "min": 1, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.property.valueQuantity", "path": "Device.property.valueQuantity", "short": "Property value as a quantity", "definition": "Property value as a quantity.", "min": 0, "max": "*", "base": {"path": "Device.property.valueQuantity", "min": 0, "max": "*"}, "type": [{"code": "Quantity"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.property.valueCode", "path": "Device.property.valueCode", "short": "Property value as a code, e.g., NTP4 (synced to NTP)", "definition": "Property value as a code, e.g., NTP4 (synced to NTP).", "min": 0, "max": "*", "base": {"path": "Device.property.valueCode", "min": 0, "max": "*"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.patient", "path": "Device.patient", "short": "Patient to whom <PERSON><PERSON> is affixed", "definition": "Patient information, If the device is affixed to a person.", "requirements": "If the device is implanted in a patient, then need to associate the device to the patient.", "min": 1, "max": "1", "base": {"path": "Device.patient", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/us/core/StructureDefinition/us-core-patient"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false}, {"id": "Device.owner", "path": "Device.owner", "short": "Organization responsible for device", "definition": "An organization that is responsible for the provision and ongoing maintenance of the device.", "min": 0, "max": "1", "base": {"path": "Device.owner", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Organization"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.contact", "path": "Device.contact", "short": "Details for human/organization for support", "definition": "Contact details for an organization or a particular human that is responsible for the device.", "comment": "used for troubleshooting etc.", "min": 0, "max": "*", "base": {"path": "Device.contact", "min": 0, "max": "*"}, "type": [{"code": "ContactPoint"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.location", "path": "Device.location", "short": "Where the device is found", "definition": "The place where the device can be found.", "requirements": "Device.location can be used to track device location.", "min": 0, "max": "1", "base": {"path": "Device.location", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Location"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.url", "path": "Device.url", "short": "Network address to contact device", "definition": "A network address on which the device may be contacted directly.", "comment": "If the device is running a FHIR server, the network address should  be the Base URL from which a conformance statement may be retrieved.", "min": 0, "max": "1", "base": {"path": "Device.url", "min": 0, "max": "1"}, "type": [{"code": "uri"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.note", "path": "Device.note", "short": "<PERSON><PERSON> notes and comments", "definition": "Descriptive information, usage information or implantation information that is not captured in an existing element.", "min": 0, "max": "*", "base": {"path": "Device.note", "min": 0, "max": "*"}, "type": [{"code": "Annotation"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Device.safety", "path": "Device.safety", "short": "Safety Characteristics of Device", "definition": "Provides additional safety characteristics about a medical device.  For example devices containing latex.", "min": 0, "max": "*", "base": {"path": "Device.safety", "min": 0, "max": "*"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Device.parent", "path": "Device.parent", "short": "The parent device", "definition": "The parent device.", "min": 0, "max": "1", "base": {"path": "Device.parent", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Device"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}]}}, {"resourceType": "StructureDefinition", "id": "us-core-blood-pressure", "url": "http://hl7.org/fhir/us/core/StructureDefinition/us-core-blood-pressure", "version": "5.0.1", "name": "USCoreBloodPressureProfile", "title": "US Core Blood Pressure Profile", "status": "active", "experimental": false, "date": "2022-04-20", "publisher": "HL7 International - Cross-Group Projects", "contact": [{"name": "HL7 International - Cross-Group Projects", "telecom": [{"system": "url", "value": "http://www.hl7.org/Special/committees/cgp"}, {"system": "email", "value": "<EMAIL>"}]}], "description": "To promote interoperability and adoption through common implementation, this profile sets minimum expectations for the Observation resource to record, search, and fetch diastolic and systolic blood pressure observations with standard LOINC codes and UCUM units of measure. It is based on the US Core Vital Signs Profile and identifies the *additional* mandatory core elements, extensions, vocabularies and value sets which **SHALL** be present in the Observation resource when using this profile. It provides the floor for standards development for specific uses cases.", "jurisdiction": [{"coding": [{"system": "urn:iso:std:iso:3166", "code": "US"}]}], "copyright": "Used by permission of HL7 International, all rights reserved Creative Commons License", "fhirVersion": "4.0.1", "kind": "resource", "abstract": false, "type": "Observation", "baseDefinition": "http://hl7.org/fhir/us/core/StructureDefinition/us-core-vital-signs", "derivation": "constraint", "snapshot": {"element": [{"id": "Observation", "path": "Observation", "short": "US Core Blood Pressure Profile", "definition": "\\-", "comment": "\\-", "alias": ["Vital Signs", "Measurement", "Results", "Tests"], "min": 0, "max": "*", "base": {"path": "Observation", "min": 0, "max": "*"}, "constraint": [{"key": "dom-2", "severity": "error", "human": "If the resource is contained in another resource, it SHALL NOT contain nested Resources", "expression": "contained.contained.empty()", "xpath": "not(parent::f:contained and f:contained)", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"key": "dom-3", "severity": "error", "human": "If the resource is contained in another resource, it SHALL be referred to from elsewhere in the resource or SHALL refer to the containing resource", "expression": "contained.where((('#'+id in (%resource.descendants().reference | %resource.descendants().as(canonical) | %resource.descendants().as(uri) | %resource.descendants().as(url))) or descendants().where(reference = '#').exists() or descendants().where(as(canonical) = '#').exists() or descendants().where(as(canonical) = '#').exists()).not()).trace('unmatched', id).empty()", "xpath": "not(exists(for $id in f:contained/*/f:id/@value return $contained[not(parent::*/descendant::f:reference/@value=concat('#', $contained/*/id/@value) or descendant::f:reference[@value='#'])]))", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"key": "dom-4", "severity": "error", "human": "If a resource is contained in another resource, it SHALL NOT have a meta.versionId or a meta.lastUpdated", "expression": "contained.meta.versionId.empty() and contained.meta.lastUpdated.empty()", "xpath": "not(exists(f:contained/*/f:meta/f:versionId)) and not(exists(f:contained/*/f:meta/f:lastUpdated))", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"key": "dom-5", "severity": "error", "human": "If a resource is contained in another resource, it SHALL NOT have a security label", "expression": "contained.meta.security.empty()", "xpath": "not(exists(f:contained/*/f:meta/f:security))", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bestpractice", "valueBoolean": true}, {"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bestpractice-explanation", "valueMarkdown": "When a resource has no narrative, only systems that fully understand the data can display the resource to a human safely. Including a human readable representation in the resource makes for a much more robust eco-system and cheaper handling of resources by intermediary systems. Some ecosystems restrict distribution of resources to only those systems that do fully understand the resources, and as a consequence implementers may believe that the narrative is superfluous. However experience shows that such eco-systems often open up to new participants over time."}], "key": "dom-6", "severity": "warning", "human": "A resource should have narrative for robust management", "expression": "text.`div`.exists()", "xpath": "exists(f:text/h:div)", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"key": "obs-6", "severity": "error", "human": "dataAbsentReason SHALL only be present if Observation.value[x] is not present", "expression": "dataAbsentReason.empty() or value.empty()", "xpath": "not(exists(f:dataAbsentReason)) or (not(exists(*[starts-with(local-name(.), 'value')])))", "source": "http://hl7.org/fhir/StructureDefinition/Observation"}, {"key": "obs-7", "severity": "error", "human": "If Observation.code is the same as an Observation.component.code then the value element associated with the code SHALL NOT be present", "expression": "value.empty() or component.code.where(coding.intersect(%resource.code.coding).exists()).empty()", "xpath": "not(f:*[starts-with(local-name(.), 'value')] and (for $coding in f:code/f:coding return f:component/f:code/f:coding[f:code/@value=$coding/f:code/@value] [f:system/@value=$coding/f:system/@value]))", "source": "http://hl7.org/fhir/StructureDefinition/Observation"}, {"key": "vs-2", "severity": "error", "human": "If there is no component or hasMember element then either a value[x] or a data absent reason must be present.", "expression": "(component.empty() and hasMember.empty()) implies (dataAbsentReason.exists() or value.exists())", "xpath": "f:component or f:memberOF or f:*[starts-with(local-name(.), 'value')] or f:dataAbsentReason", "source": "http://hl7.org/fhir/StructureDefinition/vitalsigns"}], "isModifier": false, "isSummary": false}, {"id": "Observation.id", "path": "Observation.id", "short": "Logical id of this artifact", "definition": "The logical id of the resource, as used in the URL for the resource. Once assigned, this value never changes.", "comment": "The only time that a resource does not have an id is when it is being submitted to the server using a create operation.", "min": 0, "max": "1", "base": {"path": "Resource.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": true}, {"id": "Observation.meta", "path": "Observation.meta", "short": "Metadata about the resource", "definition": "The metadata about the resource. This is content that is maintained by the infrastructure. Changes to the content might not always be associated with version changes to the resource.", "min": 0, "max": "1", "base": {"path": "Resource.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Observation.implicitRules", "path": "Observation.implicitRules", "short": "A set of rules under which this content was created", "definition": "A reference to a set of rules that were followed when the resource was constructed, and which must be understood when processing the content. Often, this is a reference to an implementation guide that defines the special rules along with other profiles etc.", "comment": "Asserting this rule set restricts the content to be only understood by a limited set of trading partners. This inherently limits the usefulness of the data in the long term. However, the existing health eco-system is highly fractured, and not yet ready to define, collect, and exchange data in a generally computable sense. Wherever possible, implementers and/or specification writers should avoid using this element. Often, when used, the URL is a reference to an implementation guide that defines these special rules as part of it's narrative along with other profiles, value sets, etc.", "min": 0, "max": "1", "base": {"path": "Resource.implicitRules", "min": 0, "max": "1"}, "type": [{"code": "uri"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": true, "isModifierReason": "This element is labeled as a modifier because the implicit rules may provide additional knowledge about the resource that modifies it's meaning or interpretation", "isSummary": true}, {"id": "Observation.language", "path": "Observation.language", "short": "Language of the resource content", "definition": "The base language in which the resource is written.", "comment": "Language is provided to support indexing and accessibility (typically, services such as text to speech use the language tag). The html language tag in the narrative applies  to the narrative. The language tag on the resource may be used to specify the language of other presentations generated from the data in the resource. Not all the content has to be in the base language. The Resource.language should not be assumed to apply to the narrative automatically. If a language is specified, it should it also be specified on the div element in the html (see rules in HTML5 for information about the relationship between xml:lang and the html lang attribute).", "min": 0, "max": "1", "base": {"path": "Resource.language", "min": 0, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-maxValueSet", "valueCanonical": "http://hl7.org/fhir/ValueSet/all-languages"}, {"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "Language"}, {"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-isCommonBinding", "valueBoolean": true}], "strength": "preferred", "description": "A human language.", "valueSet": "http://hl7.org/fhir/ValueSet/languages"}}, {"id": "Observation.text", "path": "Observation.text", "short": "Text summary of the resource, for human interpretation", "definition": "A human-readable narrative that contains a summary of the resource and can be used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative. Resource definitions may define what content should be represented in the narrative to ensure clinical safety.", "comment": "Contained resources do not have narrative. Resources that are not contained SHOULD have a narrative. In some cases, a resource may only have text with little or no additional discrete data (as long as all minOccurs=1 elements are satisfied).  This may be necessary for data from legacy systems where information is captured as a \"text blob\" or where text is additionally entered raw or narrated and encoded information is added later.", "alias": ["narrative", "html", "xhtml", "display"], "min": 0, "max": "1", "base": {"path": "DomainResource.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Observation.contained", "path": "Observation.contained", "short": "Contained, inline Resources", "definition": "These resources do not have an independent existence apart from the resource that contains them - they cannot be identified independently, and nor can they have their own independent transaction scope.", "comment": "This should never be done when the content can be identified properly, as once identification is lost, it is extremely difficult (and context dependent) to restore it again. Contained resources may have profiles and tags In their meta elements, but SHALL NOT have security labels.", "alias": ["inline resources", "anonymous resources", "contained resources"], "min": 0, "max": "*", "base": {"path": "DomainResource.contained", "min": 0, "max": "*"}, "type": [{"code": "Resource"}], "isModifier": false, "isSummary": false}, {"id": "Observation.extension", "path": "Observation.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the resource. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Observation.modifierExtension", "path": "Observation.modifierExtension", "short": "Extensions that cannot be ignored", "definition": "May be used to represent additional information that is not part of the basic definition of the resource and that modifies the understanding of the element that contains it and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer is allowed to define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/extensibility.html#modifierExtension).", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "DomainResource.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the resource that contains them", "isSummary": false}, {"id": "Observation.identifier", "path": "Observation.identifier", "short": "Business Identifier for observation", "definition": "A unique identifier assigned to this observation.", "requirements": "Allows observations to be distinguished and referenced.", "min": 0, "max": "*", "base": {"path": "Observation.identifier", "min": 0, "max": "*"}, "type": [{"code": "Identifier"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Observation.basedOn", "path": "Observation.basedOn", "short": "Fulfills plan, proposal or order", "definition": "A plan, proposal or order that is fulfilled in whole or in part by this event.  For example, a MedicationRequest may require a patient to have laboratory test performed before  it is dispensed.", "requirements": "Allows tracing of authorization for the event and tracking whether proposals/recommendations were acted upon.", "alias": ["Fulfills"], "min": 0, "max": "*", "base": {"path": "Observation.basedOn", "min": 0, "max": "*"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/CarePlan", "http://hl7.org/fhir/StructureDefinition/DeviceRequest", "http://hl7.org/fhir/StructureDefinition/ImmunizationRecommendation", "http://hl7.org/fhir/StructureDefinition/MedicationRequest", "http://hl7.org/fhir/StructureDefinition/NutritionOrder", "http://hl7.org/fhir/StructureDefinition/ServiceRequest"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Observation.partOf", "path": "Observation.partOf", "short": "Part of referenced event", "definition": "A larger event of which this particular Observation is a component or step.  For example,  an observation as part of a procedure.", "comment": "To link an Observation to an Encounter use `encounter`.  See the  [Notes](http://hl7.org/fhir/observation.html#obsgrouping) below for guidance on referencing another Observation.", "alias": ["Container"], "min": 0, "max": "*", "base": {"path": "Observation.partOf", "min": 0, "max": "*"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/MedicationAdministration", "http://hl7.org/fhir/StructureDefinition/MedicationDispense", "http://hl7.org/fhir/StructureDefinition/MedicationStatement", "http://hl7.org/fhir/StructureDefinition/Procedure", "http://hl7.org/fhir/StructureDefinition/Immunization", "http://hl7.org/fhir/StructureDefinition/ImagingStudy"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Observation.status", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-display-hint", "valueString": "default: final"}], "path": "Observation.status", "short": "registered | preliminary | final | amended +", "definition": "The status of the result value.", "comment": "This element is labeled as a modifier because the status contains codes that mark the resource as not currently valid.", "requirements": "Need to track the status of individual results. Some results are finalized before the whole report is finalized.", "min": 1, "max": "1", "base": {"path": "Observation.status", "min": 1, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": true, "isModifierReason": "This element is labeled as a modifier because it is a status element that contains status entered-in-error which means that the resource should not be treated as valid", "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "Status"}], "strength": "required", "valueSet": "http://hl7.org/fhir/ValueSet/observation-status|4.0.1"}}, {"id": "Observation.category", "path": "Observation.category", "slicing": {"discriminator": [{"type": "value", "path": "coding.code"}, {"type": "value", "path": "coding.system"}], "ordered": false, "rules": "open"}, "short": "Classification of  type of observation", "definition": "A code that classifies the general type of observation being made.", "comment": "In addition to the required category valueset, this element allows various categorization schemes based on the owner’s definition of the category and effectively multiple categories can be used at once.  The level of granularity is defined by the category concepts in the value set.", "requirements": "Used for filtering what observations are retrieved and displayed.", "min": 1, "max": "*", "base": {"path": "Observation.category", "min": 0, "max": "*"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "ObservationCategory"}], "strength": "preferred", "description": "Codes for high level observation categories.", "valueSet": "http://hl7.org/fhir/ValueSet/observation-category"}}, {"id": "Observation.category:VSCat", "path": "Observation.category", "sliceName": "VSCat", "short": "Classification of  type of observation", "definition": "A code that classifies the general type of observation being made.", "comment": "In addition to the required category valueset, this element allows various categorization schemes based on the owner’s definition of the category and effectively multiple categories can be used at once.  The level of granularity is defined by the category concepts in the value set.", "requirements": "Used for filtering what observations are retrieved and displayed.", "min": 1, "max": "1", "base": {"path": "Observation.category", "min": 0, "max": "*"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "ObservationCategory"}], "strength": "preferred", "description": "Codes for high level observation categories.", "valueSet": "http://hl7.org/fhir/ValueSet/observation-category"}}, {"id": "Observation.category:VSCat.id", "path": "Observation.category.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Observation.category:VSCat.extension", "path": "Observation.category.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Observation.category:VSCat.coding", "path": "Observation.category.coding", "short": "Code defined by a terminology system", "definition": "A reference to a code defined by a terminology system.", "comment": "Codes may be defined very casually in enumerations, or code lists, up to very formal definitions such as SNOMED CT - see the HL7 v3 Core Principles for more information.  Ordering of codings is undefined and SHALL NOT be used to infer meaning. Generally, at most only one of the coding values will be labeled as UserSelected = true.", "requirements": "Allows for alternative encodings within a code system, and translations to other code systems.", "min": 1, "max": "*", "base": {"path": "CodeableConcept.coding", "min": 0, "max": "*"}, "type": [{"code": "Coding"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Observation.category:VSCat.coding.id", "path": "Observation.category.coding.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Observation.category:VSCat.coding.extension", "path": "Observation.category.coding.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Observation.category:VSCat.coding.system", "path": "Observation.category.coding.system", "short": "Identity of the terminology system", "definition": "The identification of the code system that defines the meaning of the symbol in the code.", "comment": "The URI may be an OID (urn:oid:...) or a UUID (urn:uuid:...).  OIDs and UUIDs SHALL be references to the HL7 OID registry. Otherwise, the URI should come from HL7's list of FHIR defined special URIs or it should reference to some definition that establishes the system clearly and unambiguously.", "requirements": "Need to be unambiguous about the source of the definition of the symbol.", "min": 1, "max": "1", "base": {"path": "Coding.system", "min": 0, "max": "1"}, "type": [{"code": "uri"}], "fixedUri": "http://terminology.hl7.org/CodeSystem/observation-category", "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Observation.category:VSCat.coding.version", "path": "Observation.category.coding.version", "short": "Version of the system - if relevant", "definition": "The version of the code system which was used when choosing this code. Note that a well-maintained code system does not need the version reported, because the meaning of codes is consistent across versions. However this cannot consistently be assured, and when the meaning is not guaranteed to be consistent, the version SHOULD be exchanged.", "comment": "Where the terminology does not clearly define what string should be used to identify code system versions, the recommendation is to use the date (expressed in FHIR date format) on which that version was officially published as the version date.", "min": 0, "max": "1", "base": {"path": "Coding.version", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Observation.category:VSCat.coding.code", "path": "Observation.category.coding.code", "short": "Symbol in syntax defined by the system", "definition": "A symbol in syntax defined by the system. The symbol may be a predefined code or an expression in a syntax defined by the coding system (e.g. post-coordination).", "requirements": "Need to refer to a particular code in the system.", "min": 1, "max": "1", "base": {"path": "Coding.code", "min": 0, "max": "1"}, "type": [{"code": "code"}], "fixedCode": "vital-signs", "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Observation.category:VSCat.coding.display", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-translatable", "valueBoolean": true}], "path": "Observation.category.coding.display", "short": "Representation defined by the system", "definition": "A representation of the meaning of the code in the system, following the rules of the system.", "requirements": "Need to be able to carry a human-readable meaning of the code for readers that do not know  the system.", "min": 0, "max": "1", "base": {"path": "Coding.display", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Observation.category:VSCat.coding.userSelected", "path": "Observation.category.coding.userSelected", "short": "If this coding was chosen directly by the user", "definition": "Indicates that this coding was chosen by a user directly - e.g. off a pick list of available items (codes or displays).", "comment": "Amongst a set of alternatives, a directly chosen code is the most appropriate starting point for new translations. There is some ambiguity about what exactly 'directly chosen' implies, and trading partner agreement may be needed to clarify the use of this element and its consequences more completely.", "requirements": "This has been identified as a clinical safety criterium - that this exact system/code pair was chosen explicitly, rather than inferred by the system based on some rules or language processing.", "min": 0, "max": "1", "base": {"path": "Coding.userSelected", "min": 0, "max": "1"}, "type": [{"code": "boolean"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Observation.category:VSCat.text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-translatable", "valueBoolean": true}], "path": "Observation.category.text", "short": "Plain text representation of the concept", "definition": "A human language representation of the concept as seen/selected/uttered by the user who entered the data and/or which represents the intended meaning of the user.", "comment": "Very often the text is the same as a displayName of one of the codings.", "requirements": "The codes from the terminologies do not always capture the correct meaning with all the nuances of the human using them, or sometimes there is no appropriate code at all. In these cases, the text is used to capture the full meaning of the source.", "min": 0, "max": "1", "base": {"path": "CodeableConcept.text", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Observation.code", "path": "Observation.code", "short": "Blood Pressure", "definition": "Coded Responses from C-CDA Vital Sign Results.", "comment": "*All* code-value and, if present, component.code-component.value pairs need to be taken into account to correctly understand the meaning of the observation.", "requirements": "5. SHALL contain exactly one [1..1] code, where the @code SHOULD be selected from ValueSet HITSP Vital Sign Result Type 2.16.840.1.113883.3.88.12.80.62 DYNAMIC (CONF:7301).", "alias": ["Name"], "min": 1, "max": "1", "base": {"path": "Observation.code", "min": 1, "max": "1"}, "type": [{"code": "CodeableConcept"}], "patternCodeableConcept": {"coding": [{"system": "http://loinc.org", "code": "85354-9"}]}, "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true, "binding": {"strength": "extensible", "description": "The vital sign codes from the base FHIR and US Core Vital Signs.", "valueSet": "http://hl7.org/fhir/us/core/ValueSet/us-core-vital-signs"}}, {"id": "Observation.subject", "path": "Observation.subject", "short": "Who and/or what the observation is about", "definition": "The patient, or group of patients, location, or device this observation is about and into whose record the observation is placed. If the actual focus of the observation is different from the subject (or a sample of, part, or region of the subject), the `focus` element or the `code` itself specifies the actual focus of the observation.", "comment": "One would expect this element to be a cardinality of 1..1. The only circumstance in which the subject can be missing is when the observation is made by a device that does not know the patient. In this case, the observation SHALL be matched to a patient through some context/channel matching technique, and at this point, the observation should be updated.", "requirements": "Observations have no value if you don't know who or what they're about.", "min": 1, "max": "1", "base": {"path": "Observation.subject", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/us/core/StructureDefinition/us-core-patient"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Observation.focus", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-standards-status", "valueCode": "trial-use"}], "path": "Observation.focus", "short": "What the observation is about, when it is not about the subject of record", "definition": "The actual focus of an observation when it is not the patient of record representing something or someone associated with the patient such as a spouse, parent, fetus, or donor. For example, fetus observations in a mother's record.  The focus of an observation could also be an existing condition,  an intervention, the subject's diet,  another observation of the subject,  or a body structure such as tumor or implanted device.   An example use case would be using the Observation resource to capture whether the mother is trained to change her child's tracheostomy tube. In this example, the child is the patient of record and the mother is the focus.", "comment": "Typically, an observation is made about the subject - a patient, or group of patients, location, or device - and the distinction between the subject and what is directly measured for an observation is specified in the observation code itself ( e.g., \"Blood Glucose\") and does not need to be represented separately using this element.  Use `specimen` if a reference to a specimen is required.  If a code is required instead of a resource use either  `bodysite` for bodysites or the standard extension [focusCode](http://hl7.org/fhir/extension-observation-focuscode.html).", "min": 0, "max": "*", "base": {"path": "Observation.focus", "min": 0, "max": "*"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Resource"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Observation.encounter", "path": "Observation.encounter", "short": "Healthcare event during which this observation is made", "definition": "The healthcare event  (e.g. a patient and healthcare provider interaction) during which this observation is made.", "comment": "This will typically be the encounter the event occurred within, but some events may be initiated prior to or after the official completion of an encounter but still be tied to the context of the encounter (e.g. pre-admission laboratory tests).", "requirements": "For some observations it may be important to know the link between an observation and a particular encounter.", "alias": ["Context"], "min": 0, "max": "1", "base": {"path": "Observation.encounter", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Encounter"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Observation.effective[x]", "path": "Observation.effective[x]", "short": "Often just a dateTime for Vital Signs", "definition": "Often just a dateTime for Vital Signs.", "comment": "At least a date should be present unless this observation is a historical report.  For recording imprecise or \"fuzzy\" times (For example, a blood glucose measurement taken \"after breakfast\") use the [Timing](http://hl7.org/fhir/datatypes.html#timing) datatype which allow the measurement to be tied to regular life events.", "requirements": "Knowing when an observation was deemed true is important to its relevance as well as determining trends.", "alias": ["Occurrence"], "min": 1, "max": "1", "base": {"path": "Observation.effective[x]", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-type-must-support", "valueBoolean": true}], "code": "dateTime"}, {"code": "Period"}], "condition": ["vs-1"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "vs-1", "severity": "error", "human": "if Observation.effective[x] is dateTime and has a value then that value shall be precise to the day", "expression": "($this as dateTime).toString().length() >= 8", "xpath": "f:effectiveDateTime[matches(@value, '^\\d{4}-\\d{2}-\\d{2}')]", "source": "http://hl7.org/fhir/StructureDefinition/vitalsigns"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Observation.issued", "path": "Observation.issued", "short": "Date/Time this version was made available", "definition": "The date and time this version of the observation was made available to providers, typically after the results have been reviewed and verified.", "comment": "For Observations that don’t require review and verification, it may be the same as the [`lastUpdated` ](http://hl7.org/fhir/resource-definitions.html#Meta.lastUpdated) time of the resource itself.  For Observations that do require review and verification for certain updates, it might not be the same as the `lastUpdated` time of the resource itself due to a non-clinically significant update that doesn’t require the new version to be reviewed and verified again.", "min": 0, "max": "1", "base": {"path": "Observation.issued", "min": 0, "max": "1"}, "type": [{"code": "instant"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Observation.performer", "path": "Observation.performer", "short": "Who is responsible for the observation", "definition": "Who was responsible for asserting the observed value as \"true\".", "requirements": "May give a degree of confidence in the observation and also indicates where follow-up questions should be directed.", "min": 0, "max": "*", "base": {"path": "Observation.performer", "min": 0, "max": "*"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Practitioner", "http://hl7.org/fhir/StructureDefinition/PractitionerRole", "http://hl7.org/fhir/StructureDefinition/Organization", "http://hl7.org/fhir/StructureDefinition/CareTeam", "http://hl7.org/fhir/StructureDefinition/Patient", "http://hl7.org/fhir/StructureDefinition/RelatedPerson"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Observation.value[x]", "path": "Observation.value[x]", "short": "Vital Signs Value", "definition": "Vital Signs value are typically recorded using the Quantity data type.", "comment": "An observation may have; 1)  a single value here, 2)  both a value and a set of related or component values,  or 3)  only a set of related or component values. If a value is present, the datatype for this element should be determined by Observation.code.  A CodeableConcept with just a text would be used instead of a string if the field was usually coded, or if the type associated with the Observation.code defines a coded value.  For additional guidance, see the [Notes section](http://hl7.org/fhir/observation.html#notes) below.", "requirements": "9. SHALL contain exactly one [1..1] value with @xsi:type=\"PQ\" (CONF:7305).", "min": 0, "max": "1", "base": {"path": "Observation.value[x]", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-type-must-support", "valueBoolean": true}], "code": "Quantity"}, {"code": "CodeableConcept"}, {"code": "string"}, {"code": "boolean"}, {"code": "integer"}, {"code": "Range"}, {"code": "<PERSON><PERSON>"}, {"code": "SampledData"}, {"code": "time"}, {"code": "dateTime"}, {"code": "Period"}], "condition": ["obs-7", "vs-2"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true, "binding": {"strength": "extensible", "description": "Common UCUM units for recording Vital Signs.", "valueSet": "http://hl7.org/fhir/ValueSet/ucum-vitals-common|4.0.1"}}, {"id": "Observation.dataAbsentReason", "path": "Observation.dataAbsentReason", "short": "Why the result is missing", "definition": "Provides a reason why the expected value in the element Observation.value[x] is missing.", "comment": "Null or exceptional values can be represented two ways in FHIR Observations.  One way is to simply include them in the value set and represent the exceptions in the value.  For example, measurement values for a serology test could be  \"detected\", \"not detected\", \"inconclusive\", or  \"specimen unsatisfactory\".   \n\nThe alternate way is to use the value element for actual observations and use the explicit dataAbsentReason element to record exceptional values.  For example, the dataAbsentReason code \"error\" could be used when the measurement was not completed. Note that an observation may only be reported if there are values to report. For example differential cell counts values may be reported only when > 0.  Because of these options, use-case agreements are required to interpret general observations for null or exceptional values.", "requirements": "For many results it is necessary to handle exceptional values in measurements.", "min": 0, "max": "1", "base": {"path": "Observation.dataAbsentReason", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "condition": ["obs-6", "vs-2"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "ObservationValueAbsentReason"}], "strength": "extensible", "description": "Codes specifying why the result (`Observation.value[x]`) is missing.", "valueSet": "http://hl7.org/fhir/ValueSet/data-absent-reason"}}, {"id": "Observation.interpretation", "path": "Observation.interpretation", "short": "High, low, normal, etc.", "definition": "A categorical assessment of an observation value.  For example, high, low, normal.", "comment": "Historically used for laboratory results (known as 'abnormal flag' ),  its use extends to other use cases where coded interpretations  are relevant.  Often reported as one or more simple compact codes this element is often placed adjacent to the result value in reports and flow sheets to signal the meaning/normalcy status of the result.", "requirements": "For some results, particularly numeric results, an interpretation is necessary to fully understand the significance of a result.", "alias": ["Abnormal Flag"], "min": 0, "max": "*", "base": {"path": "Observation.interpretation", "min": 0, "max": "*"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "ObservationInterpretation"}], "strength": "extensible", "description": "Codes identifying interpretations of observations.", "valueSet": "http://hl7.org/fhir/ValueSet/observation-interpretation"}}, {"id": "Observation.note", "path": "Observation.note", "short": "Comments about the observation", "definition": "Comments about the observation or the results.", "comment": "May include general statements about the observation, or statements about significant, unexpected or unreliable results values, or information about its source when relevant to its interpretation.", "requirements": "Need to be able to provide free text additional information.", "min": 0, "max": "*", "base": {"path": "Observation.note", "min": 0, "max": "*"}, "type": [{"code": "Annotation"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Observation.bodySite", "path": "Observation.bodySite", "short": "Observed body part", "definition": "Indicates the site on the subject's body where the observation was made (i.e. the target site).", "comment": "Only used if not implicit in code found in Observation.code.  In many systems, this may be represented as a related observation instead of an inline component.   \n\nIf the use case requires BodySite to be handled as a separate resource (e.g. to identify and track separately) then use the standard extension[ bodySite](http://hl7.org/fhir/extension-bodysite.html).", "min": 0, "max": "1", "base": {"path": "Observation.bodySite", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "BodySite"}], "strength": "example", "description": "Codes describing anatomical locations. May include laterality.", "valueSet": "http://hl7.org/fhir/ValueSet/body-site"}}, {"id": "Observation.method", "path": "Observation.method", "short": "How it was done", "definition": "Indicates the mechanism used to perform the observation.", "comment": "Only used if not implicit in code for Observation.code.", "requirements": "In some cases, method can impact results and is thus used for determining whether results can be compared or determining significance of results.", "min": 0, "max": "1", "base": {"path": "Observation.method", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "ObservationMethod"}], "strength": "example", "description": "Methods for simple observations.", "valueSet": "http://hl7.org/fhir/ValueSet/observation-methods"}}, {"id": "Observation.specimen", "path": "Observation.specimen", "short": "Specimen used for this observation", "definition": "The specimen that was used when this observation was made.", "comment": "Should only be used if not implicit in code found in `Observation.code`.  Observations are not made on specimens themselves; they are made on a subject, but in many cases by the means of a specimen. Note that although specimens are often involved, they are not always tracked and reported explicitly. Also note that observation resources may be used in contexts that track the specimen explicitly (e.g. Diagnostic Report).", "min": 0, "max": "1", "base": {"path": "Observation.specimen", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Specimen"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Observation.device", "path": "Observation.device", "short": "(Measurement) Device", "definition": "The device used to generate the observation data.", "comment": "Note that this is not meant to represent a device involved in the transmission of the result, e.g., a gateway.  Such devices may be documented using the Provenance resource where relevant.", "min": 0, "max": "1", "base": {"path": "Observation.device", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Device", "http://hl7.org/fhir/StructureDefinition/DeviceMetric"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Observation.referenceRange", "path": "Observation.referenceRange", "short": "Provides guide for interpretation", "definition": "Guidance on how to interpret the value by comparison to a normal or recommended range.  Multiple reference ranges are interpreted as an \"OR\".   In other words, to represent two distinct target populations, two `referenceRange` elements would be used.", "comment": "Most observations only have one generic reference range. Systems MAY choose to restrict to only supplying the relevant reference range based on knowledge about the patient (e.g., specific to the patient's age, gender, weight and other factors), but this might not be possible or appropriate. Whenever more than one reference range is supplied, the differences between them SHOULD be provided in the reference range and/or age properties.", "requirements": "Knowing what values are considered \"normal\" can help evaluate the significance of a particular result. Need to be able to provide multiple reference ranges for different contexts.", "min": 0, "max": "*", "base": {"path": "Observation.referenceRange", "min": 0, "max": "*"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "obs-3", "severity": "error", "human": "Must have at least a low or a high or text", "expression": "low.exists() or high.exists() or text.exists()", "xpath": "(exists(f:low) or exists(f:high)or exists(f:text))", "source": "http://hl7.org/fhir/StructureDefinition/vitalsigns"}], "isModifier": false, "isSummary": false}, {"id": "Observation.referenceRange.id", "path": "Observation.referenceRange.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Observation.referenceRange.extension", "path": "Observation.referenceRange.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Observation.referenceRange.modifierExtension", "path": "Observation.referenceRange.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true}, {"id": "Observation.referenceRange.low", "path": "Observation.referenceRange.low", "short": "Low Range, if relevant", "definition": "The value of the low bound of the reference range.  The low bound of the reference range endpoint is inclusive of the value (e.g.  reference range is >=5 - <=9). If the low bound is omitted,  it is assumed to be meaningless (e.g. reference range is <=2.3).", "min": 0, "max": "1", "base": {"path": "Observation.referenceRange.low", "min": 0, "max": "1"}, "type": [{"code": "Quantity", "profile": ["http://hl7.org/fhir/StructureDefinition/SimpleQuantity"]}], "condition": ["obs-3"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Observation.referenceRange.high", "path": "Observation.referenceRange.high", "short": "High Range, if relevant", "definition": "The value of the high bound of the reference range.  The high bound of the reference range endpoint is inclusive of the value (e.g.  reference range is >=5 - <=9). If the high bound is omitted,  it is assumed to be meaningless (e.g. reference range is >= 2.3).", "min": 0, "max": "1", "base": {"path": "Observation.referenceRange.high", "min": 0, "max": "1"}, "type": [{"code": "Quantity", "profile": ["http://hl7.org/fhir/StructureDefinition/SimpleQuantity"]}], "condition": ["obs-3"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Observation.referenceRange.type", "path": "Observation.referenceRange.type", "short": "Reference range qualifier", "definition": "Codes to indicate the what part of the targeted reference population it applies to. For example, the normal or therapeutic range.", "comment": "This SHOULD be populated if there is more than one range.  If this element is not present then the normal range is assumed.", "requirements": "Need to be able to say what kind of reference range this is - normal, recommended, therapeutic, etc.,  - for proper interpretation.", "min": 0, "max": "1", "base": {"path": "Observation.referenceRange.type", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "ObservationRangeMeaning"}], "strength": "preferred", "description": "Code for the meaning of a reference range.", "valueSet": "http://hl7.org/fhir/ValueSet/referencerange-meaning"}}, {"id": "Observation.referenceRange.appliesTo", "path": "Observation.referenceRange.appliesTo", "short": "Reference range population", "definition": "Codes to indicate the target population this reference range applies to.  For example, a reference range may be based on the normal population or a particular sex or race.  Multiple `appliesTo`  are interpreted as an \"AND\" of the target populations.  For example, to represent a target population of African American females, both a code of female and a code for African American would be used.", "comment": "This SHOULD be populated if there is more than one range.  If this element is not present then the normal population is assumed.", "requirements": "Need to be able to identify the target population for proper interpretation.", "min": 0, "max": "*", "base": {"path": "Observation.referenceRange.appliesTo", "min": 0, "max": "*"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "ObservationRangeType"}], "strength": "example", "description": "Codes identifying the population the reference range applies to.", "valueSet": "http://hl7.org/fhir/ValueSet/referencerange-appliesto"}}, {"id": "Observation.referenceRange.age", "path": "Observation.referenceRange.age", "short": "Applicable age range, if relevant", "definition": "The age at which this reference range is applicable. This is a neonatal age (e.g. number of weeks at term) if the meaning says so.", "requirements": "Some analytes vary greatly over age.", "min": 0, "max": "1", "base": {"path": "Observation.referenceRange.age", "min": 0, "max": "1"}, "type": [{"code": "Range"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Observation.referenceRange.text", "path": "Observation.referenceRange.text", "short": "Text based reference range in an observation", "definition": "Text based reference range in an observation which may be used when a quantitative range is not appropriate for an observation.  An example would be a reference value of \"Negative\" or a list or table of \"normals\".", "min": 0, "max": "1", "base": {"path": "Observation.referenceRange.text", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Observation.hasMember", "path": "Observation.hasMember", "short": "Used when reporting vital signs panel components", "definition": "Used when reporting vital signs panel components.", "comment": "When using this element, an observation will typically have either a value or a set of related resources, although both may be present in some cases.  For a discussion on the ways Observations can assembled in groups together, see [Notes](http://hl7.org/fhir/observation.html#obsgrouping) below.  Note that a system may calculate results from [QuestionnaireResponse](http://hl7.org/fhir/questionnaireresponse.html)  into a final score and represent the score as an Observation.", "min": 0, "max": "*", "base": {"path": "Observation.hasMember", "min": 0, "max": "*"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/QuestionnaireResponse", "http://hl7.org/fhir/StructureDefinition/MolecularSequence", "http://hl7.org/fhir/StructureDefinition/vitalsigns"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Observation.derivedFrom", "path": "Observation.derivedFrom", "short": "Related measurements the observation is made from", "definition": "The target resource that represents a measurement from which this observation value is derived. For example, a calculated anion gap or a fetal measurement based on an ultrasound image.", "comment": "All the reference choices that are listed in this element can represent clinical observations and other measurements that may be the source for a derived value.  The most common reference will be another Observation.  For a discussion on the ways Observations can assembled in groups together, see [Notes](http://hl7.org/fhir/observation.html#obsgrouping) below.", "min": 0, "max": "*", "base": {"path": "Observation.derivedFrom", "min": 0, "max": "*"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/DocumentReference", "http://hl7.org/fhir/StructureDefinition/ImagingStudy", "http://hl7.org/fhir/StructureDefinition/Media", "http://hl7.org/fhir/StructureDefinition/QuestionnaireResponse", "http://hl7.org/fhir/StructureDefinition/MolecularSequence", "http://hl7.org/fhir/StructureDefinition/vitalsigns"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "Observation.component", "path": "Observation.component", "slicing": {"discriminator": [{"type": "pattern", "path": "code"}], "ordered": false, "rules": "open"}, "short": "Component observations", "definition": "Used when reporting component observation such as systolic and diastolic blood pressure.", "comment": "For a discussion on the ways Observations can be assembled in groups together see [Notes](http://hl7.org/fhir/observation.html#notes) below.", "requirements": "Component observations share the same attributes in the Observation resource as the primary observation and are always treated a part of a single observation (they are not separable).   However, the reference range for the primary observation value is not inherited by the component values and is required when appropriate for each component observation.", "min": 2, "max": "*", "base": {"path": "Observation.component", "min": 0, "max": "*"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "vs-3", "severity": "error", "human": "If there is no a value a data absent reason must be present", "expression": "value.exists() or dataAbsentReason.exists()", "xpath": "f:*[starts-with(local-name(.), 'value')] or f:dataAbsentReason", "source": "http://hl7.org/fhir/StructureDefinition/vitalsigns"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Observation.component.id", "path": "Observation.component.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Observation.component.extension", "path": "Observation.component.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Observation.component.modifierExtension", "path": "Observation.component.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true}, {"id": "Observation.component.code", "path": "Observation.component.code", "short": "Type of component observation (code / type)", "definition": "Describes what was observed. Sometimes this is called the observation \"code\".", "comment": "*All* code-value and  component.code-component.value pairs need to be taken into account to correctly understand the meaning of the observation.", "requirements": "Knowing what kind of observation is being made is essential to understanding the observation.", "min": 1, "max": "1", "base": {"path": "Observation.component.code", "min": 1, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true, "binding": {"strength": "extensible", "description": "The vital sign codes from the base FHIR and US Core Vital Signs.", "valueSet": "http://hl7.org/fhir/us/core/ValueSet/us-core-vital-signs"}}, {"id": "Observation.component.value[x]", "path": "Observation.component.value[x]", "short": "Vital Sign Component Value", "definition": "Vital Signs value are typically recorded using the Quantity data type. For supporting observations such as cuff size could use other datatypes such as CodeableConcept.", "comment": "Used when observation has a set of component observations. An observation may have both a value (e.g. an  Apgar score)  and component observations (the observations from which the Apgar score was derived). If a value is present, the datatype for this element should be determined by Observation.code. A CodeableConcept with just a text would be used instead of a string if the field was usually coded, or if the type associated with the Observation.code defines a coded value.  For additional guidance, see the [Notes section](http://hl7.org/fhir/observation.html#notes) below.", "requirements": "9. SHALL contain exactly one [1..1] value with @xsi:type=\"PQ\" (CONF:7305).", "min": 0, "max": "1", "base": {"path": "Observation.component.value[x]", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-type-must-support", "valueBoolean": true}], "code": "Quantity"}, {"code": "CodeableConcept"}, {"code": "string"}, {"code": "boolean"}, {"code": "integer"}, {"code": "Range"}, {"code": "<PERSON><PERSON>"}, {"code": "SampledData"}, {"code": "time"}, {"code": "dateTime"}, {"code": "Period"}], "condition": ["vs-3"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true, "binding": {"strength": "extensible", "description": "Common UCUM units for recording Vital Signs.", "valueSet": "http://hl7.org/fhir/ValueSet/ucum-vitals-common|4.0.1"}}, {"id": "Observation.component.dataAbsentReason", "path": "Observation.component.dataAbsentReason", "short": "Why the component result is missing", "definition": "Provides a reason why the expected value in the element Observation.component.value[x] is missing.", "comment": "\"Null\" or exceptional values can be represented two ways in FHIR Observations.  One way is to simply include them in the value set and represent the exceptions in the value.  For example, measurement values for a serology test could be  \"detected\", \"not detected\", \"inconclusive\", or  \"test not done\". \n\nThe alternate way is to use the value element for actual observations and use the explicit dataAbsentReason element to record exceptional values.  For example, the dataAbsentReason code \"error\" could be used when the measurement was not completed.  Because of these options, use-case agreements are required to interpret general observations for exceptional values.", "requirements": "For many results it is necessary to handle exceptional values in measurements.", "min": 0, "max": "1", "base": {"path": "Observation.component.dataAbsentReason", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "condition": ["obs-6", "vs-3"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "ObservationValueAbsentReason"}], "strength": "extensible", "description": "Codes specifying why the result (`Observation.value[x]`) is missing.", "valueSet": "http://hl7.org/fhir/ValueSet/data-absent-reason"}}, {"id": "Observation.component.interpretation", "path": "Observation.component.interpretation", "short": "High, low, normal, etc.", "definition": "A categorical assessment of an observation value.  For example, high, low, normal.", "comment": "Historically used for laboratory results (known as 'abnormal flag' ),  its use extends to other use cases where coded interpretations  are relevant.  Often reported as one or more simple compact codes this element is often placed adjacent to the result value in reports and flow sheets to signal the meaning/normalcy status of the result.", "requirements": "For some results, particularly numeric results, an interpretation is necessary to fully understand the significance of a result.", "alias": ["Abnormal Flag"], "min": 0, "max": "*", "base": {"path": "Observation.component.interpretation", "min": 0, "max": "*"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "ObservationInterpretation"}], "strength": "extensible", "description": "Codes identifying interpretations of observations.", "valueSet": "http://hl7.org/fhir/ValueSet/observation-interpretation"}}, {"id": "Observation.component.referenceRange", "path": "Observation.component.referenceRange", "short": "Provides guide for interpretation of component result", "definition": "Guidance on how to interpret the value by comparison to a normal or recommended range.", "comment": "Most observations only have one generic reference range. Systems MAY choose to restrict to only supplying the relevant reference range based on knowledge about the patient (e.g., specific to the patient's age, gender, weight and other factors), but this might not be possible or appropriate. Whenever more than one reference range is supplied, the differences between them SHOULD be provided in the reference range and/or age properties.", "requirements": "Knowing what values are considered \"normal\" can help evaluate the significance of a particular result. Need to be able to provide multiple reference ranges for different contexts.", "min": 0, "max": "*", "base": {"path": "Observation.component.referenceRange", "min": 0, "max": "*"}, "contentReference": "http://hl7.org/fhir/StructureDefinition/Observation#Observation.referenceRange", "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Observation.component:systolic", "path": "Observation.component", "sliceName": "systolic", "short": "Systolic Blood Pressure", "definition": "Used when reporting component observation such as systolic and diastolic blood pressure.", "comment": "For a discussion on the ways Observations can be assembled in groups together see [Notes](http://hl7.org/fhir/observation.html#notes) below.", "requirements": "Component observations share the same attributes in the Observation resource as the primary observation and are always treated a part of a single observation (they are not separable).   However, the reference range for the primary observation value is not inherited by the component values and is required when appropriate for each component observation.", "min": 1, "max": "1", "base": {"path": "Observation.component", "min": 0, "max": "*"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "vs-3", "severity": "error", "human": "If there is no a value a data absent reason must be present", "expression": "value.exists() or dataAbsentReason.exists()", "xpath": "f:*[starts-with(local-name(.), 'value')] or f:dataAbsentReason", "source": "http://hl7.org/fhir/StructureDefinition/vitalsigns"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Observation.component:systolic.id", "path": "Observation.component.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Observation.component:systolic.extension", "path": "Observation.component.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Observation.component:systolic.modifierExtension", "path": "Observation.component.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true}, {"id": "Observation.component:systolic.code", "path": "Observation.component.code", "short": "Systolic Blood Pressure Code", "definition": "Describes what was observed. Sometimes this is called the observation \"code\".", "comment": "*All* code-value and  component.code-component.value pairs need to be taken into account to correctly understand the meaning of the observation.", "requirements": "Knowing what kind of observation is being made is essential to understanding the observation.", "min": 1, "max": "1", "base": {"path": "Observation.component.code", "min": 1, "max": "1"}, "type": [{"code": "CodeableConcept"}], "patternCodeableConcept": {"coding": [{"system": "http://loinc.org", "code": "8480-6"}]}, "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true, "binding": {"strength": "extensible", "description": "The vital sign codes from the base FHIR and US Core Vital Signs.", "valueSet": "http://hl7.org/fhir/us/core/ValueSet/us-core-vital-signs"}}, {"id": "Observation.component:systolic.value[x]", "path": "Observation.component.value[x]", "short": "Vital Sign Component Value", "definition": "Vital Signs value are typically recorded using the Quantity data type. For supporting observations such as cuff size could use other datatypes such as CodeableConcept.", "comment": "Used when observation has a set of component observations. An observation may have both a value (e.g. an  Apgar score)  and component observations (the observations from which the Apgar score was derived). If a value is present, the datatype for this element should be determined by Observation.code. A CodeableConcept with just a text would be used instead of a string if the field was usually coded, or if the type associated with the Observation.code defines a coded value.  For additional guidance, see the [Notes section](http://hl7.org/fhir/observation.html#notes) below.", "requirements": "9. SHALL contain exactly one [1..1] value with @xsi:type=\"PQ\" (CONF:7305).", "min": 0, "max": "1", "base": {"path": "Observation.component.value[x]", "min": 0, "max": "1"}, "type": [{"code": "Quantity"}], "condition": ["vs-3"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true, "binding": {"strength": "extensible", "description": "Common UCUM units for recording Vital Signs.", "valueSet": "http://hl7.org/fhir/ValueSet/ucum-vitals-common|4.0.1"}}, {"id": "Observation.component:systolic.value[x].id", "path": "Observation.component.value[x].id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Observation.component:systolic.value[x].extension", "path": "Observation.component.value[x].extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Observation.component:systolic.value[x].value", "path": "Observation.component.value[x].value", "short": "Numerical value (with implicit precision)", "definition": "The value of the measured amount. The value includes an implicit precision in the presentation of the value.", "comment": "The implicit precision in the value should always be honored. Monetary values have their own rules for handling precision (refer to standard accounting text books).", "requirements": "Precision is handled implicitly in almost all cases of measurement.", "min": 1, "max": "1", "base": {"path": "Quantity.value", "min": 0, "max": "1"}, "type": [{"code": "decimal"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Observation.component:systolic.value[x].comparator", "path": "Observation.component.value[x].comparator", "short": "< | <= | >= | > - how to understand the value", "definition": "How the value should be understood and represented - whether the actual value is greater or less than the stated value due to measurement issues; e.g. if the comparator is \"<\" , then the real value is < stated value.", "requirements": "Need a framework for handling measures where the value is <5ug/L or >400mg/L due to the limitations of measuring methodology.", "min": 0, "max": "1", "base": {"path": "Quantity.comparator", "min": 0, "max": "1"}, "type": [{"code": "code"}], "meaningWhenMissing": "If there is no comparator, then there is no modification of the value", "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": true, "isModifierReason": "This is labeled as \"Is Modifier\" because the comparator modifies the interpretation of the value significantly. If there is no comparator, then there is no modification of the value", "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "QuantityComparator"}], "strength": "required", "description": "How the Quantity should be understood and represented.", "valueSet": "http://hl7.org/fhir/ValueSet/quantity-comparator|4.0.1"}}, {"id": "Observation.component:systolic.value[x].unit", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-translatable", "valueBoolean": true}], "path": "Observation.component.value[x].unit", "short": "Unit representation", "definition": "A human-readable form of the unit.", "requirements": "There are many representations for units of measure and in many contexts, particular representations are fixed and required. I.e. mcg for micrograms.", "min": 1, "max": "1", "base": {"path": "Quantity.unit", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Observation.component:systolic.value[x].system", "path": "Observation.component.value[x].system", "short": "System that defines coded unit form", "definition": "The identification of the system that provides the coded form of the unit.", "requirements": "Need to know the system that defines the coded form of the unit.", "min": 1, "max": "1", "base": {"path": "Quantity.system", "min": 0, "max": "1"}, "type": [{"code": "uri"}], "fixedUri": "http://unitsofmeasure.org", "condition": ["qty-3"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Observation.component:systolic.value[x].code", "path": "Observation.component.value[x].code", "short": "Coded form of the unit", "definition": "A computer processable form of the unit in some unit representation system.", "comment": "The preferred system is UCUM, but SNOMED CT can also be used (for customary units) or ISO 4217 for currency.  The context of use may additionally require a code from a particular system.", "requirements": "Need a computable form of the unit that is fixed across all forms. UCUM provides this for quantities, but SNOMED CT provides many units of interest.", "min": 1, "max": "1", "base": {"path": "Quantity.code", "min": 0, "max": "1"}, "type": [{"code": "code"}], "fixedCode": "mm[Hg]", "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Observation.component:systolic.dataAbsentReason", "path": "Observation.component.dataAbsentReason", "short": "Why the component result is missing", "definition": "Provides a reason why the expected value in the element Observation.component.value[x] is missing.", "comment": "\"Null\" or exceptional values can be represented two ways in FHIR Observations.  One way is to simply include them in the value set and represent the exceptions in the value.  For example, measurement values for a serology test could be  \"detected\", \"not detected\", \"inconclusive\", or  \"test not done\". \n\nThe alternate way is to use the value element for actual observations and use the explicit dataAbsentReason element to record exceptional values.  For example, the dataAbsentReason code \"error\" could be used when the measurement was not completed.  Because of these options, use-case agreements are required to interpret general observations for exceptional values.", "requirements": "For many results it is necessary to handle exceptional values in measurements.", "min": 0, "max": "1", "base": {"path": "Observation.component.dataAbsentReason", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "condition": ["obs-6", "vs-3"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "ObservationValueAbsentReason"}], "strength": "extensible", "description": "Codes specifying why the result (`Observation.value[x]`) is missing.", "valueSet": "http://hl7.org/fhir/ValueSet/data-absent-reason"}}, {"id": "Observation.component:systolic.interpretation", "path": "Observation.component.interpretation", "short": "High, low, normal, etc.", "definition": "A categorical assessment of an observation value.  For example, high, low, normal.", "comment": "Historically used for laboratory results (known as 'abnormal flag' ),  its use extends to other use cases where coded interpretations  are relevant.  Often reported as one or more simple compact codes this element is often placed adjacent to the result value in reports and flow sheets to signal the meaning/normalcy status of the result.", "requirements": "For some results, particularly numeric results, an interpretation is necessary to fully understand the significance of a result.", "alias": ["Abnormal Flag"], "min": 0, "max": "*", "base": {"path": "Observation.component.interpretation", "min": 0, "max": "*"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "ObservationInterpretation"}], "strength": "extensible", "description": "Codes identifying interpretations of observations.", "valueSet": "http://hl7.org/fhir/ValueSet/observation-interpretation"}}, {"id": "Observation.component:systolic.referenceRange", "path": "Observation.component.referenceRange", "short": "Provides guide for interpretation of component result", "definition": "Guidance on how to interpret the value by comparison to a normal or recommended range.", "comment": "Most observations only have one generic reference range. Systems MAY choose to restrict to only supplying the relevant reference range based on knowledge about the patient (e.g., specific to the patient's age, gender, weight and other factors), but this might not be possible or appropriate. Whenever more than one reference range is supplied, the differences between them SHOULD be provided in the reference range and/or age properties.", "requirements": "Knowing what values are considered \"normal\" can help evaluate the significance of a particular result. Need to be able to provide multiple reference ranges for different contexts.", "min": 0, "max": "*", "base": {"path": "Observation.component.referenceRange", "min": 0, "max": "*"}, "contentReference": "http://hl7.org/fhir/StructureDefinition/Observation#Observation.referenceRange", "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "Observation.component:diastolic", "path": "Observation.component", "sliceName": "diastolic", "short": "Diastolic Blood Pressure", "definition": "Used when reporting component observation such as systolic and diastolic blood pressure.", "comment": "For a discussion on the ways Observations can be assembled in groups together see [Notes](http://hl7.org/fhir/observation.html#notes) below.", "requirements": "Component observations share the same attributes in the Observation resource as the primary observation and are always treated a part of a single observation (they are not separable).   However, the reference range for the primary observation value is not inherited by the component values and is required when appropriate for each component observation.", "min": 1, "max": "1", "base": {"path": "Observation.component", "min": 0, "max": "*"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "vs-3", "severity": "error", "human": "If there is no a value a data absent reason must be present", "expression": "value.exists() or dataAbsentReason.exists()", "xpath": "f:*[starts-with(local-name(.), 'value')] or f:dataAbsentReason", "source": "http://hl7.org/fhir/StructureDefinition/vitalsigns"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Observation.component:diastolic.id", "path": "Observation.component.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Observation.component:diastolic.extension", "path": "Observation.component.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Observation.component:diastolic.modifierExtension", "path": "Observation.component.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true}, {"id": "Observation.component:diastolic.code", "path": "Observation.component.code", "short": "Diastolic Blood Pressure Code", "definition": "Describes what was observed. Sometimes this is called the observation \"code\".", "comment": "*All* code-value and  component.code-component.value pairs need to be taken into account to correctly understand the meaning of the observation.", "requirements": "Knowing what kind of observation is being made is essential to understanding the observation.", "min": 1, "max": "1", "base": {"path": "Observation.component.code", "min": 1, "max": "1"}, "type": [{"code": "CodeableConcept"}], "patternCodeableConcept": {"coding": [{"system": "http://loinc.org", "code": "8462-4"}]}, "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true, "binding": {"strength": "extensible", "description": "The vital sign codes from the base FHIR and US Core Vital Signs.", "valueSet": "http://hl7.org/fhir/us/core/ValueSet/us-core-vital-signs"}}, {"id": "Observation.component:diastolic.value[x]", "path": "Observation.component.value[x]", "short": "Vital Sign Component Value", "definition": "Vital Signs value are typically recorded using the Quantity data type. For supporting observations such as cuff size could use other datatypes such as CodeableConcept.", "comment": "Used when observation has a set of component observations. An observation may have both a value (e.g. an  Apgar score)  and component observations (the observations from which the Apgar score was derived). If a value is present, the datatype for this element should be determined by Observation.code. A CodeableConcept with just a text would be used instead of a string if the field was usually coded, or if the type associated with the Observation.code defines a coded value.  For additional guidance, see the [Notes section](http://hl7.org/fhir/observation.html#notes) below.", "requirements": "9. SHALL contain exactly one [1..1] value with @xsi:type=\"PQ\" (CONF:7305).", "min": 0, "max": "1", "base": {"path": "Observation.component.value[x]", "min": 0, "max": "1"}, "type": [{"code": "Quantity"}], "condition": ["vs-3"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true, "binding": {"strength": "extensible", "description": "Common UCUM units for recording Vital Signs.", "valueSet": "http://hl7.org/fhir/ValueSet/ucum-vitals-common|4.0.1"}}, {"id": "Observation.component:diastolic.value[x].id", "path": "Observation.component.value[x].id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "Observation.component:diastolic.value[x].extension", "path": "Observation.component.value[x].extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "Observation.component:diastolic.value[x].value", "path": "Observation.component.value[x].value", "short": "Numerical value (with implicit precision)", "definition": "The value of the measured amount. The value includes an implicit precision in the presentation of the value.", "comment": "The implicit precision in the value should always be honored. Monetary values have their own rules for handling precision (refer to standard accounting text books).", "requirements": "Precision is handled implicitly in almost all cases of measurement.", "min": 1, "max": "1", "base": {"path": "Quantity.value", "min": 0, "max": "1"}, "type": [{"code": "decimal"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Observation.component:diastolic.value[x].comparator", "path": "Observation.component.value[x].comparator", "short": "< | <= | >= | > - how to understand the value", "definition": "How the value should be understood and represented - whether the actual value is greater or less than the stated value due to measurement issues; e.g. if the comparator is \"<\" , then the real value is < stated value.", "requirements": "Need a framework for handling measures where the value is <5ug/L or >400mg/L due to the limitations of measuring methodology.", "min": 0, "max": "1", "base": {"path": "Quantity.comparator", "min": 0, "max": "1"}, "type": [{"code": "code"}], "meaningWhenMissing": "If there is no comparator, then there is no modification of the value", "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": true, "isModifierReason": "This is labeled as \"Is Modifier\" because the comparator modifies the interpretation of the value significantly. If there is no comparator, then there is no modification of the value", "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "QuantityComparator"}], "strength": "required", "description": "How the Quantity should be understood and represented.", "valueSet": "http://hl7.org/fhir/ValueSet/quantity-comparator|4.0.1"}}, {"id": "Observation.component:diastolic.value[x].unit", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-translatable", "valueBoolean": true}], "path": "Observation.component.value[x].unit", "short": "Unit representation", "definition": "A human-readable form of the unit.", "requirements": "There are many representations for units of measure and in many contexts, particular representations are fixed and required. I.e. mcg for micrograms.", "min": 1, "max": "1", "base": {"path": "Quantity.unit", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Observation.component:diastolic.value[x].system", "path": "Observation.component.value[x].system", "short": "System that defines coded unit form", "definition": "The identification of the system that provides the coded form of the unit.", "requirements": "Need to know the system that defines the coded form of the unit.", "min": 1, "max": "1", "base": {"path": "Quantity.system", "min": 0, "max": "1"}, "type": [{"code": "uri"}], "fixedUri": "http://unitsofmeasure.org", "condition": ["qty-3"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Observation.component:diastolic.value[x].code", "path": "Observation.component.value[x].code", "short": "Coded form of the unit", "definition": "A computer processable form of the unit in some unit representation system.", "comment": "The preferred system is UCUM, but SNOMED CT can also be used (for customary units) or ISO 4217 for currency.  The context of use may additionally require a code from a particular system.", "requirements": "Need a computable form of the unit that is fixed across all forms. UCUM provides this for quantities, but SNOMED CT provides many units of interest.", "min": 1, "max": "1", "base": {"path": "Quantity.code", "min": 0, "max": "1"}, "type": [{"code": "code"}], "fixedCode": "mm[Hg]", "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "Observation.component:diastolic.dataAbsentReason", "path": "Observation.component.dataAbsentReason", "short": "Why the component result is missing", "definition": "Provides a reason why the expected value in the element Observation.component.value[x] is missing.", "comment": "\"Null\" or exceptional values can be represented two ways in FHIR Observations.  One way is to simply include them in the value set and represent the exceptions in the value.  For example, measurement values for a serology test could be  \"detected\", \"not detected\", \"inconclusive\", or  \"test not done\". \n\nThe alternate way is to use the value element for actual observations and use the explicit dataAbsentReason element to record exceptional values.  For example, the dataAbsentReason code \"error\" could be used when the measurement was not completed.  Because of these options, use-case agreements are required to interpret general observations for exceptional values.", "requirements": "For many results it is necessary to handle exceptional values in measurements.", "min": 0, "max": "1", "base": {"path": "Observation.component.dataAbsentReason", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "condition": ["obs-6", "vs-3"], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "ObservationValueAbsentReason"}], "strength": "extensible", "description": "Codes specifying why the result (`Observation.value[x]`) is missing.", "valueSet": "http://hl7.org/fhir/ValueSet/data-absent-reason"}}, {"id": "Observation.component:diastolic.interpretation", "path": "Observation.component.interpretation", "short": "High, low, normal, etc.", "definition": "A categorical assessment of an observation value.  For example, high, low, normal.", "comment": "Historically used for laboratory results (known as 'abnormal flag' ),  its use extends to other use cases where coded interpretations  are relevant.  Often reported as one or more simple compact codes this element is often placed adjacent to the result value in reports and flow sheets to signal the meaning/normalcy status of the result.", "requirements": "For some results, particularly numeric results, an interpretation is necessary to fully understand the significance of a result.", "alias": ["Abnormal Flag"], "min": 0, "max": "*", "base": {"path": "Observation.component.interpretation", "min": 0, "max": "*"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "ObservationInterpretation"}], "strength": "extensible", "description": "Codes identifying interpretations of observations.", "valueSet": "http://hl7.org/fhir/ValueSet/observation-interpretation"}}, {"id": "Observation.component:diastolic.referenceRange", "path": "Observation.component.referenceRange", "short": "Provides guide for interpretation of component result", "definition": "Guidance on how to interpret the value by comparison to a normal or recommended range.", "comment": "Most observations only have one generic reference range. Systems MAY choose to restrict to only supplying the relevant reference range based on knowledge about the patient (e.g., specific to the patient's age, gender, weight and other factors), but this might not be possible or appropriate. Whenever more than one reference range is supplied, the differences between them SHOULD be provided in the reference range and/or age properties.", "requirements": "Knowing what values are considered \"normal\" can help evaluate the significance of a particular result. Need to be able to provide multiple reference ranges for different contexts.", "min": 0, "max": "*", "base": {"path": "Observation.component.referenceRange", "min": 0, "max": "*"}, "contentReference": "http://hl7.org/fhir/StructureDefinition/Observation#Observation.referenceRange", "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}]}}, {"resourceType": "StructureDefinition", "id": "us-core-medicationrequest", "url": "http://hl7.org/fhir/us/core/StructureDefinition/us-core-medicationrequest", "version": "5.0.1", "name": "USCoreMedicationRequestProfile", "title": "US Core MedicationRequest Profile", "status": "active", "experimental": false, "date": "2020-06-26", "publisher": "HL7 International - Cross-Group Projects", "contact": [{"name": "HL7 International - Cross-Group Projects", "telecom": [{"system": "url", "value": "http://www.hl7.org/Special/committees/cgp"}, {"system": "email", "value": "<EMAIL>"}]}], "description": "The US Core Medication Request Profile is based upon the core FHIR MedicationRequest Resource and meets the U.S. Core Data for Interoperability (USCDI) v2 'Medications' requirements. The MedicationRequest resource can be used to record a patient’s medication prescription or order.  This profile sets minimum expectations for the MedicationRequest resource to record, search, and fetch a patient's medication to promote interoperability and adoption through common implementation.  It identifies which core elements, extensions, vocabularies and value sets **SHALL** be present in the resource when using this profile.  It provides the floor for standards development for specific uses cases.", "jurisdiction": [{"coding": [{"system": "urn:iso:std:iso:3166", "code": "US"}]}], "copyright": "Used by permission of HL7 International, all rights reserved Creative Commons License", "fhirVersion": "4.0.1", "kind": "resource", "abstract": false, "type": "MedicationRequest", "baseDefinition": "http://hl7.org/fhir/StructureDefinition/MedicationRequest", "derivation": "constraint", "snapshot": {"element": [{"id": "MedicationRequest", "path": "MedicationRequest", "short": "Ordering of medication for patient or group", "definition": "\\-", "comment": "\\-", "alias": ["Prescription", "Order"], "min": 0, "max": "*", "base": {"path": "MedicationRequest", "min": 0, "max": "*"}, "constraint": [{"key": "dom-2", "severity": "error", "human": "If the resource is contained in another resource, it SHALL NOT contain nested Resources", "expression": "contained.contained.empty()", "xpath": "not(parent::f:contained and f:contained)", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"key": "dom-3", "severity": "error", "human": "If the resource is contained in another resource, it SHALL be referred to from elsewhere in the resource or SHALL refer to the containing resource", "expression": "contained.where((('#'+id in (%resource.descendants().reference | %resource.descendants().as(canonical) | %resource.descendants().as(uri) | %resource.descendants().as(url))) or descendants().where(reference = '#').exists() or descendants().where(as(canonical) = '#').exists() or descendants().where(as(canonical) = '#').exists()).not()).trace('unmatched', id).empty()", "xpath": "not(exists(for $id in f:contained/*/f:id/@value return $contained[not(parent::*/descendant::f:reference/@value=concat('#', $contained/*/id/@value) or descendant::f:reference[@value='#'])]))", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"key": "dom-4", "severity": "error", "human": "If a resource is contained in another resource, it SHALL NOT have a meta.versionId or a meta.lastUpdated", "expression": "contained.meta.versionId.empty() and contained.meta.lastUpdated.empty()", "xpath": "not(exists(f:contained/*/f:meta/f:versionId)) and not(exists(f:contained/*/f:meta/f:lastUpdated))", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"key": "dom-5", "severity": "error", "human": "If a resource is contained in another resource, it SHALL NOT have a security label", "expression": "contained.meta.security.empty()", "xpath": "not(exists(f:contained/*/f:meta/f:security))", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bestpractice", "valueBoolean": true}, {"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bestpractice-explanation", "valueMarkdown": "When a resource has no narrative, only systems that fully understand the data can display the resource to a human safely. Including a human readable representation in the resource makes for a much more robust eco-system and cheaper handling of resources by intermediary systems. Some ecosystems restrict distribution of resources to only those systems that do fully understand the resources, and as a consequence implementers may believe that the narrative is superfluous. However experience shows that such eco-systems often open up to new participants over time."}], "key": "dom-6", "severity": "warning", "human": "A resource should have narrative for robust management", "expression": "text.`div`.exists()", "xpath": "exists(f:text/h:div)", "source": "http://hl7.org/fhir/StructureDefinition/DomainResource"}], "mustSupport": false, "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.id", "path": "MedicationRequest.id", "short": "Logical id of this artifact", "definition": "The logical id of the resource, as used in the URL for the resource. Once assigned, this value never changes.", "comment": "The only time that a resource does not have an id is when it is being submitted to the server using a create operation.", "min": 0, "max": "1", "base": {"path": "Resource.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": true}, {"id": "MedicationRequest.meta", "path": "MedicationRequest.meta", "short": "Metadata about the resource", "definition": "The metadata about the resource. This is content that is maintained by the infrastructure. Changes to the content might not always be associated with version changes to the resource.", "min": 0, "max": "1", "base": {"path": "Resource.meta", "min": 0, "max": "1"}, "type": [{"code": "Meta"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "MedicationRequest.implicitRules", "path": "MedicationRequest.implicitRules", "short": "A set of rules under which this content was created", "definition": "A reference to a set of rules that were followed when the resource was constructed, and which must be understood when processing the content. Often, this is a reference to an implementation guide that defines the special rules along with other profiles etc.", "comment": "Asserting this rule set restricts the content to be only understood by a limited set of trading partners. This inherently limits the usefulness of the data in the long term. However, the existing health eco-system is highly fractured, and not yet ready to define, collect, and exchange data in a generally computable sense. Wherever possible, implementers and/or specification writers should avoid using this element. Often, when used, the URL is a reference to an implementation guide that defines these special rules as part of it's narrative along with other profiles, value sets, etc.", "min": 0, "max": "1", "base": {"path": "Resource.implicitRules", "min": 0, "max": "1"}, "type": [{"code": "uri"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": true, "isModifierReason": "This element is labeled as a modifier because the implicit rules may provide additional knowledge about the resource that modifies it's meaning or interpretation", "isSummary": true}, {"id": "MedicationRequest.language", "path": "MedicationRequest.language", "short": "Language of the resource content", "definition": "The base language in which the resource is written.", "comment": "Language is provided to support indexing and accessibility (typically, services such as text to speech use the language tag). The html language tag in the narrative applies  to the narrative. The language tag on the resource may be used to specify the language of other presentations generated from the data in the resource. Not all the content has to be in the base language. The Resource.language should not be assumed to apply to the narrative automatically. If a language is specified, it should it also be specified on the div element in the html (see rules in HTML5 for information about the relationship between xml:lang and the html lang attribute).", "min": 0, "max": "1", "base": {"path": "Resource.language", "min": 0, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-maxValueSet", "valueCanonical": "http://hl7.org/fhir/ValueSet/all-languages"}, {"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "Language"}, {"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-isCommonBinding", "valueBoolean": true}], "strength": "preferred", "description": "A human language.", "valueSet": "http://hl7.org/fhir/ValueSet/languages"}}, {"id": "MedicationRequest.text", "path": "MedicationRequest.text", "short": "Text summary of the resource, for human interpretation", "definition": "A human-readable narrative that contains a summary of the resource and can be used to represent the content of the resource to a human. The narrative need not encode all the structured data, but is required to contain sufficient detail to make it \"clinically safe\" for a human to just read the narrative. Resource definitions may define what content should be represented in the narrative to ensure clinical safety.", "comment": "Contained resources do not have narrative. Resources that are not contained SHOULD have a narrative. In some cases, a resource may only have text with little or no additional discrete data (as long as all minOccurs=1 elements are satisfied).  This may be necessary for data from legacy systems where information is captured as a \"text blob\" or where text is additionally entered raw or narrated and encoded information is added later.", "alias": ["narrative", "html", "xhtml", "display"], "min": 0, "max": "1", "base": {"path": "DomainResource.text", "min": 0, "max": "1"}, "type": [{"code": "Narrative"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.contained", "path": "MedicationRequest.contained", "short": "Contained, inline Resources", "definition": "These resources do not have an independent existence apart from the resource that contains them - they cannot be identified independently, and nor can they have their own independent transaction scope.", "comment": "This should never be done when the content can be identified properly, as once identification is lost, it is extremely difficult (and context dependent) to restore it again. Contained resources may have profiles and tags In their meta elements, but SHALL NOT have security labels.", "alias": ["inline resources", "anonymous resources", "contained resources"], "min": 0, "max": "*", "base": {"path": "DomainResource.contained", "min": 0, "max": "*"}, "type": [{"code": "Resource"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.extension", "path": "MedicationRequest.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the resource. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "DomainResource.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.modifierExtension", "path": "MedicationRequest.modifierExtension", "short": "Extensions that cannot be ignored", "definition": "May be used to represent additional information that is not part of the basic definition of the resource and that modifies the understanding of the element that contains it and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer is allowed to define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R4/extensibility.html#modifierExtension).", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "DomainResource.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the resource that contains them", "isSummary": false}, {"id": "MedicationRequest.identifier", "path": "MedicationRequest.identifier", "short": "External ids for this request", "definition": "Identifiers associated with this medication request that are defined by business processes and/or used to refer to it when a direct URL reference to the resource itself is not appropriate. They are business identifiers assigned to this resource by the performer or other systems and remain constant as the resource is updated and propagates from server to server.", "comment": "This is a business identifier, not a resource identifier.", "min": 0, "max": "*", "base": {"path": "MedicationRequest.identifier", "min": 0, "max": "*"}, "type": [{"code": "Identifier"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.status", "path": "MedicationRequest.status", "short": "active | on-hold | cancelled | completed | entered-in-error | stopped | draft | unknown", "definition": "A code specifying the current state of the order.  Generally, this will be active or completed state.", "comment": "This element is labeled as a modifier because the status contains codes that mark the resource as not currently valid.", "min": 1, "max": "1", "base": {"path": "MedicationRequest.status", "min": 1, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": true, "isModifierReason": "This element is labeled as a modifier because it is a status element that contains status entered-in-error which means that the resource should not be treated as valid", "isSummary": true, "binding": {"strength": "required", "description": "A code specifying the state of the prescribing event. Describes the lifecycle of the prescription.", "valueSet": "http://hl7.org/fhir/ValueSet/medicationrequest-status"}}, {"id": "MedicationRequest.statusReason", "path": "MedicationRequest.statusReason", "short": "Reason for current status", "definition": "Captures the reason for the current state of the MedicationRequest.", "comment": "This is generally only used for \"exception\" statuses such as \"suspended\" or \"cancelled\". The reason why the MedicationRequest was created at all is captured in reasonCode, not here.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.statusReason", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "MedicationRequestStatusReason"}], "strength": "example", "description": "Identifies the reasons for a given status.", "valueSet": "http://hl7.org/fhir/ValueSet/medicationrequest-status-reason"}}, {"id": "MedicationRequest.intent", "path": "MedicationRequest.intent", "short": "proposal | plan | order | original-order | reflex-order | filler-order | instance-order | option", "definition": "Whether the request is a proposal, plan, or an original order.", "comment": "It is expected that the type of requester will be restricted for different stages of a MedicationRequest.  For example, Proposals can be created by a patient, <PERSON><PERSON><PERSON>, Practitioner or <PERSON><PERSON>.  Plans can be created by Practitioners, Patients, RelatedPersons and Devices.  Original orders can be created by a Practitioner only.\r\rAn instance-order is an instantiation of a request or order and may be used to populate Medication Administration Record.\r\rThis element is labeled as a modifier because the intent alters when and how the resource is actually applicable.", "min": 1, "max": "1", "base": {"path": "MedicationRequest.intent", "min": 1, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": true, "isModifierReason": "This element changes the interpretation of all descriptive attributes. For example \"the time the request is recommended to occur\" vs. \"the time the request is authorized to occur\" or \"who is recommended to perform the request\" vs. \"who is authorized to perform the request", "isSummary": true, "binding": {"strength": "required", "description": "The kind of medication order.", "valueSet": "http://hl7.org/fhir/ValueSet/medicationrequest-intent"}}, {"id": "MedicationRequest.category", "path": "MedicationRequest.category", "slicing": {"discriminator": [{"type": "pattern", "path": "$this"}], "rules": "open"}, "short": "Type of medication usage", "definition": "Indicates the type of medication request (for example, where the medication is expected to be consumed or administered (i.e. inpatient or outpatient)).", "comment": "The category can be used to include where the medication is expected to be consumed or other types of requests.", "min": 0, "max": "*", "base": {"path": "MedicationRequest.category", "min": 0, "max": "*"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "MedicationRequestCategory"}], "strength": "example", "description": "A coded concept identifying the category of medication request.  For example, where the medication is to be consumed or administered, or the type of medication treatment.", "valueSet": "http://hl7.org/fhir/ValueSet/medicationrequest-category"}}, {"id": "MedicationRequest.category:us-core", "path": "MedicationRequest.category", "sliceName": "us-core", "short": "Type of medication usage", "definition": "Indicates the type of medication request (for example, where the medication is expected to be consumed or administered (i.e. inpatient or outpatient)).", "comment": "The category can be used to include where the medication is expected to be consumed or other types of requests.", "min": 0, "max": "*", "base": {"path": "MedicationRequest.category", "min": 0, "max": "*"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false, "binding": {"strength": "required", "description": "The type of medication order.", "valueSet": "http://hl7.org/fhir/ValueSet/medicationrequest-category"}}, {"id": "MedicationRequest.priority", "path": "MedicationRequest.priority", "short": "routine | urgent | asap | stat", "definition": "Indicates how quickly the Medication Request should be addressed with respect to other requests.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.priority", "min": 0, "max": "1"}, "type": [{"code": "code"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "MedicationRequestPriority"}], "strength": "required", "description": "Identifies the level of importance to be assigned to actioning the request.", "valueSet": "http://hl7.org/fhir/ValueSet/request-priority|4.0.1"}}, {"id": "MedicationRequest.doNotPerform", "path": "MedicationRequest.doNotPerform", "short": "True if request is prohibiting action", "definition": "If true indicates that the provider is asking for the medication request not to occur.", "comment": "If do not perform is not specified, the request is a positive request e.g. \"do perform\".", "min": 0, "max": "1", "base": {"path": "MedicationRequest.doNotPerform", "min": 0, "max": "1"}, "type": [{"code": "boolean"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": true, "isModifierReason": "This element is labeled as a modifier because this element negates the request to occur (ie, this is a request for the medication not to be ordered or prescribed, etc)", "isSummary": true}, {"id": "MedicationRequest.reported[x]", "path": "MedicationRequest.reported[x]", "short": "Reported rather than primary record", "definition": "Indicates if this record was captured as a secondary 'reported' record rather than as an original primary source-of-truth record.  It may also indicate the source of the report.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.reported[x]", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-type-must-support", "valueBoolean": true}], "code": "boolean"}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-type-must-support", "valueBoolean": true}], "code": "Reference", "targetProfile": ["http://hl7.org/fhir/us/core/StructureDefinition/us-core-practitioner", "http://hl7.org/fhir/us/core/StructureDefinition/us-core-organization", "http://hl7.org/fhir/us/core/StructureDefinition/us-core-patient", "http://hl7.org/fhir/us/core/StructureDefinition/us-core-practitionerrole", "http://hl7.org/fhir/us/core/StructureDefinition/us-core-relatedperson"], "_targetProfile": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-type-must-support", "valueBoolean": true}]}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-type-must-support", "valueBoolean": false}]}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-type-must-support", "valueBoolean": false}]}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-type-must-support", "valueBoolean": false}]}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-type-must-support", "valueBoolean": false}]}]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "MedicationRequest.medication[x]", "path": "MedicationRequest.medication[x]", "short": "Medication to be taken", "definition": "Identifies the medication being requested. This is a link to a resource that represents the medication which may be the details of the medication or simply an attribute carrying a code that identifies the medication from a known list of medications.", "comment": "If only a code is specified, then it needs to be a code for a specific product. If more information is required, then the use of the Medication resource is recommended.  For example, if you require form or lot number or if the medication is compounded or extemporaneously prepared, then you must reference the Medication resource.", "min": 1, "max": "1", "base": {"path": "MedicationRequest.medication[x]", "min": 1, "max": "1"}, "type": [{"code": "CodeableConcept"}, {"code": "Reference", "targetProfile": ["http://hl7.org/fhir/us/core/StructureDefinition/us-core-medication"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true, "binding": {"strength": "extensible", "valueSet": "http://cts.nlm.nih.gov/fhir/ValueSet/2.16.840.1.113762.1.4.1010.4"}}, {"id": "MedicationRequest.subject", "path": "MedicationRequest.subject", "short": "Who or group medication request is for", "definition": "A link to a resource representing the person or set of individuals to whom the medication will be given.", "comment": "The subject on a medication request is mandatory.  For the secondary use case where the actual subject is not provided, there still must be an anonymized subject specified.", "min": 1, "max": "1", "base": {"path": "MedicationRequest.subject", "min": 1, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/us/core/StructureDefinition/us-core-patient"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "MedicationRequest.encounter", "path": "MedicationRequest.encounter", "short": "Encounter created as part of encounter/admission/stay", "definition": "The Encounter during which this [x] was created or to which the creation of this record is tightly associated.", "comment": "This will typically be the encounter the event occurred within, but some activities may be initiated prior to or after the official completion of an encounter but still be tied to the context of the encounter.\"    If there is a need to link to episodes of care they will be handled with an extension.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.encounter", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/us/core/StructureDefinition/us-core-encounter"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.supportingInformation", "path": "MedicationRequest.supportingInformation", "short": "Information to support ordering of the medication", "definition": "Include additional information (for example, patient height and weight) that supports the ordering of the medication.", "min": 0, "max": "*", "base": {"path": "MedicationRequest.supportingInformation", "min": 0, "max": "*"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Resource"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.authoredOn", "path": "MedicationRequest.authoredOn", "short": "When request was initially authored", "definition": "The date (and perhaps time) when the prescription was initially written or authored on.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.authoredOn", "min": 0, "max": "1"}, "type": [{"code": "dateTime"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "MedicationRequest.requester", "path": "MedicationRequest.requester", "short": "Who/What requested the Request", "definition": "The individual, organization, or device that initiated the request and has responsibility for its activation.", "min": 1, "max": "1", "base": {"path": "MedicationRequest.requester", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/us/core/StructureDefinition/us-core-practitioner", "http://hl7.org/fhir/us/core/StructureDefinition/us-core-patient", "http://hl7.org/fhir/us/core/StructureDefinition/us-core-organization", "http://hl7.org/fhir/us/core/StructureDefinition/us-core-practitionerrole", "http://hl7.org/fhir/us/core/StructureDefinition/us-core-relatedperson", "http://hl7.org/fhir/StructureDefinition/Device"], "_targetProfile": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-type-must-support", "valueBoolean": true}]}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-type-must-support", "valueBoolean": false}]}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-type-must-support", "valueBoolean": false}]}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-type-must-support", "valueBoolean": false}]}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-type-must-support", "valueBoolean": false}]}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-type-must-support", "valueBoolean": false}]}]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "MedicationRequest.performer", "path": "MedicationRequest.performer", "short": "Intended performer of administration", "definition": "The specified desired performer of the medication treatment (e.g. the performer of the medication administration).", "min": 0, "max": "1", "base": {"path": "MedicationRequest.performer", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Practitioner", "http://hl7.org/fhir/StructureDefinition/PractitionerRole", "http://hl7.org/fhir/StructureDefinition/Organization", "http://hl7.org/fhir/StructureDefinition/Patient", "http://hl7.org/fhir/StructureDefinition/Device", "http://hl7.org/fhir/StructureDefinition/RelatedPerson", "http://hl7.org/fhir/StructureDefinition/CareTeam"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.performerType", "path": "MedicationRequest.performerType", "short": "Desired kind of performer of the medication administration", "definition": "Indicates the type of performer of the administration of the medication.", "comment": "If specified without indicating a performer, this indicates that the performer must be of the specified type. If specified with a performer then it indicates the requirements of the performer if the designated performer is not available.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.performerType", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "MedicationRequestPerformerType"}], "strength": "example", "description": "Identifies the type of individual that is desired to administer the medication.", "valueSet": "http://hl7.org/fhir/ValueSet/performer-role"}}, {"id": "MedicationRequest.recorder", "path": "MedicationRequest.recorder", "short": "Person who entered the request", "definition": "The person who entered the order on behalf of another individual for example in the case of a verbal or a telephone order.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.recorder", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Practitioner", "http://hl7.org/fhir/StructureDefinition/PractitionerRole"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.reasonCode", "path": "MedicationRequest.reasonCode", "short": "Reason or indication for ordering or not ordering the medication", "definition": "The reason or the indication for ordering or not ordering the medication.", "comment": "This could be a diagnosis code. If a full condition record exists or additional detail is needed, use reasonReference.", "min": 0, "max": "*", "base": {"path": "MedicationRequest.reasonCode", "min": 0, "max": "*"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "MedicationRequestReason"}], "strength": "example", "description": "A coded concept indicating why the medication was ordered.", "valueSet": "http://hl7.org/fhir/ValueSet/condition-code"}}, {"id": "MedicationRequest.reasonReference", "path": "MedicationRequest.reasonReference", "short": "Condition or observation that supports why the prescription is being written", "definition": "Condition or observation that supports why the medication was ordered.", "comment": "This is a reference to a condition or observation that is the reason for the medication order.  If only a code exists, use reasonCode.", "min": 0, "max": "*", "base": {"path": "MedicationRequest.reasonReference", "min": 0, "max": "*"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Condition", "http://hl7.org/fhir/StructureDefinition/Observation"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.instantiatesCanonical", "path": "MedicationRequest.instantiatesCanonical", "short": "Instantiates FHIR protocol or definition", "definition": "The URL pointing to a protocol, guideline, orderset, or other definition that is adhered to in whole or in part by this MedicationRequest.", "min": 0, "max": "*", "base": {"path": "MedicationRequest.instantiatesCanonical", "min": 0, "max": "*"}, "type": [{"code": "canonical"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "MedicationRequest.instantiatesUri", "path": "MedicationRequest.instantiatesUri", "short": "Instantiates external protocol or definition", "definition": "The URL pointing to an externally maintained protocol, guideline, orderset or other definition that is adhered to in whole or in part by this MedicationRequest.", "min": 0, "max": "*", "base": {"path": "MedicationRequest.instantiatesUri", "min": 0, "max": "*"}, "type": [{"code": "uri"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "MedicationRequest.basedOn", "path": "MedicationRequest.basedOn", "short": "What request fulfills", "definition": "A plan or request that is fulfilled in whole or in part by this medication request.", "min": 0, "max": "*", "base": {"path": "MedicationRequest.basedOn", "min": 0, "max": "*"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/CarePlan", "http://hl7.org/fhir/StructureDefinition/MedicationRequest", "http://hl7.org/fhir/StructureDefinition/ServiceRequest", "http://hl7.org/fhir/StructureDefinition/ImmunizationRecommendation"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "MedicationRequest.groupIdentifier", "path": "MedicationRequest.groupIdentifier", "short": "Composite request this is part of", "definition": "A shared identifier common to all requests that were authorized more or less simultaneously by a single author, representing the identifier of the requisition or prescription.", "requirements": "Requests are linked either by a \"basedOn\" relationship (i.e. one request is fulfilling another) or by having a common requisition. Requests that are part of the same requisition are generally treated independently from the perspective of changing their state or maintaining them after initial creation.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.groupIdentifier", "min": 0, "max": "1"}, "type": [{"code": "Identifier"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "MedicationRequest.courseOfTherapyType", "path": "MedicationRequest.courseOfTherapyType", "short": "Overall pattern of medication administration", "definition": "The description of the overall patte3rn of the administration of the medication to the patient.", "comment": "This attribute should not be confused with the protocol of the medication.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.courseOfTherapyType", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "MedicationRequestCourseOfTherapy"}], "strength": "example", "description": "Identifies the overall pattern of medication administratio.", "valueSet": "http://hl7.org/fhir/ValueSet/medicationrequest-course-of-therapy"}}, {"id": "MedicationRequest.insurance", "path": "MedicationRequest.insurance", "short": "Associated insurance coverage", "definition": "Insurance plans, coverage extensions, pre-authorizations and/or pre-determinations that may be required for delivering the requested service.", "min": 0, "max": "*", "base": {"path": "MedicationRequest.insurance", "min": 0, "max": "*"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Coverage", "http://hl7.org/fhir/StructureDefinition/ClaimResponse"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.note", "path": "MedicationRequest.note", "short": "Information about the prescription", "definition": "Extra information about the prescription that could not be conveyed by the other attributes.", "min": 0, "max": "*", "base": {"path": "MedicationRequest.note", "min": 0, "max": "*"}, "type": [{"code": "Annotation"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.dosageInstruction", "path": "MedicationRequest.dosageInstruction", "short": "How the medication should be taken", "definition": "Indicates how the medication is to be used by the patient.", "comment": "There are examples where a medication request may include the option of an oral dose or an Intravenous or Intramuscular dose.  For example, \"Ondansetron 8mg orally or IV twice a day as needed for nausea\" or \"Compazine® (prochlorperazine) 5-10mg PO or 25mg PR bid prn nausea or vomiting\".  In these cases, two medication requests would be created that could be grouped together.  The decision on which dose and route of administration to use is based on the patient's condition at the time the dose is needed.", "min": 0, "max": "*", "base": {"path": "MedicationRequest.dosageInstruction", "min": 0, "max": "*"}, "type": [{"code": "Dosage"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.dosageInstruction.id", "path": "MedicationRequest.dosageInstruction.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.dosageInstruction.extension", "path": "MedicationRequest.dosageInstruction.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.dosageInstruction.modifierExtension", "path": "MedicationRequest.dosageInstruction.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R4/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true}, {"id": "MedicationRequest.dosageInstruction.sequence", "path": "MedicationRequest.dosageInstruction.sequence", "short": "The order of the dosage instructions", "definition": "Indicates the order in which the dosage instructions should be applied or interpreted.", "requirements": "If the sequence number of multiple Dosages is the same, then it is implied that the instructions are to be treated as concurrent.  If the sequence number is different, then the Dosages are intended to be sequential.", "min": 0, "max": "1", "base": {"path": "Dosage.sequence", "min": 0, "max": "1"}, "type": [{"code": "integer"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "MedicationRequest.dosageInstruction.text", "path": "MedicationRequest.dosageInstruction.text", "short": "Free text dosage instructions e.g. SIG", "definition": "Free text dosage instructions e.g. SIG.", "requirements": "Free text dosage instructions can be used for cases where the instructions are too complex to code.  The content of this attribute does not include the name or description of the medication. When coded instructions are present, the free text instructions may still be present for display to humans taking or administering the medication. It is expected that the text instructions will always be populated.  If the dosage.timing attribute is also populated, then the dosage.text should reflect the same information as the timing.  Additional information about administration or preparation of the medication should be included as text.", "min": 0, "max": "1", "base": {"path": "Dosage.text", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "mustSupport": true, "isModifier": false, "isSummary": true}, {"id": "MedicationRequest.dosageInstruction.additionalInstruction", "path": "MedicationRequest.dosageInstruction.additionalInstruction", "short": "Supplemental instruction or warnings to the patient - e.g. \"with meals\", \"may cause drowsiness\"", "definition": "Supplemental instructions to the patient on how to take the medication  (e.g. \"with meals\" or\"take half to one hour before food\") or warnings for the patient about the medication (e.g. \"may cause drowsiness\" or \"avoid exposure of skin to direct sunlight or sunlamps\").", "comment": "Information about administration or preparation of the medication (e.g. \"infuse as rapidly as possibly via intraperitoneal port\" or \"immediately following drug x\") should be populated in dosage.text.", "requirements": "Additional instruction is intended to be coded, but where no code exists, the element could include text.  For example, \"Swallow with plenty of water\" which might or might not be coded.", "min": 0, "max": "*", "base": {"path": "Dosage.additionalInstruction", "min": 0, "max": "*"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "AdditionalInstruction"}], "strength": "example", "description": "A coded concept identifying additional instructions such as \"take with water\" or \"avoid operating heavy machinery\".", "valueSet": "http://hl7.org/fhir/ValueSet/additional-instruction-codes"}}, {"id": "MedicationRequest.dosageInstruction.patientInstruction", "path": "MedicationRequest.dosageInstruction.patientInstruction", "short": "Patient or consumer oriented instructions", "definition": "Instructions in terms that are understood by the patient or consumer.", "min": 0, "max": "1", "base": {"path": "Dosage.patientInstruction", "min": 0, "max": "1"}, "type": [{"code": "string"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "MedicationRequest.dosageInstruction.timing", "path": "MedicationRequest.dosageInstruction.timing", "short": "When medication should be administered", "definition": "When medication should be administered.", "comment": "This attribute might not always be populated while the Dosage.text is expected to be populated.  If both are populated, then the Dosage.text should reflect the content of the Dosage.timing.", "requirements": "The timing schedule for giving the medication to the patient. This  data type allows many different expressions. For example: \"Every 8 hours\"; \"Three times a day\"; \"1/2 an hour before breakfast for 10 days from 23-Dec 2011:\"; \"15 Oct 2013, 17 Oct 2013 and 1 Nov 2013\".  Sometimes, a rate can imply duration when expressed as total volume / duration (e.g.  500mL/2 hours implies a duration of 2 hours).  However, when rate doesn't imply duration (e.g. 250mL/hour), then the timing.repeat.duration is needed to convey the infuse over time period.", "min": 0, "max": "1", "base": {"path": "Dosage.timing", "min": 0, "max": "1"}, "type": [{"code": "Timing"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "MedicationRequest.dosageInstruction.asNeeded[x]", "path": "MedicationRequest.dosageInstruction.asNeeded[x]", "short": "Take \"as needed\" (for x)", "definition": "Indicates whether the Medication is only taken when needed within a specific dosing schedule (Boolean option), or it indicates the precondition for taking the Medication (CodeableConcept).", "comment": "Can express \"as needed\" without a reason by setting the Boolean = True.  In this case the CodeableConcept is not populated.  Or you can express \"as needed\" with a reason by including the CodeableConcept.  In this case the Boolean is assumed to be True.  If you set the Boolean to False, then the dose is given according to the schedule and is not \"prn\" or \"as needed\".", "min": 0, "max": "1", "base": {"path": "Dosage.asNeeded[x]", "min": 0, "max": "1"}, "type": [{"code": "boolean"}, {"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "MedicationAsNeededReason"}], "strength": "example", "description": "A coded concept identifying the precondition that should be met or evaluated prior to consuming or administering a medication dose.  For example \"pain\", \"30 minutes prior to sexual intercourse\", \"on flare-up\" etc.", "valueSet": "http://hl7.org/fhir/ValueSet/medication-as-needed-reason"}}, {"id": "MedicationRequest.dosageInstruction.site", "path": "MedicationRequest.dosageInstruction.site", "short": "Body site to administer to", "definition": "Body site to administer to.", "comment": "If the use case requires attributes from the BodySite resource (e.g. to identify and track separately) then use the standard extension [bodySite](http://hl7.org/fhir/R4/extension-bodysite.html).  May be a summary code, or a reference to a very precise definition of the location, or both.", "requirements": "A coded specification of the anatomic site where the medication first enters the body.", "min": 0, "max": "1", "base": {"path": "Dosage.site", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "MedicationAdministrationSite"}], "strength": "example", "description": "A coded concept describing the site location the medicine enters into or onto the body.", "valueSet": "http://hl7.org/fhir/ValueSet/approach-site-codes"}}, {"id": "MedicationRequest.dosageInstruction.route", "path": "MedicationRequest.dosageInstruction.route", "short": "How drug should enter body", "definition": "How drug should enter body.", "requirements": "A code specifying the route or physiological path of administration of a therapeutic agent into or onto a patient's body.", "min": 0, "max": "1", "base": {"path": "Dosage.route", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "RouteOfAdministration"}], "strength": "example", "description": "A coded concept describing the route or physiological path of administration of a therapeutic agent into or onto the body of a subject.", "valueSet": "http://hl7.org/fhir/ValueSet/route-codes"}}, {"id": "MedicationRequest.dosageInstruction.method", "path": "MedicationRequest.dosageInstruction.method", "short": "Technique for administering medication", "definition": "Technique for administering medication.", "comment": "Terminologies used often pre-coordinate this term with the route and or form of administration.", "requirements": "A coded value indicating the method by which the medication is introduced into or onto the body. Most commonly used for injections.  For examples, Slow Push; Deep IV.", "min": 0, "max": "1", "base": {"path": "Dosage.method", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "MedicationAdministrationMethod"}], "strength": "example", "description": "A coded concept describing the technique by which the medicine is administered.", "valueSet": "http://hl7.org/fhir/ValueSet/administration-method-codes"}}, {"id": "MedicationRequest.dosageInstruction.doseAndRate", "path": "MedicationRequest.dosageInstruction.doseAndRate", "short": "Amount of medication administered", "definition": "The amount of medication administered.", "min": 0, "max": "*", "base": {"path": "Dosage.doseAndRate", "min": 0, "max": "*"}, "type": [{"code": "Element"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "MedicationRequest.dosageInstruction.doseAndRate.id", "path": "MedicationRequest.dosageInstruction.doseAndRate.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.dosageInstruction.doseAndRate.extension", "path": "MedicationRequest.dosageInstruction.doseAndRate.extension", "slicing": {"discriminator": [{"type": "value", "path": "url"}], "description": "Extensions are always sliced by (at least) url", "rules": "open"}, "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.dosageInstruction.doseAndRate.type", "path": "MedicationRequest.dosageInstruction.doseAndRate.type", "short": "The kind of dose or rate specified", "definition": "The kind of dose or rate specified, for example, ordered or calculated.", "requirements": "If the type is not populated, assume to be \"ordered\".", "min": 0, "max": "1", "base": {"path": "Dosage.doseAndRate.type", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "DoseAndRateType"}], "strength": "example", "description": "The kind of dose or rate specified.", "valueSet": "http://hl7.org/fhir/ValueSet/dose-rate-type"}}, {"id": "MedicationRequest.dosageInstruction.doseAndRate.dose[x]", "path": "MedicationRequest.dosageInstruction.doseAndRate.dose[x]", "short": "Amount of medication per dose", "definition": "Amount of medication per dose.", "comment": "Note that this specifies the quantity of the specified medication, not the quantity for each active ingredient(s). Each ingredient amount can be communicated in the Medication resource. For example, if one wants to communicate that a tablet was 375 mg, where the dose was one tablet, you can use the Medication resource to document that the tablet was comprised of 375 mg of drug XYZ. Alternatively if the dose was 375 mg, then you may only need to use the Medication resource to indicate this was a tablet. If the example were an IV such as dopamine and you wanted to communicate that 400mg of dopamine was mixed in 500 ml of some IV solution, then this would all be communicated in the Medication resource. If the administration is not intended to be instantaneous (rate is present or timing has a duration), this can be specified to convey the total amount to be administered over the period of time as indicated by the schedule e.g. 500 ml in dose, with timing used to convey that this should be done over 4 hours.", "requirements": "The amount of therapeutic or other substance given at one administration event.", "min": 0, "max": "1", "base": {"path": "Dosage.doseAndRate.dose[x]", "min": 0, "max": "1"}, "type": [{"code": "Range"}, {"code": "Quantity", "profile": ["http://hl7.org/fhir/StructureDefinition/SimpleQuantity"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "MedicationRequest.dosageInstruction.doseAndRate.rate[x]", "path": "MedicationRequest.dosageInstruction.doseAndRate.rate[x]", "short": "Amount of medication per unit of time", "definition": "Amount of medication per unit of time.", "comment": "It is possible to supply both a rate and a doseQuantity to provide full details about how the medication is to be administered and supplied. If the rate is intended to change over time, depending on local rules/regulations, each change should be captured as a new version of the MedicationRequest with an updated rate, or captured with a new MedicationRequest with the new rate.\r\rIt is possible to specify a rate over time (for example, 100 ml/hour) using either the rateRatio and rateQuantity.  The rateQuantity approach requires systems to have the capability to parse UCUM grammer where ml/hour is included rather than a specific ratio where the time is specified as the denominator.  Where a rate such as 500ml over 2 hours is specified, the use of rateRatio may be more semantically correct than specifying using a rateQuantity of 250 mg/hour.", "requirements": "Identifies the speed with which the medication was or will be introduced into the patient. Typically the rate for an infusion e.g. 100 ml per 1 hour or 100 ml/hr.  May also be expressed as a rate per unit of time e.g. 500 ml per 2 hours.   Other examples: 200 mcg/min or 200 mcg/1 minute; 1 liter/8 hours.  Sometimes, a rate can imply duration when expressed as total volume / duration (e.g.  500mL/2 hours implies a duration of 2 hours).  However, when rate doesn't imply duration (e.g. 250mL/hour), then the timing.repeat.duration is needed to convey the infuse over time period.", "min": 0, "max": "1", "base": {"path": "Dosage.doseAndRate.rate[x]", "min": 0, "max": "1"}, "type": [{"code": "<PERSON><PERSON>"}, {"code": "Range"}, {"code": "Quantity", "profile": ["http://hl7.org/fhir/StructureDefinition/SimpleQuantity"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "MedicationRequest.dosageInstruction.maxDosePerPeriod", "path": "MedicationRequest.dosageInstruction.maxDosePerPeriod", "short": "Upper limit on medication per unit of time", "definition": "Upper limit on medication per unit of time.", "comment": "This is intended for use as an adjunct to the dosage when there is an upper cap.  For example \"2 tablets every 4 hours to a maximum of 8/day\".", "requirements": "The maximum total quantity of a therapeutic substance that may be administered to a subject over the period of time.  For example, 1000mg in 24 hours.", "min": 0, "max": "1", "base": {"path": "Dosage.maxDosePerPeriod", "min": 0, "max": "1"}, "type": [{"code": "<PERSON><PERSON>"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "MedicationRequest.dosageInstruction.maxDosePerAdministration", "path": "MedicationRequest.dosageInstruction.maxDosePerAdministration", "short": "Upper limit on medication per administration", "definition": "Upper limit on medication per administration.", "comment": "This is intended for use as an adjunct to the dosage when there is an upper cap.  For example, a body surface area related dose with a maximum amount, such as 1.5 mg/m2 (maximum 2 mg) IV over 5 – 10 minutes would have doseQuantity of 1.5 mg/m2 and maxDosePerAdministration of 2 mg.", "requirements": "The maximum total quantity of a therapeutic substance that may be administered to a subject per administration.", "min": 0, "max": "1", "base": {"path": "Dosage.maxDosePerAdministration", "min": 0, "max": "1"}, "type": [{"code": "Quantity", "profile": ["http://hl7.org/fhir/StructureDefinition/SimpleQuantity"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "MedicationRequest.dosageInstruction.maxDosePerLifetime", "path": "MedicationRequest.dosageInstruction.maxDosePerLifetime", "short": "Upper limit on medication per lifetime of the patient", "definition": "Upper limit on medication per lifetime of the patient.", "requirements": "The maximum total quantity of a therapeutic substance that may be administered per lifetime of the subject.", "min": 0, "max": "1", "base": {"path": "Dosage.maxDosePerLifetime", "min": 0, "max": "1"}, "type": [{"code": "Quantity", "profile": ["http://hl7.org/fhir/StructureDefinition/SimpleQuantity"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": true}, {"id": "MedicationRequest.dispenseRequest", "path": "MedicationRequest.dispenseRequest", "short": "Medication supply authorization", "definition": "Indicates the specific details for the dispense or medication supply part of a medication request (also known as a Medication Prescription or Medication Order).  Note that this information is not always sent with the order.  There may be in some settings (e.g. hospitals) institutional or system support for completing the dispense details in the pharmacy department.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.dispenseRequest", "min": 0, "max": "1"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.dispenseRequest.id", "path": "MedicationRequest.dispenseRequest.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.dispenseRequest.extension", "path": "MedicationRequest.dispenseRequest.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.dispenseRequest.modifierExtension", "path": "MedicationRequest.dispenseRequest.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R4/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true}, {"id": "MedicationRequest.dispenseRequest.initialFill", "path": "MedicationRequest.dispenseRequest.initialFill", "short": "First fill details", "definition": "Indicates the quantity or duration for the first dispense of the medication.", "comment": "If populating this element, either the quantity or the duration must be included.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.dispenseRequest.initialFill", "min": 0, "max": "1"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.dispenseRequest.initialFill.id", "path": "MedicationRequest.dispenseRequest.initialFill.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.dispenseRequest.initialFill.extension", "path": "MedicationRequest.dispenseRequest.initialFill.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.dispenseRequest.initialFill.modifierExtension", "path": "MedicationRequest.dispenseRequest.initialFill.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R4/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true}, {"id": "MedicationRequest.dispenseRequest.initialFill.quantity", "path": "MedicationRequest.dispenseRequest.initialFill.quantity", "short": "First fill quantity", "definition": "The amount or quantity to provide as part of the first dispense.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.dispenseRequest.initialFill.quantity", "min": 0, "max": "1"}, "type": [{"code": "Quantity", "profile": ["http://hl7.org/fhir/StructureDefinition/SimpleQuantity"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.dispenseRequest.initialFill.duration", "path": "MedicationRequest.dispenseRequest.initialFill.duration", "short": "First fill duration", "definition": "The length of time that the first dispense is expected to last.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.dispenseRequest.initialFill.duration", "min": 0, "max": "1"}, "type": [{"code": "Duration"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.dispenseRequest.dispenseInterval", "path": "MedicationRequest.dispenseRequest.dispenseInterval", "short": "Minimum period of time between dispenses", "definition": "The minimum period of time that must occur between dispenses of the medication.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.dispenseRequest.dispenseInterval", "min": 0, "max": "1"}, "type": [{"code": "Duration"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.dispenseRequest.validityPeriod", "path": "MedicationRequest.dispenseRequest.validityPeriod", "short": "Time period supply is authorized for", "definition": "This indicates the validity period of a prescription (stale dating the Prescription).", "comment": "It reflects the prescribers' perspective for the validity of the prescription. Dispenses must not be made against the prescription outside of this period. The lower-bound of the Dispensing Window signifies the earliest date that the prescription can be filled for the first time. If an upper-bound is not specified then the Prescription is open-ended or will default to a stale-date based on regulations.", "requirements": "Indicates when the Prescription becomes valid, and when it ceases to be a dispensable Prescription.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.dispenseRequest.validityPeriod", "min": 0, "max": "1"}, "type": [{"code": "Period"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.dispenseRequest.numberOfRepeatsAllowed", "path": "MedicationRequest.dispenseRequest.numberOfRepeatsAllowed", "short": "Number of refills authorized", "definition": "An integer indicating the number of times, in addition to the original dispense, (aka refills or repeats) that the patient can receive the prescribed medication. Usage Notes: This integer does not include the original order dispense. This means that if an order indicates dispense 30 tablets plus \"3 repeats\", then the order can be dispensed a total of 4 times and the patient can receive a total of 120 tablets.  A prescriber may explicitly say that zero refills are permitted after the initial dispense.", "comment": "If displaying \"number of authorized fills\", add 1 to this number.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.dispenseRequest.numberOfRepeatsAllowed", "min": 0, "max": "1"}, "type": [{"code": "unsignedInt"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.dispenseRequest.quantity", "path": "MedicationRequest.dispenseRequest.quantity", "short": "Amount of medication to supply per dispense", "definition": "The amount that is to be dispensed for one fill.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.dispenseRequest.quantity", "min": 0, "max": "1"}, "type": [{"code": "Quantity", "profile": ["http://hl7.org/fhir/StructureDefinition/SimpleQuantity"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.dispenseRequest.expectedSupplyDuration", "path": "MedicationRequest.dispenseRequest.expectedSupplyDuration", "short": "Number of days supply per dispense", "definition": "Identifies the period time over which the supplied product is expected to be used, or the length of time the dispense is expected to last.", "comment": "In some situations, this attribute may be used instead of quantity to identify the amount supplied by how long it is expected to last, rather than the physical quantity issued, e.g. 90 days supply of medication (based on an ordered dosage). When possible, it is always better to specify quantity, as this tends to be more precise. expectedSupplyDuration will always be an estimate that can be influenced by external factors.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.dispenseRequest.expectedSupplyDuration", "min": 0, "max": "1"}, "type": [{"code": "Duration"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.dispenseRequest.performer", "path": "MedicationRequest.dispenseRequest.performer", "short": "Intended dispenser", "definition": "Indicates the intended dispensing Organization specified by the prescriber.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.dispenseRequest.performer", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Organization"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.substitution", "path": "MedicationRequest.substitution", "short": "Any restrictions on medication substitution", "definition": "Indicates whether or not substitution can or should be part of the dispense. In some cases, substitution must happen, in other cases substitution must not happen. This block explains the prescriber's intent. If nothing is specified substitution may be done.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.substitution", "min": 0, "max": "1"}, "type": [{"code": "BackboneElement"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.substitution.id", "path": "MedicationRequest.substitution.id", "representation": ["xmlAttr"], "short": "Unique id for inter-element referencing", "definition": "Unique id for the element within a resource (for internal references). This may be any string value that does not contain spaces.", "min": 0, "max": "1", "base": {"path": "Element.id", "min": 0, "max": "1"}, "type": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type", "valueUrl": "string"}], "code": "http://hl7.org/fhirpath/System.String"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.substitution.extension", "path": "MedicationRequest.substitution.extension", "short": "Additional content defined by implementations", "definition": "May be used to represent additional information that is not part of the basic definition of the element. To make the use of extensions safe and manageable, there is a strict set of governance  applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension.", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "alias": ["extensions", "user content"], "min": 0, "max": "*", "base": {"path": "Element.extension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.substitution.modifierExtension", "path": "MedicationRequest.substitution.modifierExtension", "short": "Extensions that cannot be ignored even if unrecognized", "definition": "May be used to represent additional information that is not part of the basic definition of the element and that modifies the understanding of the element in which it is contained and/or the understanding of the containing element's descendants. Usually modifier elements provide negation or qualification. To make the use of extensions safe and manageable, there is a strict set of governance applied to the definition and use of extensions. Though any implementer can define an extension, there is a set of requirements that SHALL be met as part of the definition of the extension. Applications processing a resource are required to check for modifier extensions.\n\nModifier extensions SHALL NOT change the meaning of any elements on Resource or DomainResource (including cannot change the meaning of modifierExtension itself).", "comment": "There can be no stigma associated with the use of extensions by any application, project, or standard - regardless of the institution or jurisdiction that uses or defines the extensions.  The use of extensions is what allows the FHIR specification to retain a core level of simplicity for everyone.", "requirements": "Modifier extensions allow for extensions that *cannot* be safely ignored to be clearly distinguished from the vast majority of extensions which can be safely ignored.  This promotes interoperability by eliminating the need for implementers to prohibit the presence of extensions. For further information, see the [definition of modifier extensions](http://hl7.org/fhir/R4/extensibility.html#modifierExtension).", "alias": ["extensions", "user content", "modifiers"], "min": 0, "max": "*", "base": {"path": "BackboneElement.modifierExtension", "min": 0, "max": "*"}, "type": [{"code": "Extension"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}, {"key": "ext-1", "severity": "error", "human": "Must have either extensions or value[x], not both", "expression": "extension.exists() != value.exists()", "xpath": "exists(f:extension)!=exists(f:*[starts-with(local-name(.), \"value\")])", "source": "http://hl7.org/fhir/StructureDefinition/Extension"}], "isModifier": true, "isModifierReason": "Modifier extensions are expected to modify the meaning or interpretation of the element that contains them", "isSummary": true}, {"id": "MedicationRequest.substitution.allowed[x]", "path": "MedicationRequest.substitution.allowed[x]", "short": "Whether substitution is allowed or not", "definition": "True if the prescriber allows a different drug to be dispensed from what was prescribed.", "comment": "This element is labeled as a modifier because whether substitution is allow or not, it cannot be ignored.", "min": 1, "max": "1", "base": {"path": "MedicationRequest.substitution.allowed[x]", "min": 1, "max": "1"}, "type": [{"code": "boolean"}, {"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "MedicationRequestSubstitution"}], "strength": "example", "description": "Identifies the type of substitution allowed.", "valueSet": "http://terminology.hl7.org/ValueSet/v3-ActSubstanceAdminSubstitutionCode"}}, {"id": "MedicationRequest.substitution.reason", "path": "MedicationRequest.substitution.reason", "short": "Why should (not) substitution be made", "definition": "Indicates the reason for the substitution, or why substitution must or must not be performed.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.substitution.reason", "min": 0, "max": "1"}, "type": [{"code": "CodeableConcept"}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false, "binding": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/elementdefinition-bindingName", "valueString": "MedicationIntendedSubstitutionReason"}], "strength": "example", "description": "A coded concept describing the reason that a different medication should (or should not) be substituted from what was prescribed.", "valueSet": "http://terminology.hl7.org/ValueSet/v3-SubstanceAdminSubstitutionReason"}}, {"id": "MedicationRequest.priorPrescription", "path": "MedicationRequest.priorPrescription", "short": "An order/prescription that is being replaced", "definition": "A link to a resource representing an earlier order related order or prescription.", "min": 0, "max": "1", "base": {"path": "MedicationRequest.priorPrescription", "min": 0, "max": "1"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/MedicationRequest"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.detectedIssue", "path": "MedicationRequest.detectedIssue", "short": "Clinical Issue with action", "definition": "Indicates an actual or potential clinical issue with or between one or more active or proposed clinical actions for a patient; e.g. Drug-drug interaction, duplicate therapy, dosage alert etc.", "comment": "This element can include a detected issue that has been identified either by a decision support system or by a clinician and may include information on the steps that were taken to address the issue.", "alias": ["Contraindication", "Drug Utilization Review (DUR)", "<PERSON><PERSON>"], "min": 0, "max": "*", "base": {"path": "MedicationRequest.detectedIssue", "min": 0, "max": "*"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/DetectedIssue"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}, {"id": "MedicationRequest.eventHistory", "path": "MedicationRequest.eventHistory", "short": "A list of events of interest in the lifecycle", "definition": "Links to Provenance records for past versions of this resource or fulfilling request or event resources that identify key state transitions or updates that are likely to be relevant to a user looking at the current version of the resource.", "comment": "This might not include provenances for all versions of the request – only those deemed “relevant” or important. This SHALL NOT include the provenance associated with this current version of the resource. (If that provenance is deemed to be a “relevant” change, it will need to be added as part of a later update. Until then, it can be queried directly as the provenance that points to this version using _revinclude All Provenances should have some historical version of this Request as their subject.).", "min": 0, "max": "*", "base": {"path": "MedicationRequest.eventHistory", "min": 0, "max": "*"}, "type": [{"code": "Reference", "targetProfile": ["http://hl7.org/fhir/StructureDefinition/Provenance"]}], "constraint": [{"key": "ele-1", "severity": "error", "human": "All FHIR elements must have a @value or children", "expression": "hasValue() or (children().count() > id.count())", "xpath": "@value|f:*|h:div", "source": "http://hl7.org/fhir/StructureDefinition/Element"}], "isModifier": false, "isSummary": false}]}}]