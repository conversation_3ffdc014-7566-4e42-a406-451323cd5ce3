import React from 'react';
import { TrendingUp, BarChart2 } from 'lucide-react';

const VitalsTab = ({ vitals, onChange }) => {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-6">Vital Signs Comparison</h3>
      
      <div className="space-y-6">
        <div className="grid grid-cols-4 gap-6">
          <div className="col-span-1">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 mb-3">Height & Weight</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Height</label>
                  <input
                    type="text"
                    value={vitals.height}
                    onChange={(e) => onChange('height', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    placeholder="cm"
                  />
                  <div className="text-xs text-gray-500 mt-1">cm</div>
                </div>
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Weight</label>
                  <input
                    type="text"
                    value={vitals.weight}
                    onChange={(e) => onChange('weight', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    placeholder="kg"
                  />
                  <div className="text-xs text-gray-500 mt-1">kg</div>
                </div>
              </div>
              
              <div className="mt-4">
                <label className="block text-xs text-gray-500 mb-1">BMI</label>
                <input
                  type="text"
                  value={vitals.bmi}
                  className="w-full p-2 border border-gray-300 rounded-md bg-gray-50"
                  readOnly
                />
                <div className="text-xs text-gray-500 mt-1">kg/m²</div>
              </div>
            </div>
          </div>
          
          <div className="col-span-1">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 mb-3">Blood Pressure</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Systolic</label>
                  <input
                    type="text"
                    value={vitals.systolic}
                    onChange={(e) => onChange('systolic', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    placeholder="mmHg"
                  />
                  <div className="text-xs text-gray-500 mt-1">mmHg</div>
                </div>
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Diastolic</label>
                  <input
                    type="text"
                    value={vitals.diastolic}
                    onChange={(e) => onChange('diastolic', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    placeholder="mmHg"
                  />
                  <div className="text-xs text-gray-500 mt-1">mmHg</div>
                </div>
              </div>
              
              <div className="mt-4 p-2 bg-green-50 rounded border border-green-200">
                <div className="flex items-center">
                  <TrendingUp size={16} className="text-green-600 mr-1" />
                  <span className="text-sm text-green-800">
                    14 mmHg decrease in systolic
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="col-span-1">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 mb-3">Heart Rate</h4>
              <div>
                <label className="block text-xs text-gray-500 mb-1">Resting</label>
                <input
                  type="text"
                  value={vitals.heartRate}
                  onChange={(e) => onChange('heartRate', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  placeholder="bpm"
                />
                <div className="text-xs text-gray-500 mt-1">beats/min</div>
              </div>
              
              <div className="mt-4 p-2 bg-green-50 rounded border border-green-200">
                <div className="flex items-center">
                  <TrendingUp size={16} className="text-green-600 mr-1" />
                  <span className="text-sm text-green-800">
                    8 bpm decrease
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="col-span-1">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 mb-3">Oxygen Saturation</h4>
              <div>
                <label className="block text-xs text-gray-500 mb-1">SpO2</label>
                <input
                  type="text"
                  value={vitals.spo2}
                  onChange={(e) => onChange('spo2', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  placeholder="%"
                />
                <div className="text-xs text-gray-500 mt-1">%</div>
              </div>
              
              <div className="mt-4 p-2 bg-green-50 rounded border border-green-200">
                <div className="flex items-center">
                  <TrendingUp size={16} className="text-green-600 mr-1" />
                  <span className="text-sm text-green-800">
                    +2% increase
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Vitals Comparison Chart</h4>
          <div className="aspect-[21/9] bg-white rounded-lg p-4 border border-gray-200">
            <div className="w-full h-full flex items-center justify-center text-gray-400">
              <BarChart2 size={24} className="mr-2" />
              <span>Vitals comparison chart would be displayed here</span>
            </div>
          </div>
        </div>
        
        <div className="bg-blue-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-700 mb-2">Assessment Notes</h4>
          <textarea
            value={vitals.notes}
            onChange={(e) => onChange('notes', e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md h-24"
            placeholder="Enter notes about vitals assessment..."
          ></textarea>
        </div>
      </div>
    </div>
  );
};

export default VitalsTab; 