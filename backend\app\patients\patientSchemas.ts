/**
 * @file patientSchemas.ts
 * @description This file contains the JSON schemas for patient-related data.
 * 
 */

/**
 * Interface for creating a new patient
 * Matches the simple structure you specified in your requirements
 */
export interface PatientCreate {
    family: string;
    given: string[];
    gender?: 'male' | 'female' | 'other' | 'unknown';
    birthDate?: string;
    intakeReason?: string;
}

/**
 * Interface for patient data returned from API
 * Includes FHIR metadata that Medplum adds
 */
export interface PatientRead extends PatientCreate {
    id: string;
    resourceType: 'Patient';
    meta?: {
        versionId?: string;
        lastUpdated?: string;
        source?: string;
        profile?: string[];              // FHIR profiles
        tag?: Array<{                    // Resource tags
            system?: string;
            code?: string;
            display?: string;
        }>;
    };
    identifier?: Array<{               // Patient identifiers (MRN, SSN, etc.)
        use?: string;
        type?: {
            coding?: Array<{
                system?: string;
                code?: string;
                display?: string;
            }>;
        };
        value?: string;
    }>;
    active?: boolean;                  // Whether patient record is active
    name?: Array<{                     // FHIR name structure
        use?: string;
        family?: string;
        given?: string[];
        prefix?: string[];
        suffix?: string[];
    }>;
    telecom?: Array<{                  // Contact information
        system?: 'phone' | 'email' | 'fax' | 'pager' | 'url' | 'sms' | 'other';
        value?: string;
        use?: 'home' | 'work' | 'temp' | 'old' | 'mobile';
    }>;
    address?: Array<{                  // Address information
        use?: 'home' | 'work' | 'temp' | 'old' | 'billing';
        type?: 'postal' | 'physical' | 'both';
        line?: string[];
        city?: string;
        district?: string;
        state?: string;
        postalCode?: string;
        country?: string;
    }>;
}

/**
 * Interface for updating an existing patient
 * All fields are optional since this is for PATCH operations
 */
export interface PatientUpdate {
    gender?: 'male' | 'female' | 'other' | 'unknown';
    birthDate?: string;
    intakeReason?: string;
}

/**
 * Interface for patient search/filter parameters
 */
export interface PatientSearchParams {
    name?: string;                     // Search by any part of name
    family?: string;                   // Search by family name only
    given?: string;                    // Search by given name only  
    gender?: 'male' | 'female' | 'other' | 'unknown';
    birthdate?: string;                // Exact birth date
    active?: boolean;                  // Filter by active status
    _count?: number;                   // Number of results to return
    _offset?: number;                  // Offset for pagination
}

/**
 * Interface for paginated patient list response
 */
export interface PatientListResponse {
    success: boolean;
    data: PatientRead[];
    pagination: {
        total: number;
        limit: number;
        offset: number;
        hasMore: boolean;
    };
}

/**
 * Interface for single patient response
 */
export interface PatientResponse {
    success: boolean;
    message?: string;
    data: PatientRead;
}

/**
 * Validation helper: Check if required fields are present
 */
export function validatePatientCreate(patient: Partial<PatientCreate>): string[] {
    const errors: string[] = [];

    if (!patient.family || patient.family.trim().length === 0) {
        errors.push('family name is required');
    }

    if (!patient.given || !Array.isArray(patient.given) || patient.given.length === 0) {
        errors.push('given name(s) are required');
    } else if (patient.given.some(name => !name || name.trim().length === 0)) {
        errors.push('given names cannot be empty');
    }

    if (patient.birthDate && !isValidDate(patient.birthDate)) {
        errors.push('birthDate must be in YYYY-MM-DD format');
    }

    if (patient.gender && !['male', 'female', 'other', 'unknown'].includes(patient.gender)) {
        errors.push('gender must be one of: male, female, other, unknown');
    }

    return errors;
}

/**
 * Helper function to validate date format
 */
function isValidDate(dateString: string): boolean {
    const regex = /^\d{4}-\d{2}-\d{2}$/;
    if (!regex.test(dateString)) return false;

    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime()) && date.toISOString().startsWith(dateString);
}
