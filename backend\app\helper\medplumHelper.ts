/**
 * @file medplumHelper.ts
 * @description Helper functions for Medplum operations
 */
import { MedplumClient } from '@medplum/core';

// Singleton instance
let medplumClientInstance: MedplumClient | null = null;

/**
 * Get or create a MedplumClient instance using client credentials
 * This uses system-level authentication, not user authentication
 */
export async function getMedplumClient(): Promise<MedplumClient> {
    if (!medplumClientInstance) {
        const clientId = process.env.CLIENT_ID;
        const clientSecret = process.env.CLIENT_SECRET;
        const baseUrl = process.env.MEDPLUM_BASE_URL || 'https://api.medplum.com/';

        // Validate environment variables
        if (!clientId || !clientSecret) {
            throw new Error('Missing required environment variables: CLIENT_ID and CLIENT_SECRET must be set');
        }

        console.log('🔧 Initializing Medplum client...');

        try {
            // Create new client instance
            medplumClientInstance = new MedplumClient({
                baseUrl,
                clientId,
                clientSecret,
            });

            // Authenticate using client credentials (system authentication)
            await medplumClientInstance.startClientLogin(
                clientId,
                clientSecret,
            );

            console.log('Medplum client authenticated successfully');

        } catch (error: any) {
            console.error('Failed to authenticate Medplum client:', error);
            medplumClientInstance = null;

            // Provide helpful error messages
            if (error.message.includes('401') || error.message.includes('unauthorized')) {
                throw new Error('Invalid Medplum credentials. Please check CLIENT_ID and CLIENT_SECRET in your .env file');
            } else if (error.message.includes('network') || error.message.includes('fetch')) {
                throw new Error('Network error connecting to Medplum. Please check your internet connection and MEDPLUM_BASE_URL');
            } else {
                throw new Error(`Failed to authenticate with Medplum: ${error.message}`);
            }
        }
    }

    return medplumClientInstance;
}

/**
 * Reset client instance (useful for testing or when credentials change)
 */
export function resetMedplumClient(): void {
    medplumClientInstance = null;
    console.log('🔄 Medplum client reset');
}

/**
 * Check if client is authenticated and ready
 */
export function isMedplumClientReady(): boolean {
    return medplumClientInstance !== null;
}

/**
 * Get client configuration info for debugging
 */
export function getMedplumConfig() {
    return {
        baseUrl: process.env.MEDPLUM_BASE_URL || 'https://api.medplum.com/',
        hasClientId: !!process.env.CLIENT_ID,
        hasClientSecret: !!process.env.CLIENT_SECRET,
        isAuthenticated: isMedplumClientReady(),
        environment: process.env.NODE_ENV || 'development',
    };
}

/**
 * Test Medplum connection
 */
export async function testMedplumConnection(): Promise<boolean> {
    try {
        const client = await getMedplumClient();

        // Try a simple search to test connection
        await client.searchResources('Patient', { _count: '1' });

        console.log('Medplum connection test successful');
        return true;

    } catch (error: any) {
        console.error('Medplum connection test failed:', error.message);
        return false;
    }
}