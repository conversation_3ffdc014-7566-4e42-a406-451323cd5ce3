import axios, { AxiosInstance } from 'axios';
import dotenv from 'dotenv';
dotenv.config();

/**
 * MedplumService provides methods to authenticate and perform CRUD operations
 * on FHIR resources via the Medplum API.
 */
export class MedplumService {
  private token: string | null = null;
  private client: AxiosInstance;

  /**
   * Initializes the MedplumService with credentials and base URL.
   * @param clientId - OAuth2 client ID (default: from environment variable)
   * @param clientSecret - OAuth2 client secret (default: from environment variable)
   * @param baseUrl - Medplum FHIR API base URL
   */
  constructor(
    private clientId: string = process.env.CLIENT_ID || '',
    private clientSecret: string = process.env.CLIENT_SECRET || '',
    private baseUrl: string = 'https://api.medplum.com/fhir/R4'
  ) {
    // Create an Axios instance with the base URL and default headers
    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: { 'Content-Type': 'application/fhir+json' }
    });
  }

  /**
   * Retrieves and caches an OAuth2 access token for authenticating API requests.
   * @returns The access token string.
   */
  private async getAccessToken(): Promise<string> {
    if (this.token) return this.token;

    const response = await axios.post('https://api.medplum.com/oauth2/token', new URLSearchParams({
      grant_type: 'client_credentials',
      client_id: this.clientId,
      client_secret: this.clientSecret,
      scope: 'openid'
    }));

    this.token = response.data.access_token;
    // Set the Authorization header for all future requests
    this.client.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;
    if (!this.token) {
      throw new Error('Failed to obtain access token');
    }
    return this.token;
  }

  /**
   * Creates a new FHIR resource of the specified type.
   * @param resourceType - The FHIR resource type (e.g., 'Patient').
   * @param data - The resource data to create.
   * @returns The created resource object.
   */
  async create(resourceType: string, data: any) {
    await this.getAccessToken();
    const res = await this.client.post(`/${resourceType}`, data);
    return res.data;
  }

  /**
   * Reads a FHIR resource by type and ID.
   * @param resourceType - The FHIR resource type (e.g., 'Patient').
   * @param id - The ID of the resource to read.
   * @returns The resource object.
   */
  async read(resourceType: string, id: string) {
    await this.getAccessToken();
    const res = await this.client.get(`/${resourceType}/${id}`);
    return res.data;
  }

  /**
   * Updates an existing FHIR resource.
   * @param resourceType - The FHIR resource type (e.g., 'Patient').
   * @param id - The ID of the resource to update.
   * @param data - The updated resource data.
   * @returns The updated resource object.
   */
  async update(resourceType: string, id: string, data: any) {
    await this.getAccessToken();
    const res = await this.client.put(`/${resourceType}/${id}`, data);
    return res.data;
  }

  /**
   * Deletes a FHIR resource by type and ID.
   * @param resourceType - The FHIR resource type (e.g., 'Patient').
   * @param id - The ID of the resource to delete.
   * @returns True if deletion was successful, false otherwise.
   */
  async delete(resourceType: string, id: string) {
    await this.getAccessToken();
    const res = await this.client.delete(`/${resourceType}/${id}`);
    return res.status === 204 || res.status === 200;
  }

  /**
  * Performs user login using Medplum's auth endpoint
  * @param email - User's email
  * @param password - User's password  
  * @returns Login response with access token and profile
  */
  async login(email: string, password: string) {
    try {
      console.log('MedplumService: Attempting login with:', { email, clientId: this.clientId });

      const response = await axios.post('https://api.medplum.com/auth/login', {
        email,
        password,
        clientId: this.clientId,
      });
      const token = response.data.access_token ||
        response.data.accessToken ||
        response.data.token ||
        response.data.login;

      if (!token) {
        console.error('No token found in response. Available fields:', Object.keys(response.data));
        throw new Error('No access token received');
      }

      this.token = token;
      this.client.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;

      return {
        login: this.token,
        profile: response.data.profile || response.data.user
      };
    } catch (error: any) {
      console.error('Login error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });
      throw new Error(`Login failed: ${error.response?.data?.message || error.message}`);
    }
  }
  /**
    * Gets the current access token
    * @returns The current access token
    */
  getToken(): string | null {
    return this.token;
  }

}