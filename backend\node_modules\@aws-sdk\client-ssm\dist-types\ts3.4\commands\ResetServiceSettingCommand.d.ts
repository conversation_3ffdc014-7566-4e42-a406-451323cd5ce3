import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ResetServiceSettingRequest,
  ResetServiceSettingResult,
} from "../models/models_2";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SSMClientResolvedConfig,
} from "../SSMClient";
export { __MetadataBearer };
export { $Command };
export interface ResetServiceSettingCommandInput
  extends ResetServiceSettingRequest {}
export interface ResetServiceSettingCommandOutput
  extends ResetServiceSettingResult,
    __MetadataBearer {}
declare const ResetServiceSettingCommand_base: {
  new (
    input: ResetServiceSettingCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ResetServiceSettingCommandInput,
    ResetServiceSettingCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ResetServiceSettingCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ResetServiceSettingCommandInput,
    ResetServiceSettingCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ResetServiceSettingCommand extends ResetServiceSettingCommand_base {
  protected static __types: {
    api: {
      input: ResetServiceSettingRequest;
      output: ResetServiceSettingResult;
    };
    sdk: {
      input: ResetServiceSettingCommandInput;
      output: ResetServiceSettingCommandOutput;
    };
  };
}
