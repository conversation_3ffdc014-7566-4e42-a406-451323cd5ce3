{"resourceType": "Bundle", "id": "conceptmaps", "meta": {"lastUpdated": "2019-11-01T09:29:23.356+11:00"}, "type": "collection", "entry": [{"fullUrl": "http://hl7.org/fhir/ConceptMap/cm-contact-point-use-v3", "resource": {"resourceType": "ConceptMap", "id": "cm-contact-point-use-v3", "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><h2>v3.ContactPointUse (http://hl7.org/fhir/ConceptMap/cm-contact-point-use-v3)</h2><p>Mapping from <a href=\"valueset-contact-point-use.html\">http://hl7.org/fhir/ValueSet/contact-point-use</a> to <a href=\"v3/AddressUse/vs.html\">http://terminology.hl7.org/ValueSet/v3-AddressUse</a></p><p>DRAFT. Published on 01/11/2019 9:29:23 AM by HL7 (FHIR Project) (<a href=\"http://hl7.org/fhir\">http://hl7.org/fhir</a>, <a href=\"mailto:<EMAIL>\"><EMAIL></a>). </p><br/><table class=\"grid\"><tr><td colspan=\"1\"><b>Source Concept Details</b></td><td><b>Equivalence</b></td><td colspan=\"1\"><b>Destination Concept Details</b></td><td><b>Comment</b></td></tr><tr><td><b>Code</b> from <a href=\"codesystem-contact-point-use.html\" title=\"http://hl7.org/fhir/contact-point-use\">ContactPointUse</a></td><td/><td><b>Code</b> from <a href=\"v3/AddressUse/cs.html\" title=\"http://terminology.hl7.org/CodeSystem/v3-AddressUse\">v3 Code System AddressUse</a></td><td/></tr><tr><td>home (Home)</td><td><a href=\"codesystem-concept-map-equivalence.html#equal\">equal</a></td><td>H (home address)</td><td/></tr><tr><td>work (Work)</td><td><a href=\"codesystem-concept-map-equivalence.html#equal\">equal</a></td><td>WP (work place)</td><td/></tr><tr><td>temp (Temp)</td><td><a href=\"codesystem-concept-map-equivalence.html#equal\">equal</a></td><td>TMP (temporary address)</td><td/></tr><tr><td style=\"border-bottom-style: none\">old (Old)</td><td><a href=\"codesystem-concept-map-equivalence.html#narrower\">narrower</a></td><td>OLD (no longer in use)</td><td>Old and Bad</td></tr><tr><td style=\"border-top-style: none\"/><td><a href=\"codesystem-concept-map-equivalence.html#narrower\">narrower</a></td><td>BAD (bad address)</td><td>Old and Bad</td></tr><tr><td>mobile (Mobile)</td><td><a href=\"codesystem-concept-map-equivalence.html#equal\">equal</a></td><td>MC (mobile contact))</td><td/></tr></table></div>"}, "url": "http://hl7.org/fhir/ConceptMap/cm-contact-point-use-v3", "version": "4.0.1", "name": "v3.ContactPointUse", "title": "v3 map for ContactPointUse", "status": "draft", "date": "2019-11-01T09:29:23+11:00", "publisher": "HL7 (FHIR Project)", "contact": [{"telecom": [{"system": "url", "value": "http://hl7.org/fhir"}, {"system": "email", "value": "<EMAIL>"}]}], "sourceCanonical": "http://hl7.org/fhir/ValueSet/contact-point-use", "targetCanonical": "http://terminology.hl7.org/ValueSet/v3-AddressUse", "group": [{"source": "http://hl7.org/fhir/contact-point-use", "target": "http://terminology.hl7.org/CodeSystem/v3-AddressUse", "element": [{"code": "home", "target": [{"code": "H", "equivalence": "equal"}]}, {"code": "work", "target": [{"code": "WP", "equivalence": "equal"}]}, {"code": "temp", "target": [{"code": "TMP", "equivalence": "equal"}]}, {"code": "old", "target": [{"code": "OLD", "equivalence": "narrower", "comment": "Old and Bad"}, {"code": "BAD", "equivalence": "narrower", "comment": "Old and Bad"}]}, {"code": "mobile", "target": [{"code": "MC", "equivalence": "equal"}]}]}]}}, {"fullUrl": "http://hl7.org/fhir/ConceptMap/cm-contact-point-use-v2", "resource": {"resourceType": "ConceptMap", "id": "cm-contact-point-use-v2", "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><h2>v2.ContactPointUse (http://hl7.org/fhir/ConceptMap/cm-contact-point-use-v2)</h2><p>Mapping from <a href=\"valueset-contact-point-use.html\">http://hl7.org/fhir/ValueSet/contact-point-use</a> to <a href=\"v2/0201/index.html\">http://terminology.hl7.org/ValueSet/v2-0201</a></p><p>DRAFT. Published on 01/11/2019 9:29:23 AM by HL7 (FHIR Project) (<a href=\"http://hl7.org/fhir\">http://hl7.org/fhir</a>, <a href=\"mailto:<EMAIL>\"><EMAIL></a>). </p><br/><table class=\"grid\"><tr><td colspan=\"1\"><b>Source Concept Details</b></td><td><b>Equivalence</b></td><td colspan=\"1\"><b>Destination Concept Details</b></td></tr><tr><td><b>Code</b> from <a href=\"codesystem-contact-point-use.html\" title=\"http://hl7.org/fhir/contact-point-use\">ContactPointUse</a></td><td/><td><b>Code</b> from <a href=\"v2/0201/index.html\" title=\"http://terminology.hl7.org/CodeSystem/v2-0201\">v2 Telecommunication Use Code</a></td></tr><tr><td style=\"border-bottom-style: none\">home (Home)</td><td><a href=\"codesystem-concept-map-equivalence.html#wider\">wider</a></td><td>PRN (Primary Residence Number)</td></tr><tr><td style=\"border-top-style: none; border-bottom-style: none\"/><td><a href=\"codesystem-concept-map-equivalence.html#wider\">wider</a></td><td>ORN (Other Residence Number)</td></tr><tr><td style=\"border-top-style: none\"/><td><a href=\"codesystem-concept-map-equivalence.html#wider\">wider</a></td><td>VHN (Vacation Home Number)</td></tr><tr><td>work (Work)</td><td><a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a></td><td>WPN (Work Number)</td></tr><tr><td>mobile (Mobile)</td><td><a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a></td><td>PRS (Personal)</td></tr></table></div>"}, "url": "http://hl7.org/fhir/ConceptMap/cm-contact-point-use-v2", "version": "4.0.1", "name": "v2.ContactPointUse", "title": "v2 map for ContactPointUse", "status": "draft", "date": "2019-11-01T09:29:23+11:00", "publisher": "HL7 (FHIR Project)", "contact": [{"telecom": [{"system": "url", "value": "http://hl7.org/fhir"}, {"system": "email", "value": "<EMAIL>"}]}], "sourceCanonical": "http://hl7.org/fhir/ValueSet/contact-point-use", "targetCanonical": "http://terminology.hl7.org/ValueSet/v2-0201", "group": [{"source": "http://hl7.org/fhir/contact-point-use", "target": "http://terminology.hl7.org/CodeSystem/v2-0201", "element": [{"code": "home", "target": [{"code": "PRN", "equivalence": "wider"}, {"code": "ORN", "equivalence": "wider"}, {"code": "VHN", "equivalence": "wider"}]}, {"code": "work", "target": [{"code": "WPN", "equivalence": "equivalent"}]}, {"code": "mobile", "target": [{"code": "PRS", "equivalence": "equivalent"}]}]}]}}, {"fullUrl": "http://hl7.org/fhir/ConceptMap/cm-address-type-v3", "resource": {"resourceType": "ConceptMap", "id": "cm-address-type-v3", "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><h2>v3.AddressType (http://hl7.org/fhir/ConceptMap/cm-address-type-v3)</h2><p>Mapping from <a href=\"valueset-address-type.html\">http://hl7.org/fhir/ValueSet/address-type</a> to <a href=\"v3/AddressUse/vs.html\">http://terminology.hl7.org/ValueSet/v3-AddressUse</a></p><p>DRAFT. Published on 01/11/2019 9:29:23 AM by HL7 (FHIR Project) (<a href=\"http://hl7.org/fhir\">http://hl7.org/fhir</a>, <a href=\"mailto:<EMAIL>\"><EMAIL></a>). </p><br/><table class=\"grid\"><tr><td><b>Source Code</b></td><td><b>Equivalence</b></td><td><b>Destination Code</b></td></tr><tr><td>postal</td><td>equal</td><td>PST (postal address)</td></tr><tr><td>physical</td><td>equal</td><td>PHYS (physical visit address)</td></tr></table></div>"}, "url": "http://hl7.org/fhir/ConceptMap/cm-address-type-v3", "version": "4.0.1", "name": "v3.AddressType", "title": "v3 map for AddressType", "status": "draft", "date": "2019-11-01T09:29:23+11:00", "publisher": "HL7 (FHIR Project)", "contact": [{"telecom": [{"system": "url", "value": "http://hl7.org/fhir"}, {"system": "email", "value": "<EMAIL>"}]}], "sourceCanonical": "http://hl7.org/fhir/ValueSet/address-type", "targetCanonical": "http://terminology.hl7.org/ValueSet/v3-AddressUse", "group": [{"source": "http://hl7.org/fhir/address-type", "target": "http://terminology.hl7.org/CodeSystem/v3-AddressUse", "element": [{"code": "postal", "target": [{"code": "PST", "equivalence": "equal"}]}, {"code": "physical", "target": [{"code": "PHYS", "equivalence": "equal"}]}]}]}}, {"fullUrl": "http://hl7.org/fhir/ConceptMap/cm-name-use-v2", "resource": {"resourceType": "ConceptMap", "id": "cm-name-use-v2", "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><h2>v2.NameUse (http://hl7.org/fhir/ConceptMap/cm-name-use-v2)</h2><p>Mapping from <a href=\"valueset-name-use.html\">http://hl7.org/fhir/ValueSet/name-use</a> to <a href=\"v2/0200/index.html\">http://terminology.hl7.org/ValueSet/v2-0200</a></p><p>DRAFT. Published on 01/11/2019 9:29:23 AM by HL7 (FHIR Project) (<a href=\"http://hl7.org/fhir\">http://hl7.org/fhir</a>, <a href=\"mailto:<EMAIL>\"><EMAIL></a>). </p><br/><table class=\"grid\"><tr><td colspan=\"1\"><b>Source Concept Details</b></td><td><b>Equivalence</b></td><td colspan=\"1\"><b>Destination Concept Details</b></td></tr><tr><td><b>Code</b> from <a href=\"codesystem-name-use.html\" title=\"http://hl7.org/fhir/name-use\">NameUse</a></td><td/><td><b>Code</b> from <a href=\"v2/0200/index.html\" title=\"http://terminology.hl7.org/CodeSystem/v2-0200\">v2 Name Type</a></td></tr><tr><td>usual (Usual)</td><td><a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a></td><td>D (Customary Name)</td></tr><tr><td>official (Official)</td><td><a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a></td><td>L (Official Registry Name)</td></tr><tr><td>temp (Temp)</td><td><a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a></td><td>TEMP (Temporary Name)</td></tr><tr><td>nickname (Nickname)</td><td><a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a></td><td>N (Nickname)</td></tr><tr><td>anonymous (Anonymous)</td><td><a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a></td><td>S (Pseudonym)</td></tr><tr><td style=\"border-bottom-style: none\">old (Old)</td><td><a href=\"codesystem-concept-map-equivalence.html#wider\">wider</a></td><td>NOUSE (No Longer To Be Used)</td></tr><tr><td style=\"border-top-style: none\"/><td><a href=\"codesystem-concept-map-equivalence.html#wider\">wider</a></td><td>BAD (Bad Name)</td></tr><tr><td>maiden (Name changed for Marriage)</td><td><a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a></td><td>M (Maiden Name)</td></tr></table></div>"}, "url": "http://hl7.org/fhir/ConceptMap/cm-name-use-v2", "version": "4.0.1", "name": "v2.NameUse", "title": "v2 map for NameUse", "status": "draft", "date": "2019-11-01T09:29:23+11:00", "publisher": "HL7 (FHIR Project)", "contact": [{"telecom": [{"system": "url", "value": "http://hl7.org/fhir"}, {"system": "email", "value": "<EMAIL>"}]}], "sourceCanonical": "http://hl7.org/fhir/ValueSet/name-use", "targetCanonical": "http://terminology.hl7.org/ValueSet/v2-0200", "group": [{"source": "http://hl7.org/fhir/name-use", "target": "http://terminology.hl7.org/CodeSystem/v2-0200", "element": [{"code": "usual", "target": [{"code": "D", "equivalence": "equivalent"}]}, {"code": "official", "target": [{"code": "L", "equivalence": "equivalent"}]}, {"code": "temp", "target": [{"code": "TEMP", "equivalence": "equivalent"}]}, {"code": "nickname", "target": [{"code": "N", "equivalence": "equivalent"}]}, {"code": "anonymous", "target": [{"code": "S", "equivalence": "equivalent"}]}, {"code": "old", "target": [{"code": "NOUSE", "equivalence": "wider"}, {"code": "BAD", "equivalence": "wider"}]}, {"code": "maiden", "target": [{"code": "M", "equivalence": "equivalent"}]}]}]}}, {"fullUrl": "http://hl7.org/fhir/ConceptMap/cm-name-use-v3", "resource": {"resourceType": "ConceptMap", "id": "cm-name-use-v3", "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><h2>v3.NameUse (http://hl7.org/fhir/ConceptMap/cm-name-use-v3)</h2><p>Mapping from <a href=\"valueset-name-use.html\">http://hl7.org/fhir/ValueSet/name-use</a> to <a href=\"v3/EntityNameUseR2/vs.html\">http://terminology.hl7.org/ValueSet/v3-EntityNameUseR2</a></p><p>DRAFT. Published on 01/11/2019 9:29:23 AM by HL7 (FHIR Project) (<a href=\"http://hl7.org/fhir\">http://hl7.org/fhir</a>, <a href=\"mailto:<EMAIL>\"><EMAIL></a>). </p><br/><table class=\"grid\"><tr><td><b>Source Code</b></td><td><b>Equivalence</b></td><td><b>Destination Code</b></td></tr><tr><td>usual</td><td>equal</td><td>C (customary)</td></tr><tr><td>official</td><td>equal</td><td>OR (official registry name)</td></tr><tr><td>temp</td><td>equal</td><td>T (temporary)</td></tr><tr><td>nickname</td><td>equivalent</td><td>P (Other/Pseudonym/Alias)</td></tr><tr><td>anonymous</td><td>equivalent</td><td>ANON (Anonymous)</td></tr><tr><td>old</td><td>equivalent</td><td>OLD (no longer in use)</td></tr><tr><td>maiden (Name changed for Marriage)</td><td>equal</td><td>M (maiden name)</td></tr></table></div>"}, "url": "http://hl7.org/fhir/ConceptMap/cm-name-use-v3", "version": "4.0.1", "name": "v3.NameUse", "title": "v3 map for NameUse", "status": "draft", "date": "2019-11-01T09:29:23+11:00", "publisher": "HL7 (FHIR Project)", "contact": [{"telecom": [{"system": "url", "value": "http://hl7.org/fhir"}, {"system": "email", "value": "<EMAIL>"}]}], "sourceCanonical": "http://hl7.org/fhir/ValueSet/name-use", "targetCanonical": "http://terminology.hl7.org/ValueSet/v3-EntityNameUseR2", "group": [{"source": "http://hl7.org/fhir/name-use", "target": "http://terminology.hl7.org/CodeSystem/v3-EntityNameUseR2", "element": [{"code": "usual", "target": [{"code": "C", "equivalence": "equal"}]}, {"code": "official", "target": [{"code": "OR", "equivalence": "equal"}]}, {"code": "temp", "target": [{"code": "T", "equivalence": "equal"}]}, {"code": "nickname", "target": [{"code": "P", "equivalence": "equivalent"}]}, {"code": "anonymous", "target": [{"code": "ANON", "equivalence": "equivalent"}]}, {"code": "old", "target": [{"code": "OLD", "equivalence": "equivalent"}]}, {"code": "maiden", "target": [{"code": "M", "equivalence": "equal"}]}]}]}}, {"fullUrl": "http://hl7.org/fhir/ConceptMap/cm-address-use-v3", "resource": {"resourceType": "ConceptMap", "id": "cm-address-use-v3", "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><h2>v3.AddressUse (http://hl7.org/fhir/ConceptMap/cm-address-use-v3)</h2><p>Mapping from <a href=\"valueset-address-use.html\">http://hl7.org/fhir/ValueSet/address-use</a> to <a href=\"v3/AddressUse/vs.html\">http://terminology.hl7.org/ValueSet/v3-AddressUse</a></p><p>DRAFT. Published on 01/11/2019 9:29:23 AM by HL7 (FHIR Project) (<a href=\"http://hl7.org/fhir\">http://hl7.org/fhir</a>, <a href=\"mailto:<EMAIL>\"><EMAIL></a>). </p><br/><table class=\"grid\"><tr><td colspan=\"1\"><b>Source Concept Details</b></td><td><b>Equivalence</b></td><td colspan=\"1\"><b>Destination Concept Details</b></td><td><b>Comment</b></td></tr><tr><td><b>Code</b> from <a href=\"codesystem-address-use.html\" title=\"http://hl7.org/fhir/address-use\">AddressUse</a></td><td/><td><b>Code</b> from <a href=\"v3/AddressUse/cs.html\" title=\"http://terminology.hl7.org/CodeSystem/v3-AddressUse\">v3 Code System AddressUse</a></td><td/></tr><tr><td>home (Home)</td><td><a href=\"codesystem-concept-map-equivalence.html#equal\">equal</a></td><td>H (home address)</td><td/></tr><tr><td>work (Work)</td><td><a href=\"codesystem-concept-map-equivalence.html#equal\">equal</a></td><td>WP (work place)</td><td/></tr><tr><td>temp (Temporary)</td><td><a href=\"codesystem-concept-map-equivalence.html#equal\">equal</a></td><td>TMP (temporary address)</td><td/></tr><tr><td style=\"border-bottom-style: none\">old (Old / Incorrect)</td><td><a href=\"codesystem-concept-map-equivalence.html#narrower\">narrower</a></td><td>OLD (no longer in use)</td><td>Bad or Old</td></tr><tr><td style=\"border-top-style: none\"/><td><a href=\"codesystem-concept-map-equivalence.html#narrower\">narrower</a></td><td>BAD (bad address)</td><td>Bad or Old</td></tr></table></div>"}, "url": "http://hl7.org/fhir/ConceptMap/cm-address-use-v3", "version": "4.0.1", "name": "v3.AddressUse", "title": "v3 map for AddressUse", "status": "draft", "date": "2019-11-01T09:29:23+11:00", "publisher": "HL7 (FHIR Project)", "contact": [{"telecom": [{"system": "url", "value": "http://hl7.org/fhir"}, {"system": "email", "value": "<EMAIL>"}]}], "sourceCanonical": "http://hl7.org/fhir/ValueSet/address-use", "targetCanonical": "http://terminology.hl7.org/ValueSet/v3-AddressUse", "group": [{"source": "http://hl7.org/fhir/address-use", "target": "http://terminology.hl7.org/CodeSystem/v3-AddressUse", "element": [{"code": "home", "target": [{"code": "H", "equivalence": "equal"}]}, {"code": "work", "target": [{"code": "WP", "equivalence": "equal"}]}, {"code": "temp", "target": [{"code": "TMP", "equivalence": "equal"}]}, {"code": "old", "target": [{"code": "OLD", "equivalence": "narrower", "comment": "Bad or Old"}, {"code": "BAD", "equivalence": "narrower", "comment": "Bad or Old"}]}]}]}}, {"fullUrl": "http://hl7.org/fhir/ConceptMap/cm-administrative-gender-v2", "resource": {"resourceType": "ConceptMap", "id": "cm-administrative-gender-v2", "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><h2>v2.AdministrativeGender (http://hl7.org/fhir/ConceptMap/cm-administrative-gender-v2)</h2><p>Mapping from <a href=\"valueset-administrative-gender.html\">http://hl7.org/fhir/ValueSet/administrative-gender</a> to <a href=\"v2/0001/index.html\">http://terminology.hl7.org/ValueSet/v2-0001</a></p><p>DRAFT. Published on 01/11/2019 9:29:23 AM by HL7 (FHIR Project). </p><br/><table class=\"grid\"><tr><td colspan=\"1\"><b>Source Concept Details</b></td><td><b>Equivalence</b></td><td colspan=\"1\"><b>Destination Concept Details</b></td></tr><tr><td><b>Code</b> from <a href=\"codesystem-administrative-gender.html\" title=\"http://hl7.org/fhir/administrative-gender\">AdministrativeGender</a></td><td/><td><b>Code</b> from <a href=\"v2/0001/index.html\" title=\"http://terminology.hl7.org/CodeSystem/v2-0001\">v2 SEX</a></td></tr><tr><td>male (Male)</td><td><a href=\"codesystem-concept-map-equivalence.html#equal\">equal</a></td><td>M (Male)</td></tr><tr><td>female (Female)</td><td><a href=\"codesystem-concept-map-equivalence.html#equal\">equal</a></td><td>F (Female)</td></tr><tr><td style=\"border-bottom-style: none\">other (Other)</td><td><a href=\"codesystem-concept-map-equivalence.html#wider\">wider</a></td><td>A (Ambiguous)</td></tr><tr><td style=\"border-top-style: none\"/><td><a href=\"codesystem-concept-map-equivalence.html#wider\">wider</a></td><td>O (Other)</td></tr><tr><td>unknown (Unknown)</td><td><a href=\"codesystem-concept-map-equivalence.html#equal\">equal</a></td><td>U (Unknown)</td></tr></table></div>"}, "url": "http://hl7.org/fhir/ConceptMap/cm-administrative-gender-v2", "version": "4.0.1", "name": "v2.AdministrativeGender", "title": "v2 map for AdministrativeGender", "status": "draft", "date": "2019-11-01T09:29:23+11:00", "publisher": "HL7 (FHIR Project)", "sourceCanonical": "http://hl7.org/fhir/ValueSet/administrative-gender", "targetCanonical": "http://terminology.hl7.org/ValueSet/v2-0001", "group": [{"source": "http://hl7.org/fhir/administrative-gender", "target": "http://terminology.hl7.org/CodeSystem/v2-0001", "element": [{"code": "male", "target": [{"code": "M", "equivalence": "equal"}]}, {"code": "female", "target": [{"code": "F", "equivalence": "equal"}]}, {"code": "other", "target": [{"code": "A", "equivalence": "wider"}, {"code": "O", "equivalence": "wider"}]}, {"code": "unknown", "target": [{"code": "U", "equivalence": "equal"}]}]}]}}, {"fullUrl": "http://hl7.org/fhir/ConceptMap/cm-administrative-gender-v3", "resource": {"resourceType": "ConceptMap", "id": "cm-administrative-gender-v3", "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><h2>v3.AdministrativeGender (http://hl7.org/fhir/ConceptMap/cm-administrative-gender-v3)</h2><p>Mapping from <a href=\"valueset-administrative-gender.html\">http://hl7.org/fhir/ValueSet/administrative-gender</a> to <a href=\"v3/AdministrativeGender/vs.html\">http://terminology.hl7.org/ValueSet/v3-AdministrativeGender</a></p><p>DRAFT. Published on 01/11/2019 9:29:23 AM by HL7 (FHIR Project). </p><br/><table class=\"grid\"><tr><td><b>Source Code</b></td><td><b>Equivalence</b></td><td><b>Destination Code</b></td></tr><tr><td>male</td><td>equal</td><td>M (Male)</td></tr><tr><td>female</td><td>equal</td><td>F (Female)</td></tr><tr><td>other</td><td>wider</td><td>UN (Undifferentiated)</td></tr><tr><td>unknown</td><td>equal</td><td>UNK</td></tr></table></div>"}, "url": "http://hl7.org/fhir/ConceptMap/cm-administrative-gender-v3", "version": "4.0.1", "name": "v3.Administrative<PERSON><PERSON>", "title": "v3 map for AdministrativeGender", "status": "draft", "date": "2019-11-01T09:29:23+11:00", "publisher": "HL7 (FHIR Project)", "sourceCanonical": "http://hl7.org/fhir/ValueSet/administrative-gender", "targetCanonical": "http://terminology.hl7.org/ValueSet/v3-AdministrativeGender", "group": [{"source": "http://hl7.org/fhir/administrative-gender", "target": "http://terminology.hl7.org/CodeSystem/v3-AdministrativeGender", "element": [{"code": "male", "target": [{"code": "M", "equivalence": "equal"}]}, {"code": "female", "target": [{"code": "F", "equivalence": "equal"}]}, {"code": "other", "target": [{"code": "UN", "equivalence": "wider"}]}, {"code": "unknown", "target": [{"code": "UNK", "equivalence": "equal"}]}]}]}}, {"fullUrl": "http://hl7.org/fhir/ConceptMap/cm-document-reference-status-v3", "resource": {"resourceType": "ConceptMap", "id": "cm-document-reference-status-v3", "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><h2>v3.DocumentReferenceStatus (http://hl7.org/fhir/ConceptMap/cm-document-reference-status-v3)</h2><p>Mapping from <a href=\"valueset-document-reference-status.html\">http://hl7.org/fhir/ValueSet/document-reference-status</a> to <a href=\"v3/ActStatus/vs.html\">http://terminology.hl7.org/ValueSet/v3-ActStatus</a></p><p>DRAFT. Published on 01/11/2019 9:29:23 AM by HL7 (FHIR Project). </p><br/><table class=\"grid\"><tr><td><b>Source Code</b></td><td><b>Equivalence</b></td><td><b>Destination Code</b></td></tr><tr><td>current</td><td>equivalent</td><td>active</td></tr><tr><td>superseded</td><td>equivalent</td><td>obsolete</td></tr><tr><td>entered-in-error</td><td>equivalent</td><td>nullified</td></tr></table></div>"}, "url": "http://hl7.org/fhir/ConceptMap/cm-document-reference-status-v3", "version": "4.0.1", "name": "v3.DocumentReferenceStatus", "title": "v3 map for DocumentReferenceStatus", "status": "draft", "date": "2019-11-01T09:29:23+11:00", "publisher": "HL7 (FHIR Project)", "sourceCanonical": "http://hl7.org/fhir/ValueSet/document-reference-status", "targetCanonical": "http://terminology.hl7.org/ValueSet/v3-ActStatus", "group": [{"source": "http://hl7.org/fhir/document-reference-status", "target": "http://terminology.hl7.org/CodeSystem/v3-ActStatus", "element": [{"code": "current", "target": [{"code": "active", "equivalence": "equivalent"}]}, {"code": "superseded", "target": [{"code": "obsolete", "equivalence": "equivalent"}]}, {"code": "entered-in-error", "target": [{"code": "nullified", "equivalence": "equivalent"}]}]}]}}, {"fullUrl": "http://hl7.org/fhir/ConceptMap/cm-address-use-v2", "resource": {"resourceType": "ConceptMap", "id": "cm-address-use-v2", "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><h2>v2.AddressUse (http://hl7.org/fhir/ConceptMap/cm-address-use-v2)</h2><p>Mapping from <a href=\"valueset-address-use.html\">http://hl7.org/fhir/ValueSet/address-use</a> to <a href=\"v2/0190/index.html\">http://terminology.hl7.org/ValueSet/v2-0190</a></p><p>DRAFT. Published on 01/11/2019 9:29:23 AM by HL7 (FHIR Project) (<a href=\"http://hl7.org/fhir\">http://hl7.org/fhir</a>, <a href=\"mailto:<EMAIL>\"><EMAIL></a>). </p><br/><table class=\"grid\"><tr><td><b>Source Code</b></td><td><b>Equivalence</b></td><td><b>Destination Code</b></td><td><b>Comment</b></td></tr><tr><td>home</td><td>equivalent</td><td>H (Home)</td><td/></tr><tr><td>work</td><td>equivalent</td><td>O (Office/Business)</td><td/></tr><tr><td>temp (Temporary)</td><td>equivalent</td><td>C (Current Or Temporary)</td><td/></tr><tr><td>old (Old / Incorrect)</td><td>wider</td><td>BA (Bad address)</td><td>unclear about old addresses</td></tr><tr><td>billing</td><td>equivalent</td><td>BI (Billing Address)</td><td/></tr></table></div>"}, "url": "http://hl7.org/fhir/ConceptMap/cm-address-use-v2", "version": "4.0.1", "name": "v2.AddressUse", "title": "v2 map for AddressUse", "status": "draft", "date": "2019-11-01T09:29:23+11:00", "publisher": "HL7 (FHIR Project)", "contact": [{"telecom": [{"system": "url", "value": "http://hl7.org/fhir"}, {"system": "email", "value": "<EMAIL>"}]}], "sourceCanonical": "http://hl7.org/fhir/ValueSet/address-use", "targetCanonical": "http://terminology.hl7.org/ValueSet/v2-0190", "group": [{"source": "http://hl7.org/fhir/address-use", "target": "http://terminology.hl7.org/CodeSystem/v2-0190", "element": [{"code": "home", "target": [{"code": "H", "equivalence": "equivalent"}]}, {"code": "work", "target": [{"code": "O", "equivalence": "equivalent"}]}, {"code": "temp", "target": [{"code": "C", "equivalence": "equivalent"}]}, {"code": "old", "target": [{"code": "BA", "equivalence": "wider", "comment": "unclear about old addresses"}]}, {"code": "billing", "target": [{"code": "BI", "equivalence": "equivalent"}]}]}]}}, {"fullUrl": "http://hl7.org/fhir/ConceptMap/cm-detectedissue-severity-v3", "resource": {"resourceType": "ConceptMap", "id": "cm-detectedissue-severity-v3", "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><h2>v3.DetectedIssueSeverity (http://hl7.org/fhir/ConceptMap/cm-detectedissue-severity-v3)</h2><p>Mapping from <a href=\"valueset-detectedissue-severity.html\">http://hl7.org/fhir/ValueSet/detectedissue-severity</a> to <a href=\"v3/SeverityObservation/vs.html\">http://terminology.hl7.org/ValueSet/v3-SeverityObservation</a></p><p>DRAFT. Published on 01/11/2019 9:29:23 AM by HL7 (FHIR Project) (<a href=\"http://hl7.org/fhir\">http://hl7.org/fhir</a>, <a href=\"mailto:<EMAIL>\"><EMAIL></a>). </p><br/><table class=\"grid\"><tr><td><b>Source Code</b></td><td><b>Equivalence</b></td><td><b>Destination Code</b></td></tr><tr><td>high</td><td>equal</td><td>H (High)</td></tr><tr><td>moderate</td><td>equal</td><td>M (Moderate)</td></tr><tr><td>low</td><td>equal</td><td>L (Low)</td></tr></table></div>"}, "url": "http://hl7.org/fhir/ConceptMap/cm-detectedissue-severity-v3", "version": "4.0.1", "name": "v3.DetectedIssueSeverity", "title": "v3 map for DetectedIssueSeverity", "status": "draft", "date": "2019-11-01T09:29:23+11:00", "publisher": "HL7 (FHIR Project)", "contact": [{"telecom": [{"system": "url", "value": "http://hl7.org/fhir"}, {"system": "email", "value": "<EMAIL>"}]}], "sourceCanonical": "http://hl7.org/fhir/ValueSet/detectedissue-severity", "targetCanonical": "http://terminology.hl7.org/ValueSet/v3-SeverityObservation", "group": [{"source": "http://hl7.org/fhir/detectedissue-severity", "target": "http://terminology.hl7.org/CodeSystem/v3-ObservationValue", "element": [{"code": "high", "target": [{"code": "H", "equivalence": "equal"}]}, {"code": "moderate", "target": [{"code": "M", "equivalence": "equal"}]}, {"code": "low", "target": [{"code": "L", "equivalence": "equal"}]}]}]}}, {"fullUrl": "http://hl7.org/fhir/ConceptMap/cm-composition-status-v3", "resource": {"resourceType": "ConceptMap", "id": "cm-composition-status-v3", "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><h2>v3.CompositionStatus (http://hl7.org/fhir/ConceptMap/cm-composition-status-v3)</h2><p>Mapping from <a href=\"valueset-composition-status.html\">http://hl7.org/fhir/ValueSet/composition-status</a> to <a href=\"v3/ActStatus/vs.html\">http://terminology.hl7.org/ValueSet/v3-ActStatus</a></p><p>DRAFT. Published on 01/11/2019 9:29:23 AM by HL7 (FHIR Project) (<a href=\"http://hl7.org/fhir\">http://hl7.org/fhir</a>, <a href=\"mailto:<EMAIL>\"><EMAIL></a>). </p><br/><table class=\"grid\"><tr><td><b>Source Code</b></td><td><b>Equivalence</b></td><td><b>Destination Code</b></td></tr><tr><td>preliminary</td><td>equivalent</td><td>active</td></tr><tr><td>final</td><td>wider</td><td>completed</td></tr><tr><td>amended</td><td>wider</td><td>completed</td></tr><tr><td>entered-in-error</td><td>equivalent</td><td>nullified</td></tr></table></div>"}, "url": "http://hl7.org/fhir/ConceptMap/cm-composition-status-v3", "version": "4.0.1", "name": "v3.CompositionStatus", "title": "v3 map for CompositionStatus", "status": "draft", "date": "2019-11-01T09:29:23+11:00", "publisher": "HL7 (FHIR Project)", "contact": [{"telecom": [{"system": "url", "value": "http://hl7.org/fhir"}, {"system": "email", "value": "<EMAIL>"}]}], "sourceCanonical": "http://hl7.org/fhir/ValueSet/composition-status", "targetCanonical": "http://terminology.hl7.org/ValueSet/v3-ActStatus", "group": [{"source": "http://hl7.org/fhir/composition-status", "target": "http://terminology.hl7.org/CodeSystem/v3-ActStatus", "element": [{"code": "preliminary", "target": [{"code": "active", "equivalence": "equivalent"}]}, {"code": "final", "target": [{"code": "completed", "equivalence": "wider"}]}, {"code": "amended", "target": [{"code": "completed", "equivalence": "wider"}]}, {"code": "entered-in-error", "target": [{"code": "nullified", "equivalence": "equivalent"}]}]}]}}, {"fullUrl": "http://hl7.org/fhir/ConceptMap/cm-data-absent-reason-v3", "resource": {"resourceType": "ConceptMap", "id": "cm-data-absent-reason-v3", "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><h2>v3.DataAbsentReason (http://hl7.org/fhir/ConceptMap/cm-data-absent-reason-v3)</h2><p>Mapping from <a href=\"valueset-data-absent-reason.html\">http://hl7.org/fhir/ValueSet/data-absent-reason</a> to <a href=\"v3/NullFlavor/vs.html\">http://terminology.hl7.org/ValueSet/v3-NullFlavor</a></p><p>DRAFT. Published on 01/11/2019 9:29:23 AM by HL7 (FHIR Project). </p><br/><table class=\"grid\"><tr><td><b>Source Code</b></td><td><b>Equivalence</b></td><td><b>Destination Code</b></td></tr><tr><td>unknown</td><td>equal</td><td><PERSON>K (unknown)</td></tr><tr><td>asked-unknown (Asked But Unknown)</td><td>equal</td><td>ASKU (asked but unknown)</td></tr><tr><td>temp-unknown (Temporarily Unknown)</td><td>equal</td><td>NAV (temporarily unavailable)</td></tr><tr><td>not-asked</td><td>equal</td><td>NASK (not asked)</td></tr><tr><td>masked</td><td>equal</td><td>MSK (masked)</td></tr><tr><td>not-applicable</td><td>equal</td><td>NA (not applicable)</td></tr><tr><td>negative-infinity (Negative Infinity (NINF))</td><td>equal</td><td>NINF (negative infinity)</td></tr><tr><td>positive-infinity (Positive Infinity (PINF))</td><td>equal</td><td>PINF (positive infinity)</td></tr><tr><td>not-permitted</td><td>equal</td><td>OTH (other)</td></tr></table></div>"}, "url": "http://hl7.org/fhir/ConceptMap/cm-data-absent-reason-v3", "version": "4.0.1", "name": "v3.DataAbsentReason", "title": "v3 map for DataAbsentReason", "status": "draft", "date": "2019-11-01T09:29:23+11:00", "publisher": "HL7 (FHIR Project)", "sourceCanonical": "http://hl7.org/fhir/ValueSet/data-absent-reason", "targetCanonical": "http://terminology.hl7.org/ValueSet/v3-NullFlavor", "group": [{"source": "http://terminology.hl7.org/CodeSystem/data-absent-reason", "target": "http://terminology.hl7.org/CodeSystem/v3-NullFlavor", "element": [{"code": "unknown", "target": [{"code": "UNK", "equivalence": "equal"}]}, {"code": "asked-unknown", "target": [{"code": "ASKU", "equivalence": "equal"}]}, {"code": "temp-unknown", "target": [{"code": "NAV", "equivalence": "equal"}]}, {"code": "not-asked", "target": [{"code": "NASK", "equivalence": "equal"}]}, {"code": "masked", "target": [{"code": "MSK", "equivalence": "equal"}]}, {"code": "not-applicable", "target": [{"code": "NA", "equivalence": "equal"}]}, {"code": "negative-infinity", "target": [{"code": "NINF", "equivalence": "equal"}]}, {"code": "positive-infinity", "target": [{"code": "PINF", "equivalence": "equal"}]}, {"code": "not-permitted", "target": [{"code": "OTH", "equivalence": "equal"}]}]}]}}, {"fullUrl": "http://hl7.org/fhir/ConceptMap/cm-contact-point-system-v2", "resource": {"resourceType": "ConceptMap", "id": "cm-contact-point-system-v2", "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\"><h2>v2.ContactPointSystem (http://hl7.org/fhir/ConceptMap/cm-contact-point-system-v2)</h2><p>Mapping from <a href=\"valueset-contact-point-system.html\">http://hl7.org/fhir/ValueSet/contact-point-system</a> to <a href=\"v2/0202/index.html\">http://terminology.hl7.org/ValueSet/v2-0202</a></p><p>DRAFT. Published on 01/11/2019 9:29:23 AM by HL7 (FHIR Project) (<a href=\"http://hl7.org/fhir\">http://hl7.org/fhir</a>, <a href=\"mailto:<EMAIL>\"><EMAIL></a>). </p><br/><table class=\"grid\"><tr><td><b>Source Code</b></td><td><b>Equivalence</b></td><td><b>Destination Code</b></td><td><b>Comment</b></td></tr><tr><td>phone</td><td>equivalent</td><td>PH (Telephone)</td><td/></tr><tr><td>fax</td><td>equivalent</td><td>FX (Fax)</td><td/></tr><tr><td>email</td><td>narrower</td><td>Internet (Internet Address)</td><td>for email addresses</td></tr><tr><td>pager</td><td>equivalent</td><td>BP (Beeper)</td><td/></tr><tr><td>url</td><td>narrower</td><td>Internet (Internet Address)</td><td>for non-email kinds of addresses</td></tr></table></div>"}, "url": "http://hl7.org/fhir/ConceptMap/cm-contact-point-system-v2", "version": "4.0.1", "name": "v2.ContactPointSystem", "title": "v2 map for ContactPointSystem", "status": "draft", "date": "2019-11-01T09:29:23+11:00", "publisher": "HL7 (FHIR Project)", "contact": [{"telecom": [{"system": "url", "value": "http://hl7.org/fhir"}, {"system": "email", "value": "<EMAIL>"}]}], "sourceCanonical": "http://hl7.org/fhir/ValueSet/contact-point-system", "targetCanonical": "http://terminology.hl7.org/ValueSet/v2-0202", "group": [{"source": "http://hl7.org/fhir/contact-point-system", "target": "http://terminology.hl7.org/CodeSystem/v2-0202", "element": [{"code": "phone", "target": [{"code": "PH", "equivalence": "equivalent"}]}, {"code": "fax", "target": [{"code": "FX", "equivalence": "equivalent"}]}, {"code": "email", "target": [{"code": "Internet", "equivalence": "narrower", "comment": "for email addresses"}]}, {"code": "pager", "target": [{"code": "BP", "equivalence": "equivalent"}]}, {"code": "url", "target": [{"code": "Internet", "equivalence": "narrower", "comment": "for non-email kinds of addresses"}]}]}]}}, {"fullUrl": "http://hl7.org/fhir/ConceptMap/101", "resource": {"resourceType": "ConceptMap", "id": "101", "meta": {"lastUpdated": "2019-11-01T09:29:23.356+11:00"}, "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\">\n      <h2>FHIR-v3-Address-Use (http://hl7.org/fhir/ConceptMap/101)</h2>\n      <p>Mapping from \n        <a href=\"valueset-address-use.html\">http://hl7.org/fhir/ValueSet/address-use</a> to \n        <a href=\"v3/AddressUse/vs.html\">http://terminology.hl7.org/ValueSet/v3-AddressUse</a>\n      </p>\n      <p>DRAFT (not intended for production usage). Published on 13/06/2012 by HL7, Inc (FHIR project team (example): \n        <a href=\"http://hl7.org/fhir\">http://hl7.org/fhir</a>). Creative Commons 0\n      </p>\n      <div>\n        <p>A mapping between the FHIR and HL7 v3 AddressUse Code systems</p>\n\n      </div>\n      <br/>\n      <table class=\"grid\">\n        <tr>\n          <td>\n            <b>Source Code</b>\n          </td>\n          <td>\n            <b>Equivalence</b>\n          </td>\n          <td>\n            <b>Destination Code</b>\n          </td>\n          <td>\n            <b>Comment</b>\n          </td>\n        </tr>\n        <tr>\n          <td>home</td>\n          <td>equivalent</td>\n          <td>H (home address)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>work</td>\n          <td>equivalent</td>\n          <td>WP (work place)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>temp (Temporary)</td>\n          <td>equivalent</td>\n          <td>TMP (temporary address)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>old (Old / Incorrect)</td>\n          <td>disjoint</td>\n          <td>BAD (bad address)</td>\n          <td>In the HL7 v3 AD, old is handled by the usablePeriod element, but you have to provide a time, there's no simple equivalent of flagging an address as old</td>\n        </tr>\n      </table>\n    </div>"}, "url": "http://hl7.org/fhir/ConceptMap/101", "identifier": {"system": "urn:ietf:rfc:3986", "value": "urn:uuid:53cd62ee-033e-414c-9f58-3ca97b5ffc3b"}, "version": "4.0.1", "name": "FHIR-v3-Address-Use", "title": "FHIR/v3 Address Use Mapping", "status": "draft", "experimental": true, "date": "2012-06-13", "publisher": "HL7, Inc", "contact": [{"name": "FHIR project team (example)", "telecom": [{"system": "url", "value": "http://hl7.org/fhir"}]}], "description": "A mapping between the FHIR and HL7 v3 AddressUse Code systems", "useContext": [{"code": {"system": "http://terminology.hl7.org/CodeSystem/usage-context-type", "code": "venue"}, "valueCodeableConcept": {"text": "for CCDA Usage"}}], "jurisdiction": [{"coding": [{"system": "urn:iso:std:iso:3166", "code": "US"}]}], "purpose": "To help implementers map from HL7 v3/CDA to FHIR", "copyright": "Creative Commons 0", "sourceUri": "http://hl7.org/fhir/ValueSet/address-use", "targetUri": "http://terminology.hl7.org/ValueSet/v3-AddressUse", "group": [{"source": "http://hl7.org/fhir/address-use", "target": "http://terminology.hl7.org/CodeSystem/v3-AddressUse", "element": [{"code": "home", "display": "home", "target": [{"code": "H", "display": "home", "equivalence": "equivalent"}]}, {"code": "work", "display": "work", "target": [{"code": "WP", "display": "work place", "equivalence": "equivalent"}]}, {"code": "temp", "display": "temp", "target": [{"code": "TMP", "display": "temporary address", "equivalence": "equivalent"}]}, {"code": "old", "display": "old", "target": [{"code": "BAD", "display": "bad address", "equivalence": "disjoint", "comment": "In the HL7 v3 AD, old is handled by the usablePeriod element, but you have to provide a time, there's no simple equivalent of flagging an address as old"}]}], "unmapped": {"mode": "fixed", "code": "temp", "display": "temp"}}]}}, {"fullUrl": "http://hl7.org/fhir/ConceptMap/103", "resource": {"resourceType": "ConceptMap", "id": "103", "meta": {"lastUpdated": "2019-11-01T09:29:23.356+11:00"}, "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\">\n      <h2>SNOMED CT to ICD-10-CM mappings for fracture of ulna (http://hl7.org/fhir/ConceptMap/103)</h2>\n      <p>Mapping from http://snomed.info/sct?fhir_vs to http://hl7.org/fhir/sid/icd-10-us</p>\n      <p>DRAFT (not intended for production usage). Published on 13/06/2012 by HL7, Inc (FHIR project team (example): \n        <a href=\"http://hl7.org/fhir\">http://hl7.org/fhir</a>). Creative Commons 0\n      </p>\n      <div>\n        <p>Example rule-based mappings between SNOMED CT to ICD-10-CM for fracture of ulna</p>\n\n      </div>\n      <br/>\n      <table class=\"grid\">\n        <tr>\n          <td>\n            <b>Source Code</b>\n          </td>\n          <td>\n            <b>Equivalence</b>\n          </td>\n          <td>\n            <b>Destination Code</b>\n          </td>\n          <td>\n            <b>Comment</b>\n          </td>\n        </tr>\n        <tr>\n          <td>263204007 (Fracture of shaft of ulna)</td>\n          <td>narrower</td>\n          <td>S52.209A</td>\n          <td>The target mapping to ICD-10-CM is narrower, since additional patient data on the encounter (initial vs. subsequent) and fracture type is required for a valid ICD-10-CM mapping.</td>\n        </tr>\n        <tr>\n          <td/>\n          <td>narrower</td>\n          <td>S52.209D</td>\n          <td>The target mapping to ICD-10-CM is narrower, since additional patient data on the encounter (initial vs. subsequent), fracture type and healing (for subsequent encounter) is required for a valid ICD-10-CM mapping.</td>\n        </tr>\n      </table>\n    </div>"}, "url": "http://hl7.org/fhir/ConceptMap/103", "identifier": {"system": "urn:ietf:rfc:3986", "value": "urn:uuid:53cd62ee-033e-414c-9f58-3ca97b5ffc3b"}, "version": "4.0.1", "name": "SNOMED CT to ICD-10-CM mappings for fracture of ulna", "status": "draft", "experimental": true, "date": "2012-06-13", "publisher": "HL7, Inc", "contact": [{"name": "FHIR project team (example)", "telecom": [{"system": "url", "value": "http://hl7.org/fhir"}]}], "description": "Example rule-based mappings between SNOMED CT to ICD-10-CM for fracture of ulna", "jurisdiction": [{"coding": [{"system": "http://unstats.un.org/unsd/methods/m49/m49.htm", "code": "840", "display": "United States of America"}]}], "purpose": "Show example rule based mappings", "copyright": "Creative Commons 0", "sourceCanonical": "http://snomed.info/sct?fhir_vs", "targetCanonical": "http://hl7.org/fhir/sid/icd-10-us", "group": [{"source": "http://snomed.info/sct", "sourceVersion": "March 2015 US Edition", "target": "http://hl7.org/fhir/sid/icd-10-us", "targetVersion": "2015", "element": [{"code": "263204007", "target": [{"code": "S52.209A", "equivalence": "narrower", "comment": "The target mapping to ICD-10-CM is narrower, since additional patient data on the encounter (initial vs. subsequent) and fracture type is required for a valid ICD-10-CM mapping."}]}, {"target": [{"code": "S52.209D", "equivalence": "narrower", "comment": "The target mapping to ICD-10-CM is narrower, since additional patient data on the encounter (initial vs. subsequent), fracture type and healing (for subsequent encounter) is required for a valid ICD-10-CM mapping."}]}]}]}}, {"fullUrl": "http://hl7.org/fhir/ConceptMap/102", "resource": {"resourceType": "ConceptMap", "id": "102", "meta": {"lastUpdated": "2019-11-01T09:29:23.356+11:00"}, "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\">\n      <h2>Specimen mapping from v2 table 0487 to SNOMED CT (http://hl7.org/fhir/ConceptMap/102)</h2>\n      <p>Mapping from \n        <a href=\"v2/0487/index.html\">http://terminology.hl7.org/ValueSet/v2-0487</a> to http://snomed.info/sct?fhir_vs\n      </p>\n      <p>DRAFT. Published on 25/07/2013 by FHIR project team (original source: LabMCoP) (\n        <a href=\"http://hl7.org/fhir\">http://hl7.org/fhir</a>, \n        <a href=\"http://www.phconnect.org/group/laboratorymessagingcommunityofpractice/forum/attachment/download?id=3649725%3AUploadedFile%3A145786\">http://www.phconnect.org/group...</a>). \n      </p>\n      <br/>\n      <table class=\"grid\">\n        <tr>\n          <td colspan=\"1\">\n            <b>Source Concept Details</b>\n          </td>\n          <td>\n            <b>Equivalence</b>\n          </td>\n          <td colspan=\"4\">\n            <b>Destination Concept Details</b>\n          </td>\n          <td>\n            <b>Comment</b>\n          </td>\n        </tr>\n        <tr>\n          <td>\n            <b>Code</b> from \n            <a href=\"v2/0487/index.html\" title=\"http://terminology.hl7.org/CodeSystem/v2-0487\">v2 Specimen Type</a>\n          </td>\n          <td/>\n          <td>\n            <b>Code</b> from \n            <a href=\"codesystem-snomedct.html\" title=\"http://snomed.info/sct\">SNOMED CT (all versions)</a>\n          </td>\n          <td>\n            <b>Code</b> from \n            <a href=\"codesystem-snomedct.html\" title=\"http://snomed.info/sct\">SNOMED CT (all versions)</a>\n          </td>\n          <td>\n            <b>Code</b> from \n            <a href=\"codesystem-snomedct.html\" title=\"http://snomed.info/sct\">SNOMED CT (all versions)</a>\n          </td>\n          <td>\n            <b>Code</b> from \n            <a href=\"codesystem-snomedct.html\" title=\"http://snomed.info/sct\">SNOMED CT (all versions)</a>\n          </td>\n          <td/>\n        </tr>\n        <tr>\n          <td>ACNE (Tissue, Acne)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Skin lesion sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>ACNFLD (Fluid, Acne)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Pus specimen)</td>\n          <td/>\n          <td/>\n          <td>47002008 (Pustule)</td>\n          <td>HL7 term is a historical term. mapped to Pus</td>\n        </tr>\n        <tr>\n          <td>AIRS (Air Sample)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Air sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>ALL (Allograft)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Tissue specimen)</td>\n          <td/>\n          <td/>\n          <td>7970006 (Allograft)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>AMP (Amputation)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Specimen obtained by amputation)</td>\n          <td>81723002 (Amputation)</td>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>ANGI (Catheter Tip, Angio)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Catheter tip specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>ARTC (Catheter Tip, Arterial)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Catheter tip specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>ASERU (Serum, Acute)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>pending</td>\n        </tr>\n        <tr>\n          <td>ASP (Aspirate)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119295008 (Specimen obtained by aspiration)</td>\n          <td>14766002 (Removal by suction)</td>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>ATTE (Environment, Attest)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>AUTOC (Environmental, Autoclave Capsule)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>AUTP (Autopsy)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>This really is not a specimen per se - it is the state of the subject from whom the specimen is collected, so it should be used as a  specimen type modifier ONLY!. Often this is indicated with a special medical record number or other notation on the patient. needs to have specimen type (e.g. SPM-4) and source site (SPM.8) and spatial orientation (SPM.9)</td>\n        </tr>\n        <tr>\n          <td>BBL (Blood bag)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119304001 (Blood bag specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>BCYST (Cyst, Baker's)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td>32361000 (Popliteal fossa)</td>\n          <td/>\n          <td>submitted (PLR155) with parent of  167874004^knee joint synovial fluid (specimen), with specimen source topography 32361000^Popliteal fossa structure (body structure)</td>\n        </tr>\n        <tr>\n          <td>BITE (Bite)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>Submit for new term with parent *********</td>\n        </tr>\n        <tr>\n          <td>BLEB (Bleb)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>309049000 (Lesion sample)</td>\n          <td/>\n          <td/>\n          <td>339008 (Blister)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>BLIST (Blister)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>309049000 (Lesion sample)</td>\n          <td/>\n          <td/>\n          <td>339008 (Blister)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>BOIL (Boil)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119295008 (Specimen obtained by aspiration)</td>\n          <td>14766002 (Removal by suction)</td>\n          <td/>\n          <td>59843005 (Furuncle)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>BON (Bone)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>430268003 (Specimen from bone)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>BOWL (Bowel contents)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>Don't use this term for human samples - use Stool instead. animal would use small intestinal contents, large intestinal contents</td>\n        </tr>\n        <tr>\n          <td>BPU (Blood product unit)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119300005 (Specimen from blood product)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>BRN (Burn)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119367005 (Specimen from burn injury)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>BRSH (Brush)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258415003 (Biopsy sample)</td>\n          <td>439336003 (Brush biopsy)</td>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>BRTH (Breath (use EXHLD))</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119336008 (Exhaled air specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>BRUS (Brushing)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>309176002 (Bronchial brushings sample)</td>\n          <td>80657008 (Bronchoscopy with brush biopsy)</td>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>BUB (Bubo)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>302795002 (Lymph node aspirate)</td>\n          <td>14766002 (Removal by suction)</td>\n          <td/>\n          <td>11585000 (Bubo)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>BULLA (Bulla/Bullae)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258482009 (Vesicle fluid sample)</td>\n          <td/>\n          <td/>\n          <td>339008 (Blister)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>BX (Biopsy)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258415003 (Biopsy sample)</td>\n          <td>86273004 (Biopsy)</td>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>CALC (Calculus (=Stone))</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119350003 (Calculus specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>CARBU (Carbuncle)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>309051001 (Body fluid sample)</td>\n          <td/>\n          <td/>\n          <td>41570003 (Carbuncle)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>CAT (Catheter)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119311002 (Catheter specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>CBITE (Bite, Cat)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>Submit for new term with parent *********</td>\n        </tr>\n        <tr>\n          <td>CLIPP (Clippings)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119327009 (Nail specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td>Be more specific use either: 119326000^hair specimen or 119327009^nail specimen</td>\n        </tr>\n        <tr>\n          <td>CNJT (Conjunctiva)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119401005 (Specimen from conjunctiva)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>CNJT (Conjunctiva)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>128160006 (Tissue specimen from conjunctiva)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>CNJT (Conjunctiva)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258498002 (Conjunctival swab)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>COL (Colostrum)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119329007 (Colostrum specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>CONE (Biospy, Cone)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>399713008 (Specimen from uterine cervix obtained by cone biopsy)</td>\n          <td>54535009 (Cone biopsy of cervix)</td>\n          <td>71252005 (Uterine cervix)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>CSCR (Scratch, Cat)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>submit for new term with parent *********^Specimen from wound (specimen)</td>\n        </tr>\n        <tr>\n          <td>CSERU (Serum, Convalescent)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>pending</td>\n        </tr>\n        <tr>\n          <td>CSITE (Catheter Insertion Site)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258507003 (Swab of line insertion site)</td>\n          <td>285570007 (Taking of swab)</td>\n          <td/>\n          <td>386144009 (Line insertion site)</td>\n          <td>Prefer to have aspirate of the pus oozing out from cleaned insertion site - if swab is all that can be obtained, then swab after cleaning, otherwise you may get a contaminated specimen and a falsely identified infected central line. Do not just swab the reddened area - that will just collect skin flora       </td>\n        </tr>\n        <tr>\n          <td>CSMY (Fluid,  Cystostomy Tube)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>309051001 (Body fluid sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>CST (Fluid, Cyst)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258453008 (Cyst fluid sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>CSVR (Blood, Cell Saver)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD - may use blood and SPM-6</td>\n        </tr>\n        <tr>\n          <td>CTP (Catheter tip)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Catheter tip specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>CVPS (Site, CVP)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258507003 (Swab of line insertion site)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>CVPT (Catheter Tip, CVP)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Catheter tip specimen)</td>\n          <td/>\n          <td/>\n          <td>445085009 (Tunneled central venous catheter)</td>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>CYN (Nodule, Cystic)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119368000 (Specimen from cyst)</td>\n          <td/>\n          <td/>\n          <td>27925004 (Nodule)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>CYST (Cyst)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119368000 (Specimen from cyst)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>DBITE (Bite, Dog)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>Submit for new term with parent *********</td>\n        </tr>\n        <tr>\n          <td>DCS (Sputum, Deep Cough)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119335007 (Coughed sputum specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>DEC (Ulcer, Decubitus)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258505006 (Skin ulcer swab)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>DEION (Environmental, Water  (Deionized))</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>DIA (Dialysate)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119360007 (Dialysis fluid specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>DISCHG (Discharge)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258439008 (Discharge)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>DIV (Diverticulum)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Tissue specimen)</td>\n          <td/>\n          <td/>\n          <td>31113003 (Diverticulum)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>DRN (Drain)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119306004 (Drain device specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>DRNG (Drainage, Tube)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258455001 (Drainage fluid sample)</td>\n          <td>********* (Drainage)</td>\n          <td/>\n          <td/>\n          <td>Historical term - consider what is being drained and indicate that in SPM-4 instead: not an acceptable specimen for micro - not specific enough term</td>\n        </tr>\n        <tr>\n          <td>DRNGP (Drainage, Penrose)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>309051001 (Body fluid sample)</td>\n          <td>********* (Drainage)</td>\n          <td/>\n          <td/>\n          <td>Historical term - consider what is being drained and indicate that in SPM-4 instead</td>\n        </tr>\n        <tr>\n          <td>EARW (Ear wax (cerumen))</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>122580007 (Cerumen specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>EBRUSH (Brush, Esophageal)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>309210009 (Oesophageal brushings sample)</td>\n          <td>36213007 (Endoscopy and brush biopsy)</td>\n          <td>32849002 (Esophagus)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>EEYE (Environmental, Eye Wash)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>EFF (Environmental, Effluent)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>EFFUS (Effusion)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258440005 (Effusion sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>EFOD (Environmental, Food)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119320006 (Food specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>EISO (Environmental, Isolette)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258536003 (Incubator swab)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>ELT (Electrode)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119314005 (Electrode specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>ENVIR (Environmental, Unidentified Substance)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119324002 (Specimen of unknown material)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>EOTH (Environmental, Other Substance)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>ESOI (Environmental, Soil)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>ESOS (Environmental, Solution (Sterile))</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>ETA (Aspirate,  Endotrach)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119307008 (Endotracheal tube specimen)</td>\n          <td>14766002 (Removal by suction)</td>\n          <td>321667001 (Respiratory tract structure)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>ETTP (Catheter Tip, Endotracheal)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Catheter tip specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>ETTUB (Tube, Endotracheal)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119307008 (Endotracheal tube specimen)</td>\n          <td>14766002 (Removal by suction)</td>\n          <td>321667001 (Respiratory tract structure)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>EWHI (Environmental, Whirlpool)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>EXG (Gas, exhaled (=breath))</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119336008 (Exhaled air specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>EXS (Shunt, External)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>EXUDTE (Exudate)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258441009 (Exudate sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>FAW (Environmental, Water  (Well))</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>FBLOOD (Blood, Fetal)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119297000 (Blood specimen)</td>\n          <td/>\n          <td/>\n          <td>303112003 (Fetal period)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>FGA (Fluid,  Abdomen)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>168139001 (Peritoneal fluid sample)</td>\n          <td/>\n          <td>83670000 (Peritoneal cavity)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>FIST (Fistula)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119370009 (Specimen from fistula)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>FLD (Fluid, Other)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>309051001 (Body fluid sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>FLT (Filter)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>FLU (Fluid, Body unsp)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>309051001 (Body fluid sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>FLUID (Fluid)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258442002 (Fluid sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>FOLEY (Catheter Tip, Foley)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Catheter tip specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>FRS (Fluid, Respiratory)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258442002 (Fluid sample)</td>\n          <td/>\n          <td>272626006 (Respiratory organ)</td>\n          <td/>\n          <td>this term is not specific enough, choose from terms that more accurately describe the specimen</td>\n        </tr>\n        <tr>\n          <td>FSCLP (Scalp, Fetal)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>309502007 (Fetus specimen)</td>\n          <td/>\n          <td>41695006 (Scalp)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>FUR (Furuncle)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119295008 (Specimen obtained by aspiration)</td>\n          <td/>\n          <td/>\n          <td>59843005 (Furuncle)</td>\n          <td>Further describe the sample as tissue or pus. or by the collection method. The term boil is not specific to a body site - need to indicate source site (spm.8). preferred term is Aspirate_Boil</td>\n        </tr>\n        <tr>\n          <td>GAS (Gas)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119317003 (Gaseous material specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>GASA (Aspirate, Gastric)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>168137004 (Gastric aspirate sample)</td>\n          <td>14766002 (Removal by suction)</td>\n          <td>69695003 (Stomach)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>GASAN (Antrum, Gastric)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119379005 (Specimen from stomach)</td>\n          <td/>\n          <td>66051006 (Pyloric antrum)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>GASBR (Brushing, Gastric)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>309213006 (Gastric brushings sample)</td>\n          <td>235157009 (Endoscopic brushings of GIT)</td>\n          <td>69695003 (Stomach)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>GASD (Drainage, Gastric)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258459007 (Gastric fluid sample)</td>\n          <td>********* (Drainage)</td>\n          <td>69695003 (Stomach)</td>\n          <td/>\n          <td>Historical term - consider what is being drained and indicate that in SPM-4 instead</td>\n        </tr>\n        <tr>\n          <td>GAST (Fluid/contents, Gastric)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258459007 (Gastric fluid sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>GENV (Genital vaginal)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119394009 (Specimen from vagina)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>GRAFT (Graft)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>440493002 (Graft sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>GRANU (Granuloma)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Tissue specimen)</td>\n          <td/>\n          <td/>\n          <td>45647009 (Granuloma)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>GROSH (Catheter, Groshong)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119311002 (Catheter specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>GSOL (Solution, Gastrostomy)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>GSPEC (Biopsy, Gastric)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>309211008 (Gastric biopsy sample)</td>\n          <td>79121003 (Biopsy of stomach)</td>\n          <td>69695003 (Stomach)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>GT (Tube, Gastric)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>GTUBE (Drainage Tube, Drainage (Gastrostomy))</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258459007 (Gastric fluid sample)</td>\n          <td>********* (Drainage)</td>\n          <td>69695003 (Stomach)</td>\n          <td>127490009 (Gastrostomy route)</td>\n          <td>Historical term - consider what is being drained and indicate that in SPM-4 instead</td>\n        </tr>\n        <tr>\n          <td>HBITE (Bite, Human)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>Submit for new term with parent *********</td>\n        </tr>\n        <tr>\n          <td>HBLUD (Blood, Autopsy)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119297000 (Blood specimen)</td>\n          <td/>\n          <td/>\n          <td>303113008 (Postmortem period)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>HEMAQ (Catheter Tip, Hemaquit)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Catheter tip specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>HEMO (Catheter Tip, Hemovac)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Catheter tip specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>HERNI (Tissue, Herniated)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Tissue specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>HEV (Drain, Hemovac)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119306004 (Drain device specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>HIC (Catheter, Hickman)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Catheter tip specimen)</td>\n          <td/>\n          <td/>\n          <td>445085009 (Tunneled central venous catheter)</td>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>HYDC (Fluid, Hydrocele)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>309051001 (Body fluid sample)</td>\n          <td/>\n          <td/>\n          <td>55434001 (Hydrocele)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>IBITE (Bite, Insect)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>Submit for new term with parent *********</td>\n        </tr>\n        <tr>\n          <td>ICYST (Cyst, Inclusion)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Skin cyst sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>IDC (Catheter Tip, Indwelling)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Catheter tip specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>IHG (Gas, Inhaled)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119337004 (Inhaled gas specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>ILEO (Drainage, Ileostomy)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258455001 (Drainage fluid sample)</td>\n          <td/>\n          <td/>\n          <td>419954003 (Ileostomy route)</td>\n          <td>Historical term - consider what is being drained and indicate that in SPM-4 instead: stool is what is expected, should use stool sample</td>\n        </tr>\n        <tr>\n          <td>ILLEG (Source of Specimen Is Illegible)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>IMP (Implant)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>439961009 (Implant submitted as specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>INCI (Site, Incision/Surgical)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>438660002 (Specimen from prosthetic device)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>INFIL (Infiltrate)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>This describes a morphologic abnormality, not a sample</td>\n        </tr>\n        <tr>\n          <td>INS (Insect)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258614005 (Insect sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>INTRD (Catheter Tip, Introducer)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Catheter tip specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>IT (Intubation tube)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119307008 (Endotracheal tube specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>IUD (Intrauterine Device)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>pending</td>\n        </tr>\n        <tr>\n          <td>IVCAT (Catheter Tip, IV)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Catheter tip specimen)</td>\n          <td/>\n          <td/>\n          <td>255560000 (Intravenous)</td>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>IVFLD (Fluid, IV)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258649003 (Intravenous infusion fluid sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>IVTIP (Tubing Tip, IV)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Catheter tip specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>JEJU (Drainage, Jejunal)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258463000 (Jejunal fluid sample)</td>\n          <td>********* (Drainage)</td>\n          <td>21306003 (Jejunum)</td>\n          <td/>\n          <td>Historical term - consider what is being drained and indicate that in SPM-4 instead: stool is what is expected, should use stool sample</td>\n        </tr>\n        <tr>\n          <td>JNTFLD (Fluid, Joint)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119332005 (Synovial fluid specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>JP (Drainage, Jackson Pratt)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>309051001 (Body fluid sample)</td>\n          <td>********* (Drainage)</td>\n          <td/>\n          <td/>\n          <td>Historical term - consider what is being drained and indicate that in SPM-4 instead</td>\n        </tr>\n        <tr>\n          <td>KELOI (Lavage)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Wash out specimen)</td>\n          <td>67889009 (Irrigation)</td>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>KIDFLD (Fluid, Kidney)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>309051001 (Body fluid sample)</td>\n          <td/>\n          <td>64033007 (Kidney)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>LAVG (Lavage, Bronhial)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258607008 (Bronchoalveolar lavage fluid sample)</td>\n          <td>397394009 (Bronchoalveolar lavage)</td>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>LAVGG (Lavage, Gastric)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>168138009 (Gastric lavage aspirate sample)</td>\n          <td>173830003 (Irrigation of stomach)</td>\n          <td>69695003 (Stomach)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>LAVGP (Lavage, Peritoneal)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>440137008 (Specimen obtained by peritoneal lavage)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>LAVPG (Lavage, Pre-Bronch)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Wash out specimen)</td>\n          <td>67889009 (Irrigation)</td>\n          <td>44567001 (Trachea)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>LENS1 (Contact Lens)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>440473005 (Contact lens submitted as specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>LENS2 (Contact Lens Case)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>LESN (Lesion)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>309049000 (Lesion sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td>should be more specific what kind of lesion is observed - be more specific is it a wound, abscess, mass - specify! Ask SNOMED CT to mark it as a grouper term only (309049000)</td>\n        </tr>\n        <tr>\n          <td>LIQ (Liquid, Unspecified)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258442002 (Fluid sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>LIQO (Liquid, Other)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>LSAC (Fluid, Lumbar Sac)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258450006 (Cerebrospinal fluid sample)</td>\n          <td/>\n          <td>303949008 (Lumbar spinal cerebrospinal fluid pathway)</td>\n          <td/>\n          <td>The HL7 term is a historical term Mapped to CSF obtained by lumbar puncture</td>\n        </tr>\n        <tr>\n          <td>MAHUR (Catheter Tip, Makurkour)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Catheter tip specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>MASS (Mass)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>420548004 (Specimen from mass)</td>\n          <td/>\n          <td/>\n          <td>4147007 (Mass)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>MBLD (Blood, Menstrual)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119345009 (Menstrual blood specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>MUCOS (Mucosa)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Tissue specimen)</td>\n          <td/>\n          <td>414781009 (Mucous membrane structure)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>MUCUS (Mucus)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258483004 (Mucus sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>NASDR (Drainage, Nasal)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258474009 (Sinus fluid sample)</td>\n          <td>********* (Drainage)</td>\n          <td>2095001 (Accessory sinus)</td>\n          <td/>\n          <td>Historical term - consider what is being drained and indicate that in SPM-4 instead</td>\n        </tr>\n        <tr>\n          <td>NEDL (Needle)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>NEPH (Site, Nephrostomy)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>438660002 (Specimen from prosthetic device)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>NGASP (Aspirate, Nasogastric)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>302794003 (Nasogastric aspirate)</td>\n          <td>6853008 (Nasogastric tube aspiration)</td>\n          <td>69695003 (Stomach)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>NGAST (Drainage, Nasogastric)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258459007 (Gastric fluid sample)</td>\n          <td>********* (Drainage)</td>\n          <td>69695003 (Stomach)</td>\n          <td>127492001 (Nasogastric route)</td>\n          <td>Historical term - consider what is being drained and indicate that in SPM-4 instead</td>\n        </tr>\n        <tr>\n          <td>NGS (Site, Naso/Gastric)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>438660002 (Specimen from prosthetic device)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>NODUL (Nodule(s))</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>pending</td>\n        </tr>\n        <tr>\n          <td>NSECR (Secretion, Nasal)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>168141000 (Nasal fluid sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>ORH (Other)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>123038009 (Specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>ORL (Lesion, Oral)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td>74262004 (Oral cavity)</td>\n          <td/>\n          <td>be more precise use ulcer, tumor, vesicle</td>\n        </tr>\n        <tr>\n          <td>OTH (Source, Other)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>123038009 (Specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>PACEM (Pacemaker)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>PCFL (Fluid, Pericardial)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>122571007 (Pericardial fluid specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>PDSIT (Site, Peritoneal Dialysis)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>438660002 (Specimen from prosthetic device)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>PDTS (Site, Peritoneal Dialysis Tunnel)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>submitted for code</td>\n        </tr>\n        <tr>\n          <td>PELVA (Abscess, Pelvic)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119371008 (Specimen from abscess)</td>\n          <td/>\n          <td>12921003 (Pelvis)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>PENIL (Lesion, Penile)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td>18911002 (Penis)</td>\n          <td/>\n          <td>need to know what kind of lesion, so map to: UlcerTissue_penile, VesicleFluid_penile, Wound_penile, Mass tissue_penile, Necrotic tissue_penile, AbscessAspirate_penile, Anything else?</td>\n        </tr>\n        <tr>\n          <td>PERIA (Abscess, Perianal)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119371008 (Specimen from abscess)</td>\n          <td/>\n          <td>397158004 (Perianal region structure)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>PILOC (Cyst, Pilonidal)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119368000 (Specimen from cyst)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>PINS (Site, Pin)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>438660002 (Specimen from prosthetic device)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>PIS (Site, Pacemaker Insetion)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258507003 (Swab of line insertion site)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>PLAN (Plant Material)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119301009 (Plant specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>PLAS (Plasma)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119361006 (Plasma specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>PLB (Plasma bag)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119305000 (Plasma bag specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>PLEVS (Serum, Peak Level)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119364003 (Serum specimen)</td>\n          <td/>\n          <td/>\n          <td>255587001 (Peak)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>PND (Drainage, Penile)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258439008 (Discharge)</td>\n          <td>********* (Drainage)</td>\n          <td>13648007 (Urethra)</td>\n          <td/>\n          <td>Historical term -though in this case more often used for discharge</td>\n        </tr>\n        <tr>\n          <td>POL (Polyps)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Tissue specimen)</td>\n          <td/>\n          <td/>\n          <td>41329004 (Polyp)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>POPGS (Graft Site, Popliteal)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>440493002 (Graft sample)</td>\n          <td/>\n          <td>6902008 (Popliteal region)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>POPLG (Graft, Popliteal)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>440493002 (Graft sample)</td>\n          <td/>\n          <td>6902008 (Popliteal region)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>POPLV (Site, Popliteal Vein)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>PORTA (Catheter, Porta)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119311002 (Catheter specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>PPP (Plasma, Platelet poor)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119362004 (Platelet poor plasma specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>PROST (Prosthetic Device)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>438660002 (Specimen from prosthetic device)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>PRP (Plasma, Platelet rich)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119363009 (Platelet rich plasma specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>PSC (Pseudocyst)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119368000 (Specimen from cyst)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>PUNCT (Wound, Puncture)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Specimen from wound)</td>\n          <td/>\n          <td/>\n          <td>129300006 (Puncture - action)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>PUS (Pus)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Pus specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>PUSFR (Pustule)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Pus specimen)</td>\n          <td/>\n          <td/>\n          <td>47002008 (Pustule)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>PUST (Pus)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Pus specimen)</td>\n          <td/>\n          <td/>\n          <td>47002008 (Pustule)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>QC3 (Quality Control)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>RANDU (Urine, Random)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>278020009 (Spot urine sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>RBITE (Bite, Reptile)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>Submit for new term with parent: *********</td>\n        </tr>\n        <tr>\n          <td>RECT (Drainage, Rectal)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119339001 (Stool specimen)</td>\n          <td>********* (Drainage)</td>\n          <td>34402009 (Rectum)</td>\n          <td/>\n          <td>Historical term - consider what is being drained and indicate that in SPM-4 instead: stool is what is expected, should use stool sample</td>\n        </tr>\n        <tr>\n          <td>RECTA (Abscess, Rectal)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119371008 (Specimen from abscess)</td>\n          <td/>\n          <td>34402009 (Rectum)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>RENALC (Cyst, Renal)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258420003 (Cyst tissue)</td>\n          <td/>\n          <td>64033007 (Kidney)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>RENC (Fluid, Renal Cyst)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258453008 (Cyst fluid sample)</td>\n          <td/>\n          <td>64033007 (Kidney)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>RES (Respiratory)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258603007 (Respiratory sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>SAL (Saliva)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119342007 (Saliva specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>SCAR (Tissue, Keloid (Scar))</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Tissue specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>SCLV (Catheter Tip, Subclavian)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Catheter tip specimen)</td>\n          <td/>\n          <td>9454009 (Subclavian vein)</td>\n          <td/>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>SCROA (Abscess, Scrotal)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119371008 (Specimen from abscess)</td>\n          <td/>\n          <td>20233005 (Scrotum)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>SECRE (Secretion(s))</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>432825001 (Body secretion specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>SER (Serum)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119364003 (Serum specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>SHU (Site, Shunt)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119295008 (Specimen obtained by aspiration)</td>\n          <td>14766002 (Removal by suction)</td>\n          <td/>\n          <td>257351008 (Shunt)</td>\n          <td>Preferred is aspiration with sterile syringe from inflamed area. Specify body location of shunt site</td>\n        </tr>\n        <tr>\n          <td>SHUNF (Fluid, Shunt)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>446861007 (Cerebrospinal fluid specimen obtained via ventriculoperitoneal shunt)</td>\n          <td>446860008 (Collection of cerebrospinal fluid via ventriculoperitoneal shunt)</td>\n          <td>279107003 (Central nervous system space)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>SHUNT (Shunt)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>SITE (Site)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>SKBP (Biopsy, Skin)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>309066003 (Skin biopsy sample)</td>\n          <td>240977001 (Biopsy of skin)</td>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>SKN (Skin)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119325001 (Skin specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>SMM (Mass, Sub-Mandibular)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>420548004 (Specimen from mass)</td>\n          <td/>\n          <td/>\n          <td>4147007 (Mass)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>SNV (Fluid, synovial (Joint fluid))</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119332005 (Synovial fluid specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>SPRM (Spermatozoa)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119349003 (Spermatozoa specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>SPRP (Catheter Tip, Suprapubic)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Catheter tip specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>SPRPB (Cathether Tip, Suprapubic)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Catheter tip specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>SPS (Environmental, Spore Strip)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>SPT (Sputum)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119334006 (Sputum specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>SPTC (Sputum - coughed)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119335007 (Coughed sputum specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>SPTT (Sputum - tracheal aspirate)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258609006 (Sputum specimen obtained by aspiration from trachea)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>SPUT1 (Sputum, Simulated)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>SPUTIN (Sputum, Inducted)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258610001 (Sputum specimen obtained by sputum induction)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>SPUTSP (Sputum, Spontaneous)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119335007 (Coughed sputum specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>STER (Environmental, Sterrad)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>STL (Stool = Fecal)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119339001 (Stool specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>STONE (Stone, Kidney)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119350003 (Calculus specimen)</td>\n          <td/>\n          <td>64033007 (Kidney)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>SUBMA (Abscess, Submandibular)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119371008 (Specimen from abscess)</td>\n          <td/>\n          <td>5713008 (Submandibular triangle)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>SUBMX (Abscess, Submaxillary)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119371008 (Specimen from abscess)</td>\n          <td/>\n          <td>4335006 (Upper jaw)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>SUMP (Drainage, Sump)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>446562005 (Body fluid specimen obtained via sump drain)</td>\n          <td>********* (Drainage)</td>\n          <td/>\n          <td/>\n          <td>Historical term - consider what is being drained and indicate that in SPM-4 instead</td>\n        </tr>\n        <tr>\n          <td>SUP (Suprapubic Tap)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>122575003 (Urine specimen)</td>\n          <td>58088002 (Urine specimen collection, suprapubic)</td>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>SUTUR (Suture)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>SWGZ (Catheter Tip, Swan Gantz)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Catheter tip specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>TASP (Aspirate, Tracheal)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>122877000 (Upper respiratory fluid specimen obtained by tracheal aspiration)</td>\n          <td>129112001 (Aspiration from trachea)</td>\n          <td>44567001 (Trachea)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>TISS (Tissue)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Tissue specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>TISU (Tissue ulcer)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>122593002 (Tissue specimen obtained from ulcer)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>TLC (Cathether Tip, Triple Lumen)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Catheter tip specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>TRAC (Site, Tracheostomy)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>438660002 (Specimen from prosthetic device)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>TRANS (Transudate)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258538002 (Transudate sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>TSERU (Serum, Trough)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119364003 (Serum specimen)</td>\n          <td/>\n          <td/>\n          <td>255588006 (Trough)</td>\n          <td/>\n        </tr>\n        <tr>\n          <td>TSTES (Abscess, Testicular)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119371008 (Specimen from abscess)</td>\n          <td/>\n          <td>279572002 (Testicular structure)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>TTRA (Aspirate, Transtracheal)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258480001 (Transtracheal aspirate sample)</td>\n          <td>129112001 (Aspiration from trachea)</td>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>TUBES (Tubes)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>119310001 (Tube specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>TUMOR (Tumor)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258435002 (Tumour tissue)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>TZANC (Smear, Tzanck)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>This is the name of a lab test. A skin sample is examined for viral inclusions.</td>\n        </tr>\n        <tr>\n          <td>UDENT (Source, Unidentified)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>123038009 (Specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>UR (Urine)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>122575003 (Urine specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>URC (Urine clean catch)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>122880004 (Urine specimen obtained by clean catch procedure)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>URINB (Urine, Bladder Washings)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>122575003 (Urine specimen)</td>\n          <td>78533007 (Irrigation of urinary bladder)</td>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>URINC (Urine, Catheterized)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>446846006 (Urine specimen obtained via indwelling urinary catheter)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>URINM (Urine, Midstream)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>258574006 (Mid-stream urine sample)</td>\n          <td>225271002 (Collection of mid-stream specimen of urine)</td>\n          <td>431938005 (Structure of urinary tract proper)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>URINN (Urine, Nephrostomy)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>446277003 (Urine specimen obtained from nephrostomy tube after percutaneous insertion)</td>\n          <td>225109005 (Collection of nephrostomy urine specimen)</td>\n          <td>25990002 (Renal pelvis)</td>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>URINP (Urine, Pedibag)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>URT (Urine catheter)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>122565001 (Urinary catheter specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>USCOP (Urine, Cystoscopy)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td>176178006 (Diagnostic cystoscopy)</td>\n          <td>89837001 (Urinary bladder)</td>\n          <td/>\n          <td>NEW specimenTERM 7</td>\n        </tr>\n        <tr>\n          <td>USPEC (Source, Unspecified)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>123038009 (Specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>VASTIP (Catheter Tip, Vas)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Catheter tip specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>VENT (Catheter Tip, Ventricular)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Catheter tip specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD in detail</td>\n        </tr>\n        <tr>\n          <td>VITF (Vitreous Fluid)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Vitreous humor sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>VOM (Vomitus)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Vomitus specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>WASH (Wash)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Wash out specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>WASI (Washing, e.g. bronchial washing)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Specimen from lung obtained by bronchial washing procedure)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>WAT (Water)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Water specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>WB (Blood, Whole)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>Bloodbanking term ONLY now map to blood and the respective preservative</td>\n        </tr>\n        <tr>\n          <td>WEN (Wen)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Skin cyst sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>WICK (Wick)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>WND (Wound)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Specimen from wound)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>WNDA (Wound abscess)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Specimen from wound abscess)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>WNDD (Wound drainage)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Fluid specimen from wound)</td>\n          <td>********* (Drainage)</td>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>WNDE (Wound exudate)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Exudate specimen from wound)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>WORM (Worm)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Helminth sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>WRT (Wart)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Skin lesion sample)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>WWA (Environmental, Water)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>********* (Water specimen)</td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n        </tr>\n        <tr>\n          <td>WWO (Environmental, Water (Ocean))</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>WWT (Environmental, Water  (Tap))</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td/>\n          <td>TBD</td>\n        </tr>\n        <tr>\n          <td>CSITE (Catheter Insertion Site)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td>386144009 (Line insertion site)</td>\n          <td>14766002 (Removal by suction)</td>\n          <td>119295008 (Specimen obtained by aspiration)</td>\n          <td>Prefer to have aspirate of the pus oozing out from cleaned insertion site - if swab is all that can be obtained, then swab after cleaning, otherwise you may get a contaminated specimen and a falsely identified infected central line. Do not just swab the reddened area - that will just collect skin flora</td>\n        </tr>\n        <tr>\n          <td>CLIPP (Clippings)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td/>\n          <td/>\n          <td>119326000 (Hair specimen)</td>\n          <td>Be more specific use either: 119326000^hair specimen, or 119327009^nail specimen</td>\n        </tr>\n        <tr>\n          <td>SHU (Site, Shunt)</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#unmatched\">unmatched</a>\n          </td>\n          <td/>\n          <td>257351008 (Shunt)</td>\n          <td/>\n          <td>438660002 (Specimen from prosthetic device)</td>\n          <td>assume swab from shunt site for mapping here - clean surface of skin prior to expressing the shunt site - preferred is aspiration with sterile syringe should use SPM.8 to specify body approximate match location of shunt site</td>\n        </tr>\n      </table>\n    </div>"}, "url": "http://hl7.org/fhir/ConceptMap/102", "version": "4.0.1", "name": "Specimen mapping from v2 table 0487 to SNOMED CT", "status": "draft", "experimental": false, "date": "2013-07-25", "publisher": "FHIR project team (original source: LabMCoP)", "contact": [{"telecom": [{"system": "url", "value": "http://hl7.org/fhir"}]}, {"telecom": [{"system": "url", "value": "http://www.phconnect.org/group/laboratorymessagingcommunityofpractice/forum/attachment/download?id=3649725%3AUploadedFile%3A145786"}]}], "sourceCanonical": "http://terminology.hl7.org/ValueSet/v2-0487", "targetCanonical": "http://snomed.info/sct?fhir_vs", "group": [{"source": "http://terminology.hl7.org/CodeSystem/v2-0487", "target": "http://snomed.info/sct", "element": [{"code": "ACNE", "target": [{"code": "*********", "equivalence": "equivalent"}]}, {"code": "ACNFLD", "target": [{"code": "*********", "equivalence": "equivalent", "comment": "HL7 term is a historical term. mapped to Pus", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "47002008"}]}]}, {"code": "AIRS", "target": [{"code": "*********", "equivalence": "equivalent"}]}, {"code": "ALL", "target": [{"code": "*********", "equivalence": "equivalent", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "7970006"}]}]}, {"code": "AMP", "target": [{"code": "*********", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "81723002"}]}]}, {"code": "ANGI", "target": [{"code": "*********", "equivalence": "equivalent", "comment": "TBD in detail"}]}, {"code": "ARTC", "target": [{"code": "*********", "equivalence": "equivalent", "comment": "TBD in detail"}]}, {"code": "ASERU", "target": [{"equivalence": "unmatched", "comment": "pending"}]}, {"code": "ASP", "target": [{"code": "119295008", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "14766002"}]}]}, {"code": "ATTE", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "AUTOC", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "AUTP", "target": [{"equivalence": "unmatched", "comment": "This really is not a specimen per se - it is the state of the subject from whom the specimen is collected, so it should be used as a  specimen type modifier ONLY!. Often this is indicated with a special medical record number or other notation on the patient. needs to have specimen type (e.g. SPM-4) and source site (SPM.8) and spatial orientation (SPM.9)"}]}, {"code": "BBL", "target": [{"code": "119304001", "equivalence": "equivalent"}]}, {"code": "BCYST", "target": [{"equivalence": "unmatched", "comment": "submitted (PLR155) with parent of  167874004^knee joint synovial fluid (specimen), with specimen source topography 32361000^Popliteal fossa structure (body structure)", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "32361000"}]}]}, {"code": "BITE", "target": [{"equivalence": "unmatched", "comment": "Submit for new term with parent *********"}]}, {"code": "BLEB", "target": [{"code": "309049000", "equivalence": "equivalent", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "339008"}]}]}, {"code": "BLIST", "target": [{"code": "309049000", "equivalence": "equivalent", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "339008"}]}]}, {"code": "BOIL", "target": [{"code": "119295008", "equivalence": "equivalent", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "59843005"}, {"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "14766002"}]}]}, {"code": "BON", "target": [{"code": "430268003", "equivalence": "equivalent"}]}, {"code": "BOWL", "target": [{"equivalence": "unmatched", "comment": "Don't use this term for human samples - use Stool instead. animal would use small intestinal contents, large intestinal contents"}]}, {"code": "BPU", "target": [{"code": "119300005", "equivalence": "equivalent"}]}, {"code": "BRN", "target": [{"code": "119367005", "equivalence": "equivalent"}]}, {"code": "BRSH", "target": [{"code": "258415003", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "439336003"}]}]}, {"code": "BRTH", "target": [{"code": "119336008", "equivalence": "equivalent"}]}, {"code": "BRUS", "target": [{"code": "309176002", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "80657008"}]}]}, {"code": "BUB", "target": [{"code": "302795002", "equivalence": "equivalent", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "11585000"}, {"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "14766002"}]}]}, {"code": "BULLA", "target": [{"code": "258482009", "equivalence": "equivalent", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "339008"}]}]}, {"code": "BX", "target": [{"code": "258415003", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "86273004"}]}]}, {"code": "CALC", "target": [{"code": "119350003", "equivalence": "equivalent"}]}, {"code": "CARBU", "target": [{"code": "309051001", "equivalence": "equivalent", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "41570003"}]}]}, {"code": "CAT", "target": [{"code": "119311002", "equivalence": "equivalent"}]}, {"code": "CBITE", "target": [{"equivalence": "unmatched", "comment": "Submit for new term with parent *********"}]}, {"code": "CLIPP", "target": [{"code": "119327009", "equivalence": "equivalent", "comment": "Be more specific use either: 119326000^hair specimen or 119327009^nail specimen"}]}, {"code": "CNJT", "target": [{"code": "119401005", "equivalence": "equivalent"}]}, {"code": "CNJT", "target": [{"code": "128160006", "equivalence": "equivalent"}]}, {"code": "CNJT", "target": [{"code": "258498002", "equivalence": "equivalent"}]}, {"code": "COL", "target": [{"code": "119329007", "equivalence": "equivalent"}]}, {"code": "CONE", "target": [{"code": "399713008", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "54535009"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "71252005"}]}]}, {"code": "CSCR", "target": [{"equivalence": "unmatched", "comment": "submit for new term with parent *********^Specimen from wound (specimen)"}]}, {"code": "CSERU", "target": [{"equivalence": "unmatched", "comment": "pending"}]}, {"code": "CSITE", "target": [{"code": "258507003", "equivalence": "equivalent", "comment": "Prefer to have aspirate of the pus oozing out from cleaned insertion site - if swab is all that can be obtained, then swab after cleaning, otherwise you may get a contaminated specimen and a falsely identified infected central line. Do not just swab the reddened area - that will just collect skin flora       ", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "386144009"}, {"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "285570007"}]}]}, {"code": "CSMY", "target": [{"code": "309051001", "equivalence": "equivalent"}]}, {"code": "CST", "target": [{"code": "258453008", "equivalence": "equivalent"}]}, {"code": "CSVR", "target": [{"equivalence": "unmatched", "comment": "TBD - may use blood and SPM-6"}]}, {"code": "CTP", "target": [{"code": "*********", "equivalence": "equivalent", "comment": "TBD in detail"}]}, {"code": "CVPS", "target": [{"code": "258507003", "equivalence": "equivalent"}]}, {"code": "CVPT", "target": [{"code": "*********", "equivalence": "equivalent", "comment": "TBD in detail", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "445085009"}]}]}, {"code": "CYN", "target": [{"code": "119368000", "equivalence": "equivalent", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "27925004"}]}]}, {"code": "CYST", "target": [{"code": "119368000", "equivalence": "equivalent"}]}, {"code": "DBITE", "target": [{"equivalence": "unmatched", "comment": "Submit for new term with parent *********"}]}, {"code": "DCS", "target": [{"code": "119335007", "equivalence": "equivalent"}]}, {"code": "DEC", "target": [{"code": "258505006", "equivalence": "equivalent"}]}, {"code": "DEION", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "DIA", "target": [{"code": "119360007", "equivalence": "equivalent"}]}, {"code": "DISCHG", "target": [{"code": "258439008", "equivalence": "equivalent"}]}, {"code": "DIV", "target": [{"code": "*********", "equivalence": "equivalent", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "31113003"}]}]}, {"code": "DRN", "target": [{"code": "119306004", "equivalence": "equivalent"}]}, {"code": "DRNG", "target": [{"code": "258455001", "equivalence": "equivalent", "comment": "Historical term - consider what is being drained and indicate that in SPM-4 instead: not an acceptable specimen for micro - not specific enough term", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "*********"}]}]}, {"code": "DRNGP", "target": [{"code": "309051001", "equivalence": "equivalent", "comment": "Historical term - consider what is being drained and indicate that in SPM-4 instead", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "*********"}]}]}, {"code": "EARW", "target": [{"code": "122580007", "equivalence": "equivalent"}]}, {"code": "EBRUSH", "target": [{"code": "309210009", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "36213007"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "32849002"}]}]}, {"code": "EEYE", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "EFF", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "EFFUS", "target": [{"code": "258440005", "equivalence": "equivalent"}]}, {"code": "EFOD", "target": [{"code": "119320006", "equivalence": "equivalent"}]}, {"code": "EISO", "target": [{"code": "258536003", "equivalence": "equivalent"}]}, {"code": "ELT", "target": [{"code": "119314005", "equivalence": "equivalent"}]}, {"code": "ENVIR", "target": [{"code": "119324002", "equivalence": "equivalent"}]}, {"code": "EOTH", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "ESOI", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "ESOS", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "ETA", "target": [{"code": "119307008", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "14766002"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "321667001"}]}]}, {"code": "ETTP", "target": [{"code": "*********", "equivalence": "equivalent", "comment": "TBD in detail"}]}, {"code": "ETTUB", "target": [{"code": "119307008", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "14766002"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "321667001"}]}]}, {"code": "EWHI", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "EXG", "target": [{"code": "119336008", "equivalence": "equivalent"}]}, {"code": "EXS", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "EXUDTE", "target": [{"code": "258441009", "equivalence": "equivalent"}]}, {"code": "FAW", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "FBLOOD", "target": [{"code": "119297000", "equivalence": "equivalent", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "303112003"}]}]}, {"code": "FGA", "target": [{"code": "168139001", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "83670000"}]}]}, {"code": "FIST", "target": [{"code": "119370009", "equivalence": "equivalent"}]}, {"code": "FLD", "target": [{"code": "309051001", "equivalence": "equivalent"}]}, {"code": "FLT", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "FLU", "target": [{"code": "309051001", "equivalence": "equivalent"}]}, {"code": "FLUID", "target": [{"code": "258442002", "equivalence": "equivalent"}]}, {"code": "FOLEY", "target": [{"code": "*********", "equivalence": "equivalent", "comment": "TBD in detail"}]}, {"code": "FRS", "target": [{"code": "258442002", "equivalence": "equivalent", "comment": "this term is not specific enough, choose from terms that more accurately describe the specimen", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "272626006"}]}]}, {"code": "FSCLP", "target": [{"code": "309502007", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "41695006"}]}]}, {"code": "FUR", "target": [{"code": "119295008", "equivalence": "equivalent", "comment": "Further describe the sample as tissue or pus. or by the collection method. The term boil is not specific to a body site - need to indicate source site (spm.8). preferred term is Aspirate_Boil", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "59843005"}]}]}, {"code": "GAS", "target": [{"code": "119317003", "equivalence": "equivalent"}]}, {"code": "GASA", "target": [{"code": "168137004", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "14766002"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "69695003"}]}]}, {"code": "GASAN", "target": [{"code": "119379005", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "66051006"}]}]}, {"code": "GASBR", "target": [{"code": "309213006", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "235157009"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "69695003"}]}]}, {"code": "GASD", "target": [{"code": "258459007", "equivalence": "equivalent", "comment": "Historical term - consider what is being drained and indicate that in SPM-4 instead", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "*********"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "69695003"}]}]}, {"code": "GAST", "target": [{"code": "258459007", "equivalence": "equivalent"}]}, {"code": "GENV", "target": [{"code": "119394009", "equivalence": "equivalent"}]}, {"code": "GRAFT", "target": [{"code": "440493002", "equivalence": "equivalent"}]}, {"code": "GRANU", "target": [{"code": "*********", "equivalence": "equivalent", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "45647009"}]}]}, {"code": "GROSH", "target": [{"code": "119311002", "equivalence": "equivalent", "comment": "TBD in detail"}]}, {"code": "GSOL", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "GSPEC", "target": [{"code": "309211008", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "79121003"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "69695003"}]}]}, {"code": "GT", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "GTUBE", "target": [{"code": "258459007", "equivalence": "equivalent", "comment": "Historical term - consider what is being drained and indicate that in SPM-4 instead", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "127490009"}, {"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "*********"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "69695003"}]}]}, {"code": "HBITE", "target": [{"equivalence": "unmatched", "comment": "Submit for new term with parent *********"}]}, {"code": "HBLUD", "target": [{"code": "119297000", "equivalence": "equivalent", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "303113008"}]}]}, {"code": "HEMAQ", "target": [{"code": "*********", "equivalence": "equivalent", "comment": "TBD in detail"}]}, {"code": "HEMO", "target": [{"code": "*********", "equivalence": "equivalent", "comment": "TBD in detail"}]}, {"code": "HERNI", "target": [{"code": "*********", "equivalence": "equivalent"}]}, {"code": "HEV", "target": [{"code": "119306004", "equivalence": "equivalent"}]}, {"code": "HIC", "target": [{"code": "*********", "equivalence": "equivalent", "comment": "TBD in detail", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "445085009"}]}]}, {"code": "HYDC", "target": [{"code": "309051001", "equivalence": "equivalent", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "55434001"}]}]}, {"code": "IBITE", "target": [{"equivalence": "unmatched", "comment": "Submit for new term with parent *********"}]}, {"code": "ICYST", "target": [{"code": "*********", "equivalence": "equivalent"}]}, {"code": "IDC", "target": [{"code": "*********", "equivalence": "equivalent", "comment": "TBD in detail"}]}, {"code": "IHG", "target": [{"code": "119337004", "equivalence": "equivalent"}]}, {"code": "ILEO", "target": [{"code": "258455001", "equivalence": "equivalent", "comment": "Historical term - consider what is being drained and indicate that in SPM-4 instead: stool is what is expected, should use stool sample", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "419954003"}]}]}, {"code": "ILLEG", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "IMP", "target": [{"code": "439961009", "equivalence": "equivalent"}]}, {"code": "INCI", "target": [{"code": "438660002", "equivalence": "equivalent"}]}, {"code": "INFIL", "target": [{"equivalence": "unmatched", "comment": "This describes a morphologic abnormality, not a sample"}]}, {"code": "INS", "target": [{"code": "258614005", "equivalence": "equivalent"}]}, {"code": "INTRD", "target": [{"code": "*********", "equivalence": "equivalent", "comment": "TBD in detail"}]}, {"code": "IT", "target": [{"code": "119307008", "equivalence": "equivalent"}]}, {"code": "IUD", "target": [{"equivalence": "unmatched", "comment": "pending"}]}, {"code": "IVCAT", "target": [{"code": "*********", "equivalence": "equivalent", "comment": "TBD in detail", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "255560000"}]}]}, {"code": "IVFLD", "target": [{"code": "258649003", "equivalence": "equivalent"}]}, {"code": "IVTIP", "target": [{"code": "*********", "equivalence": "equivalent"}]}, {"code": "JEJU", "target": [{"code": "258463000", "equivalence": "equivalent", "comment": "Historical term - consider what is being drained and indicate that in SPM-4 instead: stool is what is expected, should use stool sample", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "*********"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "21306003"}]}]}, {"code": "JNTFLD", "target": [{"code": "119332005", "equivalence": "equivalent"}]}, {"code": "JP", "target": [{"code": "309051001", "equivalence": "equivalent", "comment": "Historical term - consider what is being drained and indicate that in SPM-4 instead", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "*********"}]}]}, {"code": "KELOI", "target": [{"code": "*********", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "67889009"}]}]}, {"code": "KIDFLD", "target": [{"code": "309051001", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "64033007"}]}]}, {"code": "LAVG", "target": [{"code": "258607008", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "397394009"}]}]}, {"code": "LAVGG", "target": [{"code": "168138009", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "173830003"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "69695003"}]}]}, {"code": "LAVGP", "target": [{"code": "440137008", "equivalence": "equivalent"}]}, {"code": "LAVPG", "target": [{"code": "*********", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "67889009"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "44567001"}]}]}, {"code": "LENS1", "target": [{"code": "440473005", "equivalence": "equivalent"}]}, {"code": "LENS2", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "LESN", "target": [{"code": "309049000", "equivalence": "equivalent", "comment": "should be more specific what kind of lesion is observed - be more specific is it a wound, abscess, mass - specify! Ask SNOMED CT to mark it as a grouper term only (309049000)"}]}, {"code": "LIQ", "target": [{"code": "258442002", "equivalence": "equivalent"}]}, {"code": "LIQO", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "LSAC", "target": [{"code": "258450006", "equivalence": "equivalent", "comment": "The HL7 term is a historical term Mapped to CSF obtained by lumbar puncture", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "303949008"}]}]}, {"code": "MAHUR", "target": [{"code": "*********", "equivalence": "equivalent", "comment": "TBD in detail"}]}, {"code": "MASS", "target": [{"code": "420548004", "equivalence": "equivalent", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "4147007"}]}]}, {"code": "MBLD", "target": [{"code": "119345009", "equivalence": "equivalent"}]}, {"code": "MUCOS", "target": [{"code": "*********", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "414781009"}]}]}, {"code": "MUCUS", "target": [{"code": "258483004", "equivalence": "equivalent"}]}, {"code": "NASDR", "target": [{"code": "258474009", "equivalence": "equivalent", "comment": "Historical term - consider what is being drained and indicate that in SPM-4 instead", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "*********"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "2095001"}]}]}, {"code": "NEDL", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "NEPH", "target": [{"code": "438660002", "equivalence": "equivalent"}]}, {"code": "NGASP", "target": [{"code": "302794003", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "6853008"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "69695003"}]}]}, {"code": "NGAST", "target": [{"code": "258459007", "equivalence": "equivalent", "comment": "Historical term - consider what is being drained and indicate that in SPM-4 instead", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "127492001"}, {"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "*********"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "69695003"}]}]}, {"code": "NGS", "target": [{"code": "438660002", "equivalence": "equivalent"}]}, {"code": "NODUL", "target": [{"equivalence": "unmatched", "comment": "pending"}]}, {"code": "NSECR", "target": [{"code": "168141000", "equivalence": "equivalent"}]}, {"code": "ORH", "target": [{"code": "123038009", "equivalence": "equivalent"}]}, {"code": "ORL", "target": [{"equivalence": "unmatched", "comment": "be more precise use ulcer, tumor, vesicle", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "74262004"}]}]}, {"code": "OTH", "target": [{"code": "123038009", "equivalence": "equivalent"}]}, {"code": "PACEM", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "PCFL", "target": [{"code": "122571007", "equivalence": "equivalent"}]}, {"code": "PDSIT", "target": [{"code": "438660002", "equivalence": "equivalent"}]}, {"code": "PDTS", "target": [{"equivalence": "unmatched", "comment": "submitted for code"}]}, {"code": "PELVA", "target": [{"code": "119371008", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "12921003"}]}]}, {"code": "PENIL", "target": [{"equivalence": "unmatched", "comment": "need to know what kind of lesion, so map to: UlcerTissue_penile, VesicleFluid_penile, Wound_penile, Mass tissue_penile, Necrotic tissue_penile, AbscessAspirate_penile, Anything else?", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "18911002"}]}]}, {"code": "PERIA", "target": [{"code": "119371008", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "397158004"}]}]}, {"code": "PILOC", "target": [{"code": "119368000", "equivalence": "equivalent"}]}, {"code": "PINS", "target": [{"code": "438660002", "equivalence": "equivalent"}]}, {"code": "PIS", "target": [{"code": "258507003", "equivalence": "equivalent"}]}, {"code": "PLAN", "target": [{"code": "119301009", "equivalence": "equivalent"}]}, {"code": "PLAS", "target": [{"code": "119361006", "equivalence": "equivalent"}]}, {"code": "PLB", "target": [{"code": "119305000", "equivalence": "equivalent"}]}, {"code": "PLEVS", "target": [{"code": "119364003", "equivalence": "equivalent", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "255587001"}]}]}, {"code": "PND", "target": [{"code": "258439008", "equivalence": "equivalent", "comment": "Historical term -though in this case more often used for discharge", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "*********"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "13648007"}]}]}, {"code": "POL", "target": [{"code": "*********", "equivalence": "equivalent", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "41329004"}]}]}, {"code": "POPGS", "target": [{"code": "440493002", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "6902008"}]}]}, {"code": "POPLG", "target": [{"code": "440493002", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "6902008"}]}]}, {"code": "POPLV", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "PORTA", "target": [{"code": "119311002", "equivalence": "equivalent", "comment": "TBD in detail"}]}, {"code": "PPP", "target": [{"code": "119362004", "equivalence": "equivalent"}]}, {"code": "PROST", "target": [{"code": "438660002", "equivalence": "equivalent"}]}, {"code": "PRP", "target": [{"code": "119363009", "equivalence": "equivalent"}]}, {"code": "PSC", "target": [{"code": "119368000", "equivalence": "equivalent"}]}, {"code": "PUNCT", "target": [{"code": "*********", "equivalence": "equivalent", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "129300006"}]}]}, {"code": "PUS", "target": [{"code": "*********", "equivalence": "equivalent"}]}, {"code": "PUSFR", "target": [{"code": "*********", "equivalence": "equivalent", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "47002008"}]}]}, {"code": "PUST", "target": [{"code": "*********", "equivalence": "equivalent", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "47002008"}]}]}, {"code": "QC3", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "RANDU", "target": [{"code": "278020009", "equivalence": "equivalent"}]}, {"code": "RBITE", "target": [{"equivalence": "unmatched", "comment": "Submit for new term with parent: *********"}]}, {"code": "RECT", "target": [{"code": "119339001", "equivalence": "equivalent", "comment": "Historical term - consider what is being drained and indicate that in SPM-4 instead: stool is what is expected, should use stool sample", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "*********"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "34402009"}]}]}, {"code": "RECTA", "target": [{"code": "119371008", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "34402009"}]}]}, {"code": "RENALC", "target": [{"code": "258420003", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "64033007"}]}]}, {"code": "RENC", "target": [{"code": "258453008", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "64033007"}]}]}, {"code": "RES", "target": [{"code": "258603007", "equivalence": "equivalent"}]}, {"code": "SAL", "target": [{"code": "119342007", "equivalence": "equivalent"}]}, {"code": "SCAR", "target": [{"code": "*********", "equivalence": "equivalent"}]}, {"code": "SCLV", "target": [{"code": "*********", "equivalence": "equivalent", "comment": "TBD in detail", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "9454009"}]}]}, {"code": "SCROA", "target": [{"code": "119371008", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "20233005"}]}]}, {"code": "SECRE", "target": [{"code": "432825001", "equivalence": "equivalent"}]}, {"code": "SER", "target": [{"code": "119364003", "equivalence": "equivalent"}]}, {"code": "SHU", "target": [{"code": "119295008", "equivalence": "equivalent", "comment": "Preferred is aspiration with sterile syringe from inflamed area. Specify body location of shunt site", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "257351008"}, {"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "14766002"}]}]}, {"code": "SHUNF", "target": [{"code": "446861007", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "446860008"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "279107003"}]}]}, {"code": "SHUNT", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "SITE", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "SKBP", "target": [{"code": "309066003", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "240977001"}]}]}, {"code": "SKN", "target": [{"code": "119325001", "equivalence": "equivalent"}]}, {"code": "SMM", "target": [{"code": "420548004", "equivalence": "equivalent", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "4147007"}]}]}, {"code": "SNV", "target": [{"code": "119332005", "equivalence": "equivalent"}]}, {"code": "SPRM", "target": [{"code": "119349003", "equivalence": "equivalent"}]}, {"code": "SPRP", "target": [{"code": "*********", "equivalence": "equivalent", "comment": "TBD in detail"}]}, {"code": "SPRPB", "target": [{"code": "*********", "equivalence": "equivalent", "comment": "TBD in detail"}]}, {"code": "SPS", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "SPT", "target": [{"code": "119334006", "equivalence": "equivalent"}]}, {"code": "SPTC", "target": [{"code": "119335007", "equivalence": "equivalent"}]}, {"code": "SPTT", "target": [{"code": "258609006", "equivalence": "equivalent"}]}, {"code": "SPUT1", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "SPUTIN", "target": [{"code": "258610001", "equivalence": "equivalent"}]}, {"code": "SPUTSP", "target": [{"code": "119335007", "equivalence": "equivalent"}]}, {"code": "STER", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "STL", "target": [{"code": "119339001", "equivalence": "equivalent"}]}, {"code": "STONE", "target": [{"code": "119350003", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "64033007"}]}]}, {"code": "SUBMA", "target": [{"code": "119371008", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "5713008"}]}]}, {"code": "SUBMX", "target": [{"code": "119371008", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "4335006"}]}]}, {"code": "SUMP", "target": [{"code": "446562005", "equivalence": "equivalent", "comment": "Historical term - consider what is being drained and indicate that in SPM-4 instead", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "*********"}]}]}, {"code": "SUP", "target": [{"code": "122575003", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "58088002"}]}]}, {"code": "SUTUR", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "SWGZ", "target": [{"code": "*********", "equivalence": "equivalent", "comment": "TBD in detail"}]}, {"code": "TASP", "target": [{"code": "122877000", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "129112001"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "44567001"}]}]}, {"code": "TISS", "target": [{"code": "*********", "equivalence": "equivalent"}]}, {"code": "TISU", "target": [{"code": "122593002", "equivalence": "equivalent"}]}, {"code": "TLC", "target": [{"code": "*********", "equivalence": "equivalent", "comment": "TBD in detail"}]}, {"code": "TRAC", "target": [{"code": "438660002", "equivalence": "equivalent"}]}, {"code": "TRANS", "target": [{"code": "258538002", "equivalence": "equivalent"}]}, {"code": "TSERU", "target": [{"code": "119364003", "equivalence": "equivalent", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "255588006"}]}]}, {"code": "TSTES", "target": [{"code": "119371008", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "279572002"}]}]}, {"code": "TTRA", "target": [{"code": "258480001", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "129112001"}]}]}, {"code": "TUBES", "target": [{"code": "119310001", "equivalence": "equivalent"}]}, {"code": "TUMOR", "target": [{"code": "258435002", "equivalence": "equivalent"}]}, {"code": "TZANC", "target": [{"equivalence": "unmatched", "comment": "This is the name of a lab test. A skin sample is examined for viral inclusions."}]}, {"code": "UDENT", "target": [{"code": "123038009", "equivalence": "equivalent"}]}, {"code": "UR", "target": [{"code": "122575003", "equivalence": "equivalent"}]}, {"code": "URC", "target": [{"code": "122880004", "equivalence": "equivalent"}]}, {"code": "URINB", "target": [{"code": "122575003", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "78533007"}]}]}, {"code": "URINC", "target": [{"code": "446846006", "equivalence": "equivalent"}]}, {"code": "URINM", "target": [{"code": "258574006", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "225271002"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "431938005"}]}]}, {"code": "URINN", "target": [{"code": "446277003", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "225109005"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "25990002"}]}]}, {"code": "URINP", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "URT", "target": [{"code": "122565001", "equivalence": "equivalent"}]}, {"code": "USCOP", "target": [{"equivalence": "unmatched", "comment": "NEW specimenTERM 7", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "176178006"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "89837001"}]}]}, {"code": "USPEC", "target": [{"code": "123038009", "equivalence": "equivalent"}]}, {"code": "VASTIP", "target": [{"code": "*********", "equivalence": "equivalent", "comment": "TBD in detail"}]}, {"code": "VENT", "target": [{"code": "*********", "equivalence": "equivalent", "comment": "TBD in detail"}]}, {"code": "VITF", "target": [{"code": "*********", "equivalence": "equivalent"}]}, {"code": "VOM", "target": [{"code": "*********", "equivalence": "equivalent"}]}, {"code": "WASH", "target": [{"code": "*********", "equivalence": "equivalent"}]}, {"code": "WASI", "target": [{"code": "*********", "equivalence": "equivalent"}]}, {"code": "WAT", "target": [{"code": "*********", "equivalence": "equivalent"}]}, {"code": "WB", "target": [{"equivalence": "unmatched", "comment": "Bloodbanking term ONLY now map to blood and the respective preservative"}]}, {"code": "WEN", "target": [{"code": "*********", "equivalence": "equivalent"}]}, {"code": "WICK", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "WND", "target": [{"code": "*********", "equivalence": "equivalent"}]}, {"code": "WNDA", "target": [{"code": "*********", "equivalence": "equivalent"}]}, {"code": "WNDD", "target": [{"code": "*********", "equivalence": "equivalent", "product": [{"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "*********"}]}]}, {"code": "WNDE", "target": [{"code": "*********", "equivalence": "equivalent"}]}, {"code": "WORM", "target": [{"code": "*********", "equivalence": "equivalent"}]}, {"code": "WRT", "target": [{"code": "*********", "equivalence": "equivalent"}]}, {"code": "WWA", "target": [{"code": "*********", "equivalence": "equivalent"}]}, {"code": "WWO", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "WWT", "target": [{"equivalence": "unmatched", "comment": "TBD"}]}, {"code": "CSITE", "target": [{"equivalence": "unmatched", "comment": "Prefer to have aspirate of the pus oozing out from cleaned insertion site - if swab is all that can be obtained, then swab after cleaning, otherwise you may get a contaminated specimen and a falsely identified infected central line. Do not just swab the reddened area - that will just collect skin flora", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "119295008"}, {"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "386144009"}, {"property": "http://snomed.info/id/**********", "system": "http://snomed.info/sct", "value": "14766002"}]}]}, {"code": "CLIPP", "target": [{"equivalence": "unmatched", "comment": "Be more specific use either: 119326000^hair specimen, or 119327009^nail specimen", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "119326000"}]}]}, {"code": "SHU", "target": [{"equivalence": "unmatched", "comment": "assume swab from shunt site for mapping here - clean surface of skin prior to expressing the shunt site - preferred is aspiration with sterile syringe should use SPM.8 to specify body approximate match location of shunt site", "product": [{"property": "TypeModifier", "system": "http://snomed.info/sct", "value": "438660002"}, {"property": "http://snomed.info/id/*********", "system": "http://snomed.info/sct", "value": "257351008"}]}]}]}]}}, {"fullUrl": "http://hl7.org/fhir/ConceptMap/example2", "resource": {"resourceType": "ConceptMap", "id": "example2", "meta": {"lastUpdated": "2019-11-01T09:29:23.356+11:00"}, "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\">\n      <h2>FHIR-exanple-2 (http://hl7.org/fhir/ConceptMap/example2)</h2>\n      <p>Mapping from http://example.org/fhir/example1 to http://example.org/fhir/example2</p>\n      <p>DRAFT (not intended for production usage). Published on 13/06/2012 by HL7, Inc (FHIR project team (example): \n        <a href=\"http://hl7.org/fhir\">http://hl7.org/fhir</a>). \n      </p>\n      <div>\n        <p>An example mapping</p>\n\n      </div>\n      <br/>\n      <table class=\"grid\">\n        <tr>\n          <td colspan=\"2\">\n            <b>Source Concept Details</b>\n          </td>\n          <td>\n            <b>Equivalence</b>\n          </td>\n          <td colspan=\"1\">\n            <b>Destination Concept Details</b>\n          </td>\n        </tr>\n        <tr>\n          <td>\n            <b>Code</b> from http://example.org/fhir/example1\n          </td>\n          <td>\n            <b>Code</b> from http://example.org/fhir/example3\n          </td>\n          <td/>\n          <td>\n            <b>Code</b> from http://example.org/fhir/example2\n          </td>\n        </tr>\n        <tr>\n          <td>code</td>\n          <td>some-code</td>\n          <td>\n            <a href=\"codesystem-concept-map-equivalence.html#equivalent\">equivalent</a>\n          </td>\n          <td>code2</td>\n        </tr>\n      </table>\n    </div>"}, "url": "http://hl7.org/fhir/ConceptMap/example2", "version": "4.0.1", "name": "FHIR-exanple-2", "title": "FHIR Example 2", "status": "draft", "experimental": true, "date": "2012-06-13", "publisher": "HL7, Inc", "contact": [{"name": "FHIR project team (example)", "telecom": [{"system": "url", "value": "http://hl7.org/fhir"}]}], "description": "An example mapping", "purpose": "To illustrate mapping features", "sourceUri": "http://example.org/fhir/example1", "targetUri": "http://example.org/fhir/example2", "group": [{"source": "http://example.org/fhir/example1", "target": "http://example.org/fhir/example2", "element": [{"code": "code", "display": "Example Code", "target": [{"code": "code2", "display": "Some Example Code", "equivalence": "equivalent", "dependsOn": [{"property": "http://example.org/fhir/property-value/example", "system": "http://example.org/fhir/example3", "value": "some-code", "display": "Something Coded"}]}]}], "unmapped": {"mode": "other-map", "url": "http://example.org/fhir/ConceptMap/map2"}}]}}, {"fullUrl": "http://hl7.org/fhir/ConceptMap/cdshooks-indicator", "resource": {"resourceType": "ConceptMap", "id": "cdshooks-indicator", "meta": {"lastUpdated": "2019-11-01T09:29:23.356+11:00"}, "text": {"status": "generated", "div": "<div xmlns=\"http://www.w3.org/1999/xhtml\">\n      <h2>IndicatorToRequestPriority (http://cds-hooks.hl7.org/ConceptMap/indicator-to-request-priority)</h2>\n      <p>Mapping from http://cds-hooks.hl7.org/ValueSet/indicator to \n        <a href=\"valueset-request-priority.html\">http://hl7.org/fhir/ValueSet/request-priority</a>\n      </p>\n      <p>DRAFT. Published on ?? by null. </p>\n      <div>\n        <p>This concept map defines a mapping from CDS Hooks indicator to request priority.</p>\n\n      </div>\n      <br/>\n      <table class=\"grid\">\n        <tr>\n          <td>\n            <b>Source Code</b>\n          </td>\n          <td>\n            <b>Equivalence</b>\n          </td>\n          <td>\n            <b>Destination Code</b>\n          </td>\n        </tr>\n        <tr>\n          <td>info</td>\n          <td>equal</td>\n          <td>routine</td>\n        </tr>\n        <tr>\n          <td>warning</td>\n          <td>equal</td>\n          <td>urgent</td>\n        </tr>\n        <tr>\n          <td>critical</td>\n          <td>equal</td>\n          <td>stat</td>\n        </tr>\n      </table>\n    </div>"}, "url": "http://cds-hooks.hl7.org/ConceptMap/indicator-to-request-priority", "name": "IndicatorToRequestPriority", "status": "draft", "experimental": false, "description": "This concept map defines a mapping from CDS Hooks indicator to request priority.", "sourceCanonical": "http://cds-hooks.hl7.org/ValueSet/indicator", "targetCanonical": "http://hl7.org/fhir/ValueSet/request-priority", "group": [{"element": [{"code": "info", "target": [{"code": "routine", "equivalence": "equal"}]}, {"code": "warning", "target": [{"code": "urgent", "equivalence": "equal"}]}, {"code": "critical", "target": [{"code": "stat", "equivalence": "equal"}]}]}]}}]}