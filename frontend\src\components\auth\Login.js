import { useState } from 'react';
import { FaEnvelope, FaLock, FaUserCog, FaUserMd, FaUserNurse } from 'react-icons/fa';
import { Link, useNavigate } from 'react-router-dom';
import logo from '../../assets/imgs/logo.png';
import { useAuth } from '../../context/AuthContext';

/**
 * Login component that handles user authentication and role selection.
 * Provides a two-step process: first authentication, then role selection.
 * Integrates with AuthContext for state management and navigation.
 * 
 * Features:
 * - Email/password authentication with validation
 * - Role-based dashboard navigation (doctor/nurse/admin)
 * - Loading states and error handling
 * - Responsive design with company branding
 * 
 * @returns {JSX.Element} Login form and role selection interface
 */
const Login = () => {
  // Form state management
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Authentication context and navigation
  const { login, setRole } = useAuth();
  const navigate = useNavigate();

  /**
   * Validates the login form inputs.
   * Checks for required fields and basic email format validation.
   * 
   * @returns {Object} Validation result with isValid flag and error message
   */
  const validateForm = () => {
    if (!email.trim() || !password.trim()) {
      return { isValid: false, error: 'Please enter both email and password.' };
    }

    // Basic email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return { isValid: false, error: 'Please enter a valid email address.' };
    }

    return { isValid: true, error: '' };
  };

  /**
   * Handles form submission for user login.
   * Validates inputs, attempts authentication, and manages loading states.
   * 
   * @param {Event} e - Form submit event
   */
  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    // Validate form inputs
    const validation = validateForm();
    if (!validation.isValid) {
      setError(validation.error);
      setIsLoading(false);
      return;
    }

    try {
      const success = await login(email.toLowerCase().trim(), password);

      if (success) {
        setIsLoggedIn(true);
      } else {
        setError('Invalid email or password. Please try again.');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('Login failed. Please check your credentials and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handles role selection after successful login.
   * Sets the user role and navigates to the appropriate dashboard.
   * 
   * @param {string} role - Selected user role ('doctor', 'nurse', 'admin')
   */
  const handleRoleSelect = (role) => {
    try {
      setRole(role);

      // Navigate to the appropriate dashboard based on role
      const dashboardRoutes = {
        doctor: '/doctor-dashboard',
        nurse: '/nurse-dashboard',
        admin: '/admin-dashboard',
      };

      const targetRoute = dashboardRoutes[role];
      if (targetRoute) {
        navigate(targetRoute);
      } else {
        console.error(`Invalid role selected: ${role}`);
        navigate('/');
      }
    } catch (error) {
      console.error('Role selection error:', error);
      setError('Failed to set user role. Please try again.');
    }
  };

  /**
   * Handles input changes and clears errors when user starts typing.
   * 
   * @param {Event} e - Input change event
   * @param {Function} setter - State setter function
   */
  const handleInputChange = (e, setter) => {
    setter(e.target.value);
    if (error) {
      setError('');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col justify-center items-center px-4">
      {/* Company Logo */}
      <div className="mb-10">
        <img
          src={logo}
          alt="Syncore Medical Technologies Logo"
          className="h-32"
        />
      </div>

      {/* Main Login Card */}
      <div className="max-w-md w-full bg-white rounded-xl shadow-lg overflow-hidden">
        {/* Card Content */}
        <div className="px-6 pt-6 pb-6">
          {/* Error Banner */}
          {error && (
            <div className="mb-4 bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg" role="alert">
              <div className="flex">
                <div className="flex-shrink-0">
                  <FaLock className="h-5 w-5 text-red-400" aria-hidden="true" />
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-700">
                    {error}
                  </p>
                </div>
              </div>
            </div>
          )}

          {!isLoggedIn ? (
            // Login Form
            <form onSubmit={handleSubmit} className="space-y-6" noValidate>
              {/* Email Field */}
              <div className="mb-6">
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Email address
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaEnvelope className="h-5 w-5 text-gray-400" aria-hidden="true" />
                  </div>
                  <input
                    id="email"
                    type="email"
                    name="email"
                    autoComplete="email"
                    value={email}
                    onChange={(e) => handleInputChange(e, setEmail)}
                    required
                    disabled={isLoading}
                    className="w-full px-4 py-3 pl-10 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors disabled:bg-gray-100 disabled:cursor-not-allowed"
                    placeholder="<EMAIL>"
                    aria-describedby={error && error.includes('email') ? 'email-error' : undefined}
                  />
                </div>
              </div>

              {/* Password Field */}
              <div className="mb-6">
                <div className="flex justify-between mb-2">
                  <label
                    htmlFor="password"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Password
                  </label>
                  <button
                    type="button"
                    className="text-sm text-indigo-600 hover:text-indigo-800 transition-colors"
                    disabled={isLoading}
                  >
                    Forgot password?
                  </button>
                </div>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaLock className="h-5 w-5 text-gray-400" aria-hidden="true" />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type="password"
                    autoComplete="current-password"
                    value={password}
                    onChange={(e) => handleInputChange(e, setPassword)}
                    required
                    disabled={isLoading}
                    className="w-full px-4 py-3 pl-10 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors disabled:bg-gray-100 disabled:cursor-not-allowed"
                    placeholder="••••••••"
                    aria-describedby={error && error.includes('password') ? 'password-error' : undefined}
                  />
                </div>
              </div>

              {/* Submit Button */}
              <div className="mb-6">
                <button
                  type="submit"
                  disabled={isLoading}
                  className={`w-full py-3 px-4 rounded-lg text-white font-medium ${isLoading
                      ? 'bg-indigo-400 cursor-not-allowed'
                      : 'bg-indigo-600 hover:bg-indigo-700'
                    } transition-colors shadow-md flex justify-center items-center`}
                >
                  {isLoading ? (
                    <>
                      <FaLock className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" aria-hidden="true" />
                      Signing in...
                    </>
                  ) : (
                    'Sign In'
                  )}
                </button>
              </div>

              {/* Contact Administrator Link */}
              <div className="text-center text-sm text-gray-600">
                <span>Don't have an account? </span>
                <Link
                  to="/Contact"
                  className="text-indigo-600 hover:text-indigo-800 font-medium transition-colors"
                >
                  Contact Your Administrator
                </Link>
              </div>
            </form>
          ) : (
            // Role Selection
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900">
                  Select Your Role
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  Choose your role to access the appropriate dashboard
                </p>
              </div>

              <div className="grid grid-cols-1 gap-4" role="group" aria-labelledby="role-selection-heading">
                {/* Doctor Role Button */}
                <button
                  onClick={() => handleRoleSelect('doctor')}
                  className="flex items-center justify-center px-4 py-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
                  aria-label="Select Doctor role"
                >
                  <FaUserMd className="h-6 w-6 mr-3" aria-hidden="true" />
                  <span className="text-lg">Doctor</span>
                </button>

                {/* Nurse Role Button */}
                <button
                  onClick={() => handleRoleSelect('nurse')}
                  className="flex items-center justify-center px-4 py-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200"
                  aria-label="Select Nurse role"
                >
                  <FaUserNurse className="h-6 w-6 mr-3" aria-hidden="true" />
                  <span className="text-lg">Nurse</span>
                </button>

                {/* Administrator Role Button */}
                <button
                  onClick={() => handleRoleSelect('admin')}
                  className="flex items-center justify-center px-4 py-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200"
                  aria-label="Select Administrator role"
                >
                  <FaUserCog className="h-6 w-6 mr-3" aria-hidden="true" />
                  <span className="text-lg">Administrator</span>
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-3 bg-gray-50 border-t border-gray-100 text-center">
          <p className="text-xs text-gray-500">
            &copy; {new Date().getFullYear()} Syncore Medical Technologies. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;