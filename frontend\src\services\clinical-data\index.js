// Export all components from a single entry point
const { OpenAIService } = require('./collection/openAIService.service');
const { ClinicalResponseParser } = require('./extraction/responseParser');
const { ClinicalPromptBuilder } = require('./prompt/promptTemplates');
const { CLINICAL_DATA_SCHEMA } = require('./validation/clinicalSchemas');

class ClinicalDataService {
  constructor() {
    this.openAIService = new OpenAIService();
    this.responseParser = new ClinicalResponseParser();
    this.promptBuilder = new ClinicalPromptBuilder();
  }

  async processClinicalData(transcript, extractionType) {
    try {
      const response = await this.openAIService.extractClinicalData(transcript, extractionType);
      return this.responseParser.parseResponse(response, extractionType);
    } catch (error) {
      throw error;
    }
  }
}

module.exports = {
  ClinicalDataService,
  OpenAIService,
  ClinicalResponseParser,
  ClinicalPromptBuilder,
  CLINICAL_DATA_SCHEMA
};