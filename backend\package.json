{"name": "backend", "version": "1.0.0", "type": "module", "main": "dist/main.js", "scripts": {"dev": "nodemon", "build": "tsc", "start": "node dist/main.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^24.0.4", "@types/pdfmake": "^0.2.11", "dotenv": "^16.6.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "dependencies": {"@medplum/cli": "^4.2.0", "@medplum/core": "^4.1.12", "@medplum/fhirpath": "^0.9.6", "@medplum/fhirtypes": "^4.2.4", "@medplum/mock": "^4.1.12", "@types/cors": "^2.8.19", "axios": "^1.10.0", "cors": "^2.8.5", "express": "^5.1.0", "tsx": "^4.1.0", "zod": "^3.25.67"}, "types": "./dist/main.d.ts", "description": ""}