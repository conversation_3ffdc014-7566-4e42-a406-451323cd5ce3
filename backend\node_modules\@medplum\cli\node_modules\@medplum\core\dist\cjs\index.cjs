"use strict";var Ur=Object.defineProperty;var rs=Object.getOwnPropertyDescriptor;var ns=Object.getOwnPropertyNames;var is=Object.prototype.hasOwnProperty;var os=(r,e)=>{for(var t in e)Ur(r,t,{get:e[t],enumerable:!0})},ss=(r,e,t,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of ns(e))!is.call(r,i)&&i!==t&&Ur(r,i,{get:()=>e[i],enumerable:!(n=rs(e,i))||n.enumerable});return r};var as=r=>ss(Ur({},"__esModule",{value:!0}),r);var kl={};os(kl,{AccessPolicyInteraction:()=>xe,AckCode:()=>fl,AndAtom:()=>vt,ArithemticOperatorAtom:()=>D,AsAtom:()=>ce,BooleanInfixOperatorAtom:()=>R,CPT:()=>ps,ClientStorage:()=>ze,ConcatAtom:()=>dt,ContainsAtom:()=>ft,ContentType:()=>w,DEFAULT_ACCEPT:()=>Do,DEFAULT_MAX_SEARCH_COUNT:()=>bc,DEFAULT_SEARCH_COUNT:()=>Sc,DataSampler:()=>Or,DotAtom:()=>ae,EmptySetAtom:()=>lt,EqualsAtom:()=>mt,EquivalentAtom:()=>gt,EventTarget:()=>Sr,ExternalSecretSystems:()=>Tu,FHIRCAST_EVENT_NAMES:()=>br,FHIRCAST_EVENT_RESOURCES:()=>So,FHIRCAST_EVENT_VERSION_REQUIRED:()=>Er,FHIRCAST_RESOURCE_TYPES:()=>To,FhirFilterComparison:()=>Ut,FhirFilterConnective:()=>Wt,FhirFilterNegation:()=>Bt,FhirPathAtom:()=>ke,FhircastConnection:()=>Ot,FileBuilder:()=>ri,FunctionAtom:()=>Z,HTTP_HL7_ORG:()=>Wr,HTTP_TERMINOLOGY_HL7_ORG:()=>hs,Hl7Context:()=>le,Hl7Field:()=>H,Hl7Message:()=>ii,Hl7Segment:()=>Xe,ICD10:()=>ds,ImpliesAtom:()=>bt,InAtom:()=>ht,IndexerAtom:()=>ge,InfixOperatorAtom:()=>ee,IsAtom:()=>ye,LOINC:()=>cs,LRUCache:()=>Se,LiteralAtom:()=>L,LogLevel:()=>Le,LogLevelNames:()=>Yo,Logger:()=>oi,MEDPLUM_CLI_CLIENT_ID:()=>cu,MEDPLUM_RELEASES_URL:()=>Zo,MEDPLUM_VERSION:()=>Ar,MedplumClient:()=>_t,MedplumKeyValueClient:()=>Dt,MemoryStorage:()=>Pr,MockAsyncClientStorage:()=>$n,NDC:()=>fs,NotEqualsAtom:()=>yt,NotEquivalentAtom:()=>xt,OAuthClientAssertionType:()=>Mo,OAuthGrantType:()=>Me,OAuthTokenAuthMethod:()=>mu,OAuthTokenType:()=>Vo,OperationOutcomeError:()=>d,Operator:()=>f,OperatorPrecedence:()=>g,OrAtom:()=>Tt,Parser:()=>qt,ParserBuilder:()=>Ze,PrefixOperatorAtom:()=>Ye,PropertyType:()=>l,RXNORM:()=>ls,ReadablePromise:()=>V,ReconnectingWebSocket:()=>Vt,SNOMED:()=>us,SearchParameterType:()=>q,SubscriptionEmitter:()=>Ke,SubscriptionManager:()=>Mt,SymbolAtom:()=>W,Tokenizer:()=>ue,TransformMapCollection:()=>Mr,TypedEventTarget:()=>G,UCUM:()=>Br,UnaryOperatorAtom:()=>pt,UnionAtom:()=>me,VALID_HOSTNAME_REGEX:()=>$i,XorAtom:()=>St,accepted:()=>Es,accessPolicySupportsInteraction:()=>_n,addProfileToResource:()=>_a,allOk:()=>ms,append:()=>Ae,applyDefaultValuesToElement:()=>ku,applyDefaultValuesToResource:()=>Iu,applyFixedOrPatternValue:()=>Ft,arrayBufferToBase64:()=>Rn,arrayBufferToHex:()=>En,arrayify:()=>Ue,assert:()=>Po,assertContextVersionOptional:()=>Un,assertOk:()=>_s,assertReleaseManifest:()=>es,badRequest:()=>b,booleanToTypedValue:()=>h,buildElementsContext:()=>kr,buildTypeName:()=>Yi,calculateAge:()=>at,calculateAgeString:()=>ua,canReadResourceType:()=>Gc,canWriteResource:()=>Qc,canWriteResourceType:()=>Hc,capitalize:()=>O,checkForNull:()=>si,checkIfValidMedplumVersion:()=>Al,clearReleaseCache:()=>wl,compressElement:()=>Fs,concatUrls:()=>U,conceptMapTranslate:()=>Yn,conflict:()=>Rs,convertContainedResourcesToBundle:()=>Zc,convertToTransactionBundle:()=>go,crawlTypedValue:()=>Yt,crawlTypedValueAsync:()=>Gs,createConstraintIssue:()=>Zr,createFhircastMessagePayload:()=>Cr,createOperationOutcomeIssue:()=>pe,createProcessingIssue:()=>en,createReference:()=>de,createStructureIssue:()=>T,created:()=>ys,decodeBase64:()=>yo,decodeBase64Url:()=>Ln,deepClone:()=>he,deepEquals:()=>ne,deepIncludes:()=>Be,encodeBase64:()=>At,encodeBase64Url:()=>Jc,encryptSHA256:()=>Fn,ensureNoLeadingSlash:()=>ji,ensureTrailingSlash:()=>pr,escapeHtml:()=>Ua,evalFhirPath:()=>De,evalFhirPathTyped:()=>P,evalSqlOnFhir:()=>bl,expandSampledData:()=>Qn,expandSampledObservation:()=>Ru,fetchLatestVersionString:()=>Ol,fetchVersionManifest:()=>Nr,fhirPathArrayEquals:()=>yn,fhirPathArrayEquivalent:()=>rr,fhirPathArrayNotEquals:()=>gn,fhirPathEquals:()=>We,fhirPathEquivalent:()=>Ii,fhirPathIs:()=>it,fhirPathNot:()=>mn,fhirTypeToJsType:()=>Zt,findObservationInterval:()=>Ea,findObservationReferenceRange:()=>Ra,findObservationReferenceRanges:()=>Ui,findResourceByCode:()=>Da,flatMapFilter:()=>Na,forbidden:()=>xs,formatAddress:()=>Hi,formatCodeableConcept:()=>qe,formatCoding:()=>In,formatDate:()=>ja,formatDateTime:()=>dr,formatFamilyName:()=>qa,formatGivenName:()=>Wa,formatHl7DateTime:()=>Xo,formatHumanName:()=>je,formatMoney:()=>Xa,formatObservationValue:()=>zi,formatPeriod:()=>$a,formatQuantity:()=>oe,formatRange:()=>Ka,formatReferenceString:()=>Gi,formatSearchQuery:()=>Dc,formatTime:()=>Qi,formatTiming:()=>za,generateId:()=>ve,getAllDataTypes:()=>sn,getAllQuestionnaireAnswers:()=>pa,getCodeBySystem:()=>or,getDataType:()=>te,getDateProperty:()=>ca,getDefaultValuesForNewSliceEntry:()=>Du,getDisplayString:()=>Vi,getElementDefinition:()=>st,getElementDefinitionFromElements:()=>eo,getElementDefinitionTypeName:()=>Qt,getExpressionForResourceType:()=>Tc,getExpressionsForResourceType:()=>Dn,getExtension:()=>re,getExtensionValue:()=>fa,getIdentifier:()=>Li,getImageSrc:()=>aa,getNestedProperty:()=>Pe,getParsedExpressionForResourceType:()=>no,getPathDifference:()=>ie,getPathDisplayName:()=>ic,getPropertyDisplayName:()=>Zi,getQueryString:()=>On,getQuestionnaireAnswers:()=>la,getRandomString:()=>Tr,getReferenceString:()=>N,getResourceTypes:()=>tc,getSearchParameter:()=>nc,getSearchParameterDetails:()=>gr,getSearchParameters:()=>rc,getStatus:()=>Ms,getTypedPropertyValue:()=>C,getTypedPropertyValueWithPath:()=>bi,getTypedPropertyValueWithSchema:()=>Ai,getTypedPropertyValueWithoutSchema:()=>Oi,getValueSliceName:()=>Jn,getWebSocketUrl:()=>An,globalSchema:()=>_,gone:()=>vs,indexDefaultSearchParameters:()=>pn,indexSearchParameter:()=>hr,indexSearchParameterBundle:()=>ec,indexStructureDefinitionBundle:()=>Jt,inflateBaseSchema:()=>tn,inflateElement:()=>mi,initFhirPathParserBuilder:()=>Ge,isAccepted:()=>As,isCodeableConcept:()=>ir,isCoding:()=>nr,isCompletedSubscriptionRequest:()=>Bn,isComplexTypeCode:()=>cr,isConflict:()=>ks,isContextVersionRequired:()=>Nn,isCreated:()=>ws,isDataTypeLoaded:()=>an,isDateString:()=>xn,isDateTimeString:()=>we,isEmpty:()=>S,isError:()=>fi,isFhirCriteriaMet:()=>wo,isFhircastResourceType:()=>bo,isGone:()=>Ds,isJwt:()=>Wn,isLowerCase:()=>Sa,isMedplumAccessToken:()=>qn,isNotFound:()=>Is,isObject:()=>E,isOk:()=>Ht,isOperationOutcome:()=>Ee,isPeriod:()=>ot,isPopulated:()=>K,isPrimitiveType:()=>tt,isProfileLoaded:()=>un,isProfileResource:()=>Di,isQuantity:()=>I,isQuantityEquivalent:()=>vn,isRedirect:()=>Os,isReference:()=>z,isResource:()=>A,isResourceType:()=>cn,isResourceTypeSchema:()=>Xt,isResourceWithId:()=>sr,isSliceDefinitionWithTypes:()=>zn,isString:()=>ar,isStringArray:()=>bn,isTextObject:()=>Fi,isTypedValue:()=>ac,isUUID:()=>Ta,isUnauthenticated:()=>Vs,isValidDate:()=>fr,isValidHostname:()=>Ma,isValidMedplumSemver:()=>ts,lazy:()=>wn,loadDataType:()=>Kt,mapByIdentifier:()=>La,matchDiscriminant:()=>tr,matchesRange:()=>Pn,matchesSearchRequest:()=>wt,multipleMatches:()=>Ss,normalizeArrayBufferView:()=>Cn,normalizeCreateBinaryOptions:()=>Hn,normalizeCreatePdfOptions:()=>_o,normalizeErrorString:()=>Re,normalizeOperationOutcome:()=>et,notFound:()=>Jr,notModified:()=>gs,operationOutcomeIssueToString:()=>hi,operationOutcomeToString:()=>Yr,parseFhirPath:()=>Ct,parseFilterParameter:()=>dl,parseHl7DateTime:()=>ml,parseJWTPayload:()=>kt,parseLogLevel:()=>gl,parseMappingLanguage:()=>qu,parseParameter:()=>co,parseReference:()=>ia,parseSearchRequest:()=>He,parseStructureDefinition:()=>zt,parseXFhirQuery:()=>kc,preciseEquals:()=>Oa,preciseGreaterThan:()=>ka,preciseGreaterThanOrEquals:()=>qi,preciseLessThan:()=>Ia,preciseLessThanOrEquals:()=>Wi,preciseRound:()=>Aa,preconditionFailed:()=>Ts,projectAdminResourceTypes:()=>po,protectedResourceTypes:()=>lo,readInteractions:()=>fo,redirect:()=>Ps,removeDuplicates:()=>nt,removeProfileFromResource:()=>Fa,reorderBundle:()=>xo,resolveId:()=>fe,resourceMatchesSubscriptionCriteria:()=>su,satisfiedAccessPolicy:()=>ho,serializeFhircastSubscriptionRequest:()=>Rr,serverError:()=>Xr,serverTimeout:()=>Cs,setCodeBySystem:()=>ba,setIdentifier:()=>da,singleton:()=>$,singularize:()=>Va,sleep:()=>ur,sortStringArray:()=>ut,splitN:()=>lr,splitSearchOnComma:()=>Pt,streamToBuffer:()=>Sl,stringify:()=>ct,stringifyTypedValue:()=>kn,structureMapTransform:()=>ju,subsetResource:()=>Bs,summarizeObservations:()=>Su,toJsBoolean:()=>M,toPeriod:()=>ra,toTypedValue:()=>v,tooManyRequests:()=>bs,tryGetDataType:()=>Ne,tryGetJwtExpiration:()=>jn,tryGetProfile:()=>ln,typedValueToString:()=>Ba,unauthorized:()=>be,unauthorizedTokenAudience:()=>Gt,unauthorizedTokenExpired:()=>Kr,validateFhircastSubscriptionRequest:()=>It,validateResource:()=>Js,validateResourceType:()=>xl,validateTypedValue:()=>Ks,validationError:()=>y,validationRegexes:()=>rt,warnIfNewerVersionAvailable:()=>Il,wordWrap:()=>Jo});module.exports=as(kl);var Ye=class{constructor(e,t){this.operator=e,this.child=t}toString(){return`${this.operator}(${this.child.toString()})`}},ee=class{constructor(e,t,n){this.operator=e,this.left=t,this.right=n}toString(){return`(${this.left.toString()} ${this.operator} ${this.right.toString()})`}},Ze=class{constructor(){this.prefixParselets={};this.infixParselets={}}registerInfix(e,t){return this.infixParselets[e]=t,this}registerPrefix(e,t){return this.prefixParselets[e]=t,this}prefix(e,t,n){return this.registerPrefix(e,{parse(i,o){let s=i.consumeAndParse(t);return n(o,s)}})}infixLeft(e,t,n){return this.registerInfix(e,{parse(i,o,s){let a=i.consumeAndParse(t);return n(o,s,a)},precedence:t})}construct(e){return new qt(e,this.prefixParselets,this.infixParselets)}},qt=class{constructor(e,t,n){this.tokens=e,this.prefixParselets=t,this.infixParselets=n}hasMore(){return this.tokens.length>0}match(e){return this.peek()?.id!==e?!1:(this.consume(),!0)}consumeAndParse(e=1/0){let t=this.consume(),n=this.prefixParselets[t.id];if(!n)throw Error(`Parse error at "${t.value}" (line ${t.line}, column ${t.column}). No matching prefix parselet.`);let i=n.parse(this,t);for(;e>this.getPrecedence();){let o=this.consume();i=this.getInfixParselet(o).parse(this,i,o)}return i}getPrecedence(){let e=this.peek();if(!e)return 1/0;let t=this.getInfixParselet(e);return t?t.precedence:1/0}consume(e,t){if(!this.tokens.length)throw Error("Cant consume unknown more tokens.");if(e&&this.peek()?.id!==e){let n=this.peek();throw Error(`Expected ${e} but got "${n.id}" (${n.value}) at line ${n.line} column ${n.column}.`)}if(t&&this.peek()?.value!==t){let n=this.peek();throw Error(`Expected "${t}" but got "${n.value}" at line ${n.line} column ${n.column}.`)}return this.tokens.shift()}peek(){return this.tokens.length>0?this.tokens[0]:void 0}removeComments(){this.tokens=this.tokens.filter(e=>e.id!=="Comment")}getInfixParselet(e){return this.infixParselets[e.id==="Symbol"?e.value:e.id]}};var Se=class{constructor(e=10){this.max=e,this.cache=new Map}clear(){this.cache.clear()}get(e){let t=this.cache.get(e);return t&&(this.cache.delete(e),this.cache.set(e,t)),t}set(e,t){this.cache.has(e)?this.cache.delete(e):this.cache.size>=this.max&&this.cache.delete(this.first()),this.cache.set(e,t)}delete(e){this.cache.delete(e)}keys(){return this.cache.keys()}first(){return this.cache.keys().next().value}};var Br="http://unitsofmeasure.org",cs="http://loinc.org",us="http://snomed.info/sct",ls="http://www.nlm.nih.gov/research/umls/rxnorm",ps="http://www.ama-assn.org/go/cpt",ds="http://hl7.org/fhir/sid/icd-10",fs="http://hl7.org/fhir/sid/ndc",Wr="http://hl7.org",hs="http://terminology.hl7.org";var qr="ok",jt="created",jr="gone",$r="not-modified",Gr="found",Hr="not-found",Qr="conflict",zr="unauthorized",ci="forbidden",ui="precondition-failed",li="multiple-matches",pi="too-many-requests",$t="accepted",di="server-timeout",ms={resourceType:"OperationOutcome",id:qr,issue:[{severity:"information",code:"informational",details:{text:"All OK"}}]},ys={resourceType:"OperationOutcome",id:jt,issue:[{severity:"information",code:"informational",details:{text:"Created"}}]},gs={resourceType:"OperationOutcome",id:$r,issue:[{severity:"information",code:"informational",details:{text:"Not Modified"}}]},Jr={resourceType:"OperationOutcome",id:Hr,issue:[{severity:"error",code:"not-found",details:{text:"Not found"}}]},be={resourceType:"OperationOutcome",id:zr,issue:[{severity:"error",code:"login",details:{text:"Unauthorized"}}]},Kr={...be,issue:[...be.issue,{severity:"error",code:"expired",details:{text:"Token expired"}}]},Gt={...be,issue:[...be.issue,{severity:"error",code:"invalid",details:{text:"Token not issued for this audience"}}]},xs={resourceType:"OperationOutcome",id:ci,issue:[{severity:"error",code:"forbidden",details:{text:"Forbidden"}}]},vs={resourceType:"OperationOutcome",id:jr,issue:[{severity:"error",code:"deleted",details:{text:"Gone"}}]},Ts={resourceType:"OperationOutcome",id:ui,issue:[{severity:"error",code:"processing",details:{text:"Precondition Failed"}}]},Ss={resourceType:"OperationOutcome",id:li,issue:[{severity:"error",code:"multiple-matches",details:{text:"Multiple resources found matching condition"}}]},bs={resourceType:"OperationOutcome",id:pi,issue:[{severity:"error",code:"throttled",details:{text:"Too Many Requests"}}]};function Es(r){return{resourceType:"OperationOutcome",id:$t,issue:[{severity:"information",code:"informational",details:{text:"Accepted"},diagnostics:r}]}}function b(r,e){return{resourceType:"OperationOutcome",issue:[{severity:"error",code:"invalid",details:{text:r},...e?{expression:[e]}:void 0}]}}function Rs(r,e){return{resourceType:"OperationOutcome",id:Qr,issue:[{severity:"error",code:"conflict",details:{coding:e?[{code:e}]:void 0,text:r}}]}}function y(r){return{resourceType:"OperationOutcome",issue:[{severity:"error",code:"structure",details:{text:r}}]}}function Xr(r){return{resourceType:"OperationOutcome",issue:[{severity:"error",code:"exception",details:{text:"Internal server error"},diagnostics:r.toString()}]}}function Cs(r){return{resourceType:"OperationOutcome",id:di,issue:[{severity:"error",code:"timeout",details:{text:r??"Server timeout"}}]}}function Ps(r){let e=r.toString();return{resourceType:"OperationOutcome",id:Gr,issue:[{severity:"information",code:"informational",details:{coding:[{system:"urn:ietf:rfc:3986",code:e}],text:"Redirect to "+e}}]}}function fi(r){return!r||typeof r!="object"?!1:r instanceof Error||typeof DOMException<"u"&&r instanceof DOMException?!0:Object.prototype.toString.call(r)==="[object Error]"}function Ee(r){return typeof r=="object"&&r!==null&&r.resourceType==="OperationOutcome"}function Ht(r){return r.id===qr||r.id===jt||r.id===$r||r.id===$t}function ws(r){return r.id===jt}function As(r){return r.id===$t}function Os(r){return r.id===Gr}function Is(r){return r.id===Hr}function ks(r){return r.id===Qr}function Ds(r){return r.id===jr}function Vs(r){return r.id===zr}function Ms(r){switch(r.id){case qr:return 200;case jt:return 201;case $t:return 202;case Gr:return 302;case $r:return 304;case zr:return 401;case ci:return 403;case Hr:return 404;case Qr:return 409;case jr:return 410;case ui:case li:return 412;case pi:return 429;case di:return 504;default:return r.issue?.[0]?.code==="exception"?500:400}}function _s(r,e){if(!Ht(r)||e===void 0)throw new d(r)}var d=class extends Error{constructor(e,t){super(Yr(e)),this.outcome=e,this.cause=t}};function et(r){return r instanceof d?r.outcome:Ee(r)?r:b(Re(r))}function Re(r){return r?typeof r=="string"?r:fi(r)?r.message:Ee(r)?Yr(r):typeof r=="object"&&"code"in r&&typeof r.code=="string"?r.code:JSON.stringify(r):"Unknown error"}function Yr(r){let e=r.issue?.map(hi)??[];return e.length>0?e.join("; "):"Unknown error"}function hi(r){let e;return r.details?.text?r.diagnostics?e=`${r.details.text} (${r.diagnostics})`:e=r.details.text:r.diagnostics?e=r.diagnostics:e="Unknown error",r.expression?.length&&(e+=` (${r.expression.join(", ")})`),e}function pe(r,e,t,n,i){let o={severity:r,code:e,details:{text:t},expression:[n]};return i&&(o.diagnostics=JSON.stringify(i)),o}function T(r,e){return pe("error","structure",e,r)}function Zr(r,e){return pe("error","invariant",`Constraint ${e.key} not met: ${e.description}`,r,{fhirpath:e.expression})}function en(r,e,t,n){return pe("error","processing",e,r,{...n,error:t})}var Ls={"http://hl7.org/fhirpath/System.String":"string"};function Fs(r){let e={};return r.min!==0&&(e.min=r.min),r.max!==1&&Number.isFinite(r.max)?e.max=r.max:r.max===Number.POSITIVE_INFINITY&&(e.max=Number.MAX_SAFE_INTEGER),e.type=r.type?.map(t=>({...t,extension:void 0,code:Ls[t.code]??t.code})),e}function mi(r,e){let t=e.max&&e.max===Number.MAX_SAFE_INTEGER?Number.POSITIVE_INFINITY:e.max;return{path:r,description:"",type:e.type??[],min:e.min??0,max:t??1,isArray:!!t&&t>1,constraints:[]}}function tn(r){let e=Object.create(null);for(let[t,n]of Object.entries(r))e[t]={name:t,type:t,path:t,elements:Object.fromEntries(Object.entries(n.elements).map(([i,o])=>[i,mi(i,o)])),constraints:[],innerTypes:[]};return e}var yi={Element:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]}}},BackboneElement:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},modifierExtension:{max:****************,type:[{code:"Extension"}]}}},Address:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},use:{type:[{code:"code"}]},type:{type:[{code:"code"}]},text:{type:[{code:"string"}]},line:{max:****************,type:[{code:"string"}]},city:{type:[{code:"string"}]},district:{type:[{code:"string"}]},state:{type:[{code:"string"}]},postalCode:{type:[{code:"string"}]},country:{type:[{code:"string"}]},period:{type:[{code:"Period"}]}}},Age:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},value:{type:[{code:"decimal"}]},comparator:{type:[{code:"code"}]},unit:{type:[{code:"string"}]},system:{type:[{code:"uri"}]},code:{type:[{code:"code"}]}}},Annotation:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},"author[x]":{type:[{code:"Reference",targetProfile:["http://hl7.org/fhir/StructureDefinition/Practitioner","http://hl7.org/fhir/StructureDefinition/Patient","http://hl7.org/fhir/StructureDefinition/RelatedPerson","http://hl7.org/fhir/StructureDefinition/Organization"]},{code:"string"}]},time:{type:[{code:"dateTime"}]},text:{min:1,type:[{code:"markdown"}]}}},Attachment:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},contentType:{type:[{code:"code"}]},language:{type:[{code:"code"}]},data:{type:[{code:"base64Binary"}]},url:{type:[{code:"url"}]},size:{type:[{code:"unsignedInt"}]},hash:{type:[{code:"base64Binary"}]},title:{type:[{code:"string"}]},creation:{type:[{code:"dateTime"}]}}},CodeableConcept:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},coding:{max:****************,type:[{code:"Coding"}]},text:{type:[{code:"string"}]}}},Coding:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},system:{type:[{code:"uri"}]},version:{type:[{code:"string"}]},code:{type:[{code:"code"}]},display:{type:[{code:"string"}]},userSelected:{type:[{code:"boolean"}]}}},ContactDetail:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},name:{type:[{code:"string"}]},telecom:{max:****************,type:[{code:"ContactPoint"}]}}},ContactPoint:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},system:{type:[{code:"code"}]},value:{type:[{code:"string"}]},use:{type:[{code:"code"}]},rank:{type:[{code:"positiveInt"}]},period:{type:[{code:"Period"}]}}},Contributor:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},type:{min:1,type:[{code:"code"}]},name:{min:1,type:[{code:"string"}]},contact:{max:****************,type:[{code:"ContactDetail"}]}}},Count:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},value:{type:[{code:"decimal"}]},comparator:{type:[{code:"code"}]},unit:{type:[{code:"string"}]},system:{type:[{code:"uri"}]},code:{type:[{code:"code"}]}}},DataRequirement:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},type:{min:1,type:[{code:"code"}]},profile:{max:****************,type:[{code:"canonical",targetProfile:["http://hl7.org/fhir/StructureDefinition/StructureDefinition"]}]},"subject[x]":{type:[{code:"CodeableConcept"},{code:"Reference",targetProfile:["http://hl7.org/fhir/StructureDefinition/Group"]}]},mustSupport:{max:****************,type:[{code:"string"}]},codeFilter:{max:****************,type:[{code:"DataRequirementCodeFilter"}]},dateFilter:{max:****************,type:[{code:"DataRequirementDateFilter"}]},limit:{type:[{code:"positiveInt"}]},sort:{max:****************,type:[{code:"DataRequirementSort"}]}}},DataRequirementCodeFilter:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},path:{type:[{code:"string"}]},searchParam:{type:[{code:"string"}]},valueSet:{type:[{code:"canonical",targetProfile:["http://hl7.org/fhir/StructureDefinition/ValueSet"]}]},code:{max:****************,type:[{code:"Coding"}]}}},DataRequirementDateFilter:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},path:{type:[{code:"string"}]},searchParam:{type:[{code:"string"}]},"value[x]":{type:[{code:"dateTime"},{code:"Period"},{code:"Duration"}]}}},DataRequirementSort:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},path:{min:1,type:[{code:"string"}]},direction:{min:1,type:[{code:"code"}]}}},Distance:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},value:{type:[{code:"decimal"}]},comparator:{type:[{code:"code"}]},unit:{type:[{code:"string"}]},system:{type:[{code:"uri"}]},code:{type:[{code:"code"}]}}},Dosage:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},modifierExtension:{max:****************,type:[{code:"Extension"}]},sequence:{type:[{code:"integer"}]},text:{type:[{code:"string"}]},additionalInstruction:{max:****************,type:[{code:"CodeableConcept"}]},patientInstruction:{type:[{code:"string"}]},timing:{type:[{code:"Timing"}]},"asNeeded[x]":{type:[{code:"boolean"},{code:"CodeableConcept"}]},site:{type:[{code:"CodeableConcept"}]},route:{type:[{code:"CodeableConcept"}]},method:{type:[{code:"CodeableConcept"}]},doseAndRate:{max:****************,type:[{code:"DosageDoseAndRate"}]},maxDosePerPeriod:{type:[{code:"Ratio"}]},maxDosePerAdministration:{type:[{code:"Quantity",profile:["http://hl7.org/fhir/StructureDefinition/SimpleQuantity"]}]},maxDosePerLifetime:{type:[{code:"Quantity",profile:["http://hl7.org/fhir/StructureDefinition/SimpleQuantity"]}]}}},DosageDoseAndRate:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},type:{type:[{code:"CodeableConcept"}]},"dose[x]":{type:[{code:"Range"},{code:"Quantity",profile:["http://hl7.org/fhir/StructureDefinition/SimpleQuantity"]}]},"rate[x]":{type:[{code:"Ratio"},{code:"Range"},{code:"Quantity",profile:["http://hl7.org/fhir/StructureDefinition/SimpleQuantity"]}]}}},Duration:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},value:{type:[{code:"decimal"}]},comparator:{type:[{code:"code"}]},unit:{type:[{code:"string"}]},system:{type:[{code:"uri"}]},code:{type:[{code:"code"}]}}},ElementDefinition:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},modifierExtension:{max:****************,type:[{code:"Extension"}]},path:{min:1,type:[{code:"string"}]},representation:{max:****************,type:[{code:"code"}]},sliceName:{type:[{code:"string"}]},sliceIsConstraining:{type:[{code:"boolean"}]},label:{type:[{code:"string"}]},code:{max:****************,type:[{code:"Coding"}]},slicing:{type:[{code:"ElementDefinitionSlicing"}]},short:{type:[{code:"string"}]},definition:{type:[{code:"markdown"}]},comment:{type:[{code:"markdown"}]},requirements:{type:[{code:"markdown"}]},alias:{max:****************,type:[{code:"string"}]},min:{type:[{code:"unsignedInt"}]},max:{type:[{code:"string"}]},base:{type:[{code:"ElementDefinitionBase"}]},contentReference:{type:[{code:"uri"}]},type:{max:****************,type:[{code:"ElementDefinitionType"}]},"defaultValue[x]":{type:[{code:"base64Binary"},{code:"boolean"},{code:"canonical"},{code:"code"},{code:"date"},{code:"dateTime"},{code:"decimal"},{code:"id"},{code:"instant"},{code:"integer"},{code:"markdown"},{code:"oid"},{code:"positiveInt"},{code:"string"},{code:"time"},{code:"unsignedInt"},{code:"uri"},{code:"url"},{code:"uuid"},{code:"Address"},{code:"Age"},{code:"Annotation"},{code:"Attachment"},{code:"CodeableConcept"},{code:"Coding"},{code:"ContactPoint"},{code:"Count"},{code:"Distance"},{code:"Duration"},{code:"HumanName"},{code:"Identifier"},{code:"Money"},{code:"Period"},{code:"Quantity"},{code:"Range"},{code:"Ratio"},{code:"Reference"},{code:"SampledData"},{code:"Signature"},{code:"Timing"},{code:"ContactDetail"},{code:"Contributor"},{code:"DataRequirement"},{code:"Expression"},{code:"ParameterDefinition"},{code:"RelatedArtifact"},{code:"TriggerDefinition"},{code:"UsageContext"},{code:"Dosage"},{code:"Meta"}]},meaningWhenMissing:{type:[{code:"markdown"}]},orderMeaning:{type:[{code:"string"}]},"fixed[x]":{type:[{code:"base64Binary"},{code:"boolean"},{code:"canonical"},{code:"code"},{code:"date"},{code:"dateTime"},{code:"decimal"},{code:"id"},{code:"instant"},{code:"integer"},{code:"markdown"},{code:"oid"},{code:"positiveInt"},{code:"string"},{code:"time"},{code:"unsignedInt"},{code:"uri"},{code:"url"},{code:"uuid"},{code:"Address"},{code:"Age"},{code:"Annotation"},{code:"Attachment"},{code:"CodeableConcept"},{code:"Coding"},{code:"ContactPoint"},{code:"Count"},{code:"Distance"},{code:"Duration"},{code:"HumanName"},{code:"Identifier"},{code:"Money"},{code:"Period"},{code:"Quantity"},{code:"Range"},{code:"Ratio"},{code:"Reference"},{code:"SampledData"},{code:"Signature"},{code:"Timing"},{code:"ContactDetail"},{code:"Contributor"},{code:"DataRequirement"},{code:"Expression"},{code:"ParameterDefinition"},{code:"RelatedArtifact"},{code:"TriggerDefinition"},{code:"UsageContext"},{code:"Dosage"},{code:"Meta"}]},"pattern[x]":{type:[{code:"base64Binary"},{code:"boolean"},{code:"canonical"},{code:"code"},{code:"date"},{code:"dateTime"},{code:"decimal"},{code:"id"},{code:"instant"},{code:"integer"},{code:"markdown"},{code:"oid"},{code:"positiveInt"},{code:"string"},{code:"time"},{code:"unsignedInt"},{code:"uri"},{code:"url"},{code:"uuid"},{code:"Address"},{code:"Age"},{code:"Annotation"},{code:"Attachment"},{code:"CodeableConcept"},{code:"Coding"},{code:"ContactPoint"},{code:"Count"},{code:"Distance"},{code:"Duration"},{code:"HumanName"},{code:"Identifier"},{code:"Money"},{code:"Period"},{code:"Quantity"},{code:"Range"},{code:"Ratio"},{code:"Reference"},{code:"SampledData"},{code:"Signature"},{code:"Timing"},{code:"ContactDetail"},{code:"Contributor"},{code:"DataRequirement"},{code:"Expression"},{code:"ParameterDefinition"},{code:"RelatedArtifact"},{code:"TriggerDefinition"},{code:"UsageContext"},{code:"Dosage"},{code:"Meta"}]},example:{max:****************,type:[{code:"ElementDefinitionExample"}]},"minValue[x]":{type:[{code:"date"},{code:"dateTime"},{code:"instant"},{code:"time"},{code:"decimal"},{code:"integer"},{code:"positiveInt"},{code:"unsignedInt"},{code:"Quantity"}]},"maxValue[x]":{type:[{code:"date"},{code:"dateTime"},{code:"instant"},{code:"time"},{code:"decimal"},{code:"integer"},{code:"positiveInt"},{code:"unsignedInt"},{code:"Quantity"}]},maxLength:{type:[{code:"integer"}]},condition:{max:****************,type:[{code:"id"}]},constraint:{max:****************,type:[{code:"ElementDefinitionConstraint"}]},mustSupport:{type:[{code:"boolean"}]},isModifier:{type:[{code:"boolean"}]},isModifierReason:{type:[{code:"string"}]},isSummary:{type:[{code:"boolean"}]},binding:{type:[{code:"ElementDefinitionBinding"}]},mapping:{max:****************,type:[{code:"ElementDefinitionMapping"}]}}},ElementDefinitionSlicingDiscriminator:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},type:{min:1,type:[{code:"code"}]},path:{min:1,type:[{code:"string"}]}}},ElementDefinitionSlicing:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},discriminator:{max:****************,type:[{code:"ElementDefinitionSlicingDiscriminator"}]},description:{type:[{code:"string"}]},ordered:{type:[{code:"boolean"}]},rules:{min:1,type:[{code:"code"}]}}},ElementDefinitionBase:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},path:{min:1,type:[{code:"string"}]},min:{min:1,type:[{code:"unsignedInt"}]},max:{min:1,type:[{code:"string"}]}}},ElementDefinitionType:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},code:{min:1,type:[{code:"uri"}]},profile:{max:****************,type:[{code:"canonical",targetProfile:["http://hl7.org/fhir/StructureDefinition/StructureDefinition","http://hl7.org/fhir/StructureDefinition/ImplementationGuide"]}]},targetProfile:{max:****************,type:[{code:"canonical",targetProfile:["http://hl7.org/fhir/StructureDefinition/StructureDefinition","http://hl7.org/fhir/StructureDefinition/ImplementationGuide"]}]},aggregation:{max:****************,type:[{code:"code"}]},versioning:{type:[{code:"code"}]}}},ElementDefinitionExample:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},label:{min:1,type:[{code:"string"}]},"value[x]":{min:1,type:[{code:"base64Binary"},{code:"boolean"},{code:"canonical"},{code:"code"},{code:"date"},{code:"dateTime"},{code:"decimal"},{code:"id"},{code:"instant"},{code:"integer"},{code:"markdown"},{code:"oid"},{code:"positiveInt"},{code:"string"},{code:"time"},{code:"unsignedInt"},{code:"uri"},{code:"url"},{code:"uuid"},{code:"Address"},{code:"Age"},{code:"Annotation"},{code:"Attachment"},{code:"CodeableConcept"},{code:"Coding"},{code:"ContactPoint"},{code:"Count"},{code:"Distance"},{code:"Duration"},{code:"HumanName"},{code:"Identifier"},{code:"Money"},{code:"Period"},{code:"Quantity"},{code:"Range"},{code:"Ratio"},{code:"Reference"},{code:"SampledData"},{code:"Signature"},{code:"Timing"},{code:"ContactDetail"},{code:"Contributor"},{code:"DataRequirement"},{code:"Expression"},{code:"ParameterDefinition"},{code:"RelatedArtifact"},{code:"TriggerDefinition"},{code:"UsageContext"},{code:"Dosage"},{code:"Meta"}]}}},ElementDefinitionConstraint:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},key:{min:1,type:[{code:"id"}]},requirements:{type:[{code:"string"}]},severity:{min:1,type:[{code:"code"}]},human:{min:1,type:[{code:"string"}]},expression:{type:[{code:"string"}]},xpath:{type:[{code:"string"}]},source:{type:[{code:"canonical",targetProfile:["http://hl7.org/fhir/StructureDefinition/StructureDefinition"]}]}}},ElementDefinitionBinding:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},strength:{min:1,type:[{code:"code"}]},description:{type:[{code:"string"}]},valueSet:{type:[{code:"canonical",targetProfile:["http://hl7.org/fhir/StructureDefinition/ValueSet"]}]}}},ElementDefinitionMapping:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},identity:{min:1,type:[{code:"id"}]},language:{type:[{code:"code"}]},map:{min:1,type:[{code:"string"}]},comment:{type:[{code:"string"}]}}},Expression:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},description:{type:[{code:"string"}]},name:{type:[{code:"id"}]},language:{min:1,type:[{code:"code"}]},expression:{type:[{code:"string"}]},reference:{type:[{code:"uri"}]}}},Extension:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},url:{min:1,type:[{code:"uri"}]},"value[x]":{type:[{code:"base64Binary"},{code:"boolean"},{code:"canonical"},{code:"code"},{code:"date"},{code:"dateTime"},{code:"decimal"},{code:"id"},{code:"instant"},{code:"integer"},{code:"markdown"},{code:"oid"},{code:"positiveInt"},{code:"string"},{code:"time"},{code:"unsignedInt"},{code:"uri"},{code:"url"},{code:"uuid"},{code:"Address"},{code:"Age"},{code:"Annotation"},{code:"Attachment"},{code:"CodeableConcept"},{code:"Coding"},{code:"ContactPoint"},{code:"Count"},{code:"Distance"},{code:"Duration"},{code:"HumanName"},{code:"Identifier"},{code:"Money"},{code:"Period"},{code:"Quantity"},{code:"Range"},{code:"Ratio"},{code:"Reference"},{code:"SampledData"},{code:"Signature"},{code:"Timing"},{code:"ContactDetail"},{code:"Contributor"},{code:"DataRequirement"},{code:"Expression"},{code:"ParameterDefinition"},{code:"RelatedArtifact"},{code:"TriggerDefinition"},{code:"UsageContext"},{code:"Dosage"},{code:"Meta"}]}}},HumanName:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},use:{type:[{code:"code"}]},text:{type:[{code:"string"}]},family:{type:[{code:"string"}]},given:{max:****************,type:[{code:"string"}]},prefix:{max:****************,type:[{code:"string"}]},suffix:{max:****************,type:[{code:"string"}]},period:{type:[{code:"Period"}]}}},Identifier:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},use:{type:[{code:"code"}]},type:{type:[{code:"CodeableConcept"}]},system:{type:[{code:"uri"}]},value:{type:[{code:"string"}]},period:{type:[{code:"Period"}]},assigner:{type:[{code:"Reference",targetProfile:["http://hl7.org/fhir/StructureDefinition/Organization"]}]}}},MarketingStatus:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},modifierExtension:{max:****************,type:[{code:"Extension"}]},country:{min:1,type:[{code:"CodeableConcept"}]},jurisdiction:{type:[{code:"CodeableConcept"}]},status:{min:1,type:[{code:"CodeableConcept"}]},dateRange:{min:1,type:[{code:"Period"}]},restoreDate:{type:[{code:"dateTime"}]}}},Meta:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},versionId:{type:[{code:"id"}]},lastUpdated:{type:[{code:"instant"}]},source:{type:[{code:"uri"}]},profile:{max:****************,type:[{code:"canonical",targetProfile:["http://hl7.org/fhir/StructureDefinition/StructureDefinition"]}]},security:{max:****************,type:[{code:"Coding"}]},tag:{max:****************,type:[{code:"Coding"}]},project:{type:[{code:"uri"}]},author:{type:[{code:"Reference"}]},onBehalfOf:{type:[{code:"Reference"}]},account:{type:[{code:"Reference"}]},accounts:{max:****************,type:[{code:"Reference"}]},compartment:{max:****************,type:[{code:"Reference"}]}}},Money:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},value:{type:[{code:"decimal"}]},currency:{type:[{code:"code"}]}}},Narrative:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},status:{min:1,type:[{code:"code"}]},div:{min:1,type:[{code:"xhtml"}]}}},ParameterDefinition:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},name:{type:[{code:"code"}]},use:{min:1,type:[{code:"code"}]},min:{type:[{code:"integer"}]},max:{type:[{code:"string"}]},documentation:{type:[{code:"string"}]},type:{min:1,type:[{code:"code"}]},profile:{type:[{code:"canonical",targetProfile:["http://hl7.org/fhir/StructureDefinition/StructureDefinition"]}]}}},Period:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},start:{type:[{code:"dateTime"}]},end:{type:[{code:"dateTime"}]}}},Population:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},modifierExtension:{max:****************,type:[{code:"Extension"}]},"age[x]":{type:[{code:"Range"},{code:"CodeableConcept"}]},gender:{type:[{code:"CodeableConcept"}]},race:{type:[{code:"CodeableConcept"}]},physiologicalCondition:{type:[{code:"CodeableConcept"}]}}},ProdCharacteristic:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},modifierExtension:{max:****************,type:[{code:"Extension"}]},height:{type:[{code:"Quantity"}]},width:{type:[{code:"Quantity"}]},depth:{type:[{code:"Quantity"}]},weight:{type:[{code:"Quantity"}]},nominalVolume:{type:[{code:"Quantity"}]},externalDiameter:{type:[{code:"Quantity"}]},shape:{type:[{code:"string"}]},color:{max:****************,type:[{code:"string"}]},imprint:{max:****************,type:[{code:"string"}]},image:{max:****************,type:[{code:"Attachment"}]},scoring:{type:[{code:"CodeableConcept"}]}}},ProductShelfLife:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},modifierExtension:{max:****************,type:[{code:"Extension"}]},identifier:{type:[{code:"Identifier"}]},type:{min:1,type:[{code:"CodeableConcept"}]},period:{min:1,type:[{code:"Quantity"}]},specialPrecautionsForStorage:{max:****************,type:[{code:"CodeableConcept"}]}}},Quantity:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},value:{type:[{code:"decimal"}]},comparator:{type:[{code:"code"}]},unit:{type:[{code:"string"}]},system:{type:[{code:"uri"}]},code:{type:[{code:"code"}]}}},Range:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},low:{type:[{code:"Quantity",profile:["http://hl7.org/fhir/StructureDefinition/SimpleQuantity"]}]},high:{type:[{code:"Quantity",profile:["http://hl7.org/fhir/StructureDefinition/SimpleQuantity"]}]}}},Ratio:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},numerator:{type:[{code:"Quantity"}]},denominator:{type:[{code:"Quantity"}]}}},Reference:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},reference:{type:[{code:"string"}]},type:{type:[{code:"uri"}]},identifier:{type:[{code:"Identifier"}]},display:{type:[{code:"string"}]}}},RelatedArtifact:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},type:{min:1,type:[{code:"code"}]},label:{type:[{code:"string"}]},display:{type:[{code:"string"}]},citation:{type:[{code:"markdown"}]},url:{type:[{code:"url"}]},document:{type:[{code:"Attachment"}]},resource:{type:[{code:"canonical",targetProfile:["http://hl7.org/fhir/StructureDefinition/Resource"]}]}}},SampledData:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},origin:{min:1,type:[{code:"Quantity",profile:["http://hl7.org/fhir/StructureDefinition/SimpleQuantity"]}]},period:{min:1,type:[{code:"decimal"}]},factor:{type:[{code:"decimal"}]},lowerLimit:{type:[{code:"decimal"}]},upperLimit:{type:[{code:"decimal"}]},dimensions:{min:1,type:[{code:"positiveInt"}]},data:{type:[{code:"string"}]}}},Signature:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},type:{min:1,max:****************,type:[{code:"Coding"}]},when:{min:1,type:[{code:"instant"}]},who:{min:1,type:[{code:"Reference",targetProfile:["http://hl7.org/fhir/StructureDefinition/Practitioner","http://hl7.org/fhir/StructureDefinition/PractitionerRole","http://hl7.org/fhir/StructureDefinition/RelatedPerson","http://hl7.org/fhir/StructureDefinition/Patient","http://hl7.org/fhir/StructureDefinition/Device","http://hl7.org/fhir/StructureDefinition/Organization"]}]},onBehalfOf:{type:[{code:"Reference",targetProfile:["http://hl7.org/fhir/StructureDefinition/Practitioner","http://hl7.org/fhir/StructureDefinition/PractitionerRole","http://hl7.org/fhir/StructureDefinition/RelatedPerson","http://hl7.org/fhir/StructureDefinition/Patient","http://hl7.org/fhir/StructureDefinition/Device","http://hl7.org/fhir/StructureDefinition/Organization"]}]},targetFormat:{type:[{code:"code"}]},sigFormat:{type:[{code:"code"}]},data:{type:[{code:"base64Binary"}]}}},SubstanceAmount:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},modifierExtension:{max:****************,type:[{code:"Extension"}]},"amount[x]":{type:[{code:"Quantity"},{code:"Range"},{code:"string"}]},amountType:{type:[{code:"CodeableConcept"}]},amountText:{type:[{code:"string"}]},referenceRange:{type:[{code:"SubstanceAmountReferenceRange"}]}}},SubstanceAmountReferenceRange:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},lowLimit:{type:[{code:"Quantity"}]},highLimit:{type:[{code:"Quantity"}]}}},Timing:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},modifierExtension:{max:****************,type:[{code:"Extension"}]},event:{max:****************,type:[{code:"dateTime"}]},repeat:{type:[{code:"TimingRepeat"}]},code:{type:[{code:"CodeableConcept"}]}}},TimingRepeat:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},"bounds[x]":{type:[{code:"Duration"},{code:"Range"},{code:"Period"}]},count:{type:[{code:"positiveInt"}]},countMax:{type:[{code:"positiveInt"}]},duration:{type:[{code:"decimal"}]},durationMax:{type:[{code:"decimal"}]},durationUnit:{type:[{code:"code"}]},frequency:{type:[{code:"positiveInt"}]},frequencyMax:{type:[{code:"positiveInt"}]},period:{type:[{code:"decimal"}]},periodMax:{type:[{code:"decimal"}]},periodUnit:{type:[{code:"code"}]},dayOfWeek:{max:****************,type:[{code:"code"}]},timeOfDay:{max:****************,type:[{code:"time"}]},when:{max:****************,type:[{code:"code"}]},offset:{type:[{code:"unsignedInt"}]}}},TriggerDefinition:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},type:{min:1,type:[{code:"code"}]},name:{type:[{code:"string"}]},"timing[x]":{type:[{code:"Timing"},{code:"Reference",targetProfile:["http://hl7.org/fhir/StructureDefinition/Schedule"]},{code:"date"},{code:"dateTime"}]},data:{max:****************,type:[{code:"DataRequirement"}]},condition:{type:[{code:"Expression"}]}}},UsageContext:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},code:{min:1,type:[{code:"Coding"}]},"value[x]":{min:1,type:[{code:"CodeableConcept"},{code:"Quantity"},{code:"Range"},{code:"Reference",targetProfile:["http://hl7.org/fhir/StructureDefinition/PlanDefinition","http://hl7.org/fhir/StructureDefinition/ResearchStudy","http://hl7.org/fhir/StructureDefinition/InsurancePlan","http://hl7.org/fhir/StructureDefinition/HealthcareService","http://hl7.org/fhir/StructureDefinition/Group","http://hl7.org/fhir/StructureDefinition/Location","http://hl7.org/fhir/StructureDefinition/Organization"]}]}}},MoneyQuantity:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},value:{type:[{code:"decimal"}]},comparator:{type:[{code:"code"}]},unit:{type:[{code:"string"}]},system:{type:[{code:"uri"}]},code:{type:[{code:"code"}]}}},SimpleQuantity:{elements:{id:{type:[{code:"string"}]},extension:{max:****************,type:[{code:"Extension"}]},value:{type:[{code:"decimal"}]},comparator:{max:0,type:[{code:"code"}]},unit:{type:[{code:"string"}]},system:{type:[{code:"uri"}]},code:{type:[{code:"code"}]}}},IdentityProvider:{elements:{authorizeUrl:{min:1,type:[{code:"string"}]},tokenUrl:{min:1,type:[{code:"string"}]},tokenAuthMethod:{type:[{code:"code"}]},userInfoUrl:{min:1,type:[{code:"string"}]},clientId:{min:1,type:[{code:"string"}]},clientSecret:{min:1,type:[{code:"string"}]},usePkce:{type:[{code:"boolean"}]},useSubject:{type:[{code:"boolean"}]}}}};function zt(r){return new nn(r).parse()}var Ce=tn(yi),on=Object.create(null),gi=Object.create(null),Us={"http://hl7.org/fhir/StructureDefinition/MoneyQuantity":"MoneyQuantity","http://hl7.org/fhir/StructureDefinition/SimpleQuantity":"SimpleQuantity","http://hl7.org/fhir/uv/sql-on-fhir/StructureDefinition/ViewDefinition":"ViewDefinition"};function Si(r){let e;return e=gi[r],e||(e=gi[r]=Object.create(null)),e}function Jt(r){let t=(Array.isArray(r)?r:r.entry?.map(n=>n.resource)??[]).filter(n=>n?.resourceType==="StructureDefinition");pn(t);for(let n of t)Kt(n)}function Kt(r){if(!r?.name)throw new Error("Failed loading StructureDefinition from bundle");if(r.resourceType!=="StructureDefinition")return;let e=zt(r),t=Us[r.url],n,i;t?(n=Ce,i=t):r.url===`http://hl7.org/fhir/StructureDefinition/${r.type}`||r.url===`https://medplum.com/fhir/StructureDefinition/${r.type}`||r.type?.startsWith("http://")||r.type?.startsWith("https://")?(n=Ce,i=r.type):(n=Si(r.url),i=r.type),n[i]=e;for(let o of e.innerTypes)o.parentType=e,n[o.name]=o;on[r.url]=e}function sn(){return Ce}function an(r){return!!Ce[r]}function Ne(r,e){if(e){let t=Si(e)[r];if(t)return t}return Ce[r]}function te(r,e){let t=Ne(r,e);if(!t)throw new d(b("Unknown data type: "+r));return t}function cn(r){let e=Ce[r];return e&&Xt(e)}function un(r){return!!on[r]}function ln(r){return on[r]}var nn=class{constructor(e){if(!e.snapshot?.element||e.snapshot.element.length===0)throw new Error(`No snapshot defined for StructureDefinition '${e.name}'`);this.root=e.snapshot.element[0],this.elements=e.snapshot.element.slice(1),this.elementIndex=Object.create(null),this.index=0,this.resourceSchema={name:e.name,path:this.root.path,title:e.title,type:e.type,url:e.url,version:e.version,kind:e.kind,description:$s(e),elements:{},constraints:this.parseElementDefinition(this.root).constraints,innerTypes:[],summaryProperties:new Set,mandatoryProperties:new Set},this.innerTypes=[]}parse(){let e=this.next();for(;e;){if(e.sliceName)this.parseSliceStart(e);else if(e.id?.includes(":")){if(this.slicingContext?.current){let t=rn(e,this.slicingContext.path);this.slicingContext.current.elements[t]=this.parseElementDefinition(e)}}else{let t=this.parseElementDefinition(e);this.checkFieldEnter(e,t);let n=this.backboneContext;for(;n;){if(e.path?.startsWith(n.path+".")){n.type.elements[rn(e,n.path)]=t;break}n=n.parent}if(!n){let i=rn(e,this.root.path);e.isSummary&&this.resourceSchema.summaryProperties?.add(i.replace("[x]","")),t.min>0&&this.resourceSchema.mandatoryProperties?.add(i.replace("[x]","")),this.resourceSchema.elements[i]=t}this.checkFieldExit(e)}e=this.next()}return this.checkFieldExit(),this.innerTypes.length>0&&(this.resourceSchema.innerTypes=this.innerTypes),this.resourceSchema}checkFieldEnter(e,t){this.isInnerType(e)&&this.enterInnerType(e),this.slicingContext&&!Fe(this.slicingContext.path,e?.path)&&(this.slicingContext=void 0),e.slicing&&!this.slicingContext&&this.enterSlice(e,t)}enterInnerType(e){for(;this.backboneContext&&!Fe(this.backboneContext?.path,e.path);)this.innerTypes.push(this.backboneContext.type),this.backboneContext=this.backboneContext.parent;let t=Qt(e);this.backboneContext={type:{name:t,type:t,path:e.path,title:e.label,description:e.definition,elements:{},constraints:this.parseElementDefinition(e).constraints,innerTypes:[]},path:e.path,parent:Fe(this.backboneContext?.path,e.path)?this.backboneContext:this.backboneContext?.parent}}enterSlice(e,t){js(e)&&!this.peek()?.sliceName||(t.slicing={discriminator:(e.slicing?.discriminator??[]).map(n=>{if(n.type!=="value"&&n.type!=="pattern"&&n.type!=="type")throw new Error(`Unsupported slicing discriminator type: ${n.type}`);return{path:n.path,type:n.type}}),slices:[],ordered:e.slicing?.ordered??!1,rule:e.slicing?.rules},this.slicingContext={field:t.slicing,path:e.path??""})}checkFieldExit(e=void 0){if(this.backboneContext&&!Fe(this.backboneContext.path,e?.path))if(this.backboneContext.parent)do this.innerTypes.push(this.backboneContext.type),this.backboneContext=this.backboneContext.parent;while(this.backboneContext&&!Fe(this.backboneContext.path,e?.path));else this.innerTypes.push(this.backboneContext.type),this.backboneContext=void 0}next(){let e=this.peek();if(e)return this.index++,e}peek(){let e=this.elements[this.index];if(e){if(this.elementIndex[e.path??""]=e,e.contentReference){let t=e.contentReference.slice(e.contentReference.indexOf("#")+1),n=this.elementIndex[t];return n?{...n,id:e.id,path:e.path,min:e.min??n.min,max:e.max??n.max,base:{path:n.base?.path??t,min:e.base?.min??n.base?.min??n.min,max:e.base?.max??n.base?.max??n.max},contentReference:e.contentReference,definition:e.definition}:void 0}return e}}isInnerType(e){let t=this.peek();return!!(Fe(e?.path,t?.path)&&e.type?.some(n=>["BackboneElement","Element"].includes(n.code)))}parseSliceStart(e){if(!this.slicingContext)throw new Error(`Invalid slice start before discriminator: ${e.sliceName} (${e.id})`);this.slicingContext.current={...this.parseElementDefinition(e),name:e.sliceName??"",definition:e.definition,elements:{}},this.slicingContext.field.slices.push(this.slicingContext.current)}parseElementDefinitionType(e){return(e.type??[]).map(t=>{let n;return(t.code==="BackboneElement"||t.code==="Element")&&(n=Qt(e)),n||(n=re(t,"http://hl7.org/fhir/StructureDefinition/structuredefinition-fhir-type")?.valueUrl),n||(n=t.code??""),{code:n,targetProfile:t.targetProfile,profile:t.profile}})}parseElementDefinition(e){let t=vi(e.max),n=e.base?.max?vi(e.base.max):t,i={type:"ElementDefinition",value:e};return{description:e.definition||"",path:e.path||e.base?.path||"",min:e.min??0,max:t,isArray:n>1,constraints:(e.constraint??[]).map(o=>({key:o.key??"",severity:o.severity??"error",expression:o.expression??"",description:o.human??""})),type:this.parseElementDefinitionType(e),fixed:Ti(C(i,"fixed[x]")),pattern:Ti(C(i,"pattern[x]")),binding:e.binding}}};function Bs(r,e){if(!r)return;let t=[];for(let n of e){t.push("_"+n);let i=Ce[r.resourceType].elements[n+"[x]"];i&&t.push(...i.type.map(o=>n+O(o.code)))}for(let n of Object.getOwnPropertyNames(r))!e.includes(n)&&!t.includes(n)&&!Ws.includes(n)&&Object.defineProperty(r,n,{enumerable:!1,writable:!1,value:void 0});return r.meta={...r.meta,tag:r.meta?.tag?r.meta.tag.concat(xi):[xi]},r}var xi={system:"http://hl7.org/fhir/v3/ObservationValue",code:"SUBSETTED"},Ws=["resourceType","id","meta"];function vi(r){return r==="*"?Number.POSITIVE_INFINITY:Number.parseInt(r,10)}function rn(r,e=""){return qs(r.path,e)}function qs(r,e){return r?e&&r.startsWith(e)?r.substring(e.length+1):r:""}function Fe(r,e){return!r||!e?!1:e.startsWith(r+".")||e===r}function Ti(r){return Array.isArray(r)&&r.length>0?r[0]:S(r)?void 0:r}function js(r){let e=r.slicing?.discriminator;return!!(r.type?.some(t=>t.code==="Extension")&&e?.length===1&&e[0].type==="value"&&e[0].path==="url")}function $s(r){let e=r.description;return e?.startsWith(`Base StructureDefinition for ${r.name} Type: `)&&(e=e.substring(`Base StructureDefinition for ${r.name} Type: `.length)),e}function Yt(r,e,t){new dn(r,e,t).crawl()}function Gs(r,e,t){return new fn(r,e,t).crawl()}var dn=class{constructor(e,t,n){this.root=e,this.visitor=t,this.schema=n?.schema??te(e.type),this.initialPath=n?.initialPath??this.schema.path,this.excludeMissingProperties=n?.skipMissingProperties}crawl(){this.crawlObject({...this.root,path:this.initialPath},this.schema,this.initialPath)}crawlObject(e,t,n){let i=A(e.value);if(i&&this.visitor.onEnterResource&&this.visitor.onEnterResource(n,e,t),this.visitor.onEnterObject&&this.visitor.onEnterObject(n,e,t),this.excludeMissingProperties)for(let o of Object.keys(e.value))this.crawlProperty(e,o,t,`${n}.${o}`);else for(let o of Object.keys(t.elements))this.crawlProperty(e,o,t,`${n}.${o}`);this.visitor.onExitObject&&this.visitor.onExitObject(n,e,t),i&&this.visitor.onExitResource&&this.visitor.onExitResource(n,e,t)}crawlProperty(e,t,n,i){let o=Pe(e,t,{withPath:!0});this.visitor.visitProperty&&this.visitor.visitProperty(e,t,i,o,n);for(let s of o)if(s)for(let a of Ue(s))this.crawlPropertyValue(a,i)}crawlPropertyValue(e,t){if(!tt(e.type)){let n=te(e.type);this.crawlObject(e,n,t)}}},fn=class{constructor(e,t,n){this.root=e,this.visitor=t,this.schema=n?.schema??te(e.type),this.initialPath=n?.initialPath??this.schema.path,this.excludeMissingProperties=n?.skipMissingProperties}async crawl(){return this.crawlObject({...this.root,path:this.initialPath},this.schema,this.initialPath)}async crawlObject(e,t,n){let i=A(e.value);if(i&&this.visitor.onEnterResource&&await this.visitor.onEnterResource(n,e,t),this.visitor.onEnterObject&&await this.visitor.onEnterObject(n,e,t),this.excludeMissingProperties&&e.value)for(let o of Object.keys(e.value))await this.crawlProperty(e,o,t,`${n}.${o}`);else for(let o of Object.keys(t.elements))await this.crawlProperty(e,o,t,`${n}.${o}`);this.visitor.onExitObject&&await this.visitor.onExitObject(n,e,t),i&&this.visitor.onExitResource&&await this.visitor.onExitResource(n,e,t)}async crawlProperty(e,t,n,i){let o=Pe(e,t,{withPath:!0});if(this.visitor.visitPropertyAsync)for(let s of o)await this.visitor.visitPropertyAsync(e,t,i,s,n);for(let s of o)if(s)for(let a of Ue(s))await this.crawlPropertyValue(a,i)}async crawlPropertyValue(e,t){if(!tt(e.type)){let n=te(e.type);await this.crawlObject(e,n,t)}}};function Pe(r,e,t){if(r===void 0)return[void 0];if(e==="$this")return[r];let n=t?.withPath?bi:C,[i,...o]=e.split("."),s=[n(r,i,t)];for(let a of o){let c=[];for(let u of s)if(Array.isArray(u))for(let p of u)c.push(n(p,a,t));else(t?.withPath&&u&&u.value!==void 0||!t?.withPath&&u!==void 0)&&c.push(n(u,a,t));s=c}return s}function bi(r,e,t){let n=r.path;return Hs(C(r,e,t),n,e)}function Hs(r,e,t){let n=e?e+".":"";return r===void 0?{type:"undefined",value:void 0,path:`${n}${t}`}:Array.isArray(r)?r.map((i,o)=>({...i,path:`${n}${t}[${o}]`})):{...r,path:`${n}${t}`}}var Zt={base64Binary:"string",boolean:"boolean",canonical:"string",code:"string",date:"string",dateTime:"string",decimal:"number",id:"string",instant:"string",integer:"number",integer64:"string",markdown:"string",oid:"string",positiveInt:"number",string:"string",time:"string",unsignedInt:"number",uri:"string",url:"string",uuid:"string",xhtml:"string","http://hl7.org/fhirpath/System.String":"string"},Qs=new Se(1e3);function tt(r){return r==="undefined"||r in Zt}var rt={base64Binary:/^([A-Za-z\d+/]{4})*([A-Za-z\d+/]{2}==|[A-Za-z\d+/]{3}=)?$/,canonical:/^\S*$/,code:/^[^\s]+( [^\s]+)*$/,date:/^(\d(\d(\d[1-9]|[1-9]0)|[1-9]00)|[1-9]000)(-(0[1-9]|1[0-2])(-(0[1-9]|[1-2]\d|3[0-1]))?)?$/,dateTime:/^(\d(\d(\d[1-9]|[1-9]0)|[1-9]00)|[1-9]000)(-(0[1-9]|1[0-2])(-(0[1-9]|[1-2]\d|3[0-1])(T([01]\d|2[0-3])(:[0-5]\d:([0-5]\d|60)(\.\d{1,9})?)?)?)?(Z|[+-]((0\d|1[0-3]):[0-5]\d|14:00)?)?)?$/,id:/^[A-Za-z0-9\-.]{1,64}$/,instant:/^(\d(\d(\d[1-9]|[1-9]0)|[1-9]00)|[1-9]000)-(0[1-9]|1[0-2])-(0[1-9]|[1-2]\d|3[0-1])T([01]\d|2[0-3]):[0-5]\d:([0-5]\d|60)(\.\d{1,9})?(Z|[+-]((0\d|1[0-3]):[0-5]\d|14:00))$/,markdown:/^[\r\n\t\u0020-\uFFFF]+$/,oid:/^urn:oid:[0-2](\.(0|[1-9]\d*))+$/,string:/^[\r\n\t\u0020-\uFFFF]+$/,time:/^([01]\d|2[0-3]):[0-5]\d:([0-5]\d|60)(\.\d{1,9})?$/,uri:/^\S*$/,url:/^\S*$/,uuid:/^urn:uuid:[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/,xhtml:/.*/},zs={"ele-1":!0,"dom-3":!0,"org-1":!0,"sdf-19":!0};function Js(r,e){if(!r.resourceType)throw new d(y("Missing resource type"));return new er(v(r),e).validate()}function Ks(r,e){return new er(r,e).validate()}var er=class{constructor(e,t){this.issues=[],this.root=e,this.currentResource=[],A(e.value)&&this.currentResource.push(e.value),t?.profile?this.schema=zt(t.profile):this.schema=te(e.type)}validate(){this.constraintsCheck({...this.root,path:this.schema.path},this.schema),hn(this.root.value,this.schema.path,this.issues),Yt(this.root,this,{schema:this.schema,initialPath:this.schema.path});let e=this.issues,t=!1;for(let n of e)n.severity==="error"&&(t=!0);if(t)throw new d({resourceType:"OperationOutcome",issue:e});return e}onExitObject(e,t,n){this.checkAdditionalProperties(t,n.elements,t.path)}onEnterResource(e,t){this.currentResource.push(t.value)}onExitResource(){this.currentResource.pop()}visitProperty(e,t,n,i,o){let s=o.elements[t];if(!s)throw new Error(`Missing element validation schema for ${t}`);for(let a of i){if(!this.checkPresence(a,s,n))return;let c;if(s.isArray){if(!Array.isArray(a)){this.issues.push(T(n,"Expected array of values for property"));return}c=a}else{if(Array.isArray(a)){this.issues.push(T(n,"Expected single value for property"));return}c=[a]}(c.length<s.min||c.length>s.max)&&this.issues.push(T(s.path,`Invalid number of values: expected ${s.min}..${Number.isFinite(s.max)?s.max:"*"}, but found ${c.length}`)),Zs(a,s)||this.issues.push(T(n,"Value did not match expected pattern"));let u=s.slicing?Object.fromEntries(s.slicing.slices.map(p=>[p.name,0])):void 0;for(let p of c){this.constraintsCheck(p,s),this.referenceTypeCheck(p,s),this.checkPropertyValue(p);let m=ea(p,s.slicing);m&&u&&(u[m]+=1)}this.validateSlices(s.slicing?.slices,u,n)}}checkPresence(e,t,n){return!Array.isArray(e)&&e.value===void 0?(t.min>0&&this.issues.push(T(e.path,"Missing required property")),!1):S(e)?(this.issues.push(T(n,"Invalid empty value")),!1):!0}checkPropertyValue(e){tt(e.type)?this.validatePrimitiveType(e):S(e.value)&&this.issues.push(T(e.path,"Invalid empty non-primitive value"))}validateSlices(e,t,n){if(!(!e||!t))for(let i of e){let o=t[i.name];(o<i.min||o>i.max)&&this.issues.push(T(n,`Incorrect number of values provided for slice '${i.name}': expected ${i.min}..${Number.isFinite(i.max)?i.max:"*"}, but found ${o}`))}}checkAdditionalProperties(e,t,n){let i=e.value;if(!i)return;let o={};for(let s of Object.keys(i)){if(s==="resourceType")continue;let a=Ys(e,s,t);if(a){let c,u;a.startsWith("_")?(c=a.slice(1),u=s.slice(1)):(c="_"+a,u="_"+s),c in o&&o[c]!==u&&this.issues.push(pe("warning","structure",`Type of primitive extension does not match the type of property "${a.startsWith("_")?a.slice(1):a}"`,a)),o[a]&&this.issues.push(pe("warning","structure",`Conflicting choice of type properties: "${s}", "${o[a]}"`,s)),o[a]=s;continue}!(s in t)&&!(s.startsWith("_")&&s.slice(1)in t)&&this.issues.push(T(`${n}.${s}`,`Invalid additional property "${s}"`))}}constraintsCheck(e,t){let n=t.constraints;if(n){for(let i of n)if(i.severity==="error"&&!(i.key in zs)&&!this.isExpressionTrue(i,e)){this.issues.push(Zr(e.path,i));return}}}referenceTypeCheck(e,t){if(e.type!=="Reference")return;let n=e.value;if(!z(n)||n.reference.startsWith("#"))return;let i=n.reference.includes("?")?n.reference.split("?")[0]:n.reference.split("/")[0];if(!i)return;let o=t.type.find(m=>m.code==="Reference")?.targetProfile;if(!o)return;let s=Wr+"/fhir/StructureDefinition/",a=s+"Resource",c=s+i,u="https://medplum.com/fhir/StructureDefinition/",p=u+i;for(let m of o)if(m===a||m===c||m===p||!m.startsWith(s)&&!m.startsWith(u))return;this.issues.push(pe("warning","structure",`Invalid reference: got "${i}", expected "${o.join('", "')}"`,e.path))}isExpressionTrue(e,t){let n={"%context":t,"%ucum":v(Br)};this.currentResource.length>0&&(n["%resource"]=v(this.currentResource[this.currentResource.length-1])),A(this.root.value)&&(n["%rootResource"]=this.root);try{let i=P(e.expression,[t],n,Qs);return i.length===1&&i[0].value===!0}catch(i){return this.issues.push(en(t.path,"Error evaluating invariant expression",i,{fhirpath:e.expression})),!1}}validatePrimitiveType(e){let[t,n]=ta(e),i=e.path;if(t){let{type:o,value:s}=t;if(!(o in Zt)){this.issues.push(T(i,`Invalid JSON type: ${o} is not a valid FHIR type`));return}let a=Zt[o];if(typeof s!==a){s!==null&&this.issues.push(T(i,`Invalid JSON type: expected ${a}, but got ${typeof s}`));return}a==="string"?this.validateString(s,o,i):a==="number"&&this.validateNumber(s,o,i)}n&&Yt(n,this,{schema:te("Element"),initialPath:i})}validateString(e,t,n){if(!e.trim()){this.issues.push(T(n,"String must contain non-whitespace content"));return}let i=rt[t];i&&!i.exec(e)&&this.issues.push(T(n,"Invalid "+t+" format"))}validateNumber(e,t,n){Number.isNaN(e)||!Number.isFinite(e)?this.issues.push(T(n,"Invalid numeric value")):Xs(t)&&!Number.isInteger(e)?this.issues.push(T(n,"Expected number to be an integer")):t===l.positiveInt&&e<=0?this.issues.push(T(n,"Expected number to be positive")):t===l.unsignedInt&&e<0&&this.issues.push(T(n,"Expected number to be non-negative"))}};function Xs(r){return r===l.integer||r===l.positiveInt||r===l.unsignedInt}function Ys(r,e,t){let n="";e.startsWith("_")&&(e=e.slice(1),n="_");let i=e.split(/(?=[A-Z])/g),o="";for(let s of i){o+=s;let a=o+"[x]";if(t[a])return C(r,o)?n+a:void 0}}function hn(r,e,t){for(let[n,i]of Object.entries(r)){let o=`${e}.${n}`,s=n.startsWith("_")?n.slice(1):`_${n}`;if(i===null)t.push(T(o,"Invalid null value"));else if(Array.isArray(i))for(let a=0;a<i.length;a++)i[a]===void 0?t.push(T(`${o}[${a}]`,"Invalid undefined value")):i[a]===null&&!r[s]?.[a]?t.push(T(`${o}[${a}]`,"Invalid null value")):i[a]&&hn(i[a],`${o}[${a}]`,t);else typeof i=="object"&&hn(i,o,t)}}function Zs(r,e){let t=Array.isArray(r)?r.map(n=>({type:n.type,value:n.value})):{type:r.type,value:r.value};return!(e.pattern&&!Be(t,e.pattern)||e.fixed&&!ne(t,e.fixed))}function tr(r,e,t,n){if(Array.isArray(r))return!1;let i;e.path==="$this"?i=t:i=(n??t.elements)[e.path];let o=t.type;switch(e.type){case"value":case"pattern":if(!r||!i)return!1;if(i.pattern)return Be(r,i.pattern);if(i.fixed)return ne(r,i.fixed);if(i.binding?.strength==="required"&&i.binding.valueSet)return!0;break;case"type":return!r||!o?.length?!1:o.some(s=>s.code===r.type)}return!1}function ea(r,e){if(e){for(let t of e.slices)if(e.discriminator.every(n=>Ue(Pe(r,n.path))?.some(i=>tr(i,n,t))))return t.name}}function ta(r){if(typeof r.value!="object"||!r.value)return[r,void 0];let e=r.value.valueOf();if(e===r.value)return[void 0,{type:"Element",value:r.value}];let t=new Set(Object.keys(e)),n=Object.entries(r.value).filter(([o,s])=>!t.has(o)),i=n.length>0?Object.fromEntries(n):void 0;return[{type:r.type,value:e},{type:"Element",value:i}]}function h(r){return[{type:l.boolean,value:r}]}function v(r){return r==null?{type:"undefined",value:void 0}:Number.isSafeInteger(r)?{type:l.integer,value:r}:typeof r=="number"?{type:l.decimal,value:r}:typeof r=="boolean"?{type:l.boolean,value:r}:typeof r=="string"?{type:l.string,value:r}:I(r)?{type:l.Quantity,value:r}:A(r)?{type:r.resourceType,value:r}:ir(r)?{type:l.CodeableConcept,value:r}:nr(r)?{type:l.Coding,value:r}:{type:l.BackboneElement,value:r}}function M(r){return r.length===0?!1:!!r[0].value}function $(r,e){if(r.length!==0){if(r.length===1&&(!e||r[0].type===e))return r[0];throw new Error(`Expected singleton of type ${e}, but found ${JSON.stringify(r)}`)}}function C(r,e,t){if(!r.value)return;let n=st(r.type,e,t?.profileUrl);return n?Ai(r,e,n):Oi(r,e)}function Ai(r,e,t){let n=r.value,i=t.type;if(!i||i.length===0)return;let o,s="undefined",a,c=t.path.lastIndexOf("."),u=t.path.substring(c+1);for(let p of i){let m=u.replace("[x]",O(p.code));if(o=n[m],a=n["_"+m],o!==void 0||a!==void 0){s=p.code;break}}if(a)if(Array.isArray(o)){o=o.slice();for(let p=0;p<Math.max(o.length,a.length);p++)o[p]=wi(o[p],a[p])}else o=wi(o,a);if(!S(o))return(s==="Element"||s==="BackboneElement")&&(s=t.type[0].code),Array.isArray(o)?o.map(p=>Ei(p,s)):Ei(o,s)}function Ei(r,e){return e==="Resource"&&A(r)&&(e=r.resourceType),{type:e,value:r}}function Oi(r,e){let t=r.value;if(!t||typeof t!="object")return;let n;if(e in t){let i=t[e];Array.isArray(i)?n=i.map(v):n=v(i)}else{let i=e.endsWith("[x]")?e.substring(0,e.length-3):e;for(let o of Object.values(l)){let s=i+O(o);if(s in t){let a=t[s];Array.isArray(a)?n=a.map(c=>({type:o,value:c})):n={type:o,value:a};break}}}if(Array.isArray(n)){if(n.length===0||S(n[0]))return}else if(S(n))return;return n}function nt(r){let e=[];for(let t of r){let n=!1;for(let i of e)if(M(We(t,i))){n=!0;break}n||e.push(t)}return e}function mn(r){return h(!M(r))}function yn(r,e){return r.length===0||e.length===0?[]:r.length!==e.length?h(!1):h(r.every((t,n)=>M(We(t,e[n]))))}function gn(r,e){return r.length===0||e.length===0?[]:r.length!==e.length?h(!0):h(r.some((t,n)=>!M(We(t,e[n]))))}function We(r,e){let t=r.value?.valueOf(),n=e.value?.valueOf();return typeof t=="number"&&typeof n=="number"?h(Math.abs(t-n)<1e-8):I(t)&&I(n)?h(vn(t,n)):h(typeof t=="object"&&typeof n=="object"?Tn(r,e):t===n)}function rr(r,e){return r.length===0&&e.length===0?h(!0):r.length!==e.length?h(!1):(r.sort(Ri),e.sort(Ri),h(r.every((t,n)=>M(Ii(t,e[n])))))}function Ii(r,e){let{type:t,value:n}=r,{type:i,value:o}=e,s=n?.valueOf(),a=o?.valueOf();return typeof s=="number"&&typeof a=="number"?h(Math.abs(s-a)<.01):I(s)&&I(a)?h(vn(s,a)):h(t==="Coding"&&i==="Coding"?typeof s!="object"||typeof a!="object"?!1:s.code===a.code&&s.system===a.system:typeof s=="object"&&typeof a=="object"?Tn({...s,id:void 0},{...a,id:void 0}):typeof s=="string"&&typeof a=="string"?s.toLowerCase()===a.toLowerCase():s===a)}function Ri(r,e){let t=r.value?.valueOf(),n=e.value?.valueOf();return typeof t=="number"&&typeof n=="number"?t-n:typeof t=="string"&&typeof n=="string"?t.localeCompare(n):0}function it(r,e){let{value:t}=r;if(t==null)return!1;let n=e;switch(n.startsWith("System.")&&(n=n.substring(7)),n.startsWith("FHIR.")&&(n=n.substring(5)),n){case"Boolean":return typeof t=="boolean";case"Decimal":case"Integer":return typeof t=="number";case"Date":return xn(t);case"DateTime":return we(t);case"Time":return typeof t=="string"&&!!/^T\d/.exec(t);case"Period":return ot(t);case"Quantity":return I(t);default:return r.type===n||typeof t=="object"&&t?.resourceType===n}}function xn(r){return typeof r=="string"&&!!rt.date.exec(r)}function we(r){return typeof r=="string"&&!!rt.dateTime.exec(r)}function ot(r){return!!(r&&typeof r=="object"&&("start"in r&&we(r.start)||"end"in r&&we(r.end)))}function ra(r){if(r){if(xn(r))return{start:Ci(r,"0000-00-00T00:00:00.000Z"),end:Ci(r,"xxxx-12-31T23:59:59.999Z")};if(we(r))return{start:r,end:r};if(ot(r))return r}}function Ci(r,e){return r+e.substring(r.length)}function I(r){return!!(r&&typeof r=="object"&&"value"in r&&typeof r.value=="number")}function vn(r,e){return Math.abs(r.value-e.value)<.01&&(r.unit===e.unit||r.code===e.code||r.unit===e.code||r.code===e.unit)}function Tn(r,e){let t=Object.keys(r),n=Object.keys(e);if(t.length!==n.length)return!1;for(let i of t){let o=r[i],s=e[i];if(Pi(o)&&Pi(s)){if(!Tn(o,s))return!1}else if(o!==s)return!1}return!0}function Pi(r){return r!==null&&typeof r=="object"}function wi(r,e){if(e){if(typeof e!="object")throw new Error("Primitive extension must be an object");return na(r??{},e)}return r}function na(r,e){return delete e.__proto__,delete e.constructor,Object.assign(r,e)}function sr(r,e){return A(r,e)&&"id"in r&&typeof r.id=="string"}function de(r){let e=N(r)??"undefined/undefined",t=Vi(r);return t===e?{reference:e}:{reference:e,display:t}}function N(r){if(z(r))return r.reference;if(sr(r))return`${r.resourceType}/${r.id}`}function fe(r){if(r)return z(r)?r.reference.split("/")[1]:r.id}function ia(r){if(r?.reference===void 0)throw new d(y("Reference missing reference property."));let[e,t]=r.reference.split("/");if(e===""||t===""||t===void 0)throw new d(y("Unable to parse reference string."));return[e,t]}function Di(r){return r.resourceType==="Patient"||r.resourceType==="Practitioner"||r.resourceType==="RelatedPerson"}function Vi(r){if(Di(r)){let e=oa(r);if(e)return e}if(r.resourceType==="Device"){let e=sa(r);if(e)return e}if(r.resourceType==="MedicationRequest"&&r.medicationCodeableConcept)return qe(r.medicationCodeableConcept);if(r.resourceType==="Subscription"&&r.criteria)return r.criteria;if(r.resourceType==="User"&&r.email)return r.email;if("name"in r&&r.name&&typeof r.name=="string")return r.name;if("code"in r&&r.code){let e=r.code;if(Array.isArray(e)&&(e=e[0]),ir(e))return qe(e);if(Fi(e))return e.text}return N(r)??""}function oa(r){let e=r.name;if(e&&e.length>0)return je(e[0])}function sa(r){let e=r.deviceName;if(e&&e.length>0)return e[0].name}function aa(r){if(!("photo"in r))return;let e=r.photo;if(e)if(Array.isArray(e))for(let t of e){let n=ki(t);if(n)return n}else return ki(e)}function ki(r){if(r.url&&r.contentType?.startsWith("image/"))return r.url}function ca(r){return r?new Date(r):void 0}function at(r,e){let t=new Date(r);t.setUTCHours(0,0,0,0);let n=e?new Date(e):new Date;n.setUTCHours(0,0,0,0);let i=t.getUTCFullYear(),o=t.getUTCMonth(),s=t.getUTCDate(),a=n.getUTCFullYear(),c=n.getUTCMonth(),u=n.getUTCDate(),p=a-i;(c<o||c===o&&u<s)&&p--;let m=a*12+c-(i*12+o);u<s&&m--;let x=Math.floor((n.getTime()-t.getTime())/(1e3*60*60*24));return{years:p,months:m,days:x}}function ua(r,e){let{years:t,months:n,days:i}=at(r,e);return t>=2?t.toString().padStart(3,"0")+"Y":n>=1?n.toString().padStart(3,"0")+"M":i.toString().padStart(3,"0")+"D"}function la(r){let e={};return Mi(r.item,e),e}function Mi(r,e){if(r)for(let t of r)t.linkId&&t.answer&&t.answer.length>0&&(e[t.linkId]=t.answer[0]),Mi(t.item,e)}function pa(r){let e={};return _i(r.item,e),e}function _i(r,e){if(r)for(let t of r)t.linkId&&t.answer&&t.answer.length>0&&(e[t.linkId]?e[t.linkId]=[...e[t.linkId],...t.answer]:e[t.linkId]=t.answer),_i(t.item,e)}function Li(r,e){let t=r.identifier;if(!t)return;let n=Array.isArray(t)?t:[t];for(let i of n)if(i.system===e)return i.value}function da(r,e,t){let n=r.identifier;if(!n){r.identifier=[{system:e,value:t}];return}for(let i of n)if(i.system===e){i.value=t;return}n.push({system:e,value:t})}function fa(r,...e){let t=re(r,...e);if(!t)return;let n=C({type:"Extension",value:t},"value[x]");if(n)return Array.isArray(n)?n[0].value:n.value}function re(r,...e){let t=r;for(let n=0;n<e.length&&t;n++)t=t?.extension?.find(i=>i.url===e[n]);return t}function ct(r,e){let t=Sn(r);return JSON.stringify(t,null,e?2:void 0)??""}function Sn(r){if(!(r==null||r===""))return typeof r=="object"?Array.isArray(r)?ha(r):ma(r):r}function ha(r){let e=r.length;if(e===0)return;let t,n=0;for(let i=0;i<e;i++){let o=r[i],s=Sn(o);s!==o&&!t&&(t=Array.from(r)),s===void 0?t&&(t[i]=null):(t&&(t[i]=s),n++)}if(n!==0)return t??r}function ma(r){let e,t=0;for(let n in r){let i=r[n],o=Sn(i);o!==i&&!e&&(e={...r}),o===void 0?e&&delete e[n]:(e&&(e[n]=o),t++)}if(t!==0)return e??r}function S(r){if(r==null)return!0;let e=typeof r;return e==="string"||e==="object"?!K(r):!1}function K(r){if(r==null)return!1;let e=typeof r;return e==="string"&&r!==""||e==="object"&&("length"in r&&r.length>0||Object.keys(r).length>0)}function ne(r,e,t){return r===e||S(r)&&S(e)?!0:S(r)||S(e)?!1:Array.isArray(r)&&Array.isArray(e)?ya(r,e):Array.isArray(r)||Array.isArray(e)?!1:E(r)&&E(e)?ga(r,e,t):(E(r)||E(e),!1)}function ya(r,e){if(r.length!==e.length)return!1;for(let t=0;t<r.length;t++)if(!ne(r[t],e[t]))return!1;return!0}function ga(r,e,t){let n=new Set;Object.keys(r).forEach(i=>n.add(i)),Object.keys(e).forEach(i=>n.add(i)),t==="meta"&&(n.delete("versionId"),n.delete("lastUpdated"),n.delete("author"));for(let i of n){let o=r[i],s=e[i];if(!ne(o,s,i))return!1}return!0}function Be(r,e){return S(r)?!0:S(e)?!1:Array.isArray(r)&&Array.isArray(e)?xa(r,e):Array.isArray(r)||Array.isArray(e)?!1:E(r)&&E(e)?va(r,e):E(r)||E(e)?!1:r===e}function xa(r,e){return e.every(t=>r.some(n=>Be(n,t)))}function va(r,e){return Object.entries(e).every(([t,n])=>t in r&&Be(r[t],n))}function he(r){return r===void 0?r:JSON.parse(JSON.stringify(r))}function Ta(r){return/^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/i.test(r)}function E(r){return r!==null&&typeof r=="object"}function bn(r){return r.every(ar)}function ar(r){return typeof r=="string"}function nr(r){return E(r)&&"code"in r&&typeof r.code=="string"}function ir(r){return E(r)&&"coding"in r&&Array.isArray(r.coding)&&r.coding.every(nr)}function Fi(r){return E(r)&&"text"in r&&typeof r.text=="string"}var Ni=[];for(let r=0;r<256;r++)Ni.push(r.toString(16).padStart(2,"0"));function En(r){let e=Cn(r),t=new Uint8Array(e),n=new Array(t.length);for(let i=0;i<t.length;i++)n[i]=Ni[t[i]];return n.join("")}function Rn(r){let e=Cn(r),t=new Uint8Array(e),n=new Array(t.length);for(let i=0;i<t.length;i++)n[i]=String.fromCharCode(t[i]);return window.btoa(n.join(""))}function Cn(r){return ArrayBuffer.isView(r)?r.buffer:r}function O(r){return r?r.charAt(0).toUpperCase()+r.substring(1):""}function Sa(r){return r===r.toLowerCase()&&r!==r.toUpperCase()}function cr(r){return r.length>0&&r.startsWith(r[0].toUpperCase())}function ie(r,e){let t=r+".";if(e.startsWith(t))return e.slice(t.length)}function or(r,e){return r.coding?.find(t=>t.system===e)?.code}function ba(r,e,t){r.coding||(r.coding=[]);let n=r.coding.find(i=>i.system===e);n?n.code=t:r.coding.push({system:e,code:t})}function Ea(r,e,t,n){return r.qualifiedInterval?.find(i=>Bi(i,e)&&wa(i,t,r.quantitativeDetails?.decimalPrecision)&&(n===void 0||i.category===n))}function Ra(r,e,t){return Ui(r,e,t)[0]}function Ui(r,e,t){return r.qualifiedInterval?.filter(n=>Bi(n,e)&&(!t||t.includes(n.condition)))??[]}function Bi(r,e){return Ca(r,e)&&Pa(r,e)}function Ca(r,e){return!r.gender||r.gender===e.gender}function Pa(r,e){return!r.age||Pn(at(e.birthDate).years,r.age)}function wa(r,e,t){return!!r.range&&Pn(e,r.range,t)}function Pn(r,e,t){return(e.low?.value===void 0||qi(r,e.low.value,t))&&(e.high?.value===void 0||Wi(r,e.high.value,t))}function Aa(r,e){return parseFloat(r.toFixed(e))}function Oa(r,e,t){return J(r,t)===J(e,t)}function Ia(r,e,t){return J(r,t)<J(e,t)}function ka(r,e,t){return J(r,t)>J(e,t)}function Wi(r,e,t){return J(r,t)<=J(e,t)}function qi(r,e,t){return J(r,t)>=J(e,t)}function J(r,e){return e===void 0?r:Math.round(r*Math.pow(10,e))}function Da(r,e,t){return r.find(n=>typeof e=="string"?or(n.code||{},t)===e:or(n.code||{},t)===or(e,t))}function Ue(r){if(r!==void 0)return Array.isArray(r)?r:[r]}function Va(r){return Array.isArray(r)?r[0]:r}var ur=r=>new Promise(e=>{setTimeout(e,r)});function lr(r,e,t){let n=[];for(let i=0;i<t-1;i++){let o=r.indexOf(e);if(o<0)break;n.push(r.slice(0,o)),r=r.slice(o+e.length)}return n.push(r),n}function wn(r){let e,t=!1;return function(){return t||(e=r(),t=!0),e}}function Ae(r,e){return r?(r.push(e),r):[e]}function ut(r){return r.sort((e,t)=>e.localeCompare(t))}function pr(r){return r.endsWith("/")?r:r+"/"}function ji(r){return r.startsWith("/")?r.slice(1):r}function U(r,e){return new URL(ji(e),pr(r.toString())).toString()}function An(r,e){return U(r,e).toString().replace("http://","ws://").replace("https://","wss://")}function On(r){return typeof r=="object"&&!Array.isArray(r)&&!(r instanceof URLSearchParams)&&(r=Object.fromEntries(Object.entries(r).filter(e=>e[1]!==void 0))),new URLSearchParams(r).toString()}var $i=/^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-_]*[a-zA-Z0-9])\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\-_]*[A-Za-z0-9])$/;function Ma(r){return $i.test(r)}function _a(r,e){return r?.meta?.profile?.includes(e)||(r.meta=r.meta??{},r.meta.profile=r.meta.profile??[],r.meta.profile.push(e)),r}function La(r,e){return new Map(r.entry?.filter(n=>!!n.resource).map(n=>[Li(n.resource,e),n.resource]).filter(([n])=>n!==void 0))}function Fa(r,e){if(r?.meta?.profile?.includes(e)){let t=r.meta.profile.indexOf(e);r.meta.profile.splice(t,1)}return r}function Na(r,e){let t=[];if(!r)return t;for(let n=0;n<r.length;n++){let i=e(r[n],n);Array.isArray(i)?t.push(...i.flat()):i!==void 0&&t.push(i)}return t}function Ua(r){return r.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/“/g,"&ldquo;").replace(/”/g,"&rdquo;").replace(/‘/g,"&lsquo;").replace(/’/g,"&rsquo;").replace(/…/g,"&hellip;")}function Ba(r){if(!r)return"";switch(r.type){case"Address":return Hi(r.value);case"CodeableConcept":return qe(r.value);case"Coding":return In(r.value);case"ContactPoint":return r.value.value;case"HumanName":return je(r.value);case"Quantity":return oe(r.value);case"Reference":return Gi(r.value);default:return r.value.toString()}}function Gi(r){return r?r.display??r.reference??ct(r):""}function Hi(r,e){if(!r)return"";let t=[];if(r.line&&t.push(...r.line),r.city||r.state||r.postalCode){let n=[];r.city&&n.push(r.city),r.state&&n.push(r.state),r.postalCode&&n.push(r.postalCode),t.push(n.join(", "))}return r.use&&(e?.all||e?.use)&&t.push("["+r.use+"]"),t.join(e?.lineSeparator??", ").trim()}function je(r,e){if(!r)return"";let t=[];if(r.prefix&&e?.prefix!==!1&&t.push(...r.prefix),r.given&&t.push(...r.given),r.family&&t.push(r.family),r.suffix&&e?.suffix!==!1&&t.push(...r.suffix),r.use&&(e?.all||e?.use)&&t.push("["+r.use+"]"),t.length===0){let n=Oe(r.text);if(n)return n}return t.join(" ").trim()}function Wa(r){let e=[];return r.given&&e.push(...r.given),e.join(" ").trim()}function qa(r){return Oe(r.family)??""}function fr(r){return r instanceof Date&&!isNaN(r.getTime())}function ja(r,e,t){if(!r)return"";let n=new Date(r);return fr(n)?(n.setUTCHours(0,0,0,0),n.toLocaleDateString(e,{timeZone:"UTC",...t})):""}function Qi(r,e,t){if(!r)return"";let n=new Date("2000-01-01T"+r+"Z");return fr(n)?n.toLocaleTimeString(e,t):""}function dr(r,e,t){if(!r)return"";let n=new Date(r);return fr(n)?n.toLocaleString(e,t):""}function $a(r,e,t){return!r||!r.start&&!r.end?"":dr(r.start,e,t)+" - "+dr(r.end,e,t)}var Ga={s:"every second",min:"every minute",h:"hourly",d:"daily",wk:"weekly",mo:"monthly",a:"annually"},Ha={s:"second",min:"minute",h:"hour",d:"day",wk:"week",mo:"month",a:"year"},Qa={s:"seconds",min:"minutes",h:"hours",d:"days",wk:"weeks",mo:"months",a:"years"};function za(r){if(!r)return"";let e=[];return Ja(e,r.repeat),r.event&&e.push(r.event.map(t=>dr(t)).join(", ")),O(e.join(" ").trim())}function Ja(r,e){if(!e?.periodUnit)return;let t=e.frequency??1,n=e.period??1,i=e.periodUnit;t===1&&n===1?r.push(Ga[i]):(t===1?r.push("once"):r.push(t+" times"),n===1?r.push("per "+Ha[i]):r.push("per "+n+" "+Qa[i])),e.dayOfWeek&&r.push("on "+e.dayOfWeek.map(O).join(", ")),e.timeOfDay&&r.push("at "+e.timeOfDay.map(o=>Qi(o)).join(", "))}function Ka(r,e,t=!1){if(t&&e===void 0)throw new Error("Precision must be specified for exclusive ranges");let n=r?.low&&{...r.low,comparator:void 0},i=r?.high&&{...r.high,comparator:void 0};return n?.value===void 0&&i?.value===void 0?"":n?.value!==void 0&&i?.value===void 0?t&&e!==void 0?(n.value=Za(n.value,e),`> ${oe(n,e)}`):`>= ${oe(n,e)}`:n?.value===void 0&&i?.value!==void 0?t&&e!==void 0?(i.value=Ya(i.value,e),`< ${oe(i,e)}`):`<= ${oe(i,e)}`:(n?.unit===i?.unit&&delete n?.unit,`${oe(n,e)} - ${oe(i,e)}`)}function oe(r,e){if(!r)return"";let t=[];return r.comparator&&(t.push(r.comparator),t.push(" ")),r.value!==void 0&&(e!==void 0?t.push(r.value.toFixed(e)):t.push(r.value)),r.unit&&(r.unit!=="%"&&t[t.length-1]!==" "&&t.push(" "),t.push(r.unit)),t.join("").trim()}function Xa(r){return r?.value===void 0?"":r.value.toLocaleString(void 0,{style:"currency",currency:r.currency??"USD",currencyDisplay:"narrowSymbol"})}function qe(r){if(!r)return"";let e=Oe(r.text);return e||(r.coding?r.coding.map(t=>In(t)).join(", "):"")}function In(r,e){let t=Oe(r?.display);if(t){let n=e?Oe(r?.code):void 0;return`${t}${n?" ("+n+")":""}`}return Oe(r?.code)??""}function zi(r){if(!r)return"";let e=[];if(r.valueQuantity)e.push(oe(r.valueQuantity));else if(r.valueCodeableConcept)e.push(qe(r.valueCodeableConcept));else{let t=Oe(r.valueString);t&&e.push(t)}return"component"in r&&e.push(r.component.map(t=>zi(t)).join(" / ")),e.join(" / ").trim()}function Oe(r){return typeof r=="string"?r:void 0}function Ya(r,e,t=1){return(Ji(r,e)+t)*Math.pow(10,-e)}function Za(r,e,t=1){return(Ji(r,e)-t)*Math.pow(10,-e)}function Ji(r,e){return e===void 0?r:Math.round(r*Math.pow(10,e))}var l={Address:"Address",Age:"Age",Annotation:"Annotation",Attachment:"Attachment",BackboneElement:"BackboneElement",CodeableConcept:"CodeableConcept",Coding:"Coding",ContactDetail:"ContactDetail",ContactPoint:"ContactPoint",Contributor:"Contributor",Count:"Count",DataRequirement:"DataRequirement",Distance:"Distance",Dosage:"Dosage",Duration:"Duration",Expression:"Expression",Extension:"Extension",HumanName:"HumanName",Identifier:"Identifier",MarketingStatus:"MarketingStatus",Meta:"Meta",Money:"Money",Narrative:"Narrative",ParameterDefinition:"ParameterDefinition",Period:"Period",Population:"Population",ProdCharacteristic:"ProdCharacteristic",ProductShelfLife:"ProductShelfLife",Quantity:"Quantity",Range:"Range",Ratio:"Ratio",Reference:"Reference",RelatedArtifact:"RelatedArtifact",SampledData:"SampledData",Signature:"Signature",SubstanceAmount:"SubstanceAmount",SystemString:"http://hl7.org/fhirpath/System.String",Timing:"Timing",TriggerDefinition:"TriggerDefinition",UsageContext:"UsageContext",base64Binary:"base64Binary",boolean:"boolean",canonical:"canonical",code:"code",date:"date",dateTime:"dateTime",decimal:"decimal",id:"id",instant:"instant",integer:"integer",markdown:"markdown",oid:"oid",positiveInt:"positiveInt",string:"string",time:"time",unsignedInt:"unsignedInt",uri:"uri",url:"url",uuid:"uuid"};function ec(r){for(let e of r.entry??[]){let t=e.resource;t.resourceType==="SearchParameter"&&hr(t)}}function pn(r){let e=Array.isArray(r)?r:r.entry?.map(t=>t.resource)??[];for(let t of e)t?.resourceType==="StructureDefinition"&&t.kind==="resource"&&Xi(t.type)}function Xi(r){let e=_.types[r];return e||(e={searchParamsDetails:{}},_.types[r]=e),!e.searchParams&&r!=="Binary"&&(e.searchParams={_id:{base:[r],code:"_id",type:"token",expression:r+".id"},_lastUpdated:{base:[r],code:"_lastUpdated",type:"date",expression:r+".meta.lastUpdated"},_compartment:{base:[r],code:"_compartment",type:"reference",expression:r+".meta.compartment"},_profile:{base:[r],code:"_profile",type:"uri",expression:r+".meta.profile"},_security:{base:[r],code:"_security",type:"token",expression:r+".meta.security"},_source:{base:[r],code:"_source",type:"uri",expression:r+".meta.source"},_tag:{base:[r],code:"_tag",type:"token",expression:r+".meta.tag"}}),e}function hr(r){for(let e of r.base??[]){let t=Xi(e);t.searchParams||(t.searchParams={}),t.searchParams[r.code]=r}}function Qt(r){let e=r.type?.[0]?.code;return e==="BackboneElement"||e==="Element"?Yi((r.base?.path??r.path)?.split(".")):e}function Yi(r){return r.length===1?r[0]:r.map(O).join("")}function Xt(r){return r.kind==="resource"&&r.name!=="Resource"&&r.name!=="DomainResource"}function tc(){return Object.values(sn()).filter(Xt).map(r=>r.name)}function rc(r){return _.types[r]?.searchParams}function nc(r,e){return _.types[r]?.searchParams?.[e]}function ic(r){let e=r.replaceAll("[x]","").split(".").pop();return Zi(e)}function Zi(r){let e;return r.length<100?e=r.match(/[A-Z]+(?![a-z])|[A-Z]?[a-z]+|\d+/g)??[]:e=r.split(/(?=[A-Z])/),e.map(sc).join(" ").replace("_"," ").replace(/\s+/g," ")}var oc=new Set(["ID","IP","PKCE","JWKS","URI","URL","OMB","UDI"]);function sc(r){let e=r.toUpperCase();return r===e?r:oc.has(e)?e:e.charAt(0)+r.slice(1)}function st(r,e,t){let n=Ne(r,t);if(n)return eo(n.elements,e)}function eo(r,e){let t=r[e]??r[e+"[x]"];if(t)return t;for(let n=0;n<e.length;n++){let i=e[n];if(i>="A"&&i<="Z"){let o=e.slice(0,n)+"[x]",s=r[o];if(s)return s}}}function ac(r){return!!(r&&typeof r=="object"&&"type"in r&&"value"in r)}function A(r,e){return!(!r||typeof r!="object"||!("resourceType"in r)||e&&r.resourceType!==e)}function z(r){return!!(r&&typeof r=="object"&&"reference"in r&&typeof r.reference=="string")}var _={types:{}};function kn(r){switch(r.type){case l.uuid:case l.uri:case l.url:case l.string:case l.oid:case l.markdown:case l.id:case l.code:case l.canonical:case l.base64Binary:case l.SystemString:case l.date:case l.dateTime:case l.instant:return r.value;case l.Identifier:return`${r.value.system??""}|${r.value.value}`;case l.Coding:return Ki(r.value);case l.CodeableConcept:return r.value.coding?.map(Ki).join(",")??r.value.text;case l.HumanName:return r.value.text?r.value.text:je(r.value);case l.unsignedInt:case l.positiveInt:case l.integer:case l.decimal:return r.value.toString();case l.boolean:return r.value?"true":"false";case l.Extension:return r.value.url;case l.ContactPoint:return r.value.value;case l.Quantity:case l.Age:case l.Count:case l.Duration:return`${r.value.value}|${r.value.system??""}|${r.value.code??r.value.unit??""}`;case l.Reference:return r.value.reference;default:return sr(r.value)?N(r.value):JSON.stringify(r)}}function Ki(r){return r?`${r.system??""}|${r.code}`:""}function $e(r){if(r.startsWith("T"))return r+"T00:00:00.000Z".substring(r.length);if(r.length<=10)return r;try{return new Date(r).toISOString()}catch{return r}}var se=()=>[],k={empty:(r,e)=>h(e.length===0||e.every(t=>S(t.value))),hasValue:(r,e)=>h(e.length!==0),exists:(r,e,t)=>t?h(e.filter(n=>M(t.eval(r,[n]))).length>0):h(e.length>0&&e.every(n=>!S(n.value))),all:(r,e,t)=>h(e.every(n=>M(t.eval(r,[n])))),allTrue:(r,e)=>{for(let t of e)if(!t.value)return h(!1);return h(!0)},anyTrue:(r,e)=>{for(let t of e)if(t.value)return h(!0);return h(!1)},allFalse:(r,e)=>{for(let t of e)if(t.value)return h(!1);return h(!0)},anyFalse:(r,e)=>{for(let t of e)if(!t.value)return h(!0);return h(!1)},subsetOf:(r,e,t)=>{if(e.length===0)return h(!0);let n=t.eval(r,Ie(r));return n.length===0?h(!1):h(e.every(i=>n.some(o=>o.value===i.value)))},supersetOf:(r,e,t)=>{let n=t.eval(r,Ie(r));return n.length===0?h(!0):e.length===0?h(!1):h(n.every(i=>e.some(o=>o.value===i.value)))},count:(r,e)=>[{type:l.integer,value:e.length}],distinct:(r,e)=>{let t=[];for(let n of e)t.some(i=>i.value===n.value)||t.push(n);return t},isDistinct:(r,e)=>h(e.length===k.distinct(r,e).length),where:(r,e,t)=>e.filter(n=>M(t.eval(r,[n]))),select:(r,e,t)=>e.map(n=>t.eval({parent:r,variables:{$this:n}},[n])).flat(),repeat:se,ofType:(r,e,t)=>e.filter(n=>n.type===t.name),single:(r,e)=>{if(e.length>1)throw new Error("Expected input length one for single()");return e.length===0?[]:e.slice(0,1)},first:(r,e)=>e.length===0?[]:e.slice(0,1),last:(r,e)=>e.length===0?[]:e.slice(e.length-1,e.length),tail:(r,e)=>e.length===0?[]:e.slice(1,e.length),skip:(r,e,t)=>{let n=t.eval(r,e)[0]?.value;if(typeof n!="number")throw new Error("Expected a number for skip(num)");return n>=e.length?[]:n<=0?e:e.slice(n,e.length)},take:(r,e,t)=>{let n=t.eval(r,e)[0]?.value;if(typeof n!="number")throw new Error("Expected a number for take(num)");return n>=e.length?e:n<=0?[]:e.slice(0,n)},intersect:(r,e,t)=>{if(!t)return e;let n=t.eval(r,Ie(r)),i=[];for(let o of e)!i.some(s=>s.value===o.value)&&n.some(s=>s.value===o.value)&&i.push(o);return i},exclude:(r,e,t)=>{if(!t)return e;let n=t.eval(r,Ie(r)),i=[];for(let o of e)n.some(s=>s.value===o.value)||i.push(o);return i},union:(r,e,t)=>{if(!t)return e;let n=t.eval(r,Ie(r));return nt([...e,...n])},combine:(r,e,t)=>{if(!t)return e;let n=t.eval(r,Ie(r));return[...e,...n]},htmlChecks:(r,e,t)=>[v(!0)],iif:(r,e,t,n,i)=>{let o=t.eval(r,e);if(o.length>1||o.length===1&&typeof o[0].value!="boolean")throw new Error("Expected criterion to evaluate to a Boolean");return M(o)?n.eval(r,e):i?i.eval(r,e):[]},toBoolean:(r,e)=>{if(e.length===0)return[];let[{value:t}]=Y(e,1);if(typeof t=="boolean")return[{type:l.boolean,value:t}];if(typeof t=="number"&&(t===0||t===1))return h(!!t);if(typeof t=="string"){let n=t.toLowerCase();if(["true","t","yes","y","1","1.0"].includes(n))return h(!0);if(["false","f","no","n","0","0.0"].includes(n))return h(!1)}return[]},convertsToBoolean:(r,e)=>e.length===0?[]:h(k.toBoolean(r,e).length===1),toInteger:(r,e)=>{if(e.length===0)return[];let[{value:t}]=Y(e,1);return typeof t=="number"?[{type:l.integer,value:t}]:typeof t=="string"&&/^[+-]?\d+$/.exec(t)?[{type:l.integer,value:parseInt(t,10)}]:typeof t=="boolean"?[{type:l.integer,value:t?1:0}]:[]},convertsToInteger:(r,e)=>e.length===0?[]:h(k.toInteger(r,e).length===1),toDate:(r,e)=>{if(e.length===0)return[];let[{value:t}]=Y(e,1);return typeof t=="string"&&/^\d{4}(-\d{2}(-\d{2})?)?/.exec(t)?[{type:l.date,value:$e(t)}]:[]},convertsToDate:(r,e)=>e.length===0?[]:h(k.toDate(r,e).length===1),toDateTime:(r,e)=>{if(e.length===0)return[];let[{value:t}]=Y(e,1);return typeof t=="string"&&/^\d{4}(-\d{2}(-\d{2})?)?/.exec(t)?[{type:l.dateTime,value:$e(t)}]:[]},convertsToDateTime:(r,e)=>e.length===0?[]:h(k.toDateTime(r,e).length===1),toDecimal:(r,e)=>{if(e.length===0)return[];let[{value:t}]=Y(e,1);return typeof t=="number"?[{type:l.decimal,value:t}]:typeof t=="string"&&/^-?\d{1,9}(\.\d{1,9})?$/.exec(t)?[{type:l.decimal,value:parseFloat(t)}]:typeof t=="boolean"?[{type:l.decimal,value:t?1:0}]:[]},convertsToDecimal:(r,e)=>e.length===0?[]:h(k.toDecimal(r,e).length===1),toQuantity:(r,e)=>{if(e.length===0)return[];let[{value:t}]=Y(e,1);return I(t)?[{type:l.Quantity,value:t}]:typeof t=="number"?[{type:l.Quantity,value:{value:t,unit:"1"}}]:typeof t=="string"&&/^-?\d{1,9}(\.\d{1,9})?/.exec(t)?[{type:l.Quantity,value:{value:parseFloat(t),unit:"1"}}]:typeof t=="boolean"?[{type:l.Quantity,value:{value:t?1:0,unit:"1"}}]:[]},convertsToQuantity:(r,e)=>e.length===0?[]:h(k.toQuantity(r,e).length===1),toString:(r,e)=>{if(e.length===0)return[];let[{value:t}]=Y(e,1);return t==null?[]:I(t)?[{type:l.string,value:`${t.value} '${t.unit}'`}]:[{type:l.string,value:t.toString()}]},convertsToString:(r,e)=>e.length===0?[]:h(k.toString(r,e).length===1),toTime:(r,e)=>{if(e.length===0)return[];let[{value:t}]=Y(e,1);if(typeof t=="string"){let n=/^T?(\d{2}(:\d{2}(:\d{2})?)?)/.exec(t);if(n)return[{type:l.time,value:$e("T"+n[1])}]}return[]},convertsToTime:(r,e)=>e.length===0?[]:h(k.toTime(r,e).length===1),indexOf:(r,e,t)=>B((n,i)=>n.indexOf(i),r,e,t),substring:(r,e,t,n)=>B((i,o,s)=>{let a=o,c=s?a+s:i.length;return a<0||a>=i.length?void 0:i.substring(a,c)},r,e,t,n),startsWith:(r,e,t)=>B((n,i)=>n.startsWith(i),r,e,t),endsWith:(r,e,t)=>B((n,i)=>n.endsWith(i),r,e,t),contains:(r,e,t)=>B((n,i)=>n.includes(i),r,e,t),upper:(r,e)=>B(t=>t.toUpperCase(),r,e),lower:(r,e)=>B(t=>t.toLowerCase(),r,e),replace:(r,e,t,n)=>B((i,o,s)=>i.replaceAll(o,s),r,e,t,n),matches:(r,e,t)=>B((n,i)=>!!new RegExp(i).exec(n),r,e,t),replaceMatches:(r,e,t,n)=>B((i,o,s)=>i.replaceAll(new RegExp(o,"g"),s),r,e,t,n),length:(r,e)=>B(t=>t.length,r,e),toChars:(r,e)=>B(t=>t?t.split(""):void 0,r,e),encode:se,decode:se,escape:se,unescape:se,trim:se,split:se,join:(r,e,t)=>{let n=t?.eval(r,Ie(r))[0]?.value??"";if(typeof n!="string")throw new Error("Separator must be a string.");return[{type:l.string,value:e.map(i=>i.value?.toString()??"").join(n)}]},abs:(r,e)=>X(Math.abs,r,e),ceiling:(r,e)=>X(Math.ceil,r,e),exp:(r,e)=>X(Math.exp,r,e),floor:(r,e)=>X(Math.floor,r,e),ln:(r,e)=>X(Math.log,r,e),log:(r,e,t)=>X((n,i)=>Math.log(n)/Math.log(i),r,e,t),power:(r,e,t)=>X(Math.pow,r,e,t),round:(r,e)=>X(Math.round,r,e),sqrt:(r,e)=>X(Math.sqrt,r,e),truncate:(r,e)=>X(t=>t|0,r,e),children:se,descendants:se,trace:(r,e,t)=>e,now:()=>[{type:l.dateTime,value:new Date().toISOString()}],timeOfDay:()=>[{type:l.time,value:new Date().toISOString().substring(11)}],today:()=>[{type:l.date,value:new Date().toISOString().substring(0,10)}],between:(r,e,t,n,i)=>{let o=k.toDateTime(r,t.eval(r,e));if(o.length===0)throw new Error("Invalid start date");let s=k.toDateTime(r,n.eval(r,e));if(s.length===0)throw new Error("Invalid end date");let a=i.eval(r,e)[0]?.value;if(a!=="years"&&a!=="months"&&a!=="days")throw new Error("Invalid units");let c=at(o[0].value,s[0].value);return[{type:l.Quantity,value:{value:c[a],unit:a}}]},is:(r,e,t)=>{let n="";return t instanceof W?n=t.name:t instanceof ae&&(n=t.left.name+"."+t.right.name),n?e.map(i=>({type:l.boolean,value:it(i,n)})):[]},not:(r,e)=>k.toBoolean(r,e).map(t=>({type:l.boolean,value:!t.value})),resolve:(r,e)=>e.map(t=>{let n=t.value,i;if(typeof n=="string")i=n;else if(typeof n=="object"){let o=n;if(o.resource)return v(o.resource);o.reference?i=o.reference:o.type&&o.identifier&&(i=`${o.type}?identifier=${o.identifier.system}|${o.identifier.value}`)}if(i?.includes("?")){let[o]=i.split("?");return{type:o,value:{resourceType:o}}}if(i?.includes("/")){let[o,s]=i.split("/");return{type:o,value:{resourceType:o,id:s}}}return{type:l.BackboneElement,value:void 0}}).filter(t=>!!t.value),as:(r,e)=>e,type:(r,e)=>e.map(({value:t})=>typeof t=="boolean"?{type:l.BackboneElement,value:{namespace:"System",name:"Boolean"}}:typeof t=="number"?{type:l.BackboneElement,value:{namespace:"System",name:"Integer"}}:A(t)?{type:l.BackboneElement,value:{namespace:"FHIR",name:t.resourceType}}:{type:l.BackboneElement,value:null}),conformsTo:(r,e,t)=>{let n=t.eval(r,e)[0].value;if(!n.startsWith("http://hl7.org/fhir/StructureDefinition/"))throw new Error("Expected a StructureDefinition URL");let i=n.replace("http://hl7.org/fhir/StructureDefinition/","");return e.map(o=>({type:l.boolean,value:o.value?.resourceType===i}))},getResourceKey:(r,e)=>{let t=e[0].value;return t?.id?[{type:l.id,value:t.id}]:[]},getReferenceKey:(r,e,t)=>{let n=e[0].value;if(!n?.reference)return[];let i="";return t instanceof W&&(i=t.name),i&&!n.reference.startsWith(i+"/")?[]:[{type:l.id,value:fe(n)}]},extension:(r,e,t)=>{let n=t.eval(r,e)[0].value,i=e?.[0]?.value;if(i){let o=re(i,n);if(o)return[{type:l.Extension,value:o}]}return[]}};function B(r,e,t,...n){if(t.length===0)return[];let[{value:i}]=Y(t,1);if(typeof i!="string")throw new Error("String function cannot be called with non-string");let o=r(i,...n.map(s=>s?.eval(e,t)[0]?.value));return o===void 0?[]:Array.isArray(o)?o.map(v):[v(o)]}function X(r,e,t,...n){if(t.length===0)return[];let[{value:i}]=Y(t,1),o=I(i),s=o?i.value:i;if(typeof s!="number")throw new Error("Math function cannot be called with non-number");let a=r(s,...n.map(p=>p.eval(e,t)[0]?.value)),c=o?l.Quantity:t[0].type,u=o?{...i,value:a}:a;return[{type:c,value:u}]}function Y(r,e){if(r.length!==e)throw new Error(`Expected ${e} arguments`);for(let t of r)if(t==null)throw new Error("Expected non-null argument");return r}function Ie(r){let e=r;for(;e.parent?.variables.$this;)e=e.parent;return[e.variables.$this]}var ke=class{constructor(e,t){this.original=e,this.child=t}eval(e,t){try{if(t.length>0){let n=[];for(let i of t)n.push(this.child.eval({parent:e,variables:{$this:i}},[i]));return n.flat()}else return this.child.eval(e,[])}catch(n){throw new Error(`FhirPathError on "${this.original}": ${n}`,{cause:n})}}toString(){return this.child.toString()}},L=class{constructor(e){this.value=e}eval(){return[this.value]}toString(){let e=this.value.value;return typeof e=="string"?`'${e}'`:e.toString()}},W=class{constructor(e){this.name=e}eval(e,t){if(this.name==="$this")return t;let n=this.getVariable(e);if(n)return[n];if(this.name.startsWith("%"))throw new Error(`Undefined variable ${this.name}`);return t.flatMap(i=>this.evalValue(i)).filter(i=>i?.value!==void 0)}getVariable(e){let t=e.variables[this.name];if(t!==void 0)return t;if(e.parent)return this.getVariable(e.parent)}evalValue(e){let t=e.value;if(!(!t||typeof t!="object"))return A(t,this.name)?e:C(e,this.name)}toString(){return this.name}},lt=class{eval(){return[]}toString(){return"{}"}},pt=class extends Ye{constructor(e,t,n){super(e,t),this.impl=n}eval(e,t){return this.impl(this.child.eval(e,t))}toString(){return this.operator+this.child.toString()}},ce=class extends ee{constructor(e,t){super("as",e,t)}eval(e,t){return k.ofType(e,this.left.eval(e,t),this.right)}},R=class extends ee{},D=class extends R{constructor(e,t,n,i){super(e,t,n),this.impl=i}eval(e,t){let n=this.left.eval(e,t);if(n.length!==1)return[];let i=this.right.eval(e,t);if(i.length!==1)return[];let o=n[0].value,s=i[0].value,a=I(o)?o.value:o,c=I(s)?s.value:s,u=this.impl(a,c);return typeof u=="boolean"?h(u):I(o)?[{type:l.Quantity,value:{...o,value:u}}]:[v(u)]}},dt=class extends ee{constructor(e,t){super("&",e,t)}eval(e,t){let n=this.left.eval(e,t),i=this.right.eval(e,t),o=[...n,...i];return o.length>0&&o.every(s=>typeof s.value=="string")?[{type:l.string,value:o.map(s=>s.value).join("")}]:o}},ft=class extends R{constructor(e,t){super("contains",e,t)}eval(e,t){let n=this.left.eval(e,t),i=this.right.eval(e,t);return h(n.some(o=>o.value===i[0].value))}},ht=class extends R{constructor(e,t){super("in",e,t)}eval(e,t){let n=$(this.left.eval(e,t)),i=this.right.eval(e,t);return n?h(i.some(o=>We(n,o)[0].value)):[]}},ae=class extends ee{constructor(e,t){super(".",e,t)}eval(e,t){return this.right.eval(e,this.left.eval(e,t))}toString(){return`${this.left.toString()}.${this.right.toString()}`}},me=class extends ee{constructor(e,t){super("|",e,t)}eval(e,t){let n=this.left.eval(e,t),i=this.right.eval(e,t);return nt([...n,...i])}},mt=class extends R{constructor(e,t){super("=",e,t)}eval(e,t){let n=this.left.eval(e,t),i=this.right.eval(e,t);return yn(n,i)}},yt=class extends R{constructor(e,t){super("!=",e,t)}eval(e,t){let n=this.left.eval(e,t),i=this.right.eval(e,t);return gn(n,i)}},gt=class extends R{constructor(e,t){super("~",e,t)}eval(e,t){let n=this.left.eval(e,t),i=this.right.eval(e,t);return rr(n,i)}},xt=class extends R{constructor(e,t){super("!~",e,t)}eval(e,t){let n=this.left.eval(e,t),i=this.right.eval(e,t);return mn(rr(n,i))}},ye=class extends R{constructor(e,t){super("is",e,t)}eval(e,t){let n=this.left.eval(e,t);if(n.length!==1)return[];let i=this.right.name;return h(it(n[0],i))}},vt=class extends R{constructor(e,t){super("and",e,t)}eval(e,t){let n=$(this.left.eval(e,t),"boolean"),i=$(this.right.eval(e,t),"boolean");return n?.value===!0&&i?.value===!0?h(!0):n?.value===!1||i?.value===!1?h(!1):[]}},Tt=class extends R{constructor(e,t){super("or",e,t)}eval(e,t){let n=$(this.left.eval(e,t),"boolean"),i=$(this.right.eval(e,t),"boolean");return n?.value===!1&&i?.value===!1?h(!1):n?.value||i?.value?h(!0):[]}},St=class extends R{constructor(e,t){super("xor",e,t)}eval(e,t){let n=$(this.left.eval(e,t),"boolean"),i=$(this.right.eval(e,t),"boolean");return!n||!i?[]:h(n.value!==i.value)}},bt=class extends R{constructor(e,t){super("implies",e,t)}eval(e,t){let n=$(this.left.eval(e,t),"boolean"),i=$(this.right.eval(e,t),"boolean");return i?.value===!0||n?.value===!1?h(!0):!n||!i?[]:h(!1)}},Z=class{constructor(e,t){this.name=e,this.args=t}eval(e,t){let n=k[this.name];if(!n)throw new Error("Unrecognized function: "+this.name);return n(e,t,...this.args)}toString(){return`${this.name}(${this.args.map(e=>e.toString()).join(", ")})`}},ge=class{constructor(e,t){this.left=e,this.expr=t}eval(e,t){let n=this.expr.eval(e,t);if(n.length!==1)return[];let i=n[0].value;if(typeof i!="number")throw new Error("Invalid indexer expression: should return integer}");let o=this.left.eval(e,t);return i in o?[o[i]]:[]}toString(){return`${this.left.toString()}[${this.expr.toString()}]`}};var cc=["year","years","month","months","week","weeks","day","days","hour","hours","minute","minutes","second","seconds","millisecond","milliseconds"],ue=class{constructor(e,t,n,i){this.result=[];this.pos={index:0,line:1,column:0};this.markStack=[];this.str=e,this.keywords=t,this.operators=n,this.dateTimeLiterals=!!i?.dateTimeLiterals,this.symbolRegex=i?.symbolRegex??/[$\w%]/}tokenize(){for(;this.pos.index<this.str.length;){let e=this.consumeToken();e&&this.result.push(e)}return this.result}prevToken(){return this.result.slice(-1)[0]}peekToken(){this.mark();let e=this.consumeToken();return this.reset(),e}consumeToken(){this.consumeWhitespace();let e=this.curr();if(!e)return;this.mark();let t=this.peek();return e==="/"&&t==="*"?this.consumeMultiLineComment():e==="/"&&t==="/"?this.consumeSingleLineComment():e==="'"||e==='"'||e==="`"?this.consumeString(e):e==="@"?this.consumeDateTime():/\d/.exec(e)?this.consumeNumber():/\w/.exec(e)?this.consumeSymbol():(e==="$"||e==="%")&&/\w/.exec(t)?this.consumeSymbol():(e==="$"||e==="%")&&(t==="'"||t==='"'||t==="`")?this.consumeQuotedSymbol(t):this.consumeOperator()}consumeWhitespace(){this.consumeWhile(()=>/\s/.exec(this.curr()))}consumeMultiLineComment(){let e=this.pos.index;return this.consumeWhile(()=>this.curr()!=="*"||this.peek()!=="/"),this.advance(),this.advance(),this.buildToken("Comment",this.str.substring(e,this.pos.index))}consumeSingleLineComment(){return this.buildToken("Comment",this.consumeWhile(()=>this.curr()!==`
`))}consumeString(e){this.advance();let n=this.consumeWhile(()=>this.prev()==="\\"||this.curr()!==e).replace(/\\u([0-9a-fA-F]{4})/g,(o,s)=>String.fromCodePoint(parseInt(s,16))),i=this.buildToken(e==="`"?"Symbol":"String",n);return this.advance(),i}consumeQuotedSymbol(e){this.mark();let t=this.pos.index;this.advance(),this.consumeString(e);let n=this.str.substring(t,this.pos.index);return this.buildToken("Symbol",n)}consumeDateTime(){this.advance();let e=this.pos.index;this.consumeWhile(()=>/[\d-]/.exec(this.curr()));let t=!1,n=!1;if(this.curr()==="T"&&(t=!0,this.advance(),this.consumeWhile(()=>/[\d:]/.exec(this.curr())),this.curr()==="."&&/\d/.exec(this.peek())&&(this.advance(),this.consumeWhile(()=>/\d/.exec(this.curr()))),this.curr()==="Z"?(n=!0,this.advance()):(this.curr()==="+"||this.curr()==="-")&&(n=!0,this.advance(),this.consumeWhile(()=>/[\d:]/.exec(this.curr())))),this.pos.index===e)throw new Error("Invalid DateTime literal");let i=this.str.substring(e,this.pos.index);return i.endsWith("T")?i=i.substring(0,i.length-1):!i.startsWith("T")&&t&&!n&&(i+="Z"),this.buildToken("DateTime",i)}consumeNumber(){let e=this.pos.index,t="Number";return this.consumeWhile(()=>/\d/.exec(this.curr())),this.curr()==="."&&/\d/.exec(this.peek())&&(this.advance(),this.consumeWhile(()=>/\d/.exec(this.curr()))),this.curr()==="-"&&this.dateTimeLiterals?(this.pos.index=e-1,this.consumeDateTime()):(this.curr()===" "&&uc(this.peekToken())&&(t="Quantity",this.consumeToken()),this.buildToken(t,this.str.substring(e,this.pos.index)))}consumeSymbol(){let e=this.consumeWhile(()=>this.symbolRegex.exec(this.curr()));return this.prevToken()?.value!=="."&&this.keywords.includes(e)?this.buildToken(e,e):this.buildToken("Symbol",e)}consumeOperator(){let e=this.curr(),t=this.peek(),n=e+t;return this.operators.includes(n)?(this.advance(),this.advance(),this.buildToken(n,n)):(this.advance(),this.buildToken(e,e))}consumeWhile(e){let t=this.pos.index;for(;this.pos.index<this.str.length&&e();)this.advance();return this.str.substring(t,this.pos.index)}curr(){return this.str[this.pos.index]}prev(){return this.str[this.pos.index-1]??""}peek(){return this.str[this.pos.index+1]??""}mark(){this.markStack.push({...this.pos})}reset(){let e=this.markStack.pop();if(!e)throw new Error("No mark to reset to");this.pos.index=e.index,this.pos.line=e.line,this.pos.column=e.column}advance(){this.pos.index++,this.curr()===`
`?(this.pos.line++,this.pos.column=0):this.pos.column++}buildToken(e,t){let n=this.markStack.pop();if(!n)throw new Error("No mark for token");return{id:e,value:t,...n}}};function uc(r){return!!(r&&(r.id==="String"||r.id==="Symbol"&&cc.includes(r.value)))}var Et=["true","false"],Rt=["!=","!~","<=",">=","{}","->"];function to(r){return new ue(r,Et,Rt).tokenize()}var g={FunctionCall:0,Dot:1,Indexer:2,UnaryAdd:3,UnarySubtract:3,Multiply:4,Divide:4,IntegerDivide:4,Modulo:4,Add:5,Subtract:5,Ampersand:5,Is:6,As:6,Union:7,GreaterThan:8,GreaterThanOrEquals:8,LessThan:8,LessThanOrEquals:8,Equals:9,Equivalent:9,NotEquals:9,NotEquivalent:9,In:10,Contains:10,And:11,Xor:12,Or:12,Implies:13,Arrow:100,Semicolon:200},lc={parse(r){let e=r.consumeAndParse();if(!r.match(")"))throw new Error("Parse error: expected `)` got `"+r.peek()?.value+"`");return e}},pc={parse(r,e){let t=r.consumeAndParse();if(!r.match("]"))throw new Error("Parse error: expected `]`");return new ge(e,t)},precedence:g.Indexer},dc={parse(r,e){if(!(e instanceof W))throw new Error("Unexpected parentheses");let t=[];for(;!r.match(")");)t.push(r.consumeAndParse()),r.match(",");return new Z(e.name,t)},precedence:g.FunctionCall};function fc(r){let e=r.split(" "),t=parseFloat(e[0]),n=e[1];return n?.startsWith("'")&&n.endsWith("'")?n=n.substring(1,n.length-1):n="{"+n+"}",{value:t,unit:n}}function Ge(){return new Ze().registerPrefix("String",{parse:(r,e)=>new L({type:l.string,value:e.value})}).registerPrefix("DateTime",{parse:(r,e)=>new L({type:l.dateTime,value:$e(e.value)})}).registerPrefix("Quantity",{parse:(r,e)=>new L({type:l.Quantity,value:fc(e.value)})}).registerPrefix("Number",{parse:(r,e)=>new L({type:e.value.includes(".")?l.decimal:l.integer,value:parseFloat(e.value)})}).registerPrefix("true",{parse:()=>new L({type:l.boolean,value:!0})}).registerPrefix("false",{parse:()=>new L({type:l.boolean,value:!1})}).registerPrefix("Symbol",{parse:(r,e)=>new W(e.value)}).registerPrefix("{}",{parse:()=>new lt}).registerPrefix("(",lc).registerInfix("[",pc).registerInfix("(",dc).prefix("+",g.UnaryAdd,(r,e)=>new pt("+",e,t=>t)).prefix("-",g.UnarySubtract,(r,e)=>new D("-",e,e,(t,n)=>-n)).infixLeft(".",g.Dot,(r,e,t)=>new ae(r,t)).infixLeft("/",g.Divide,(r,e,t)=>new D("/",r,t,(n,i)=>n/i)).infixLeft("*",g.Multiply,(r,e,t)=>new D("*",r,t,(n,i)=>n*i)).infixLeft("+",g.Add,(r,e,t)=>new D("+",r,t,(n,i)=>n+i)).infixLeft("-",g.Subtract,(r,e,t)=>new D("-",r,t,(n,i)=>n-i)).infixLeft("|",g.Union,(r,e,t)=>new me(r,t)).infixLeft("=",g.Equals,(r,e,t)=>new mt(r,t)).infixLeft("!=",g.NotEquals,(r,e,t)=>new yt(r,t)).infixLeft("~",g.Equivalent,(r,e,t)=>new gt(r,t)).infixLeft("!~",g.NotEquivalent,(r,e,t)=>new xt(r,t)).infixLeft("<",g.LessThan,(r,e,t)=>new D("<",r,t,(n,i)=>n<i)).infixLeft("<=",g.LessThanOrEquals,(r,e,t)=>new D("<=",r,t,(n,i)=>n<=i)).infixLeft(">",g.GreaterThan,(r,e,t)=>new D(">",r,t,(n,i)=>n>i)).infixLeft(">=",g.GreaterThanOrEquals,(r,e,t)=>new D(">=",r,t,(n,i)=>n>=i)).infixLeft("&",g.Ampersand,(r,e,t)=>new dt(r,t)).infixLeft("and",g.And,(r,e,t)=>new vt(r,t)).infixLeft("as",g.As,(r,e,t)=>new ce(r,t)).infixLeft("contains",g.Contains,(r,e,t)=>new ft(r,t)).infixLeft("div",g.Divide,(r,e,t)=>new D("div",r,t,(n,i)=>n/i|0)).infixLeft("in",g.In,(r,e,t)=>new ht(r,t)).infixLeft("is",g.Is,(r,e,t)=>new ye(r,t)).infixLeft("mod",g.Modulo,(r,e,t)=>new D("mod",r,t,(n,i)=>n%i)).infixLeft("or",g.Or,(r,e,t)=>new Tt(r,t)).infixLeft("xor",g.Xor,(r,e,t)=>new St(r,t)).infixLeft("implies",g.Implies,(r,e,t)=>new bt(r,t))}var hc=Ge();function Ct(r){return new ke(r,hc.construct(to(r)).consumeAndParse())}function De(r,e){let t=Array.isArray(e)?e:[e];for(let n=0;n<t.length;n++){let i=t[n];typeof i=="object"&&"type"in i&&"value"in i||(t[n]=v(t[n]))}return P(r,t).map(n=>n.value)}function P(r,e,t={},n=void 0){let i;if(typeof r=="string"){let o=n?.get(r);i=o??Ct(r),n&&!o&&n.set(r,i)}else i=r;return i.eval({variables:t},e).map(o=>({type:o.type,value:o.value?.valueOf()}))}var q={BOOLEAN:"BOOLEAN",NUMBER:"NUMBER",QUANTITY:"QUANTITY",TEXT:"TEXT",REFERENCE:"REFERENCE",CANONICAL:"CANONICAL",DATE:"DATE",DATETIME:"DATETIME",PERIOD:"PERIOD",UUID:"UUID"};function gr(r,e){let t=_.types[r]?.searchParamsDetails?.[e.code];return t||(t=yc(r,e)),t}function mc(r,e,t){let n=_.types[r];n||(n={},_.types[r]=n),n.searchParamsDetails||(n.searchParamsDetails={}),n.searchParamsDetails[e]=t}function yc(r,e){let t=e.code,n=Dn(r,e.expression),i={elementDefinitions:[],propertyTypes:new Set,array:!1};for(let s of n){let a=mr(s),c=wn(()=>a.join("."));a.length===1&&a[0]instanceof R?i.propertyTypes.add("boolean"):e.code.endsWith(":identifier")?i.propertyTypes.add("Identifier"):c().endsWith("extension.value.code")||c().endsWith("extension.value.coding.code")?(i.array=!0,i.propertyTypes.add("code")):ro(i,a,r,1),c().endsWith("extension.valueDateTime")&&(i.array=!1)}let o={type:vc(e,i.propertyTypes),elementDefinitions:i.elementDefinitions,parsedExpression:no(r,e.expression),array:i.array};return mc(r,t,o),o}function ro(r,e,t,n){let i=e[n];if(i instanceof ce){r.propertyTypes.add(i.right.toString());return}if(i instanceof Z){gc(r,i);return}let o=i.toString(),s=st(t,o);if(!s)throw new Error(`Element definition not found for ${t} ${o}`);let a=!1,c=n+1;c<e.length&&e[c]instanceof ge&&(a=!0,c++);let u=e[c];if(s.isArray&&!a&&(r.array=!0),c===e.length-1&&u instanceof ce){r.elementDefinitions.push(s),r.propertyTypes.add(u.right.toString());return}if(c>=e.length){r.elementDefinitions.push(s);for(let p of s.type)r.propertyTypes.add(p.code);return}for(let p of s.type){let m=p.code;xc(m)&&(m=s.type[0].code),ro(r,e,m,c)}}function gc(r,e){if(e.name==="as"){r.propertyTypes.add(e.args[0].toString());return}if(e.name==="ofType"){r.propertyTypes.add(e.args[0].toString());return}if(e.name==="resolve"){r.propertyTypes.add("string");return}if(e.name==="where"&&e.args[0]instanceof ye){r.propertyTypes.add(e.args[0].right.toString());return}throw new Error(`Unhandled FHIRPath function: ${e.name}`)}function xc(r){return r==="Element"||r==="BackboneElement"}function vc(r,e){switch(r.type){case"date":return e.size===1&&e.has(l.date)?q.DATE:q.DATETIME;case"number":return q.NUMBER;case"quantity":return q.QUANTITY;case"reference":return e.has(l.canonical)?q.CANONICAL:q.REFERENCE;case"token":return e.size===1&&e.has(l.boolean)?q.BOOLEAN:q.TEXT;default:return q.TEXT}}function Dn(r,e){let t=[],n=Ct(e);return yr(r,n.child,t),t}function Tc(r,e){let t=Dn(r,e);if(t.length!==0)return t.map(n=>n.toString()).join(" | ")}function no(r,e){let t=[],n=Ct(e);if(yr(r,n.child,t),t.length===0)return n;let i=t[0];for(let o=1;o<t.length;o++)i=new me(i,t[o]);return new ke("<original-not-available>",i)}function yr(r,e,t){e instanceof me?(yr(r,e.left,t),yr(r,e.right,t)):e.toString().includes(r+".")&&t.push(e)}function mr(r){if(r instanceof ce||r instanceof ge)return[mr(r.left),r].flat();if(r instanceof R)return[r];if(r instanceof ae)return[mr(r.left),mr(r.right)].flat();if(r instanceof Z){if(r.name==="where"&&!(r.args[0]instanceof ye))return[];if(r.name==="last")return[]}return[r]}var Sc=20,bc=1e3,f={EQUALS:"eq",NOT_EQUALS:"ne",GREATER_THAN:"gt",LESS_THAN:"lt",GREATER_THAN_OR_EQUALS:"ge",LESS_THAN_OR_EQUALS:"le",STARTS_AFTER:"sa",ENDS_BEFORE:"eb",APPROXIMATELY:"ap",CONTAINS:"contains",STARTS_WITH:"sw",EXACT:"exact",TEXT:"text",NOT:"not",ABOVE:"above",BELOW:"below",IN:"in",NOT_IN:"not-in",OF_TYPE:"of-type",MISSING:"missing",PRESENT:"present",IDENTIFIER:"identifier",ITERATE:"iterate"},ao={contains:f.CONTAINS,exact:f.EXACT,above:f.ABOVE,below:f.BELOW,text:f.TEXT,not:f.NOT,in:f.IN,"not-in":f.NOT_IN,"of-type":f.OF_TYPE,missing:f.MISSING,identifier:f.IDENTIFIER,iterate:f.ITERATE},Vn={eq:f.EQUALS,ne:f.NOT_EQUALS,lt:f.LESS_THAN,le:f.LESS_THAN_OR_EQUALS,gt:f.GREATER_THAN,ge:f.GREATER_THAN_OR_EQUALS,sa:f.STARTS_AFTER,eb:f.ENDS_BEFORE,ap:f.APPROXIMATELY,sw:f.STARTS_WITH};function He(r,e){if(!r)throw new Error("Invalid search URL");let t="",n;if(typeof r=="string")if(r.includes("?")){let[s,a]=r.split("?");t=s,n=new URLSearchParams(a)}else t=r;else typeof r=="object"&&(t=r.pathname,n=r.searchParams);let i;t.includes("/")?i=t.split("/").filter(Boolean).pop():i=t;let o=[];if(n&&o.push(...n.entries()),e)for(let[s,a]of Object.entries(e))if(Array.isArray(a))for(let c of a)o.push([s,c]);else o.push([s,a??""]);return Ec(i,o)}function Ec(r,e){let t={resourceType:r};for(let[n,i]of e)Rc(t,n,i);return t}function Rc(r,e,t){let n,i,o=e.indexOf(":");if(o>=0?(n=e.substring(0,o),i=e.substring(o+1)):(n=e,i=""),n!=="_"){if(n==="_has"||e.includes(".")){r.filters=Ae(r.filters,{code:e,operator:f.EQUALS,value:t});return}switch(n){case"_sort":Cc(r,t);break;case"_cursor":r.cursor=t;break;case"_count":r.count=parseInt(t,10);break;case"_offset":r.offset=parseInt(t,10);break;case"_total":r.total=t;break;case"_summary":t==="count"?(r.total="accurate",r.count=0):(t==="true"||t==="data"||t==="text")&&(r.summary=t);break;case"_include":{let s=io(t);i==="iterate"&&(s.modifier=f.ITERATE),r.include=Ae(r.include,s);break}case"_revinclude":{let s=io(t);i==="iterate"&&(s.modifier=f.ITERATE),r.revInclude=Ae(r.revInclude,s);break}case"_fields":case"_elements":r.fields=t.split(",");break;case"_type":r.types=t.split(",");break;case"_format":r.format=t;break;case"_pretty":r.pretty=t==="true";break;default:{let s=_.types[r.resourceType]?.searchParams?.[n];s?r.filters=Ae(r.filters,co(s,i,t)):r.filters=Ae(r.filters,wc(n,i,t))}}}}function Cc(r,e){for(let t of e.split(",")){let n,i=!1;t.startsWith("-")?(n=t.substring(1),i=!0):n=t,r.sortRules||(r.sortRules=[]),r.sortRules.push({code:n,descending:i})}}var Pc=[f.MISSING,f.PRESENT];function co(r,e,t){if(Pc.includes(e))return{code:r.code,operator:e,value:t};switch(r.type){case"number":case"date":case"quantity":{let{operator:n,value:i}=Ac(t);if(!oo(r,i))throw new d(b(`Invalid format for ${r.type} search parameter: ${i}`));return{code:r.code,operator:n,value:i}}case"reference":case"string":case"token":case"uri":if(!oo(r,t))throw new d(b(`Invalid format for ${r.type} search parameter: ${t}`));return{code:r.code,operator:Oc(e),value:t};default:throw new Error("Unrecognized search parameter type: "+r.type)}}function wc(r,e,t){let n=f.EQUALS;if(e)n=e;else if(t.length>=2){let i=t.substring(0,2);i in Vn&&(t.length===2||t.at(2)?.match(/\d/))&&(n=i,t=t.substring(i.length))}return{code:r,operator:n,value:t}}function Ac(r){let e=r.substring(0,2),t=Vn[e];return t?{operator:t,value:r.substring(2)}:{operator:f.EQUALS,value:r}}function Oc(r){return ao[r]??f.EQUALS}function io(r){let e=r.split(":");if(e.includes("*"))throw new d(b("'*' is not supported as a value for search inclusion parameters"));if(e.length===1)throw new d(b(`Invalid include value '${r}': must be of the form ResourceType:search-parameter`));if(e.length===2)return{resourceType:e[0],searchParam:e[1]};if(e.length===3)return{resourceType:e[0],searchParam:e[1],targetType:e[2]};throw new d(b(`Invalid include value '${r}'`))}function oo(r,e){switch(r.type){case"date":return we(e);default:return!0}}var Ic=/{{([^{}]+)}}/g;function kc(r,e){return r=r.replaceAll(Ic,(t,n)=>{let i=P(n,[],e);return i.length!==1?"":kn(i[0])}),He(r)}function Dc(r){let e=[];return r.fields&&e.push("_fields="+r.fields.join(",")),r.filters&&r.filters.forEach(t=>e.push(Vc(t))),r.sortRules&&r.sortRules.length>0&&e.push(Mc(r.sortRules)),r.cursor!==void 0&&e.push("_cursor="+encodeURIComponent(r.cursor)),r.offset!==void 0&&r.offset!==0&&e.push("_offset="+r.offset),r.count!==void 0&&e.push("_count="+r.count),r.total!==void 0&&e.push("_total="+r.total),r.types&&r.types.length>0&&e.push("_type="+r.types.join(",")),r.include&&r.include.forEach(t=>e.push(so("_include",t))),r.revInclude&&r.revInclude.forEach(t=>e.push(so("_revinclude",t))),e.length===0?"":(ut(e),"?"+e.join("&"))}function Vc(r){let e=r.operator in ao?":"+r.operator:"",t=r.operator!==f.EQUALS&&r.operator in Vn?r.operator:"";return`${r.code}${e}=${t}${encodeURIComponent(r.value)}`}function Mc(r){return"_sort="+r.map(e=>e.descending?"-"+e.code:e.code).join(",")}function so(r,e){return r+(e.modifier?":"+e.modifier:"")+"="+e.resourceType+":"+e.searchParam+(e.targetType?":"+e.targetType:"")}function Pt(r){let e=[],t="",n=!1;for(let i of r)n?(t+=i,n=!1):i==="\\"?n=!0:i===","?(e.push(t),t=""):t+=i;return e.push(t),e}function wt(r,e){if(e.resourceType!==r.resourceType)return!1;if(e.filters){for(let t of e.filters)if(!_c(r,e,t))return!1}return!0}function _c(r,e,t){let n=_.types[e.resourceType]?.searchParams?.[t.code];if(!n)return!1;if(t.operator===f.MISSING||t.operator===f.PRESENT)return Lc(r,t,n);switch(n.type){case"reference":return Fc(r,t,n);case"string":case"uri":return uo(r,t,n);case"token":return Nc(r,t,n);case"date":return qc(r,t,n);default:return!1}}function Lc(r,e,t){let i=De(t.expression,r).length>0;return(e.operator===f.MISSING&&e.value==="false"||e.operator===f.PRESENT&&e.value==="true")===i}function Fc(r,e,t){let n=De(t.expression,r),i=vr(e.operator);if(e.value===""&&n.length===0)return e.operator===f.EQUALS;let o=n.map(s=>typeof s=="string"?s:s.reference);for(let s of Pt(e.value)){let a=o.includes(s);if(!a&&e.code==="_compartment"&&(a=o.some(c=>c?.endsWith("/"+s))),a)return!i}return i}function Nc(r,e,t){return gr(r.resourceType,t).type===q.BOOLEAN?Uc(r,e,t):uo(r,e,t,!0)}function Uc(r,e,t){let n=De(t.expression,r),i=e.value==="true",o=n.includes(i);return vr(e.operator)?!o:o}function uo(r,e,t,n){let o=gr(r.resourceType,t).elementDefinitions?.[0]?.type?.[0]?.code,s=De(t.expression,r),a=Pt(e.value),c=vr(e.operator);for(let u of s)for(let p of a){let m;if(o===l.Identifier?m=Bc(u,e.operator,p):o===l.CodeableConcept?m=Wc(u,e.operator,p):m=Mn(u,e.operator,p,n),m)return!c}return c}function Mn(r,e,t,n){if(n&&t.includes("|")){let[o,s]=t.split("|");return Mn(r,e,o,!1)&&(!s||Mn(r,e,s,!1))}let i="";return r&&(typeof r=="string"?i=r:typeof r=="object"&&(i=JSON.stringify(r))),i.toLowerCase().includes(t.toLowerCase())}function Bc(r,e,t){if(t.includes("|")){let[n,i]=t.split("|").map(o=>o.toLowerCase());return!n&&!i?!1:n?r.system?.toLowerCase()===n&&(!i||r.value?.toLowerCase()===i):!r.system&&r.value?.toLowerCase()===i}return r.value?.toLowerCase()===t.toLowerCase()}function Wc(r,e,t){if(t.includes("|")){let[n,i]=t.split("|").map(o=>o.toLowerCase());return!n&&!i?!1:n?r.coding?.some(o=>o.system?.toLowerCase()===n&&(!i||o.code?.toLowerCase()===i))??!1:r.coding?.some(o=>!o.system&&o.code?.toLowerCase()===i)??!1}return r.text?.toLowerCase()===t.toLowerCase()||(r.coding?.some(n=>n.code?.toLowerCase()===t.toLowerCase())??!1)}function qc(r,e,t){let n=De(t.expression,r),i=Pt(e.value),o=vr(e.operator);for(let s of n)for(let a of i)if(jc(xr(s),e.operator,xr(a)))return!o;return o}function jc(r,e,t){if(!r)return!1;switch(e){case f.STARTS_AFTER:case f.GREATER_THAN:return r>t;case f.GREATER_THAN_OR_EQUALS:return r>=t;case f.ENDS_BEFORE:case f.LESS_THAN:return r<t;case f.LESS_THAN_OR_EQUALS:return r<=t;case f.EQUALS:case f.NOT_EQUALS:return r===t;default:return!1}}function xr(r){if(ar(r))try{return new Date(r).toISOString()}catch(e){console.debug("Failed to parse date",r,e)}else if(ot(r)){if("start"in r)return xr(r.start);if("end"in r)return xr(r.end)}}function vr(r){return r===f.NOT_EQUALS||r===f.NOT}var $c={resourceType:"*"},lo=["DomainConfiguration","JsonWebKey","Login"],po=["PasswordChangeRequest","UserSecurityRequest","Project","ProjectMembership","User"],xe={READ:"read",VREAD:"vread",UPDATE:"update",DELETE:"delete",HISTORY:"history",CREATE:"create",SEARCH:"search"},fo=[xe.READ,xe.VREAD,xe.HISTORY,xe.SEARCH];function Gc(r,e){return _n(r,xe.SEARCH,e)}function Hc(r,e){return lo.includes(e)?!1:_n(r,xe.UPDATE,e)}function _n(r,e,t){return!!r.resource?.some(n=>mo(n,t,e))}function Qc(r,e){return!!ho(e,xe.UPDATE,r)}function ho(r,e,t){return t?t.resource?.find(n=>zc(r,e,n)):$c}function zc(r,e,t){let n=r.resourceType;return!(!mo(t,n,e)||t.compartment&&!r.meta?.compartment?.some(i=>i.reference===t.compartment?.reference)||t.criteria&&!wt(r,He(t.criteria)))}function mo(r,e,t){return r.resourceType!==e&&(r.resourceType!=="*"||po.includes(e))?!1:r.interaction?r.interaction.includes(t):!r.readonly||fo.includes(t)}function yo(r){if(typeof window<"u"){let e=window.atob(r),t=Uint8Array.from(e,n=>n.charCodeAt(0));return new window.TextDecoder().decode(t)}if(typeof Buffer<"u")return Buffer.from(r,"base64").toString("utf-8");throw new Error("Unable to decode base64")}function At(r){if(typeof window<"u"){let e=new window.TextEncoder().encode(r),t=String.fromCharCode.apply(null,e);return window.btoa(t)}if(typeof Buffer<"u")return Buffer.from(r,"utf8").toString("base64");throw new Error("Unable to encode base64")}function Jc(r){return At(r).replace(/\+/g,"-").replace(/\//g,"_").replace(/[=]{1,2}$/,"")}function Ln(r){r=r.padEnd(r.length+(4-r.length%4)%4,"=");let e=r.replace(/-/g,"+").replace(/_/g,"/");return yo(e)}function Tr(){let r=new Uint32Array(28);return crypto.getRandomValues(r),En(r.buffer)}async function Fn(r){return crypto.subtle.digest("SHA-256",new TextEncoder().encode(r))}function ve(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,r=>{let e=Math.random()*16|0;return(r==="x"?e:e&3|8).toString(16)})}function go(r){let e={};r=he(r);for(let i of r.entry||[]){let o=i.resource;if(!o)continue;o.meta!==void 0&&(delete o.meta.author,delete o.meta.compartment,delete o.meta.lastUpdated,delete o.meta.project,delete o.meta.versionId,Object.keys(o.meta).length===0&&delete o.meta);let s=o?.id;s&&(e[s]=ve(),i.fullUrl="urn:uuid:"+e[s],delete i.resource?.id)}let t=r.entry,n=JSON.stringify({resourceType:"Bundle",type:"transaction",entry:t?.map(i=>({fullUrl:i.fullUrl,request:{method:"POST",url:i.resource?.resourceType},resource:i.resource}))},(i,o)=>Kc(i,o,e),2);return xo(JSON.parse(n))}function Kc(r,e,t){if(r==="reference"&&typeof e=="string"){let n;if(e.includes("/")?n=e.split("/")[1]:e.startsWith("urn:uuid:")?n=e.slice(9):e.startsWith("#")&&(n=e.slice(1)),n){let i=t[n];if(i)return"urn:uuid:"+i}}return e}function xo(r){let e=Yc(r),{sorted:t,cycles:n}=Xc(e),i={};for(let s of r.entry??[])s.fullUrl&&(i[s.fullUrl]=s);let o=t.map(s=>i[s]);for(let s of n)for(let a of s){let c=i[a],u={...c,request:{...c.request,method:"PUT"}};o.push(u)}return{...r,entry:o}}var Qe={NotVisited:"NotVisited",Visiting:"Visiting",Visited:"Visited"};function Xc(r){let e=[],t={},n=[];for(let o of Object.keys(r))t[o]=Qe.NotVisited;function i(o,s){if(t[o]===Qe.Visited)return!0;if(t[o]===Qe.Visiting){let c=s.lastIndexOf(o);return c!==-1&&n.push(s.slice(c)),!0}t[o]=Qe.Visiting,s.push(o);let a=!1;for(let c of r[o])i(c,s)||(a=!0);return t[o]=Qe.Visited,s.pop(),e.unshift(o),!a}for(let o in r)t[o]===Qe.NotVisited&&i(o,[]);return{sorted:e,cycles:n}}function vo(r,e){for(let t in r)if(r[t]&&typeof r[t]=="object"){let n=r[t];if(z(n)){let i=n.reference;i.startsWith("urn:uuid:")&&e(i)}else vo(n,e)}}function Yc(r){let e={};for(let t of r.entry||[])t.fullUrl&&(e[t.fullUrl]=[]);for(let t of r.entry||[]){let n=t.fullUrl;t.resource&&vo(t.resource,i=>{e[i]&&e[i].push(n)})}return e}function Zc(r){r=he(r);let e={resourceType:"Bundle",type:"transaction",entry:[{resource:r}]};if(r.contained){for(let t of r.contained)e.entry.push({resource:t});r.contained=void 0}for(let t of e.entry)t.resource&&!t.resource.id&&(t.resource.id=ve());return go(e)}var w={CSS:"text/css",DICOM:"application/dicom",FAVICON:"image/vnd.microsoft.icon",FHIR_JSON:"application/fhir+json",FORM_URL_ENCODED:"application/x-www-form-urlencoded",HL7_V2:"x-application/hl7-v2+er7",HTML:"text/html",JAVASCRIPT:"text/javascript",JSON:"application/json",JSON_PATCH:"application/json-patch+json",JWT:"application/jwt",MULTIPART_FORM_DATA:"multipart/form-data",PNG:"image/png",SCIM_JSON:"application/scim+json",SVG:"image/svg+xml",TEXT:"text/plain",TYPESCRIPT:"text/typescript",PING:"x-application/ping",XML:"text/xml",CDA_XML:"application/cda+xml"};var Sr=class{constructor(){this.listeners={}}addEventListener(e,t){this.listeners[e]||(this.listeners[e]=[]),this.listeners[e].push(t)}removeEventListener(e,t){let n=this.listeners[e];if(n){for(let i=0;i<n.length;i++)if(n[i]===t){n.splice(i,1);return}}}dispatchEvent(e){let t=this.listeners[e.type];if(t)for(let n of t)n.call(this,e);return!e.defaultPrevented}removeAllListeners(){this.listeners={}}},G=class{constructor(){this.emitter=new Sr}dispatchEvent(e){this.emitter.dispatchEvent(e)}addEventListener(e,t){this.emitter.addEventListener(e,t)}removeEventListener(e,t){this.emitter.removeEventListener(e,t)}removeAllListeners(){this.emitter.removeAllListeners()}};var br={"Patient-open":"Patient-open","Patient-close":"Patient-close","ImagingStudy-open":"ImagingStudy-open","ImagingStudy-close":"ImagingStudy-close","Encounter-open":"Encounter-open","Encounter-close":"Encounter-close","DiagnosticReport-open":"DiagnosticReport-open","DiagnosticReport-close":"DiagnosticReport-close","DiagnosticReport-select":"DiagnosticReport-select","DiagnosticReport-update":"DiagnosticReport-update",syncerror:"syncerror"},To=["Patient","Encounter","ImagingStudy","DiagnosticReport","OperationOutcome","Bundle"],Er=["DiagnosticReport-update"];function Nn(r){return Er.includes(r)}function Un(r){if(Er.includes(r))throw new d(y(`'context.version' is required for '${r}'.`))}var So={"Patient-open":{patient:{resourceType:"Patient"},encounter:{resourceType:"Encounter",optional:!0}},"Patient-close":{patient:{resourceType:"Patient"},encounter:{resourceType:"Encounter",optional:!0}},"ImagingStudy-open":{study:{resourceType:"ImagingStudy"},encounter:{resourceType:"Encounter",optional:!0},patient:{resourceType:"Patient",optional:!0}},"ImagingStudy-close":{study:{resourceType:"ImagingStudy"},encounter:{resourceType:"Encounter",optional:!0},patient:{resourceType:"Patient",optional:!0}},"Encounter-open":{encounter:{resourceType:"Encounter"},patient:{resourceType:"Patient"}},"Encounter-close":{encounter:{resourceType:"Encounter"},patient:{resourceType:"Patient"}},"DiagnosticReport-open":{report:{resourceType:"DiagnosticReport"},encounter:{resourceType:"Encounter",optional:!0},study:{resourceType:"ImagingStudy",optional:!0,manyAllowed:!0},patient:{resourceType:"Patient"}},"DiagnosticReport-close":{report:{resourceType:"DiagnosticReport"},encounter:{resourceType:"Encounter",optional:!0},study:{resourceType:"ImagingStudy",optional:!0,manyAllowed:!0},patient:{resourceType:"Patient"}},"DiagnosticReport-select":{report:{resourceType:"DiagnosticReport",reference:!0},patient:{resourceType:"Patient",optional:!0,reference:!0},select:{resourceType:"*",reference:!0,manyAllowed:!0}},"DiagnosticReport-update":{report:{resourceType:"DiagnosticReport",reference:!0},patient:{resourceType:"Patient",optional:!0,reference:!0},updates:{resourceType:"Bundle"}},syncerror:{operationoutcome:{resourceType:"OperationOutcome"}}};function bo(r){return To.includes(r)}function Bn(r){return!!r.endpoint}function Rr(r){if(!It(r))throw new d(y("subscriptionRequest must be an object conforming to SubscriptionRequest type."));let{channelType:e,mode:t,topic:n,events:i}=r,o={"hub.channel.type":e,"hub.mode":t,"hub.topic":n,"hub.events":i.join(",")};return Bn(r)&&(o.endpoint=r.endpoint),new URLSearchParams(o).toString()}function It(r){if(typeof r!="object")return!1;let{channelType:e,mode:t,topic:n,events:i}=r;if(!(e&&t&&n&&i)||typeof n!="string"||typeof i!="object"||!Array.isArray(i)||i.length<1||e!=="websocket"||t!=="subscribe"&&t!=="unsubscribe")return!1;for(let o of i)if(!br[o])return!1;return!(Bn(r)&&!(typeof r.endpoint=="string"&&r.endpoint.startsWith("ws")))}function eu(r,e,t,n){if(typeof e!="object")throw new d(y(`context[${t}] is invalid. Context must contain a single valid FHIR resource! Resource is not an object.`));if(!(e.id&&typeof e.id=="string"))throw new d(y(`context[${t}] is invalid. Resource must contain a valid string ID.`));if(!e.resourceType)throw new d(y(`context[${t}] is invalid. Resource must contain a resource type. No resource type found.`));let i=n.resourceType;if(i!=="*"){if(!bo(e.resourceType))throw new d(y(`context[${t}] is invalid. Resource must contain a valid FHIRcast resource type. Resource type is not a known resource type.`));if(i&&e.resourceType!==i)throw new d(y(`context[${t}] is invalid. context[${t}] for the '${r}' event should contain resource of type ${i}.`))}}function tu(r,e,t,n,i){if(i.set(e.key,(i.get(e.key)??0)+1),n.reference){if(!z(e.reference))throw new d(y(`context[${t}] is invalid. Expected key '${e.key}' to be a reference.`))}else eu(r,e.resource,t,n)}function ru(r,e){let t=new Map,n=So[r];for(let i=0;i<e.length;i++){let o=e[i].key;if(!n[o])throw new d(y(`Key '${o}' not found for event '${r}'. Make sure to add only valid keys.`));tu(r,e[i],i,n[o],t)}for(let[i,o]of Object.entries(n)){if(!(o.optional||t.has(i)))throw new d(y(`Missing required key '${i}' on context for '${r}' event.`));if(!o.manyAllowed&&(t.get(i)??0)>1)throw new d(y(`${t.get(i)} context entries with key '${i}' found for the '${r}' event when schema only allows for 1.`))}}function Cr(r,e,t,n){if(!(r&&typeof r=="string"))throw new d(y("Must provide a topic."));if(!br[e])throw new d(y(`Must provide a valid FHIRcast event name. Supported events: ${Object.keys(br).join(", ")}`));if(typeof t!="object")throw new d(y("context must be a context object or array of context objects."));if(Er.includes(e)&&!n)throw new d(y(`The '${e}' event must contain a 'context.versionId'.`));let i=Array.isArray(t)?t:[t];return ru(e,i),{timestamp:new Date().toISOString(),id:ve(),event:{"hub.topic":r,"hub.event":e,context:i,...n?{"context.versionId":n}:{}}}}var Ot=class extends G{constructor(e){if(super(),this.subRequest=e,!e.endpoint)throw new d(y("Subscription request should contain an endpoint."));if(!It(e))throw new d(y("Subscription request failed validation."));let t=new WebSocket(e.endpoint);t.addEventListener("open",()=>{this.dispatchEvent({type:"connect"}),t.addEventListener("message",n=>{let i=JSON.parse(n.data);if(i["hub.topic"])return;let o=i;o.event["hub.event"]!=="heartbeat"&&(this.dispatchEvent({type:"message",payload:o}),t.send(JSON.stringify({id:i?.id,timestamp:new Date().toISOString()})))}),t.addEventListener("close",()=>{this.dispatchEvent({type:"disconnect"})})}),this.websocket=t}disconnect(){this.websocket.close()}};function nu(r){return JSON.parse(Ln(r))}function Wn(r){return r.split(".").length===3}function kt(r){let[e,t,n]=r.split(".");return nu(t)}function qn(r){try{return typeof kt(r).login_id=="string"}catch{return!1}}function jn(r){try{let t=kt(r).exp;return typeof t=="number"?t*1e3:void 0}catch{return}}var Dt=class{constructor(e){this.medplum=e}async get(e){return this.medplum.get(`keyvalue/v1/${e}`)}async set(e,t){await this.medplum.put(`keyvalue/v1/${e}`,t,w.TEXT)}async delete(e){await this.medplum.delete(`keyvalue/v1/${e}`)}};var Eo;Eo=Symbol.toStringTag;var V=class{constructor(e){this[Eo]="ReadablePromise";this.status="pending";this.suspender=e.then(t=>(this.status="success",this.response=t,t),t=>{throw this.status="error",this.error=t,t})}isPending(){return this.status==="pending"}isOk(){return this.status==="success"}read(){switch(this.status){case"pending":throw this.suspender;case"error":throw this.error;default:return this.response}}then(e,t){return this.suspender.then(e,t)}catch(e){return this.suspender.catch(e)}finally(e){return this.suspender.finally(e)}};var ze=class{constructor(e){this.storage=e??(typeof localStorage<"u"?localStorage:new Pr)}clear(){this.storage.clear()}getString(e){return this.storage.getItem(e)??void 0}setString(e,t){t?this.storage.setItem(e,t):this.storage.removeItem(e)}getObject(e){let t=this.getString(e);return t?JSON.parse(t):void 0}setObject(e,t){this.setString(e,t?ct(t):void 0)}},Pr=class{constructor(){this.data=new Map}get length(){return this.data.size}clear(){this.data.clear()}getItem(e){return this.data.get(e)??null}setItem(e,t){t?this.data.set(e,t):this.data.delete(e)}removeItem(e){this.data.delete(e)}key(e){return Array.from(this.data.keys())[e]}},$n=class extends ze{constructor(){super();this.initResolve=()=>{};this.initialized=!1,this.initPromise=new Promise(t=>{this.initResolve=t})}setInitialized(){this.initialized||(this.initResolve(),this.initialized=!0)}getInitPromise(){return this.initPromise}get isInitialized(){return this.initialized}};var Je={Event:typeof globalThis.Event<"u"?globalThis.Event:void 0,ErrorEvent:void 0,CloseEvent:void 0},Ro=!1;function iu(){if(typeof globalThis.Event>"u")throw new Error("Unable to lazy init events for ReconnectingWebSocket. globalThis.Event is not defined yet");Je.Event=globalThis.Event,Je.ErrorEvent=class extends Event{constructor(e,t){super("error",t),this.message=e.message,this.error=e}},Je.CloseEvent=class extends Event{constructor(t=1e3,n="",i){super("close",i);this.wasClean=!0;this.code=t,this.reason=n}}}function Po(r,e){if(!r)throw new Error(e)}function wr(r){return new r.constructor(r.type,r)}var Ve={maxReconnectionDelay:1e4,minReconnectionDelay:1e3+Math.random()*4e3,minUptime:5e3,reconnectionDelayGrowFactor:1.3,connectionTimeout:4e3,maxRetries:1/0,maxEnqueuedMessages:1/0,startClosed:!1,debug:!1},Co=!1,Vt=class r extends G{constructor(t,n,i={}){Ro||(iu(),Ro=!0);super();this._retryCount=-1;this._shouldReconnect=!0;this._connectLock=!1;this._closeCalled=!1;this._messageQueue=[];this._debugLogger=console.log.bind(console);this.onclose=null;this.onerror=null;this.onmessage=null;this.onopen=null;this._handleOpen=t=>{this._debug("open event");let{minUptime:n=Ve.minUptime}=this._options;clearTimeout(this._connectTimeout),this._uptimeTimeout=setTimeout(()=>this._acceptOpen(),n),Po(this._ws,"WebSocket is not defined"),this._ws.binaryType=this._binaryType,this._messageQueue.forEach(i=>this._ws?.send(i)),this._messageQueue=[],this.onopen&&this.onopen(t),this.dispatchEvent(wr(t))};this._handleMessage=t=>{this._debug("message event"),this.onmessage&&this.onmessage(t),this.dispatchEvent(wr(t))};this._handleError=t=>{this._debug("error event",t.message),this._disconnect(void 0,t.message==="TIMEOUT"?"timeout":void 0),this.onerror&&this.onerror(t),this._debug("exec error listeners"),this.dispatchEvent(wr(t)),this._connect()};this._handleClose=t=>{this._debug("close event"),this._clearTimeouts(),this._shouldReconnect&&this._connect(),this.onclose&&this.onclose(t),this.dispatchEvent(wr(t))};this._url=t,this._protocols=n,this._options=i,this._options.startClosed&&(this._shouldReconnect=!1),this._options.binaryType?this._binaryType=this._options.binaryType:this._binaryType="blob",this._options.debugLogger&&(this._debugLogger=this._options.debugLogger),this._connect()}static get CONNECTING(){return 0}static get OPEN(){return 1}static get CLOSING(){return 2}static get CLOSED(){return 3}get CONNECTING(){return r.CONNECTING}get OPEN(){return r.OPEN}get CLOSING(){return r.CLOSING}get CLOSED(){return r.CLOSED}get binaryType(){return this._ws?this._ws.binaryType:this._binaryType}set binaryType(t){this._binaryType=t,this._ws&&(this._ws.binaryType=t)}get retryCount(){return Math.max(this._retryCount,0)}get bufferedAmount(){return this._messageQueue.reduce((n,i)=>(typeof i=="string"?n+=i.length:i instanceof Blob?n+=i.size:n+=i.byteLength,n),0)+(this._ws?.bufferedAmount??0)}get extensions(){return this._ws?.extensions??""}get protocol(){return this._ws?.protocol??""}get readyState(){return this._ws?this._ws.readyState:this._options.startClosed?r.CLOSED:r.CONNECTING}get url(){return this._ws?this._ws.url:""}get shouldReconnect(){return this._shouldReconnect}close(t=1e3,n){if(this._closeCalled=!0,this._shouldReconnect=!1,this._clearTimeouts(),!this._ws){this._debug("close enqueued: no ws instance");return}if(this._ws.readyState===this.CLOSED){this._debug("close: already closed");return}this._ws.close(t,n)}reconnect(t,n){this._shouldReconnect=!0,this._closeCalled=!1,this._retryCount=-1,!this._ws||this._ws.readyState===this.CLOSED?this._connect():(this._disconnect(t,n),this._connect())}send(t){if(this._ws&&this._ws.readyState===this.OPEN)this._debug("send",t),this._ws.send(t);else{let{maxEnqueuedMessages:n=Ve.maxEnqueuedMessages}=this._options;this._messageQueue.length<n&&(this._debug("enqueue",t),this._messageQueue.push(t))}}_debug(...t){this._options.debug&&this._debugLogger("RWS>",...t)}_getNextDelay(){let{reconnectionDelayGrowFactor:t=Ve.reconnectionDelayGrowFactor,minReconnectionDelay:n=Ve.minReconnectionDelay,maxReconnectionDelay:i=Ve.maxReconnectionDelay}=this._options,o=0;return this._retryCount>0&&(o=n*Math.pow(t,this._retryCount-1),o>i&&(o=i)),this._debug("next delay",o),o}_wait(){return new Promise(t=>{setTimeout(t,this._getNextDelay())})}_connect(){if(this._connectLock||!this._shouldReconnect)return;this._connectLock=!0;let{maxRetries:t=Ve.maxRetries,connectionTimeout:n=Ve.connectionTimeout}=this._options;if(this._retryCount>=t){this._debug("max retries reached",this._retryCount,">=",t);return}this._retryCount++,this._debug("connect",this._retryCount),this._removeListeners(),this._wait().then(()=>{if(this._closeCalled){this._connectLock=!1;return}!this._options.WebSocket&&typeof WebSocket>"u"&&!Co&&(console.error("\u203C\uFE0F No WebSocket implementation available. You should define options.WebSocket."),Co=!0);let i=this._options.WebSocket||WebSocket;this._debug("connect",{url:this._url,protocols:this._protocols}),this._ws=this._protocols?new i(this._url,this._protocols):new i(this._url),this._ws.binaryType=this._binaryType,this._connectLock=!1,this._addListeners(),this._connectTimeout=setTimeout(()=>this._handleTimeout(),n)}).catch(i=>{this._connectLock=!1,this._handleError(new Je.ErrorEvent(Error(i.message),this))})}_handleTimeout(){this._debug("timeout event"),this._handleError(new Je.ErrorEvent(Error("TIMEOUT"),this))}_disconnect(t=1e3,n){if(this._clearTimeouts(),!!this._ws){this._removeListeners();try{this._ws.close(t,n),this._handleClose(new Je.CloseEvent(t,n,this))}catch{}}}_acceptOpen(){this._debug("accept open"),this._retryCount=0}_removeListeners(){this._ws&&(this._debug("removeListeners"),this._ws.removeEventListener("open",this._handleOpen),this._ws.removeEventListener("close",this._handleClose),this._ws.removeEventListener("message",this._handleMessage),this._ws.removeEventListener("error",this._handleError))}_addListeners(){this._ws&&(this._debug("addListeners"),this._ws.addEventListener("open",this._handleOpen),this._ws.addEventListener("close",this._handleClose),this._ws.addEventListener("message",this._handleMessage),this._ws.addEventListener("error",this._handleError))}_clearTimeouts(){clearTimeout(this._connectTimeout),clearTimeout(this._uptimeTimeout)}};var ou=5e3,Ke=class extends G{constructor(...e){super(),this.criteria=new Set(e)}getCriteria(){return this.criteria}_addCriteria(e){this.criteria.add(e)}_removeCriteria(e){this.criteria.delete(e)}},Gn=class{constructor(e,t){this.connecting=!1;this.criteria=e,this.emitter=new Ke(e),this.refCount=1,this.subscriptionProps=t?{...t}:void 0}clearAttachedSubscription(){this.subscriptionId=void 0,this.token=void 0}},Mt=class{constructor(e,t,n){this.pingTimer=void 0;this.waitingForPong=!1;if(!(e instanceof _t))throw new d(y("First arg of constructor should be a `MedplumClient`"));let i;try{i=new URL(t).toString()}catch{throw new d(y("Not a valid URL"))}let o=n?.ReconnectingWebSocket?new n.ReconnectingWebSocket(i,void 0,{debug:n?.debug,debugLogger:n?.debugLogger}):new Vt(i,void 0,{debug:n?.debug,debugLogger:n?.debugLogger});this.medplum=e,this.ws=o,this.masterSubEmitter=new Ke,this.criteriaEntries=new Map,this.criteriaEntriesBySubscriptionId=new Map,this.wsClosed=!1,this.pingIntervalMs=n?.pingIntervalMs??ou,this.currentProfile=e.getProfile(),this.setupListeners()}setupListeners(){let e=this.ws;e.addEventListener("message",t=>{try{let n=JSON.parse(t.data);if(n.type==="pong"){this.waitingForPong=!1;return}let i=n,o=i?.entry?.[0]?.resource;if(o.type==="heartbeat"){this.masterSubEmitter?.dispatchEvent({type:"heartbeat",payload:i});return}if(o.type==="handshake"){let a=fe(o.subscription),c={type:"connect",payload:{subscriptionId:a}};this.masterSubEmitter?.dispatchEvent(c);let u=this.criteriaEntriesBySubscriptionId.get(a);if(!u){console.warn("Received handshake for criteria the SubscriptionManager is not listening for yet");return}u.connecting=!1,u.emitter.dispatchEvent({...c});return}this.masterSubEmitter?.dispatchEvent({type:"message",payload:i});let s=this.criteriaEntriesBySubscriptionId.get(fe(o.subscription));if(!s){console.warn("Received notification for criteria the SubscriptionManager is not listening for");return}s.emitter.dispatchEvent({type:"message",payload:i})}catch(n){console.error(n);let i={type:"error",payload:n};this.masterSubEmitter?.dispatchEvent(i);for(let o of this.getAllCriteriaEmitters())o.dispatchEvent({...i})}}),e.addEventListener("error",()=>{let t={type:"error",payload:new d(Xr(new Error("WebSocket error")))};this.masterSubEmitter?.dispatchEvent(t);for(let n of this.getAllCriteriaEmitters())n.dispatchEvent({...t})}),e.addEventListener("close",()=>{let t={type:"close"};this.masterSubEmitter?.dispatchEvent(t);for(let n of this.getAllCriteriaEmitters())n.dispatchEvent({...t});this.pingTimer&&(clearInterval(this.pingTimer),this.pingTimer=void 0,this.waitingForPong=!1),this.wsClosed&&(this.criteriaEntries.clear(),this.criteriaEntriesBySubscriptionId.clear(),this.masterSubEmitter?.removeAllListeners())}),e.addEventListener("open",()=>{let t={type:"open"};this.masterSubEmitter?.dispatchEvent(t);for(let n of this.getAllCriteriaEmitters())n.dispatchEvent({...t});this.refreshAllSubscriptions().catch(console.error),this.pingTimer||(this.pingTimer=setInterval(()=>{if(this.waitingForPong){this.waitingForPong=!1,e.reconnect();return}e.send(JSON.stringify({type:"ping"})),this.waitingForPong=!0},this.pingIntervalMs))}),this.medplum.addEventListener("change",()=>{let t=this.medplum.getProfile();this.currentProfile&&t===void 0?this.ws.close():t&&this.currentProfile?.id!==t.id&&this.ws.reconnect(),this.currentProfile=t})}emitError(e,t){let n={type:"error",payload:t};this.masterSubEmitter?.dispatchEvent(n),e.emitter.dispatchEvent({...n})}maybeEmitDisconnect(e){let{subscriptionId:t}=e;if(t){let n={type:"disconnect",payload:{subscriptionId:t}};this.masterSubEmitter?.dispatchEvent(n),e.emitter.dispatchEvent({...n})}else console.warn("Called disconnect for `CriteriaEntry` before `subscriptionId` was present.")}async getTokenForCriteria(e){let t=e?.subscriptionId;t||(t=(await this.medplum.createResource({...e.subscriptionProps,resourceType:"Subscription",status:"active",reason:`WebSocket subscription for ${N(this.medplum.getProfile())}`,channel:{type:"websocket"},criteria:e.criteria})).id);let{parameter:n}=await this.medplum.get(`fhir/R4/Subscription/${t}/$get-ws-binding-token`),i=n?.find(s=>s.name==="token")?.valueString,o=n?.find(s=>s.name==="websocket-url")?.valueUrl;if(!i)throw new d(y("Failed to get token"));if(!o)throw new d(y("Failed to get URL from $get-ws-binding-token"));return[t,i]}maybeGetCriteriaEntry(e,t){let n=this.criteriaEntries.get(e);if(n){if(!t)return n.bareCriteria;for(let i of n.criteriaWithProps)if(ne(t,i.subscriptionProps))return i}}getAllCriteriaEmitters(){let e=[];for(let t of this.criteriaEntries.values()){t.bareCriteria&&e.push(t.bareCriteria.emitter);for(let n of t.criteriaWithProps)e.push(n.emitter)}return e}addCriteriaEntry(e){let{criteria:t,subscriptionProps:n}=e,i;this.criteriaEntries.has(t)?i=this.criteriaEntries.get(t):(i={criteriaWithProps:[]},this.criteriaEntries.set(t,i)),n?i.criteriaWithProps.push(e):i.bareCriteria=e}removeCriteriaEntry(e){let{criteria:t,subscriptionProps:n,subscriptionId:i,token:o}=e;if(!this.criteriaEntries.has(t))return;let s=this.criteriaEntries.get(t);n?s.criteriaWithProps=s.criteriaWithProps.filter(a=>{let c=a.subscriptionProps;return!ne(n,c)}):s.bareCriteria=void 0,!s.bareCriteria&&s.criteriaWithProps.length===0&&(this.criteriaEntries.delete(t),this.masterSubEmitter?._removeCriteria(t)),i&&this.criteriaEntriesBySubscriptionId.delete(i),o&&this.ws.readyState===WebSocket.OPEN&&this.ws.send(JSON.stringify({type:"unbind-from-token",payload:{token:o}}))}async subscribeToCriteria(e){if(!(this.ws.readyState!==WebSocket.OPEN||e.connecting)){e.connecting=!0;try{let[t,n]=await this.getTokenForCriteria(e);e.subscriptionId=t,e.token=n,this.criteriaEntriesBySubscriptionId.set(t,e),this.ws.send(JSON.stringify({type:"bind-with-token",payload:{token:n}}))}catch(t){console.error(Re(t)),this.emitError(e,t),this.removeCriteriaEntry(e)}}}async refreshAllSubscriptions(){this.criteriaEntriesBySubscriptionId.clear();for(let e of this.criteriaEntries.values())for(let t of[...e.bareCriteria?[e.bareCriteria]:[],...e.criteriaWithProps])t.clearAttachedSubscription(),await this.subscribeToCriteria(t)}addCriteria(e,t){this.masterSubEmitter&&this.masterSubEmitter._addCriteria(e);let n=this.maybeGetCriteriaEntry(e,t);if(n)return n.refCount+=1,n.emitter;let i=new Gn(e,t);return this.addCriteriaEntry(i),this.subscribeToCriteria(i).catch(console.error),i.emitter}removeCriteria(e,t){let n=this.maybeGetCriteriaEntry(e,t);if(!n){console.warn("Criteria not known to `SubscriptionManager`. Possibly called remove too many times.");return}n.refCount-=1,!(n.refCount>0)&&(this.maybeEmitDisconnect(n),this.removeCriteriaEntry(n))}getWebSocket(){return this.ws}closeWebSocket(){this.wsClosed||(this.wsClosed=!0,this.ws.close())}reconnectWebSocket(){this.ws.reconnect(),this.wsClosed=!1}getCriteriaCount(){return this.getAllCriteriaEmitters().length}getMasterEmitter(){return this.masterSubEmitter||(this.masterSubEmitter=new Ke(...Array.from(this.criteriaEntries.keys()))),this.masterSubEmitter}};async function su({resource:r,subscription:e,context:t,getPreviousResource:n,logger:i}){if(e.meta?.account&&r.meta?.account?.reference!==e.meta.account.reference)return i?.debug("Ignore resource in different account compartment"),!1;if(!au(e,i))return i?.debug("Ignore subscription without recognized channel type"),!1;let o=e.criteria;if(!o)return i?.debug("Ignore rest hook missing criteria"),!1;let s=He(o);if(r.resourceType!==s.resourceType)return i?.debug(`Ignore rest hook for different resourceType (wanted "${s.resourceType}", received "${r.resourceType}")`),!1;if(!await wo(e,r,n))return i?.debug("Ignore rest hook for criteria returning false"),!1;let c=re(e,"https://medplum.com/fhir/StructureDefinition/subscription-supported-interaction");return c&&c.valueCode!==t.interaction?(i?.debug(`Ignore rest hook for different interaction (wanted "${c.valueCode}", received "${t.interaction}")`),!1):wt(r,s)}function au(r,e){let t=r.channel?.type;return t==="rest-hook"?r.channel?.endpoint?!0:(e?.debug("Ignore rest-hook missing URL"),!1):t==="websocket"}async function wo(r,e,t){let n=re(r,"https://medplum.com/fhir/StructureDefinition/fhir-path-criteria-expression");if(!n?.valueString)return!0;let i=await t(e),o={"%current":v(e),"%previous":v(i??{})};return P(n.valueString,[v(e)],o)?.[0]?.value===!0}var Ar="4.2.0-3947c0375",cu="medplum-cli",Do=w.FHIR_JSON+", */*; q=0.1",uu="https://api.medplum.com/",lu=1e3,pu=6e4,du=0,fu=3e5,hu="Binary/",Ao={resourceType:"Device",id:"system",deviceName:[{type:"model-name",name:"System"}]},Me={ClientCredentials:"client_credentials",AuthorizationCode:"authorization_code",RefreshToken:"refresh_token",JwtBearer:"urn:ietf:params:oauth:grant-type:jwt-bearer",TokenExchange:"urn:ietf:params:oauth:grant-type:token-exchange"},Vo={AccessToken:"urn:ietf:params:oauth:token-type:access_token",RefreshToken:"urn:ietf:params:oauth:token-type:refresh_token",IdToken:"urn:ietf:params:oauth:token-type:id_token",Saml1Token:"urn:ietf:params:oauth:token-type:saml1",Saml2Token:"urn:ietf:params:oauth:token-type:saml2"},mu={ClientSecretBasic:"client_secret_basic",ClientSecretPost:"client_secret_post",ClientSecretJwt:"client_secret_jwt",PrivateKeyJwt:"private_key_jwt",None:"none"},Mo={JwtBearer:"urn:ietf:params:oauth:client-assertion-type:jwt-bearer"},_t=class extends G{constructor(t){super();this.initComplete=!0;if(t?.baseUrl&&!t.baseUrl.startsWith("http"))throw new Error("Base URL must start with http or https");this.options=t??{},this.fetch=t?.fetch??yu(),this.storage=t?.storage??new ze,this.createPdfImpl=t?.createPdf,this.baseUrl=pr(t?.baseUrl??uu),this.fhirBaseUrl=U(this.baseUrl,t?.fhirUrlPath??"fhir/R4"),this.authorizeUrl=U(this.baseUrl,t?.authorizeUrl??"oauth2/authorize"),this.tokenUrl=U(this.baseUrl,t?.tokenUrl??"oauth2/token"),this.logoutUrl=U(this.baseUrl,t?.logoutUrl??"oauth2/logout"),this.fhircastHubUrl=U(this.baseUrl,t?.fhircastHubUrl??"fhircast/STU3"),this.clientId=t?.clientId??"",this.clientSecret=t?.clientSecret??"",this.credentialsInHeader=t?.authCredentialsMethod==="header",this.defaultHeaders=t?.defaultHeaders??{},this.onUnauthenticated=t?.onUnauthenticated,this.refreshGracePeriod=t?.refreshGracePeriod??fu,this.cacheTime=t?.cacheTime??(typeof window>"u"?du:pu),this.cacheTime>0?this.requestCache=new Se(t?.resourceCacheSize??lu):this.requestCache=void 0,t?.autoBatchTime?(this.autoBatchTime=t.autoBatchTime,this.autoBatchQueue=[]):(this.autoBatchTime=0,this.autoBatchQueue=void 0),t?.accessToken&&this.setAccessToken(t.accessToken),this.storage.getInitPromise===void 0?(t?.accessToken||this.attemptResumeActiveLogin().catch(console.error),this.initPromise=Promise.resolve(),this.dispatchEvent({type:"storageInitialized"})):(this.initComplete=!1,this.initPromise=this.storage.getInitPromise(),this.initPromise.then(()=>{t?.accessToken||this.attemptResumeActiveLogin().catch(console.error),this.initComplete=!0,this.dispatchEvent({type:"storageInitialized"})}).catch(n=>{console.error(n),this.initComplete=!0,this.dispatchEvent({type:"storageInitFailed",payload:{error:n}})})),this.setupStorageListener()}get isInitialized(){return this.initComplete}getInitPromise(){return this.initPromise}async attemptResumeActiveLogin(){let t=this.getActiveLogin();t&&(this.setAccessToken(t.accessToken,t.refreshToken),await this.refreshProfile())}getBaseUrl(){return this.baseUrl}getAuthorizeUrl(){return this.authorizeUrl}getTokenUrl(){return this.tokenUrl}getLogoutUrl(){return this.logoutUrl}getFhircastHubUrl(){return this.fhircastHubUrl}getDefaultHeaders(){return this.defaultHeaders}clear(){this.storage.clear(),typeof window<"u"&&sessionStorage.clear(),this.clearActiveLogin()}clearActiveLogin(){this.storage.setString("activeLogin",void 0),this.requestCache?.clear(),this.accessToken=void 0,this.refreshToken=void 0,this.refreshPromise=void 0,this.accessTokenExpires=void 0,this.sessionDetails=void 0,this.medplumServer=void 0,this.dispatchEvent({type:"change"})}invalidateUrl(t){t=t.toString(),this.requestCache?.delete(t)}invalidateAll(){this.requestCache?.clear()}invalidateSearches(t){let n=U(this.fhirBaseUrl,t);if(this.requestCache)for(let i of this.requestCache.keys())(i.endsWith(n)||i.includes(n+"?"))&&this.requestCache.delete(i)}get(t,n={}){t=t.toString();let i=this.getCacheEntry(t,n);if(i)return i.value;let o;t.startsWith(this.fhirBaseUrl)&&this.autoBatchQueue&&!n.disableAutoBatch?o=new Promise((a,c)=>{this.autoBatchQueue.push({method:"GET",url:t.replace(this.fhirBaseUrl,""),options:n,resolve:a,reject:c}),this.autoBatchTimerId||(this.autoBatchTimerId=setTimeout(()=>this.executeAutoBatch(),this.autoBatchTime))}):o=this.request("GET",t,n);let s=new V(o);return this.setCacheEntry(t,s),s}post(t,n,i,o={}){return t=t.toString(),this.setRequestBody(o,n),i&&this.setRequestContentType(o,i),this.invalidateUrl(t),this.request("POST",t,o)}put(t,n,i,o={}){return t=t.toString(),this.setRequestBody(o,n),i&&this.setRequestContentType(o,i),this.invalidateUrl(t),this.request("PUT",t,o)}patch(t,n,i={}){return t=t.toString(),this.setRequestBody(i,n),this.setRequestContentType(i,w.JSON_PATCH),this.invalidateUrl(t),this.request("PATCH",t,i)}delete(t,n){return t=t.toString(),this.invalidateUrl(t),this.request("DELETE",t,n)}async startNewUser(t,n){let{codeChallengeMethod:i,codeChallenge:o}=await this.startPkce();return this.post("auth/newuser",{...t,clientId:t.clientId??this.clientId,codeChallengeMethod:i,codeChallenge:o},void 0,n)}async startNewProject(t,n){return this.post("auth/newproject",t,void 0,n)}async startNewPatient(t,n){return this.post("auth/newpatient",t,void 0,n)}async startLogin(t,n){return this.post("auth/login",{...await this.ensureCodeChallenge(t),clientId:t.clientId??this.clientId,scope:t.scope},void 0,n)}async startGoogleLogin(t,n){return this.post("auth/google",{...await this.ensureCodeChallenge(t),clientId:t.clientId??this.clientId,scope:t.scope},void 0,n)}async ensureCodeChallenge(t){return t.codeChallenge?t:{...t,...await this.startPkce()}}async signOut(){await this.post(this.logoutUrl,{}),this.clear()}async signInWithRedirect(t){let i=new URLSearchParams(window.location.search).get("code");if(!i){await this.requestAuthorization(t);return}return this.processCode(i)}signOutWithRedirect(){window.location.assign(this.logoutUrl)}async signInWithExternalAuth(t,n,i,o,s=!0){let a=o;s&&(a=await this.ensureCodeChallenge(o)),window.location.assign(this.getExternalAuthRedirectUri(t,n,i,a,s))}async exchangeExternalAccessToken(t,n){if(n=n??this.clientId,!n)throw new Error("MedplumClient is missing clientId");return this.fetchTokens({grant_type:Me.TokenExchange,subject_token_type:Vo.AccessToken,client_id:n,subject_token:t})}getExternalAuthRedirectUri(t,n,i,o,s=!0){let a=new URL(t);if(a.searchParams.set("response_type","code"),a.searchParams.set("client_id",n),a.searchParams.set("redirect_uri",i),a.searchParams.set("scope",o.scope??"openid profile email"),a.searchParams.set("state",JSON.stringify(o)),s){let{codeChallenge:c,codeChallengeMethod:u}=o;if(!u)throw new Error("`LoginRequest` for external auth must include a `codeChallengeMethod`.");if(!c)throw new Error("`LoginRequest` for external auth must include a `codeChallenge`.");a.searchParams.set("code_challenge_method",u),a.searchParams.set("code_challenge",c)}return a.toString()}fhirUrl(...t){return new URL(U(this.fhirBaseUrl,t.join("/")))}fhirSearchUrl(t,n){let i=this.fhirUrl(t);return n&&(i.search=On(n)),i}search(t,n,i){let o=this.fhirSearchUrl(t,n),s="search-"+o.toString(),a=this.getCacheEntry(s,i);if(a)return a.value;let c=this.getBundle(o,i);return this.setCacheEntry(s,c),c}searchOne(t,n,i){let o=this.fhirSearchUrl(t,n);o.searchParams.set("_count","1"),o.searchParams.sort();let s="searchOne-"+o.toString(),a=this.getCacheEntry(s,i);if(a)return a.value;let c=new V(this.search(t,o.searchParams,i).then(u=>u.entry?.[0]?.resource));return this.setCacheEntry(s,c),c}searchResources(t,n,i){let s="searchResources-"+this.fhirSearchUrl(t,n).toString(),a=this.getCacheEntry(s,i);if(a)return a.value;let c=new V(this.search(t,n,i).then(ko));return this.setCacheEntry(s,c),c}async*searchResourcePages(t,n,i){let o=this.fhirSearchUrl(t,n);for(;o;){let s=new URL(o).searchParams;s.has("_count")||s.set("_count","1000");let a=await this.search(t,s,i),c=a.link?.find(u=>u.relation==="next");if(!a.entry?.length&&!c)break;yield ko(a),o=c?.url?new URL(c.url):void 0}}valueSetExpand(t,n){let i=this.fhirUrl("ValueSet","$expand");return i.search=new URLSearchParams(t).toString(),this.get(i.toString(),n)}getCached(t,n){let i=this.requestCache?.get(this.fhirUrl(t,n).toString())?.value;return i?.isOk()?i.read():void 0}getCachedReference(t){let n=t.reference;if(!n)return;if(n==="system")return Ao;let[i,o]=n.split("/");if(!(!i||!o))return this.getCached(i,o)}readResource(t,n,i){if(!n)throw new Error('The "id" parameter cannot be null, undefined, or an empty string.');return this.get(this.fhirUrl(t,n),i)}readReference(t,n){let i=t.reference;if(!i)return new V(Promise.reject(new Error("Missing reference")));if(i==="system")return new V(Promise.resolve(Ao));let[o,s]=i.split("/");return!o||!s?new V(Promise.reject(new Error("Invalid reference"))):this.readResource(o,s,n)}requestSchema(t){if(an(t))return Promise.resolve();let n=t+"-requestSchema",i=this.getCacheEntry(n,void 0);if(i)return i.value;let o=new V((async()=>{let s=`{
      StructureDefinitionList(_filter: "name eq ${t}") {
        resourceType,
        name,
        kind,
        description,
        type,
        url,
        snapshot {
          element {
            id,
            path,
            definition,
            min,
            max,
            base {
              path,
              min,
              max
            },
            contentReference,
            type {
              code,
              profile,
              targetProfile
            },
            binding {
              strength,
              valueSet
            }
          }
        }
      }
      SearchParameterList(base: "${t}", _count: 100) {
        base,
        code,
        type,
        expression,
        target
      }
    }`.replace(/\s+/g," "),a=await this.graphql(s);Jt(a.data.StructureDefinitionList);for(let c of a.data.SearchParameterList)hr(c)})());return this.setCacheEntry(n,o),o}requestProfileSchema(t,n){if(!n?.expandProfile&&un(t))return Promise.resolve();let i=t+"-requestSchema"+(n?.expandProfile?"-nested":""),o=this.getCacheEntry(i,void 0);if(o)return o.value;let s=new V((async()=>{if(n?.expandProfile){let a=this.fhirUrl("StructureDefinition","$expand-profile");a.search=new URLSearchParams({url:t}).toString();let c=await this.post(a.toString(),{});Jt(c)}else{let a=await this.searchOne("StructureDefinition",{url:t,_sort:"-_lastUpdated"});if(!a){console.warn(`No StructureDefinition found for ${t}!`);return}Kt(a)}})());return this.setCacheEntry(i,s),s}readHistory(t,n,i){return this.get(this.fhirUrl(t,n,"_history"),i)}readVersion(t,n,i,o){return this.get(this.fhirUrl(t,n,"_history",i),o)}readPatientEverything(t,n){return this.getBundle(this.fhirUrl("Patient",t,"$everything"),n)}readPatientSummary(t,n){return this.getBundle(this.fhirUrl("Patient",t,"$summary"),n)}createResource(t,n){if(!t.resourceType)throw new Error("Missing resourceType");return this.invalidateSearches(t.resourceType),this.post(this.fhirUrl(t.resourceType),t,void 0,n)}async createResourceIfNoneExist(t,n,i){let o=this.fhirUrl(t.resourceType);i?i.headers?Array.isArray(i.headers)?i.headers.push(["If-None-Exist",n]):i.headers instanceof Headers?i.headers.set("If-None-Exist",n):i.headers["If-None-Exist"]=n:i.headers={"If-None-Exist":n}:i={headers:{"If-None-Exist":n}};let s=await this.post(o,t,void 0,i);return this.cacheResource(s),this.invalidateUrl(this.fhirUrl(t.resourceType,t.id,"_history")),this.invalidateSearches(t.resourceType),s}async upsertResource(t,n,i){let o=this.fhirSearchUrl(t.resourceType,n),s=await this.put(o,t,void 0,i);return s||(s=t),this.cacheResource(s),this.invalidateUrl(this.fhirUrl(t.resourceType,t.id,"_history")),this.invalidateSearches(t.resourceType),s}async createAttachment(t,n,i,o,s){let a=Hn(t,n,i,o);if(a.contentType===w.XML){let p=a.data,m;p instanceof Blob?m=await new Promise((x,j)=>{let Q=new FileReader;Q.onload=()=>{if(!Q.result){j(new Error("Failed to load file"));return}x(Q.result)},Q.readAsText(p,"utf-8")}):ArrayBuffer.isView(p)?m=new TextDecoder().decode(p):m=p,m.includes("<ClinicalDocument")&&m.includes("urn:hl7-org:v3")&&(a={...a,contentType:w.CDA_XML})}let c=s??(typeof n=="object"?n:{}),u=await this.createBinary(a,c);return{contentType:a.contentType,url:u.url,title:a.filename}}createBinary(t,n,i,o,s){let a=Hn(t,n,i,o),c=s??(typeof n=="object"?n:{}),{data:u,contentType:p,filename:m,securityContext:x,onProgress:j}=a,Q=this.fhirUrl("Binary");return m&&Q.searchParams.set("_filename",m),x?.reference&&this.setRequestHeader(c,"X-Security-Context",x.reference),j?this.uploadwithProgress(Q,u,p,j,c):this.post(Q,u,p,c)}uploadwithProgress(t,n,i,o,s){return new Promise((a,c)=>{let u=new XMLHttpRequest,p=()=>u.abort();s?.signal?.addEventListener("abort",p);let m=x=>{s?.signal?.removeEventListener("abort",p),x instanceof Error?c(x):a(x)};if(u.responseType="json",u.onabort=()=>m(new DOMException("Request aborted","AbortError")),u.onerror=()=>m(new Error("Request error")),o&&(u.upload.onprogress=x=>o(x),u.upload.onload=x=>o(x)),u.onload=()=>{u.status>=200&&u.status<300?m(u.response):m(new d(et(u.response||u.statusText)))},u.open("POST",t),u.withCredentials=!0,u.setRequestHeader("Authorization","Bearer "+this.accessToken),u.setRequestHeader("Cache-Control","no-cache, no-store, max-age=0"),u.setRequestHeader("Content-Type",i),this.options.extendedMode!==!1&&u.setRequestHeader("X-Medplum","extended"),s?.headers){let x=s.headers;for(let[j,Q]of Object.entries(x))u.setRequestHeader(j,Q)}u.send(n)})}async createPdf(t,n,i,o){if(!this.createPdfImpl)throw new Error("PDF creation not enabled");let s=_o(t,n,i,o),a=typeof n=="object"?n:{},{docDefinition:c,tableLayouts:u,fonts:p,...m}=s,x=await this.createPdfImpl(c,u,p),j={...m,data:x,contentType:"application/pdf"};return this.createBinary(j,a)}createComment(t,n,i){let o=this.getProfile(),s,a;return t.resourceType==="Encounter"&&(s=de(t),a=t.subject),t.resourceType==="ServiceRequest"&&(s=t.encounter,a=t.subject),t.resourceType==="Patient"&&(a=de(t)),this.createResource({resourceType:"Communication",status:"completed",basedOn:[de(t)],encounter:s,subject:a,sender:o?de(o):void 0,sent:new Date().toISOString(),payload:[{contentString:n}]},i)}async updateResource(t,n){if(!t.resourceType)throw new Error("Missing resourceType");if(!t.id)throw new Error("Missing id");let i=await this.put(this.fhirUrl(t.resourceType,t.id),t,void 0,n);return i||(i=t),this.cacheResource(i),this.invalidateUrl(this.fhirUrl(t.resourceType,t.id,"_history")),this.invalidateSearches(t.resourceType),i}async patchResource(t,n,i,o){let s=await this.patch(this.fhirUrl(t,n),i,o);return this.cacheResource(s),this.invalidateUrl(this.fhirUrl(t,n,"_history")),this.invalidateSearches(t),s}deleteResource(t,n,i){return this.deleteCacheEntry(this.fhirUrl(t,n).toString()),this.invalidateSearches(t),this.delete(this.fhirUrl(t,n),i)}validateResource(t,n){return this.post(this.fhirUrl(t.resourceType,"$validate"),t,void 0,n)}executeBot(t,n,i,o){let s;if(typeof t=="string"){let a=t;s=this.fhirUrl("Bot",a,"$execute")}else{let a=t;s=this.fhirUrl("Bot","$execute"),s.searchParams.set("identifier",a.system+"|"+a.value)}return this.post(s,n,i,o)}executeBatch(t,n){return this.post(this.fhirBaseUrl,t,void 0,n)}sendEmail(t,n){return this.post("email/v1/send",t,w.JSON,n)}graphql(t,n,i,o){return this.post(this.fhirUrl("$graphql"),{query:t,operationName:n,variables:i},w.JSON,o)}readResourceGraph(t,n,i,o){return this.get(`${this.fhirUrl(t,n)}/$graph?graph=${i}`,o)}pushToAgent(t,n,i,o,s,a){return this.post(this.fhirUrl("Agent",fe(t),"$push"),{destination:typeof n=="string"?n:N(n),body:i,contentType:o,waitForResponse:s},w.FHIR_JSON,a)}getActiveLogin(){return this.storage.getObject("activeLogin")}async setActiveLogin(t){(!this.sessionDetails?.profile||N(this.sessionDetails.profile)!==t.profile?.reference)&&this.clearActiveLogin(),this.setAccessToken(t.accessToken,t.refreshToken),this.storage.setObject("activeLogin",t),this.addLogin(t),this.refreshPromise=void 0,await this.refreshProfile()}getAccessToken(){return this.accessToken}isAuthenticated(t){return this.accessTokenExpires!==void 0&&Date.now()<this.accessTokenExpires-(t??this.refreshGracePeriod)}setAccessToken(t,n){this.accessToken=t,this.refreshToken=n,this.accessTokenExpires=jn(t),this.medplumServer=qn(t)}getLogins(){return this.storage.getObject("logins")??[]}addLogin(t){let n=this.getLogins().filter(i=>i.profile?.reference!==t.profile?.reference);n.push(t),this.storage.setObject("logins",n)}async refreshProfile(){return this.medplumServer?(this.profilePromise=new Promise((t,n)=>{this.get("auth/me",{cache:"no-cache"}).then(i=>{this.profilePromise=void 0;let o=this.sessionDetails?.profile?.id!==i.profile.id;this.sessionDetails=i,o&&this.dispatchEvent({type:"change"}),t(i.profile),this.dispatchEvent({type:"profileRefreshed"})}).catch(n)}),this.dispatchEvent({type:"profileRefreshing"}),this.profilePromise):Promise.resolve(void 0)}isLoading(){return!this.isInitialized||!!this.profilePromise&&!this.sessionDetails?.profile}isSuperAdmin(){return!!this.sessionDetails?.project.superAdmin}isProjectAdmin(){return!!this.sessionDetails?.membership.admin}getProject(){return this.sessionDetails?.project}getProjectMembership(){return this.sessionDetails?.membership}getProfile(){return this.sessionDetails?.profile}async getProfileAsync(){return this.profilePromise?this.profilePromise:this.sessionDetails?this.sessionDetails.profile:this.refreshProfile()}getUserConfiguration(){return this.sessionDetails?.config}getAccessPolicy(){return this.sessionDetails?.accessPolicy}async download(t,n={}){this.refreshPromise&&await this.refreshPromise;let i=t.toString();i.startsWith(hu)&&(t=this.fhirUrl(i));let o=n.headers;return o||(o={},n.headers=o),o.Accept||(o.Accept="*/*"),this.addFetchOptionsDefaults(n),(await this.fetchWithRetry(t.toString(),n)).blob()}async createMedia(t,n){let{additionalFields:i,...o}=t,s=await this.createResource({resourceType:"Media",status:"preparation",content:{contentType:t.contentType},...i});o.securityContext||(o.securityContext=de(s));let a=await this.createAttachment(o,n);return this.updateResource({...s,status:"completed",content:a})}async uploadMedia(t,n,i,o,s){return this.createMedia({data:t,contentType:n,filename:i,additionalFields:o},s)}async createDocumentReference(t,n){let{additionalFields:i,...o}=t,s=await this.createResource({resourceType:"DocumentReference",status:"current",content:[{attachment:{contentType:t.contentType}}],...i});o.securityContext||(o.securityContext=de(s));let a=await this.createAttachment(o,n);return this.updateResource({...s,content:[{attachment:a}]})}async bulkExport(t="",n,i,o){let s=t&&`${t}/`,a=this.fhirUrl(`${s}$export`);return n&&a.searchParams.set("_type",n),i&&a.searchParams.set("_since",i),this.startAsyncRequest(a.toString(),o)}async startAsyncRequest(t,n={}){this.addFetchOptionsDefaults(n);let i=n.headers;return i.Prefer="respond-async",this.request("POST",t,n)}get keyValue(){return this.keyValueClient||(this.keyValueClient=new Dt(this)),this.keyValueClient}getBundle(t,n){return new V((async()=>{let i=await this.get(t,n);if(i.entry)for(let o of i.entry)this.cacheResource(o.resource);return i})())}getCacheEntry(t,n){if(!this.requestCache||n?.cache==="no-cache"||n?.cache==="reload")return;let i=this.requestCache.get(t);if(!(!i||i.requestTime+this.cacheTime<Date.now()))return i}setCacheEntry(t,n){this.requestCache&&this.requestCache.set(t,{requestTime:Date.now(),value:n})}cacheResource(t){t?.id&&!t.meta?.tag?.some(n=>n.code==="SUBSETTED")&&this.setCacheEntry(this.fhirUrl(t.resourceType,t.id).toString(),new V(Promise.resolve(t)))}deleteCacheEntry(t){this.requestCache&&this.requestCache.delete(t)}async request(t,n,i={},o={}){await this.refreshIfExpired(),i.method=t,this.addFetchOptionsDefaults(i);let s=await this.fetchWithRetry(n,i);if(s.status===401)return this.handleUnauthenticated(t,n,i);if(s.status===204||s.status===304)return;let c=s.headers.get("content-type")?.includes("json");if(s.status===404&&!c)throw new d(Jr);let u=await this.parseBody(s,c);if(s.status===200&&i.followRedirectOnOk||s.status===201&&i.followRedirectOnCreated){let p=await Io(s,u);if(p)return this.request("GET",p,{...i,body:void 0})}if(s.status===202&&i.pollStatusOnAccepted){let m=await Io(s,u)??o.statusUrl;if(m)return this.pollStatus(m,i,o)}if(s.status>=400)throw new d(et(u));return u}async parseBody(t,n){let i;if(t.headers.get("content-length")!=="0"){if(n)try{i=await t.json()}catch(o){throw console.error("Error parsing response",t.status,o),o}else i=await t.text();return i}}async fetchWithRetry(t,n){t.startsWith("http")||(t=U(this.baseUrl,t));let i=n?.maxRetries??2;for(let o=0;o<=i;o++)try{this.options.verbose&&this.logRequest(t,n);let s=await this.fetch(t,n);if(this.options.verbose&&this.logResponse(s),this.setCurrentRateLimit(s),o>=i||!vu(s))return s;let a=this.getRetryDelay(o),c=n.maxRetryTime??2e3;if(a>c)return s;await ur(a)}catch(s){if(s.message==="Failed to fetch"&&o===0&&this.dispatchEvent({type:"offline"}),s.name==="AbortError"||o===i)throw s}throw new Error("Unreachable")}logRequest(t,n){if(console.log(`> ${n.method} ${t}`),n.headers){let i=n.headers;for(let o of ut(Object.keys(i)))console.log(`> ${o}: ${i[o]}`)}}logResponse(t){console.log(`< ${t.status} ${t.statusText}`),t.headers&&t.headers.forEach((n,i)=>console.log(`< ${i}: ${n}`))}setCurrentRateLimit(t){let n=t.headers?.get("ratelimit");n&&(this.currentRateLimits=n)}rateLimitStatus(){if(!this.currentRateLimits)return[];let t=this.currentRateLimits;return t.split(/\s*;\s*/g).map(n=>{let i=n.split(/\s*,\s*/g);if(i.length!==3)throw new Error("Could not parse RateLimit header: "+t);let o=i[0].substring(1,i[0].length-1),s=i.find(p=>p.startsWith("r=")),a=s?parseInt(s.substring(2),10):NaN,c=i.find(p=>p.startsWith("t=")),u=c?parseInt(c.substring(2),10):NaN;if(!o||Number.isNaN(a)||Number.isNaN(u))throw new Error("Could not parse RateLimit header: "+t);return{name:o,remainingUnits:a,secondsUntilReset:u}})}getRetryDelay(t){let n=this.rateLimitStatus(),i=500*Math.pow(1.5,t);for(let o of n)o.remainingUnits||(i=Math.max(i,o.secondsUntilReset*1e3));return i}async pollStatus(t,n,i){let o={...n,method:"GET",body:void 0,redirect:"follow"};if(i.pollCount===void 0)n.headers&&typeof n.headers=="object"&&"Prefer"in n.headers&&(o.headers={...n.headers},delete o.headers.Prefer),i.statusUrl=t,i.pollCount=1;else{let s=n.pollStatusPeriod??1e3;await ur(s),i.pollCount++}return this.request("GET",t,o,i)}async executeAutoBatch(){if(this.autoBatchQueue===void 0)return;let t=[...this.autoBatchQueue];if(this.autoBatchQueue.length=0,this.autoBatchTimerId=void 0,t.length===1){let o=t[0];try{o.resolve(await this.request(o.method,U(this.fhirBaseUrl,o.url),o.options))}catch(s){o.reject(new d(et(s)))}return}let n={resourceType:"Bundle",type:"batch",entry:t.map(o=>({request:{method:o.method,url:o.url},resource:o.options.body?JSON.parse(o.options.body):void 0}))},i=await this.post(this.fhirBaseUrl,n);for(let o=0;o<t.length;o++){let s=t[o],a=i.entry?.[o];a?.response?.outcome&&!Ht(a.response.outcome)?s.reject(new d(a.response.outcome)):s.resolve(a?.resource)}}addFetchOptionsDefaults(t){Object.entries(this.defaultHeaders).forEach(([n,i])=>{this.setRequestHeader(t,n,i)}),this.setRequestHeader(t,"Accept",Do,!0),this.options.extendedMode!==!1&&this.setRequestHeader(t,"X-Medplum","extended"),t.body&&this.setRequestHeader(t,"Content-Type",w.FHIR_JSON,!0),this.accessToken?this.setRequestHeader(t,"Authorization","Bearer "+this.accessToken):this.basicAuth&&this.setRequestHeader(t,"Authorization","Basic "+this.basicAuth),t.cache||(t.cache="no-cache"),t.credentials||(t.credentials="include")}setRequestContentType(t,n){this.setRequestHeader(t,"Content-Type",n)}setRequestHeader(t,n,i,o=!1){t.headers||(t.headers={});let s=t.headers;o&&s[n]||(s[n]=i)}setRequestBody(t,n){typeof n=="string"||typeof Blob<"u"&&(n instanceof Blob||n?.constructor.name==="Blob")||typeof File<"u"&&(n instanceof File||n?.constructor.name==="File")||typeof Uint8Array<"u"&&(n instanceof Uint8Array||n?.constructor.name==="Uint8Array")?t.body=n:n&&(t.body=JSON.stringify(n))}handleUnauthenticated(t,n,i){return this.refresh()?this.request(t,n,i):(this.clear(),this.onUnauthenticated&&this.onUnauthenticated(),Promise.reject(new d(be)))}async startPkce(){let t=Tr();sessionStorage.setItem("pkceState",t);let n=Tr().slice(0,128);sessionStorage.setItem("codeVerifier",n);let i=await Fn(n),o=Rn(i).replaceAll("+","-").replaceAll("/","_").replaceAll("=","");return sessionStorage.setItem("codeChallenge",o),{codeChallengeMethod:"S256",codeChallenge:o}}async requestAuthorization(t){let n=await this.ensureCodeChallenge(t??{}),i=new URL(this.authorizeUrl);i.searchParams.set("response_type","code"),i.searchParams.set("state",sessionStorage.getItem("pkceState")),i.searchParams.set("client_id",n.clientId??this.clientId),i.searchParams.set("redirect_uri",n.redirectUri??Oo()),i.searchParams.set("code_challenge_method",n.codeChallengeMethod),i.searchParams.set("code_challenge",n.codeChallenge),i.searchParams.set("scope",n.scope??"openid profile"),window.location.assign(i.toString())}processCode(t,n){let i={grant_type:Me.AuthorizationCode,code:t,client_id:n?.clientId??this.clientId??"",redirect_uri:n?.redirectUri??Oo()};if(typeof sessionStorage<"u"){let o=sessionStorage.getItem("codeVerifier");o&&(i.code_verifier=o)}return this.fetchTokens(i)}refreshIfExpired(t){return!this.refreshPromise&&this.accessTokenExpires!==void 0&&!this.isAuthenticated(t)&&this.refresh(),this.refreshPromise??Promise.resolve()}refresh(){if(this.refreshPromise)return this.refreshPromise;if(this.refreshToken)return this.refreshPromise=this.fetchTokens({grant_type:Me.RefreshToken,client_id:this.clientId??"",refresh_token:this.refreshToken}),this.refreshPromise;if(this.clientId&&this.clientSecret)return this.refreshPromise=this.startClientLogin(this.clientId,this.clientSecret),this.refreshPromise}async startClientLogin(t,n){return this.clientId=t,this.clientSecret=n,this.fetchTokens({grant_type:Me.ClientCredentials,client_id:t,client_secret:n})}async startJwtBearerLogin(t,n,i){return this.clientId=t,this.fetchTokens({grant_type:Me.JwtBearer,client_id:t,assertion:n,scope:i})}async startJwtAssertionLogin(t){return this.fetchTokens({grant_type:Me.ClientCredentials,client_assertion_type:Mo.JwtBearer,client_assertion:t})}setBasicAuth(t,n){this.clientId=t,this.clientSecret=n,this.basicAuth=At(t+":"+n)}async fhircastSubscribe(t,n){if(!(typeof t=="string"&&t!==""))throw new d(y("Invalid topic provided. Topic must be a valid string."));if(!(typeof n=="object"&&Array.isArray(n)&&n.length>0))throw new d(y("Invalid events provided. Events must be an array of event names containing at least one event."));let i={channelType:"websocket",mode:"subscribe",topic:t,events:n},s=(await this.post(this.fhircastHubUrl,Rr(i),w.FORM_URL_ENCODED))["hub.channel.endpoint"];if(!s)throw new Error("Invalid response!");return i.endpoint=s,i}async fhircastUnsubscribe(t){if(!It(t))throw new d(y("Invalid topic or subscriptionRequest. SubscriptionRequest must be an object."));if(!(t.endpoint&&typeof t.endpoint=="string"&&t.endpoint.startsWith("ws")))throw new d(y("Provided subscription request must have an endpoint in order to unsubscribe."));t.mode="unsubscribe",await this.post(this.fhircastHubUrl,Rr(t),w.FORM_URL_ENCODED)}fhircastConnect(t){return new Ot(t)}async fhircastPublish(t,n,i,o){return Nn(n)?this.post(this.fhircastHubUrl,Cr(t,n,i,o),w.JSON):(Un(n),this.post(this.fhircastHubUrl,Cr(t,n,i),w.JSON))}async fhircastGetContext(t){return this.get(`${this.fhircastHubUrl}/${t}`,{cache:"no-cache"})}async invite(t,n){return this.post("admin/projects/"+t+"/invite",n)}async fetchTokens(t){let n=new URLSearchParams(t),i={...this.defaultHeaders,"Content-Type":w.FORM_URL_ENCODED};this.basicAuth&&(i.Authorization=`Basic ${this.basicAuth}`),this.credentialsInHeader&&(n.delete("client_id"),n.delete("client_secret"),!this.basicAuth&&t.client_id&&t.client_secret&&(i.Authorization=`Basic ${At(t.client_id+":"+t.client_secret)}`));let o={method:"POST",headers:i,body:n.toString(),credentials:"include"},s;try{s=await this.fetchWithRetry(this.tokenUrl,o)}catch(c){throw this.refreshPromise=void 0,c}if(!s.ok){this.clearActiveLogin();try{let c=await s.json();throw new d(b(c.error_description))}catch(c){throw new d(b("Failed to fetch tokens"),c)}}let a=await s.json();return await this.verifyTokens(a),this.getProfile()}async verifyTokens(t){let n=t.access_token;if(Wn(n)){let i=kt(n);if(Date.now()>=i.exp*1e3)throw this.clearActiveLogin(),new d(Kr);if(i.cid){if(i.cid!==this.clientId)throw this.clearActiveLogin(),new d(Gt)}else if(this.clientId&&i.client_id!==this.clientId)throw this.clearActiveLogin(),new d(Gt)}return this.setActiveLogin({accessToken:n,refreshToken:t.refresh_token,project:t.project,profile:t.profile})}checkSessionDetailsMatchLogin(t){return this.sessionDetails&&t?t.profile?.reference?.endsWith(this.sessionDetails.profile.id)??!1:!0}setupStorageListener(){try{window.addEventListener("storage",t=>{if(t.key===null)window.location.reload();else if(t.key==="activeLogin"){let n=t.oldValue?JSON.parse(t.oldValue):void 0,i=t.newValue?JSON.parse(t.newValue):void 0;n?.profile.reference!==i?.profile.reference||!this.checkSessionDetailsMatchLogin(i)?window.location.reload():i?this.setAccessToken(i.accessToken,i.refreshToken):this.clear()}})}catch{}}getSubscriptionManager(){return this.subscriptionManager||(this.subscriptionManager=new Mt(this,An(this.baseUrl,"/ws/subscriptions-r4"))),this.subscriptionManager}subscribeToCriteria(t,n){return this.getSubscriptionManager().addCriteria(t,n)}unsubscribeFromCriteria(t,n){this.subscriptionManager&&(this.subscriptionManager.removeCriteria(t,n),this.subscriptionManager.getCriteriaCount()===0&&this.subscriptionManager.closeWebSocket())}getMasterSubscriptionEmitter(){return this.getSubscriptionManager().getMasterEmitter()}};function yu(){if(!globalThis.fetch)throw new Error("Fetch not available in this environment");return globalThis.fetch.bind(globalThis)}function Oo(){return typeof window>"u"?"":window.location.protocol+"//"+window.location.host+"/"}async function Io(r,e){let t=r.headers.get("content-location");if(t)return t;let n=r.headers.get("location");if(n)return n;if(Ee(e)&&e.issue?.[0]?.diagnostics)return e.issue[0].diagnostics}function ko(r){let e=r.entry?.map(t=>t.resource)??[];return Object.assign(e,{bundle:r})}function gu(r){return E(r)&&"data"in r&&"contentType"in r}function Hn(r,e,t,n){return gu(r)?r:{data:r,filename:e,contentType:t,onProgress:n}}function xu(r){return E(r)&&"docDefinition"in r}function _o(r,e,t,n){return xu(r)?r:{docDefinition:r,filename:e,tableLayouts:t,fonts:n}}function vu(r){return r.status===429||r.status>=500}var Tu={aws_ssm_parameter_store:"aws_ssm_parameter_store"};function Su(r,e,t){let n=new Or;Array.isArray(r)||(r=r.entry?.map(i=>i.resource)??[]);for(let i of r)n.addObservation(i);return n.summarize(e,t)}var Or=class{constructor(e){this.dataPoints=[],this.code=e?.code,this.unit=e?.unit,this.sampling=e?.sampling}addObservation(e){if(!this.code)this.code=e.code;else if(!bu(this.code,e.code))throw new Error("Observation does not match code of sampled data");e.valueQuantity?.value!==void 0?(this.checkUnit(e.valueQuantity),this.addData(e.valueQuantity.value)):e.valueInteger!==void 0?this.addData(e.valueInteger):e.valueSampledData?.data&&(this.checkUnit(e.valueSampledData.origin),this.addData(...Qn(e.valueSampledData)))}addData(...e){this.dataPoints.push(...e)}checkUnit(e){if(!this.unit)this.unit=e;else if(e.code&&e.system){if(this.unit.system!==e.system||this.unit.code!==e.code)throw new Error("Incorrect unit for Observation")}else if(e.unit&&this.unit.unit!==e.unit)throw new Error("Incorrect unit for Observation")}summarize(e,t){if(!this.code)throw new Error("Code is required for data points");let n=t(this.dataPoints);return{resourceType:"Observation",status:"final",code:e,valueQuantity:typeof n=="number"?{...this.unit,value:n}:n,component:[{code:this.code,valueSampledData:{origin:{...this.unit,value:0},dimensions:1,period:0,...this.sampling,data:Eu(this.dataPoints,this.sampling)}}]}}};function bu(r,e){return!!r.coding?.some(t=>e.coding?.some(n=>t.system===n.system&&t.code===n.code))}function Qn(r){return r.data?.split(" ").map(e=>parseFloat(e)*(r.factor??1)+(r.origin.value??0))??[]}function Eu(r,e){if(r.length)return r.map(t=>(t-(e?.origin.value??0))/(e?.factor??1)).join(" ")}function Ru(r){let e=[],t=r.effectiveInstant??r.effectiveDateTime??r.effectivePeriod?.start,n=t?Date.parse(t).valueOf():0;if(r.valueSampledData&&e.push(...Lo(r.valueSampledData,n,r)),r.component)for(let i of r.component)i.valueSampledData&&e.push(...Lo(i.valueSampledData,n,{...r,...i}));return e}function Lo(r,e,t){let n=[],i=Qn(r),o=N(t);for(let s=0;s<i.length;s++){let a=i[s],c=e+Math.floor(s/r.dimensions)*r.period;n.push({...t,id:void 0,effectiveInstant:void 0,effectivePeriod:void 0,effectiveTiming:void 0,effectiveDateTime:c?new Date(c).toISOString():void 0,valueQuantity:{...r.origin,value:a},valueSampledData:void 0,component:void 0,derivedFrom:o?[...t.derivedFrom??[],{reference:o}]:t.derivedFrom})}return n}function kr({parentContext:r,path:e,elements:t,profileUrl:n,debugMode:i,accessPolicyResource:o}){if(e===r?.path)return;i??=r?.debugMode??!1,o??=r?.accessPolicyResource;let s=Cu(e,t,r,!!i),a=lr(e,".",2)[1];s=Pu(s,o,a),s=wu(s,o,a);let c=Object.create(null);for(let[p,m]of Object.entries(s))c[e+"."+p]=m;let u;if(r&&!r.isDefaultContext)u=r.getExtendedProps;else{let p=Object.create(null);u=m=>{let x=lr(m,".",2)[1];if(x){if(!p[x]){let j=Ir(x,o?.hiddenFields);p[x]={hidden:j,readonly:j||Ir(x,o?.readonlyFields)}}return p[x]}}}return{path:e,elements:s,elementsByPath:c,profileUrl:n??r?.profileUrl,debugMode:i,getExtendedProps:u,accessPolicyResource:o}}function Cu(r,e,t,n){let i=Object.create(null);if(t)for(let[s,a]of Object.entries(t.elementsByPath)){let c=ie(r,s);c!==void 0&&(i[c]=a)}let o=!1;if(e)for(let[s,a]of Object.entries(e))s in i||(i[s]=a,o=!0);return n&&console.assert(o,"Unnecessary ElementsContext; not using any newly provided elements"),i}function Pu(r,e,t){if(!e?.hiddenFields?.length)return r;let n=t?t+".":"";return Object.fromEntries(Object.entries(r).filter(([i])=>!Ir(n+i,e.hiddenFields)))}function wu(r,e,t){if(!e?.readonlyFields?.length)return r;let n=Object.create(null),i=t?t+".":"";for(let[o,s]of Object.entries(r))Ir(i+o,e.readonlyFields)?n[o]={...s,readonly:!0}:n[o]=s;return n}function Ir(r,e){if(!e?.length)return!1;let t=r.split(".");for(let n=1;n<=t.length;n++){let i=t.slice(0,n).join(".");if(e.includes(i))return!0}return!1}function zn(r){return r.type!==void 0&&r.type.length>0}function Au(r,e,t,n){let i=Pe(r,e.path,{profileUrl:n});if(i){let o=t.typeSchema?.elements??t.elements;return i.some(s=>tr(s,e,t,o))??!1}return console.assert(!1,"getNestedProperty[%s] in isDiscriminatorComponentMatch missed",e.path),!1}function Jn(r,e,t,n){if(r)for(let i of e){let o={value:r,type:i.typeSchema?.type??i.type?.[0].code};if(t.every(s=>Au(o,s,i,i.typeSchema?.url??n)))return i.name}}var Lt=class{constructor(e,t,n){if(e.type===void 0)throw new Error("schema must include a type");this.rootSchema=e;let i=kr({parentContext:void 0,path:this.rootSchema.type,elements:n??this.rootSchema.elements,profileUrl:this.rootSchema.name===this.rootSchema.type?void 0:this.rootSchema.url});if(i===void 0)throw new Error("Could not create root elements context");this.elementsContextStack=[i],this.visitor=t}get elementsContext(){return this.elementsContextStack[this.elementsContextStack.length-1]}crawlElement(e,t,n){this.visitor.onEnterSchema&&this.visitor.onEnterSchema(this.rootSchema);let i=Object.fromEntries(Object.entries(this.elementsContext.elements).filter(([o])=>o.startsWith(t)));this.crawlElementsImpl(i,n),this.visitor.onExitSchema&&this.visitor.onExitSchema(this.rootSchema)}crawlSlice(e,t,n){let i=this.prepareSlices(n.slices,n);if(!K(i.slices))throw new Error(`cannot crawl slice ${t.name} since it has no type information`);this.visitor.onEnterSchema&&this.visitor.onEnterSchema(this.rootSchema),this.sliceAllowList=[t],this.crawlSliceImpl(i.slices[0],t.path,i),this.sliceAllowList=void 0,this.visitor.onExitSchema&&this.visitor.onExitSchema(this.rootSchema)}crawlResource(){this.visitor.onEnterSchema&&this.visitor.onEnterSchema(this.rootSchema),this.crawlElementsImpl(this.rootSchema.elements,this.rootSchema.type),this.visitor.onExitSchema&&this.visitor.onExitSchema(this.rootSchema)}crawlElementsImpl(e,t){let n=Ou(e);for(let i of n)this.crawlElementNode(i,t)}crawlElementNode(e,t){let n=t+"."+e.key;this.visitor.onEnterElement&&this.visitor.onEnterElement(n,e.element,this.elementsContext);for(let i of e.children)this.crawlElementNode(i,t);K(e.element?.slicing?.slices)&&this.crawlSlicingImpl(e.element.slicing,n),this.visitor.onExitElement&&this.visitor.onExitElement(n,e.element,this.elementsContext)}prepareSlices(e,t){let n=[];for(let o of e){if(!zn(o))continue;let s=o.type.find(a=>K(a.profile))?.profile?.[0];if(K(s)){let a=ln(s);a&&(o.typeSchema=a)}n.push(o)}return{...t,slices:n}}crawlSlicingImpl(e,t){let n=this.prepareSlices(e.slices,e);for(let i of n.slices)(this.sliceAllowList===void 0||this.sliceAllowList.includes(i))&&this.crawlSliceImpl(i,t,n)}crawlSliceImpl(e,t,n){let i=e.typeSchema;i&&this.visitor.onEnterSchema&&this.visitor.onEnterSchema(i),this.visitor.onEnterSlice&&this.visitor.onEnterSlice(t,e,n);let o,s=i?.elements??e.elements;K(s)&&(o=kr({path:t,parentContext:this.elementsContext,elements:s})),o&&this.elementsContextStack.push(o),this.crawlElementsImpl(s,t),o&&this.elementsContextStack.pop(),this.visitor.onExitSlice&&this.visitor.onExitSlice(t,e,n),i&&this.visitor.onExitSchema&&this.visitor.onExitSchema(i)}};function Ou(r){let e=[];function t(o,s){return s.startsWith(o+".")}function n(o,s){for(let a of o.children)if(t(a.key,s.key)){n(a,s);return}o.children.push(s)}let i=Object.entries(r);i.sort((o,s)=>o[0].localeCompare(s[0]));for(let[o,s]of i){let a={key:o,element:s,children:[]},c=!1;for(let u of e)if(t(u.key,o)){n(u,a),c=!0;break}c||e.push(a)}return e}var Dr="__sliceName";function Iu(r,e){let t=new Vr(r,r.resourceType,"resource");return new Lt(e,t).crawlResource(),t.getDefaultValue()}function ku(r,e,t){for(let[n,i]of Object.entries(e)){if(t===void 0||t===n){Ft(r,n,i,e);continue}let o=ie(t,n);o!==void 0&&Ft(r,o,i,e)}return r}function Du(r,e,t,n){let i=new Vr([{[Dr]:e.name}],e.path,"element");return new Lt(n,i).crawlSlice(r,e,t),i.getDefaultValue()[0]}var Vr=class{constructor(e,t,n){this.schemaStack=[],this.valueStack=[],this.rootValue=he(e),this.valueStack.splice(0,this.valueStack.length,{type:n,path:t,values:[this.rootValue]})}get schema(){return this.schemaStack[this.schemaStack.length-1]}get value(){return this.valueStack[this.valueStack.length-1]}onEnterSchema(e){this.schemaStack.push(e)}onExitSchema(){this.schemaStack.pop()}onEnterElement(e,t,n){let i=this.value.values,o=this.value.path,s=ie(o,e);if(s===void 0)throw new Error(`Expected ${e} to be prefixed by ${o}`);let a=[];for(let c of i){if(c===void 0)continue;let u=ie(n.path,o),p=Array.isArray(c)?c:[c];for(let m of p){Vu(m,s,t,n.elements,u),Ft(m,s,t,n.elements);let x=Xn(m,s,n.elements,u);x!==void 0&&a.push(x)}}this.valueStack.push({type:"element",path:e,values:a})}onExitElement(e,t,n){if(!this.valueStack.pop())throw new Error("Expected value context to exist when exiting element");let o=ie(this.value.path,e);if(o===void 0)throw new Error(`Expected ${e} to be prefixed by ${this.value.path}`);let s=ie(n.path,this.value.path);for(let a of this.value.values){let c=Xn(a,o,n.elements,s);if(Array.isArray(c))for(let u=c.length-1;u>=0;u--){let p=c[u];K(p)||c.splice(u,1)}S(c)&&Kn(a,void 0,o,t)}}onEnterSlice(e,t,n){let i=this.value.values,o=[];for(let s of i)if(s!==void 0){let a=Array.isArray(s)?s:[s],c=this.getMatchingSliceValues(a,t,n);o.push(c)}this.valueStack.push({type:"slice",path:e,values:o})}getMatchingSliceValues(e,t,n){let i=[];for(let o of e)(o[Dr]??Jn(o,[t],n.discriminator,this.schema.url))===t.name&&i.push(o);for(let o=i.length;o<t.min;o++)if(cr(t.type[0].code)){let s=Object.create(null);i.push(s),e.push(s)}return i}onExitSlice(){let e=this.valueStack.pop();if(!e)throw new Error("Expected value context to exist in onExitSlice");for(let t of e.values)for(let n=t.length-1;n>=0;n--){let i=t[n];Dr in i&&delete i[Dr]}}getDefaultValue(){return this.rootValue}};function Vu(r,e,t,n,i){let o=Xn(r,e,n,i);t.min>0&&o===void 0&&cr(t.type[0].code)&&(t.isArray?Kn(r,[Object.create(null)],e,t):Kn(r,Object.create(null),e,t))}function Kn(r,e,t,n){if(t.includes("."))throw new Error("key cannot be nested");let i=t;if(t.includes("[x]")){let o=n.type[0].code;i=t.replace("[x]",O(o))}e===void 0?delete r[i]:r[i]=e}function Xn(r,e,t,n){let i=e.split("."),o=r,s;for(let a=0;a<i.length;a++){let c=i[a];if(c.includes("[x]")){let u=(n?n+".":"")+i.slice(0,a+1).join("."),m=t[u].type[0].code;c=c.replace("[x]",O(m))}if(a===i.length-1){Array.isArray(o)?s=o.map(u=>u[c]):s=o[c];continue}if(Array.isArray(o))o=o.map(u=>u[c]);else if(E(o)){if(o[c]===void 0)return;o=o[c]}else return}return s}function Ft(r,e,t,n){if(!(t.fixed||t.pattern))return r;if(Array.isArray(r))return r.map(a=>Ft(a,e,t,n));r==null&&(r=Object.create(null));let i=r,o=e.split("."),s=i;for(let a=0;a<o.length;a++){let c=o[a];if(c.includes("[x]")){let p=n[o.slice(0,a+1).join(".")].type[0].code;c=c.replace("[x]",O(p))}if(a===o.length-1){let u=Array.isArray(s)?s:[s];for(let p of u)t.fixed?p[c]??=t.fixed.value:t.pattern&&(p[c]=Fo(p[c],t.pattern.value))}else{if(!(c in s)){let u=o.slice(0,a+1).join(".");s[c]=n[u].isArray?[Object.create(null)]:Object.create(null)}s=s[c]}}return i}function Fo(r,e){if(Array.isArray(e)&&(Array.isArray(r)||r===void 0))return(r?.length??0)>0?r:he(e);if(E(e)&&(E(r)&&!Array.isArray(r)||r===void 0)){let t=he(r)??Object.create(null);for(let n of Object.keys(e))t[n]=Fo(t[n],e[n]);return t}return r}function Yn(r,e){if(!r.group)throw new d(b("ConceptMap does not specify a mapping group","ConceptMap.group"));let t=Mu(e);if(Ee(t))throw new d(t);let n=Lu(t,e.targetsystem?r.group.filter(o=>o.target===e.targetsystem):r.group),i=n.length>0;return{result:i,match:i?n:void 0}}function Mu(r){return r.code&&!r.coding&&!r.codeableConcept?r.system===void 0?b("Missing required 'system' input parameter with 'code' parameter"):{[r.system]:[r.code]}:r.coding&&!r.code&&!r.codeableConcept?{[r.coding.system??""]:[r.coding.code??""]}:r.codeableConcept&&!r.code&&!r.coding?_u(r.codeableConcept):r.code||r.coding||r.codeableConcept?b("Ambiguous input: multiple source codings provided"):b("No source provided: 'code'+'system', 'coding', or 'codeableConcept' input parameter is required")}function _u(r){let e=Object.create(null);if(!r.coding?.length)return e;for(let{system:t,code:n}of r.coding){if(!n)continue;let i=t??"";e[i]=e[i]?[...e[i],n]:[n]}return e}function Lu(r,e){let t=[];for(let[n,i]of Object.entries(r))for(let o of e.filter(s=>(s.source??"")===n)){let s=o.element?.filter(a=>i.includes(a.code)).flatMap(a=>a.target?.map(c=>({equivalence:c.equivalence,concept:{system:o.target,code:c.code,display:c.display}}))??[]);s?.length||(s=Fu(i,o)),s&&t.push(...s)}return t}function Fu(r,e){switch(e.unmapped?.mode){case"provided":return r.map(t=>({equivalence:"equal",concept:{system:e.target,code:t}}));case"fixed":return[{equivalence:"equivalent",concept:{system:e.target,code:e.unmapped.code,display:e.unmapped.display}}];default:return}}var Nu=[...Rt,"->","<<",">>","=="];function No(r){return new ue(r,Et,Nu).tokenize()}var Uu={"-":"disjoint","==":"equal"},Zn=class{constructor(e){this.structureMap={resourceType:"StructureMap",status:"active"};this.parser=e}parse(){for(;this.parser.hasMore();){let e=this.parser.peek()?.value;switch(e){case"map":this.parseMap();break;case"uses":this.parseUses();break;case"imports":this.parseImport();break;case"group":this.parseGroup();break;case"conceptmap":this.parseConceptMap();break;default:throw new Error(`Unexpected token: ${e}`)}}return this.structureMap}parseMap(){this.parser.consume("Symbol","map"),this.structureMap.url=this.parser.consume("String").value,this.parser.consume("="),this.structureMap.name=this.parser.consume().value}parseUses(){this.parser.consume("Symbol","uses");let e={};e.url=this.parser.consume("String").value,this.parser.peek()?.value==="alias"&&(this.parser.consume("Symbol","alias"),e.alias=this.parser.consume("Symbol").value),this.parser.consume("Symbol","as"),e.mode=this.parser.consume().value,this.structureMap.structure||(this.structureMap.structure=[]),this.structureMap.structure.push(e)}parseImport(){this.parser.consume("Symbol","imports"),this.structureMap.import||(this.structureMap.import=[]),this.structureMap.import.push(this.parser.consume("String").value)}parseGroup(){let e={};this.parser.consume("Symbol","group"),e.name=this.parser.consume("Symbol").value,e.input=this.parseParameters(),this.parser.peek()?.value==="extends"&&(this.parser.consume("Symbol","extends"),e.extends=this.parser.consume("Symbol").value),this.parser.peek()?.value==="<<"?(this.parser.consume("<<"),e.typeMode=this.parser.consume().value,this.parser.peek()?.value==="+"&&(this.parser.consume("+"),e.typeMode="type-and-types"),this.parser.consume(">>")):e.typeMode="none",e.rule=this.parseRules(),this.structureMap.group||(this.structureMap.group=[]),this.structureMap.group.push(e)}parseParameters(){let e=[];for(this.parser.consume("(");this.parser.hasMore()&&this.parser.peek()?.value!==")";)e.push(this.parseParameter()),this.parser.peek()?.value===","&&this.parser.consume(",");return this.parser.consume(")"),e}parseParameter(){let e={};return e.mode=this.parser.consume().value,e.name=this.parser.consume("Symbol").value,this.parser.peek()?.value===":"&&(this.parser.consume(":"),e.type=this.parser.consume("Symbol").value),e}parseRules(){let e=[];for(this.parser.consume("{");this.parser.hasMore()&&this.parser.peek()?.value!=="}";)e.push(this.parseRule());return this.parser.consume("}"),e}parseRule(){let e={source:this.parseRuleSources()};return this.parser.peek()?.value==="->"&&(this.parser.consume("->"),e.target=this.parseRuleTargets()),this.parser.peek()?.value==="then"&&(this.parser.consume("Symbol","then"),this.parser.peek()?.id==="{"?e.rule=this.parseRules():e.dependent=this.parseRuleDependents()),this.parser.peek()?.id==="String"?e.name=this.parser.consume().value:e.name=e.source?.[0]?.element,this.parser.consume(";"),e}parseRuleSources(){this.parser.hasMore()&&this.parser.peek()?.value==="for"&&this.parser.consume("Symbol","for");let e=[this.parseRuleSource()];for(;this.parser.hasMore()&&this.parser.peek()?.value===",";)this.parser.consume(","),e.push(this.parseRuleSource());return e}parseRuleSource(){let e={},n=this.parseRuleContext().split(".");if(e.context=n[0],e.element=n[1],this.parser.hasMore()&&this.parser.peek()?.value===":"&&(this.parser.consume(":"),e.type=this.parser.consume().value),this.parser.hasMore()&&this.parser.peek()?.value==="default"&&(this.parser.consume("Symbol","default"),e.defaultValueString=this.parser.consume("String").value),(this.parser.peek()?.value==="first"||this.parser.peek()?.value==="not_first"||this.parser.peek()?.value==="last"||this.parser.peek()?.value==="not_last"||this.parser.peek()?.value==="only_one")&&(e.listMode=this.parser.consume().value),this.parser.peek()?.value==="as"&&(this.parser.consume("Symbol","as"),e.variable=this.parser.consume().value),this.parser.peek()?.value==="log"&&(this.parser.consume("Symbol","log"),e.logMessage=this.parser.consume().value),this.parser.peek()?.value==="where"){this.parser.consume("Symbol","where");let i=this.parser.consumeAndParse(g.Arrow);e.condition=i.toString()}if(this.parser.peek()?.value==="check"){this.parser.consume("Symbol","check");let i=this.parser.consumeAndParse(g.Arrow);e.check=i.toString()}return e}parseRuleTargets(){let e=[this.parseRuleTarget()];for(;this.parser.hasMore()&&this.parser.peek()?.value===",";)this.parser.consume(","),e.push(this.parseRuleTarget());return e}parseRuleTarget(){let e={},n=this.parseRuleContext().split(".");return e.contextType="variable",e.context=n[0],e.element=n[1],this.parser.peek()?.value==="="&&(this.parser.consume("="),this.parseRuleTargetTransform(e)),this.parser.peek()?.value==="as"&&(this.parser.consume("Symbol","as"),e.variable=this.parser.consume().value),this.parser.peek()?.value==="share"&&(this.parser.consume("Symbol","share"),e.listMode=["share"],this.parser.consume("Symbol")),(this.parser.peek()?.value==="first"||this.parser.peek()?.value==="last"||this.parser.peek()?.value==="collate")&&(e.listMode=[this.parser.consume().value]),e}parseRuleTargetTransform(e){let t=this.parser.consumeAndParse(g.As);t instanceof Z?(e.transform=t.name,e.parameter=t.args?.map(Uo)):t instanceof L||t instanceof W?(e.transform="copy",e.parameter=[Uo(t)]):(e.transform="evaluate",e.parameter=[{valueString:t.toString()}])}parseRuleContext(){let e=this.parser.consume().value;for(;this.parser.peek()?.value===".";)this.parser.consume("."),e+="."+this.parser.consume().value;return e}parseRuleDependents(){let e=this.parser.consumeAndParse(g.Arrow);return[{name:e.name,variable:e.args.map(t=>t.name)}]}parseConceptMap(){this.parser.consume("Symbol","conceptmap");let e={resourceType:"ConceptMap",status:"active",url:"#"+this.parser.consume("String").value};this.parser.consume("{");let t={},n=this.parser.peek()?.value;for(;n!=="}";)n==="prefix"?this.parseConceptMapPrefix(t):this.parseConceptMapRule(e,t),n=this.parser.peek()?.value;this.parser.consume("}"),this.structureMap.contained||(this.structureMap.contained=[]),this.structureMap.contained.push(e)}parseConceptMapPrefix(e){this.parser.consume("Symbol","prefix");let t=this.parser.consume().value;this.parser.consume("=");let n=this.parser.consume().value;e[t]=n}parseConceptMapRule(e,t){let n=this.parser.consume().value,i=t[n];this.parser.consume(":");let o=this.parser.consume().value,s=Uu[this.parser.consume().value],a=this.parser.consume().value,c=t[a];this.parser.consume(":");let u=this.parser.consume().value,p=e?.group?.find(m=>m.source===i&&m.target===c);p||(p={source:i,target:c,element:[]},e.group||(e.group=[]),e.group.push(p)),p.element||(p.element=[]),p.element.push({code:o,target:[{code:u,equivalence:s}]})}};function Uo(r){if(r instanceof W)return{valueId:r.name};if(r instanceof L)return Bu(r);throw new Error(`Unknown parameter atom type: ${r.constructor.name} (${r.toString()})`)}function Bu(r){switch(r.value.type){case"boolean":return{valueBoolean:r.value.value};case"decimal":return{valueDecimal:r.value.value};case"integer":return{valueInteger:r.value.value};case"dateTime":case"string":return{valueString:r.value.value};default:throw new Error("Unknown target literal type: "+r.value.type)}}var Wu=Ge().registerInfix("->",{precedence:g.Arrow}).registerInfix(";",{precedence:g.Semicolon});function qu(r){let e=Wu.construct(No(r));return e.removeComments(),new Zn(e).parse()}var Mr=class{constructor(e=[]){this.resources=e}get(e,t){let n=[];for(let i of this.resources)i.resourceType===e&&i.url&&this.matchesUrl(i.url,t)&&n.push(i);return n}matchesUrl(e,t){if(t.includes("*")){let n=t.split("*");return e.startsWith(n[0])&&e.endsWith(n[1])}else return e===t}};function ju(r,e,t=new Mr){return $u({root:r,transformMaps:t},r,e)}function $u(r,e,t){return Gu(r,e),Wo(r,e),ti(r,e.group[0],t)}function Gu(r,e){let t=Lr(r);if(t&&e.import)for(let n of e.import){let i=t.get("StructureMap",n);for(let o of i)Wo(r,o)}}function Wo(r,e){let t=Lr(r);if(t&&e.contained)for(let n of e.contained)(n.resourceType==="StructureMap"||n.resourceType==="ConceptMap")&&t.resources.push(n);if(e.group)for(let n of e.group)_r(r,n.name,{type:"StructureMapGroup",value:n})}function ti(r,e,t){let n=[],i=[];for(let u of e.input)u.mode==="source"&&n.push(u),u.mode==="target"&&i.push(u);if(n.length===0)throw new Error("Missing source definitions");if(i.length===0)throw new Error("Missing target definitions");if(t.length<n.length)throw new Error(`Not enough arguments (got ${t.length}, min ${n.length})`);if(t.length>n.length+i.length)throw new Error(`Too many arguments (got ${t.length}, max ${n.length+i.length})`);let o={},s=[],a=0;for(let u of n)Nt(o,u.name,t[a++]);for(let u of i){let p=t[a++]??{type:u.type??"BackboneElement",value:{}};Nt(o,u.name,p),s.push(p)}let c={root:r.root,parent:r,variables:o};if(e.rule)for(let u of e.rule)qo(c,u);return s}function qo(r,e){e.source&&jo(r,e,0)}function jo(r,e,t){let n=e.source[t];for(let i of Ju(r,n))_r(r,"_",i),n.variable&&_r(r,n.variable,i),t<e.source.length-1?jo(r,e,t+1):Hu(r,e)}function Hu(r,e){if(!Qu(r,e)){if(e.target)for(let t of e.target)$o(r,t);if(e.rule)for(let t of e.rule)qo(r,t);if(e.dependent)for(let t of e.dependent)ol(r,t)}}function Qu(r,e){if(!e.target||e.target.length!==1||e.target[0].transform||e.rule||e.dependent)return!1;let t=Te(r,"_");if(Array.isArray(t)&&(t=t[0]),!t)return!1;let n=zu(r,t);if(!n)return $o(r,{...e.target[0],transform:"copy",parameter:[{valueId:"_"}]}),!0;let i=e.target[0],o=Te(r,i.context),s=o.value[i.element],a=Ho(o,i.element)||Array.isArray(s),c={root:r.root,parent:r,variables:{}},u=ti(c,n,[t]);return Go(r,i,o,u,a,s),!0}function zu(r,e){let t=r;for(;t;){if(t.variables)for(let n of Object.values(t.variables)){let i=ei(n);for(let o of i)if(o.type==="StructureMapGroup"){let s=o.value;if((s.typeMode==="types"||s.typeMode==="type-and-types")&&s.input.length===2&&s.input[0].mode==="source"&&s.input[0].type===e.type&&s.input[1].mode==="target")return s}}t=t.parent}}function Ju(r,e){let t=Te(r,e.context);if(!t)return[];let n=e.element;if(!n)return[t];let i=P(n,[t]);if(!i||i.length===0)return[];if(e.condition&&!Bo(t,{[e.variable]:i[0]},e.condition))return[];if(e.check&&!Bo(t,{[e.variable]:i[0]},e.check))throw new Error("Check failed: "+e.check);return e.listMode&&(i=Ku(e,i)),i}function Bo(r,e,t){return M(P(t,[r],e))}function Ku(r,e){switch(r.listMode){case"first":return[e[0]];case"not_first":return e.slice(1);case"last":return[e[e.length-1]];case"not_last":return e.slice(0,e.length-1);case"only_one":if(e.length!==1)throw new Error("Expected only one value");break}return e}function $o(r,e){let t=Te(r,e.context);if(!t)throw new Error("Target not found: "+e.context);let n=t.value[e.element],i,o=Ho(t,e.element)||Array.isArray(n);if(e.transform)switch(e.transform){case"append":i=Xu(r,e);break;case"cast":i=Yu(r,e);break;case"cc":i=Zu(r,e);break;case"copy":i=el(r,e);break;case"create":i=tl(r,e);break;case"evaluate":i=rl(r,e);break;case"translate":i=nl(r,e);break;case"truncate":i=il(r,e);break;case"uuid":i=[{type:"string",value:ve()}];break;default:throw new Error(`Unsupported transform: ${e.transform}`)}else{let s=Qo(t,e.element)?.type,a=s?.length===1?s[0].code:void 0;o||n===void 0?i=[a?{type:a,value:{}}:v({})]:i=[a?{type:a,value:n}:v(n)]}Go(r,e,t,i,o,n)}function Go(r,e,t,n,i,o){if(n.length!==0){if(i){o||(o=[],Nt(t.value,e.element,o));for(let s of n)o.push(s.value)}else Nt(t.value,e.element,n[0].value);e.variable&&_r(r,e.variable,sl(n))}}function Ho(r,e){return Qo(r,e)?.isArray}function Qo(r,e){return Ne(r.type)?.elements?.[e]}function Xu(r,e){let t=F(r,e.parameter?.[0])?.[0]?.value,n=F(r,e.parameter?.[1])?.[0]?.value;return[{type:"string",value:(t??"").toString()+(n??"").toString()}]}function Yu(r,e){let t=F(r,e.parameter?.[0])?.[0];return F(r,e.parameter?.[1])?.[0]?.value==="string"?[{type:"string",value:t?.value?.toString()}]:[t]}function Zu(r,e){let t=e.parameter;if(t.length===2){let n=F(r,t[0])?.[0]?.value,i=F(r,t[1])?.[0]?.value;return[{type:"CodeableConcept",value:{coding:[{system:n,code:i}]}}]}else return[{type:"CodeableConcept",value:{text:F(r,t[0])?.[0]?.value}}]}function el(r,e){return e.parameter.flatMap(t=>F(r,t))}function tl(r,e){let t={};return e.parameter&&e.parameter.length>0&&(t.resourceType=F(r,e.parameter?.[0])?.[0]?.value),[v(t)]}function rl(r,e){let n=F(r,e.parameter?.[0])[0].value;return P(n,[],zo(r))}function nl(r,e){let t=e.parameter.flatMap(c=>F(r,c)),n=t[0].value,i=t[1].value,s=Lr(r)?.get("ConceptMap",i)[0];if(!s)throw new Error("ConceptMap not found: "+i);let a=Yn(s,{system:s.group?.[0]?.source,code:n});return[v(a.match?.[0]?.concept?.code)]}function il(r,e){let t=F(r,e.parameter?.[0])?.[0],n=F(r,e.parameter?.[1])?.[0]?.value;return t.type==="string"?[{type:"string",value:t.value.substring(0,n)}]:[t]}function ol(r,e){let t=Te(r,e.name);if(!t)throw new Error("Dependent group not found: "+e.name);let n=e.variable,i=[];for(let s of n){let a=Te(r,s);if(!a)throw new Error("Dependent variable not found: "+s);i.push(a)}let o={root:r.root,parent:r,variables:{}};ti(o,t.value,i)}function Lr(r){if(r.transformMaps)return r.transformMaps;if(r.parent)return Lr(r.parent)}function F(r,e){let n=C({type:"StructureMapGroupRuleTargetParameter",value:e},"value");if(!n)throw new Error("Missing target parameter: "+JSON.stringify(e));if(n=ei(n),n.length===1&&n[0].type==="id"){let i=Te(r,n[0].value);if(!i)throw new Error("Variable not found: "+n[0].value);return ei(i)}return n}function Te(r,e){let t=r.variables?.[e];if(t)return t;if(r.parent)return Te(r.parent,e)}function zo(r,e={}){if(r.parent&&zo(r.parent,e),r.variables)for(let[t,n]of Object.entries(r.variables))e[t]=n,e["%"+t]=n;return e}function _r(r,e,t){r.variables||(r.variables={}),Nt(r.variables,e,t)}function Nt(r,e,t){if(e==="__proto__"||e==="constructor"||e==="prototype")throw new Error("Invalid key: "+e);r[e]=t}function ei(r){return Array.isArray(r)?r:[r]}function sl(r){return r.length===1?r[0]:r}var al=" ".repeat(2),ri=class{constructor(e=al,t=!0){this.indent=e,this.b=[],this.indentCount=0,t&&(this.appendNoWrap("/*"),this.appendNoWrap(" * This is a generated file"),this.appendNoWrap(" * Do not edit manually."),this.appendNoWrap(" */"),this.newLine())}newLine(){this.b.push(`
`)}appendNoWrap(e){this.b.push(this.indent.repeat(this.indentCount)),this.b.push(e),this.b.push(`
`)}append(e){let t=this.indent.repeat(this.indentCount)+e;if(t.length<160)this.b.push(t),this.b.push(`
`);else{let n=!0;for(let i of Jo(t,120-this.indent.length*this.indentCount))n?this.b.push(this.indent.repeat(this.indentCount)):this.b.push(this.indent.repeat(this.indentCount+2)),this.b.push(i.trim()),this.b.push(`
`),n=!1}}toString(){return this.b.join("").replaceAll(`


`,`

`)}};function Jo(r,e){let t=[],n=[],i=0;return r.split(" ").forEach(function(o){i+o.length>e&&(t.push(n.join(" ").trim()),n=[],i=0),i+=o.length+1,n.push(o)}),n.length>0&&t.push(n.join(" ").trim()),t}var cl=[...Rt,"eq","ne","co"];function Ko(r){return new ue(r,Et,cl,{dateTimeLiterals:!0,symbolRegex:/[^\s\])]/}).tokenize()}var Ut=class{constructor(e,t,n){this.path=e,this.operator=t,this.value=n}},Bt=class{constructor(e){this.child=e}},Wt=class{constructor(e,t,n){this.keyword=e,this.left=t,this.right=n}};var ul={eq:f.EXACT,ne:f.NOT_EQUALS,co:f.CONTAINS,sw:f.STARTS_WITH,ew:void 0,gt:f.GREATER_THAN,lt:f.LESS_THAN,ge:f.GREATER_THAN_OR_EQUALS,le:f.LESS_THAN_OR_EQUALS,ap:f.APPROXIMATELY,sa:f.STARTS_AFTER,eb:f.ENDS_BEFORE,pr:f.PRESENT,po:void 0,ss:void 0,sb:void 0,in:f.IN,ni:f.NOT_IN,re:f.EQUALS,identifier:f.IDENTIFIER};function ll(r){let e=ul[r];if(!e)throw new d(b("Invalid operator: "+r));return e}var ni=class{constructor(e){this.parser=e}parse(){let e;this.parser.peek()?.value==="("?(this.parser.consume("("),e=this.parse(),this.parser.consume(")")):this.parser.peek()?.value==="not"?(this.parser.consume("Symbol","not"),this.parser.consume("("),e=new Bt(this.parse()),this.parser.consume(")")):e=new Ut(this.parser.consume("Symbol").value,ll(this.parser.consume("Symbol").value),this.parser.consume().value);let t=this.parser.peek()?.value;return t==="and"||t==="or"?(this.parser.consume("Symbol",t),new Wt(t,e,this.parse())):e}},pl=Ge();function dl(r){let e=pl.construct(Ko(r));return e.removeComments(),new ni(e).parse()}var fl={AA:"AA",AE:"AE",AR:"AR",CA:"CA",CE:"CE",CR:"CR"},hl={AA:"OK",AE:"Application Error",AR:"Application Reject",CA:"Commit Accept",CE:"Commit Error",CR:"Commit Reject"},le=class{constructor(e="\r",t="|",n="^",i="~",o="\\",s="&"){this.segmentSeparator=e,this.fieldSeparator=t,this.componentSeparator=n,this.repetitionSeparator=i,this.escapeCharacter=o,this.subcomponentSeparator=s}getMsh1(){return this.fieldSeparator}getMsh2(){return this.componentSeparator+this.repetitionSeparator+this.escapeCharacter+this.subcomponentSeparator}},ii=class r{constructor(e,t=new le){this.context=t,this.segments=e}get header(){return this.segments[0]}get(e){return this.getSegment(e)}getAll(e){return this.getAllSegments(e)}getSegment(e){return typeof e=="number"?this.segments[e]:this.segments.find(t=>t.name===e)}getAllSegments(e){return this.segments.filter(t=>t.name===e)}toString(){return this.segments.map(e=>e.toString()).join(this.context.segmentSeparator)}buildAck(e){let t=new Date,n=this.getSegment("MSH"),i=n?.getField(3)?.toString()??"",o=n?.getField(4)?.toString()??"",s=n?.getField(5)?.toString()??"",a=n?.getField(6)?.toString()??"",c=n?.getField(10)?.toString()??"",u=n?.getField(12)?.toString()??"2.5.1",p=e?.ackCode??"AA";return new r([new Xe(["MSH",this.context.getMsh2(),s,a,i,o,Xo(t),"",this.buildAckMessageType(n),t.getTime().toString(),"P",u],this.context),new Xe(["MSA",p,c,hl[p]],this.context),...e?.errSegment?[e.errSegment]:[]])}buildAckMessageType(e){let t=e?.getField(9),n=t?.getComponent(2),i=t?.getComponent(3),o="ACK";return n&&i?o=`ACK^${n}^ACK`:n&&(o=`ACK^${n}`),o}static parse(e){if(!e.startsWith("MSH")){let n=new Error("Invalid HL7 message");throw n.type="entity.parse.failed",n}let t=new le("\r",e.charAt(3),e.charAt(4),e.charAt(5),e.charAt(6),e.charAt(7));return new r(e.split(/[\r\n]+/).map(n=>Xe.parse(n,t)),t)}setSegment(e,t){if(t.name==="MSH"){if(typeof e=="number"){if(e!==0)return!1}else if(this.segments.findIndex(o=>o.name===e)!==0)return!1}else if(typeof e=="number"&&e===0&&t.name!=="MSH")return!1;if(typeof e=="number")return e>=this.segments.length?(this.segments.push(t),!0):(this.segments[e]=t,!0);let n=this.segments.findIndex(i=>i.name===e);return n===0&&t.name!=="MSH"?!1:n!==-1?(this.segments[n]=t,!0):!1}},Xe=class r{constructor(e,t=new le){this.context=t,bn(e)?this.fields=e.map(n=>H.parse(n,t)):this.fields=e,this.name=this.fields[0].components[0][0]}get(e){return this.fields[e]}getField(e){if(this.name==="MSH"){if(e===1)return new H([[this.context.getMsh1()]],this.context);if(e===2)return new H([[this.context.getMsh2()]],this.context);if(e>2)return this.fields[e-1]}return this.fields[e]}getComponent(e,t,n,i=0){return this.getField(e)?.getComponent(t,n,i)??""}toString(){return this.fields.map(e=>e.toString()).join(this.context.fieldSeparator)}static parse(e,t=new le){return new r(e.split(t.fieldSeparator).map(n=>H.parse(n,t)),t)}setField(e,t){if(this.name==="MSH"){if(e===1||e===2)return!1;if(e>2){let n=e-1;for(;this.fields.length<=n;)this.fields.push(new H([[""]],this.context));return this.fields[n]=typeof t=="string"?H.parse(t,this.context):t,!0}}for(;this.fields.length<=e;)this.fields.push(new H([[""]],this.context));return this.fields[e]=typeof t=="string"?H.parse(t,this.context):t,!0}setComponent(e,t,n,i,o=0){let s=this.getField(e);return s?s.setComponent(t,n,i,o):!1}},H=class r{constructor(e,t=new le){this.context=t,this.components=e}get(e,t,n=0){return this.getComponent(e+1,t,n)}getComponent(e,t,n=0){let i=this.components[n][e-1]??"";return t!==void 0&&(i=i.split(this.context.subcomponentSeparator)[t]??""),i}toString(){return this.components.map(e=>e.join(this.context.componentSeparator)).join(this.context.repetitionSeparator)}static parse(e,t=new le){return new r(e.split(t.repetitionSeparator).map(n=>n.split(t.componentSeparator)),t)}setComponent(e,t,n,i=0){if(e<1)return!1;if(i>=this.components.length)for(;this.components.length<=i;)this.components.push([""]);if(n!==void 0){if(n<0)return!1;let s=(this.components[i][e-1]||"").split(this.context.subcomponentSeparator);for(;s.length<=n;)s.push("");s[n]=t,this.components[i][e-1]=s.join(this.context.subcomponentSeparator)}else this.components[i][e-1]=t;return!0}};function ml(r,e){if(!r)return;let t=_e(r.slice(0,4),0),n=_e(r.slice(4,6),1)-1,i=_e(r.slice(6,8),1),o=_e(r.slice(8,10),0),s=_e(r.slice(10,12),0),a=_e(r.slice(12,14),0),c=0;r.includes(".")&&(c=_e(r.slice(15,19),0));let u=new Date(Date.UTC(t,n,i,o,s,a,c)),p=yl(r,e?.tzOffset);return p!==0&&(u=new Date(u.getTime()-p)),u.toISOString()}function _e(r,e){let t=parseInt(r,10);return isNaN(t)?e:t}function yl(r,e){let t=e,n=r.indexOf("+");n!==-1&&(t=r.slice(n));let i=r.indexOf("-");if(i!==-1&&(t=r.slice(i)),!t)return 0;let o=t.startsWith("-")?-1:1;t=t.slice(1).replace(":","");let s=parseInt(t.slice(0,2),10),a=parseInt(t.slice(2,4),10);return o*(s*60*60*1e3+a*60*1e3)}function Xo(r){let e=r instanceof Date?r:new Date(r),n=e.toISOString().replace(/[-:T]/g,"").replace(/(\.\d+)?Z$/,""),i=e.getUTCMilliseconds();return i>0&&(n+="."+i.toString()),n}var Le={NONE:0,ERROR:1,WARN:2,INFO:3,DEBUG:4},Yo=["NONE","ERROR","WARN","INFO","DEBUG"],oi=class r{constructor(e,t={},n=Le.INFO,i={}){this.write=e,this.metadata=t,this.level=n,this.options=i,i?.prefix&&(this.prefix=i.prefix),this.error=this.error.bind(this),this.warn=this.warn.bind(this),this.info=this.info.bind(this),this.debug=this.debug.bind(this),this.log=this.log.bind(this)}clone(e){let t=this.getLoggerConfig(),n=e?{...t,override:e,options:{...t.options,...e.options}}:t;return new r(n.write,n.metadata,n.level,n.options)}getLoggerConfig(){let{write:e,metadata:t,level:n,options:i}=this;return{write:e,metadata:t,level:n,options:i}}error(e,t){this.log(Le.ERROR,e,t)}warn(e,t){this.log(Le.WARN,e,t)}info(e,t){this.log(Le.INFO,e,t)}debug(e,t){this.log(Le.DEBUG,e,t)}log(e,t,n){e>this.level||(n instanceof Error&&(n={error:n.toString(),stack:n.stack?.split(`
`)}),this.write(JSON.stringify({level:Yo[e],timestamp:new Date().toISOString(),msg:this.prefix?`${this.prefix}${t}`:t,...n,...this.metadata})))}};function gl(r){let e=Le[r.toUpperCase()];if(e===void 0)throw new Error(`Invalid log level: ${r}`);return e}function xl(r){if(!r)throw new d(y("Resource type is null"));if(!cn(r))throw new d(y("Unknown resource type"))}function si(r,e,t){r===null?t.push(T(e,"Invalid null value")):Array.isArray(r)?vl(r,e,t):typeof r=="object"&&Tl(r,e,t)}function vl(r,e,t){for(let n=0;n<r.length;n++)r[n]===void 0?t.push(T(`${e}[${n}]`,"Invalid undefined value")):si(r[n],`${e}[${n}]`,t)}function Tl(r,e,t){for(let[n,i]of Object.entries(r))si(i,`${e}${e?".":""}${n}`,t)}function Sl(r){let e=[];return new Promise((t,n)=>{r.on("data",i=>e.push(Buffer.from(i))),r.on("error",i=>{r.destroy(),n(i)}),r.on("end",()=>{t(Buffer.concat(e))}),r.on("close",()=>{r.destroy()})})}function bl(r,e){let t=[];for(let n of e)t.push(...El(r,n));return t}function El(r,e){if(!r.resource)throw new Error("Resource type is required");if(r.resource!==e.resourceType)return[];let t={};if(r.constant)for(let i of r.constant){let o={type:"ViewDefinitionConstant",value:i};t["%"+i.name]=C(o,"value")}let n=v(e);if(r.where)for(let i of r.where){let o=P(i.path,[n],t);if(o.length!==1)return[];if(o[0].type!=="boolean")throw new Error("WHERE clause must evaluate to a boolean");if(!o[0].value)return[]}return ai(r,n,t)}function ai(r,e,t){let n=[],i;r.forEach?i=P(r.forEach,[e],t):r.forEachOrNull?i=P(r.forEachOrNull,[e],t):i=[e];for(let o of i){let s=[];for(let a of r.column??[]){let c=P(a.path,[o],t),u;if(c.length===0)u={[a.name]:null};else if(a.collection)u={[a.name]:c.map(p=>p.value)};else if(c.length===1)u={[a.name]:c[0].value};else throw new Error("Multiple values found but not expected for column");s.push([u])}for(let a of r.select??[]){let c=ai(a,o,t);s.push(c)}if(r.unionAll){let a=[];for(let c of r.unionAll)for(let u of ai(c,o,t))a.push(u);s.push(a)}n.push(...Rl(s))}if(i.length===0&&r.forEachOrNull){let o={};for(let s of r.column??[])o[s.name]=null;n.push(o)}return n}function Rl(r){if(r.length===0)return[];let e=r[0];for(let t=1;t<r.length;t++)e=Cl(e,r[t]);return e}function Cl(r,e){let t=[];for(let n of r)for(let i of e)t.push(Pl(n,i));return t}function Pl(r,e){let t={};return Object.assign(t,r),Object.assign(t,e),t}var Zo="https://meta.medplum.com/releases",Fr=new Map;function wl(){Fr.clear()}function es(r){let e=r;if(!e.tag_name)throw new Error("Manifest missing tag_name");let t=e.assets;if(!t?.length)throw new Error("Manifest missing assets list");for(let n of t){if(!n.browser_download_url)throw new Error("Asset missing browser download URL");if(!n.name)throw new Error("Asset missing name")}}async function Nr(r,e,t){let n=Fr.get(e??"latest");if(!n){let i=e?`v${e}`:"latest",o=new URL(`${Zo}/${i}.json`);if(o.searchParams.set("a",r),o.searchParams.set("c",Ar),t)for(let[c,u]of Object.entries(t))o.searchParams.set(c,u);let s=await fetch(o.toString());if(s.status!==200){let c;try{c=(await s.json()).message}catch(u){console.error(`Failed to parse message from body: ${Re(u)}`)}throw new Error(`Received status code ${s.status} while fetching manifest for version '${e??"latest"}'. Message: ${c}`)}let a=await s.json();es(a),n=a,Fr.set(e??"latest",n),e||Fr.set(n.tag_name.slice(1),n)}return n}function ts(r){return/^\d+\.\d+\.\d+$/.test(r)}async function Al(r,e){if(!ts(e))return!1;try{await Nr(r,e)}catch{return!1}return!0}async function Ol(r){let e=await Nr(r);if(!e.tag_name.startsWith("v"))throw new Error(`Invalid release name found. Release tag '${e.tag_name}' did not start with 'v'`);return e.tag_name.slice(1)}async function Il(r,e){try{let t=Ar.split("-")[0],i=(await Nr(r,void 0,e)).tag_name.slice(1);t!==i&&console.warn(`A new version (v${i}) of Medplum is available. Your current version (v${t}) may be missing important updates and bug fixes.`)}catch(t){console.warn(`Failed to check for newer version: ${Re(t)}`)}}0&&(module.exports={AccessPolicyInteraction,AckCode,AndAtom,ArithemticOperatorAtom,AsAtom,BooleanInfixOperatorAtom,CPT,ClientStorage,ConcatAtom,ContainsAtom,ContentType,DEFAULT_ACCEPT,DEFAULT_MAX_SEARCH_COUNT,DEFAULT_SEARCH_COUNT,DataSampler,DotAtom,EmptySetAtom,EqualsAtom,EquivalentAtom,EventTarget,ExternalSecretSystems,FHIRCAST_EVENT_NAMES,FHIRCAST_EVENT_RESOURCES,FHIRCAST_EVENT_VERSION_REQUIRED,FHIRCAST_RESOURCE_TYPES,FhirFilterComparison,FhirFilterConnective,FhirFilterNegation,FhirPathAtom,FhircastConnection,FileBuilder,FunctionAtom,HTTP_HL7_ORG,HTTP_TERMINOLOGY_HL7_ORG,Hl7Context,Hl7Field,Hl7Message,Hl7Segment,ICD10,ImpliesAtom,InAtom,IndexerAtom,InfixOperatorAtom,IsAtom,LOINC,LRUCache,LiteralAtom,LogLevel,LogLevelNames,Logger,MEDPLUM_CLI_CLIENT_ID,MEDPLUM_RELEASES_URL,MEDPLUM_VERSION,MedplumClient,MedplumKeyValueClient,MemoryStorage,MockAsyncClientStorage,NDC,NotEqualsAtom,NotEquivalentAtom,OAuthClientAssertionType,OAuthGrantType,OAuthTokenAuthMethod,OAuthTokenType,OperationOutcomeError,Operator,OperatorPrecedence,OrAtom,Parser,ParserBuilder,PrefixOperatorAtom,PropertyType,RXNORM,ReadablePromise,ReconnectingWebSocket,SNOMED,SearchParameterType,SubscriptionEmitter,SubscriptionManager,SymbolAtom,Tokenizer,TransformMapCollection,TypedEventTarget,UCUM,UnaryOperatorAtom,UnionAtom,VALID_HOSTNAME_REGEX,XorAtom,accepted,accessPolicySupportsInteraction,addProfileToResource,allOk,append,applyDefaultValuesToElement,applyDefaultValuesToResource,applyFixedOrPatternValue,arrayBufferToBase64,arrayBufferToHex,arrayify,assert,assertContextVersionOptional,assertOk,assertReleaseManifest,badRequest,booleanToTypedValue,buildElementsContext,buildTypeName,calculateAge,calculateAgeString,canReadResourceType,canWriteResource,canWriteResourceType,capitalize,checkForNull,checkIfValidMedplumVersion,clearReleaseCache,compressElement,concatUrls,conceptMapTranslate,conflict,convertContainedResourcesToBundle,convertToTransactionBundle,crawlTypedValue,crawlTypedValueAsync,createConstraintIssue,createFhircastMessagePayload,createOperationOutcomeIssue,createProcessingIssue,createReference,createStructureIssue,created,decodeBase64,decodeBase64Url,deepClone,deepEquals,deepIncludes,encodeBase64,encodeBase64Url,encryptSHA256,ensureNoLeadingSlash,ensureTrailingSlash,escapeHtml,evalFhirPath,evalFhirPathTyped,evalSqlOnFhir,expandSampledData,expandSampledObservation,fetchLatestVersionString,fetchVersionManifest,fhirPathArrayEquals,fhirPathArrayEquivalent,fhirPathArrayNotEquals,fhirPathEquals,fhirPathEquivalent,fhirPathIs,fhirPathNot,fhirTypeToJsType,findObservationInterval,findObservationReferenceRange,findObservationReferenceRanges,findResourceByCode,flatMapFilter,forbidden,formatAddress,formatCodeableConcept,formatCoding,formatDate,formatDateTime,formatFamilyName,formatGivenName,formatHl7DateTime,formatHumanName,formatMoney,formatObservationValue,formatPeriod,formatQuantity,formatRange,formatReferenceString,formatSearchQuery,formatTime,formatTiming,generateId,getAllDataTypes,getAllQuestionnaireAnswers,getCodeBySystem,getDataType,getDateProperty,getDefaultValuesForNewSliceEntry,getDisplayString,getElementDefinition,getElementDefinitionFromElements,getElementDefinitionTypeName,getExpressionForResourceType,getExpressionsForResourceType,getExtension,getExtensionValue,getIdentifier,getImageSrc,getNestedProperty,getParsedExpressionForResourceType,getPathDifference,getPathDisplayName,getPropertyDisplayName,getQueryString,getQuestionnaireAnswers,getRandomString,getReferenceString,getResourceTypes,getSearchParameter,getSearchParameterDetails,getSearchParameters,getStatus,getTypedPropertyValue,getTypedPropertyValueWithPath,getTypedPropertyValueWithSchema,getTypedPropertyValueWithoutSchema,getValueSliceName,getWebSocketUrl,globalSchema,gone,indexDefaultSearchParameters,indexSearchParameter,indexSearchParameterBundle,indexStructureDefinitionBundle,inflateBaseSchema,inflateElement,initFhirPathParserBuilder,isAccepted,isCodeableConcept,isCoding,isCompletedSubscriptionRequest,isComplexTypeCode,isConflict,isContextVersionRequired,isCreated,isDataTypeLoaded,isDateString,isDateTimeString,isEmpty,isError,isFhirCriteriaMet,isFhircastResourceType,isGone,isJwt,isLowerCase,isMedplumAccessToken,isNotFound,isObject,isOk,isOperationOutcome,isPeriod,isPopulated,isPrimitiveType,isProfileLoaded,isProfileResource,isQuantity,isQuantityEquivalent,isRedirect,isReference,isResource,isResourceType,isResourceTypeSchema,isResourceWithId,isSliceDefinitionWithTypes,isString,isStringArray,isTextObject,isTypedValue,isUUID,isUnauthenticated,isValidDate,isValidHostname,isValidMedplumSemver,lazy,loadDataType,mapByIdentifier,matchDiscriminant,matchesRange,matchesSearchRequest,multipleMatches,normalizeArrayBufferView,normalizeCreateBinaryOptions,normalizeCreatePdfOptions,normalizeErrorString,normalizeOperationOutcome,notFound,notModified,operationOutcomeIssueToString,operationOutcomeToString,parseFhirPath,parseFilterParameter,parseHl7DateTime,parseJWTPayload,parseLogLevel,parseMappingLanguage,parseParameter,parseReference,parseSearchRequest,parseStructureDefinition,parseXFhirQuery,preciseEquals,preciseGreaterThan,preciseGreaterThanOrEquals,preciseLessThan,preciseLessThanOrEquals,preciseRound,preconditionFailed,projectAdminResourceTypes,protectedResourceTypes,readInteractions,redirect,removeDuplicates,removeProfileFromResource,reorderBundle,resolveId,resourceMatchesSubscriptionCriteria,satisfiedAccessPolicy,serializeFhircastSubscriptionRequest,serverError,serverTimeout,setCodeBySystem,setIdentifier,singleton,singularize,sleep,sortStringArray,splitN,splitSearchOnComma,streamToBuffer,stringify,stringifyTypedValue,structureMapTransform,subsetResource,summarizeObservations,toJsBoolean,toPeriod,toTypedValue,tooManyRequests,tryGetDataType,tryGetJwtExpiration,tryGetProfile,typedValueToString,unauthorized,unauthorizedTokenAudience,unauthorizedTokenExpired,validateFhircastSubscriptionRequest,validateResource,validateResourceType,validateTypedValue,validationError,validationRegexes,warnIfNewerVersionAvailable,wordWrap});
/*!
 * Reconnecting WebSocket
 * by Pedro Ladaria <<EMAIL>>
 * https://github.com/pladaria/reconnecting-websocket
 * License MIT
 *
 * Copy of "partysocket" from Partykit team, a fork of the original "Reconnecting WebSocket"
 * https://github.com/partykit/partykit/blob/main/packages/partysocket
 */
//# sourceMappingURL=index.cjs.map
