import React from 'react';
import { FaCheckCircle, FaU<PERSON>Injured, FaCalendar<PERSON>lt, FaHeartbeat } from 'react-icons/fa';

const SuccessStep = ({ patientData, handleFinish }) => {

  console.log("SuccessStep")
  console.log(patientData)


  return (
    <div className="space-y-6">
      <div className="text-center">
        <FaCheckCircle className="mx-auto h-16 w-16 text-green-500" />
        <h3 className="mt-2 text-lg font-medium text-gray-900">Patient Registration Complete</h3>
        <p className="mt-1 text-sm text-gray-500">
          The patient has been successfully registered for EECP therapy
        </p>
      </div>

      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h4 className="text-md font-medium text-gray-900 flex items-center mb-4">
          <FaUserInjured className="mr-2 text-primary" />
          Patient Summary
        </h4>
        
        <div className="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2">
          <div>
            <h5 className="text-sm font-medium text-gray-500">Name</h5>
            <p className="text-sm text-gray-900">{patientData.firstName} {patientData.lastName}</p>
          </div>
          
          <div>
            <h5 className="text-sm font-medium text-gray-500">Date of Birth</h5>
            <p className="text-sm text-gray-900">{patientData.dateOfBirth}</p>
          </div>
          
          <div>
            <h5 className="text-sm font-medium text-gray-500">Primary Diagnosis</h5>
            <p className="text-sm text-gray-900">
              {patientData.diagnoses.length > 0 ? patientData.diagnoses[0] : 'Not specified'}
            </p>
          </div>
          
          <div>
            <h5 className="text-sm font-medium text-gray-500">Referring Physician</h5>
            <p className="text-sm text-gray-900">{patientData.referringPhysician || 'Not specified'}</p>
          </div>
          
          <div>
            <h5 className="text-sm font-medium text-gray-500">LVEF</h5>
            <p className={`text-sm ${patientData.lvef < 30 ? 'text-red-600 font-bold' : 'text-gray-900'}`}>
              {patientData.lvef ? `${patientData.lvef}%` : 'Not recorded'}
            </p>
          </div>
          
          <div>
            <h5 className="text-sm font-medium text-gray-500">BNP</h5>
            <p className={`text-sm ${patientData.bnp > 400 ? 'text-red-600 font-bold' : 'text-gray-900'}`}>
              {patientData.bnp ? `${patientData.bnp} pg/mL` : 'Not recorded'}
            </p>
          </div>
        </div>
      </div>

      <div className="bg-green-50 p-4 rounded-md border border-green-200">
        <div className="flex">
          <div className="flex-shrink-0">
            <FaCalendarAlt className="h-5 w-5 text-green-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-green-800">Next Steps</h3>
            <div className="mt-2 text-sm text-green-700">
              <p>
                The patient is now registered in the system. You can now:
              </p>
              <ul className="list-disc pl-5 mt-1 space-y-1">
                <li>Schedule the patient's first EECP session</li>
                <li>Review and update the patient's medical information</li>
                <li>Generate a patient summary report</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-blue-50 p-4 rounded-md border border-blue-200">
        <div className="flex">
          <div className="flex-shrink-0">
            <FaHeartbeat className="h-5 w-5 text-blue-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">About EECP Therapy</h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>
                Enhanced External Counterpulsation (EECP) is a non-invasive treatment for patients with 
                chronic stable angina and heart failure. The standard course consists of 35 one-hour 
                sessions over a 7-week period.
              </p>
              <p className="mt-2">
                EECP has been shown to reduce angina symptoms, improve exercise tolerance, and enhance 
                quality of life in patients with coronary artery disease.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-center">
        <button
          type="button"
          onClick={handleFinish}
          className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
        >
          Return to Dashboard
        </button>
      </div>
    </div>
  );
};

export default SuccessStep;
