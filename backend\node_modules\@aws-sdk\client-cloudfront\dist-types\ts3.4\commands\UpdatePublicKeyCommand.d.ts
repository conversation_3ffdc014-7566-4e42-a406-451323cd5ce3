import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CloudFrontClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../CloudFrontClient";
import {
  UpdatePublicKeyRequest,
  UpdatePublicKeyResult,
} from "../models/models_2";
export { __MetadataBearer };
export { $Command };
export interface UpdatePublicKeyCommandInput extends UpdatePublicKeyRequest {}
export interface UpdatePublicKeyCommandOutput
  extends UpdatePublicKeyResult,
    __MetadataBearer {}
declare const UpdatePublicKeyCommand_base: {
  new (
    input: UpdatePublicKeyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdatePublicKeyCommandInput,
    UpdatePublicKeyCommandOutput,
    CloudFrontClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdatePublicKeyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdatePublicKeyCommandInput,
    UpdatePublicKeyCommandOutput,
    CloudFrontClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdatePublicKeyCommand extends UpdatePublicKeyCommand_base {
  protected static __types: {
    api: {
      input: UpdatePublicKeyRequest;
      output: UpdatePublicKeyResult;
    };
    sdk: {
      input: UpdatePublicKeyCommandInput;
      output: UpdatePublicKeyCommandOutput;
    };
  };
}
