import React, { useState, useEffect, useRef } from 'react';
import { FaCog, FaInfoCircle, FaSave, FaDownload } from 'react-icons/fa';

// This component simulates a real-time ECG and plethysmogram waveform display
// In a real implementation, this would connect to actual EECP device data
const EECPWaveformDisplay = ({ 
  isRunning, 
  sessionData,
  onParameterChange,
  onCaptureSnapshot
}) => {
  const ecgCanvasRef = useRef(null);
  const plethCanvasRef = useRef(null);
  const [showSettings, setShowSettings] = useState(false);
  const [parameters, setParameters] = useState({
    cuffPressure: sessionData?.cuffPressure || 220,
    inflationRatio: 1.0,
    deflationTiming: 0,
    triggerDelay: 50
  });
  
  // Animation frame reference
  const animationRef = useRef(null);
  
  // Data points for the waveforms
  const ecgDataRef = useRef([]);
  const plethDataRef = useRef([]);
  
  // Timestamps for auto-saving
  const lastSaveRef = useRef(Date.now());
  const [lastSaved, setLastSaved] = useState('Just now');
  
  // Initialize the waveform data
  useEffect(() => {
    // Generate initial ECG waveform data (simulated)
    ecgDataRef.current = generateECGData();
    
    // Generate initial plethysmogram data (simulated)
    plethDataRef.current = generatePlethData();
    
    // Set up auto-save interval
    const autoSaveInterval = setInterval(() => {
      if (isRunning) {
        const now = Date.now();
        const timeSinceLastSave = now - lastSaveRef.current;
        
        // Auto-save every 30 seconds
        if (timeSinceLastSave >= 30000) {
          handleAutoSave();
          lastSaveRef.current = now;
          
          // Update the last saved timestamp
          setLastSaved('Just now');
        } else {
          // Update the last saved timestamp
          const secondsAgo = Math.floor(timeSinceLastSave / 1000);
          setLastSaved(`${secondsAgo}s ago`);
        }
      }
    }, 1000);
    
    return () => {
      clearInterval(autoSaveInterval);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isRunning]);
  
  // Start or stop the animation based on isRunning
  useEffect(() => {
    if (isRunning) {
      startAnimation();
    } else {
      stopAnimation();
    }
    
    return () => {
      stopAnimation();
    };
  }, [isRunning]);
  
  // Handle parameter changes
  useEffect(() => {
    if (onParameterChange) {
      onParameterChange(parameters);
    }
  }, [parameters, onParameterChange]);
  
  // Generate simulated ECG data
  const generateECGData = () => {
    const data = [];
    const cycles = 5;
    const pointsPerCycle = 100;
    
    for (let cycle = 0; cycle < cycles; cycle++) {
      for (let i = 0; i < pointsPerCycle; i++) {
        const x = i / pointsPerCycle;
        
        // P wave
        let y = 0;
        if (x < 0.2) {
          y = Math.sin(x * Math.PI * 5) * 0.2;
        }
        
        // QRS complex
        if (x >= 0.2 && x < 0.35) {
          const qrsX = (x - 0.2) / 0.15;
          y = Math.sin(qrsX * Math.PI) * (qrsX < 0.5 ? -0.8 : 1.2);
        }
        
        // T wave
        if (x >= 0.45 && x < 0.7) {
          y = Math.sin((x - 0.45) * Math.PI * 4) * 0.3;
        }
        
        data.push(y);
      }
    }
    
    return data;
  };
  
  // Generate simulated plethysmogram data
  const generatePlethData = () => {
    const data = [];
    const cycles = 5;
    const pointsPerCycle = 100;
    
    for (let cycle = 0; cycle < cycles; cycle++) {
      for (let i = 0; i < pointsPerCycle; i++) {
        const x = i / pointsPerCycle;
        
        // Systolic peak
        let y = 0;
        if (x < 0.3) {
          y = Math.pow(Math.sin(x * Math.PI / 0.3), 2) * 0.8;
        }
        
        // Diastolic decay
        if (x >= 0.3) {
          y = 0.8 * Math.exp(-(x - 0.3) * 5);
        }
        
        // Add dicrotic notch
        if (x >= 0.4 && x < 0.5) {
          y += Math.sin((x - 0.4) * Math.PI * 10) * 0.1;
        }
        
        data.push(y);
      }
    }
    
    return data;
  };
  
  // Start the animation loop
  const startAnimation = () => {
    if (!ecgCanvasRef.current || !plethCanvasRef.current) return;
    
    const ecgCanvas = ecgCanvasRef.current;
    const plethCanvas = plethCanvasRef.current;
    const ecgCtx = ecgCanvas.getContext('2d');
    const plethCtx = plethCanvas.getContext('2d');
    
    let ecgOffset = 0;
    let plethOffset = 0;
    
    const animate = () => {
      // Clear the canvases
      ecgCtx.clearRect(0, 0, ecgCanvas.width, ecgCanvas.height);
      plethCtx.clearRect(0, 0, plethCanvas.width, plethCanvas.height);
      
      // Draw the ECG waveform
      drawWaveform(ecgCtx, ecgCanvas, ecgDataRef.current, ecgOffset, '#FF4560');
      
      // Draw the plethysmogram waveform
      drawWaveform(plethCtx, plethCanvas, plethDataRef.current, plethOffset, '#00E396');
      
      // Update the offsets
      ecgOffset = (ecgOffset + 1) % ecgDataRef.current.length;
      plethOffset = (plethOffset + 1) % plethDataRef.current.length;
      
      // Request the next frame
      animationRef.current = requestAnimationFrame(animate);
    };
    
    animate();
  };
  
  // Stop the animation loop
  const stopAnimation = () => {
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
      animationRef.current = null;
    }
  };
  
  // Draw a waveform on the canvas
  const drawWaveform = (ctx, canvas, data, offset, color) => {
    const width = canvas.width;
    const height = canvas.height;
    const midY = height / 2;
    const amplitude = height * 0.4;
    
    ctx.beginPath();
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    
    // Draw the grid
    drawGrid(ctx, width, height);
    
    // Draw the waveform
    for (let x = 0; x < width; x++) {
      const dataIndex = (offset + x) % data.length;
      const y = midY - data[dataIndex] * amplitude;
      
      if (x === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    
    ctx.stroke();
  };
  
  // Draw a grid on the canvas
  const drawGrid = (ctx, width, height) => {
    ctx.save();
    
    // Draw the grid
    ctx.beginPath();
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
    ctx.lineWidth = 1;
    
    // Draw vertical grid lines
    const verticalSpacing = width / 10;
    for (let x = 0; x < width; x += verticalSpacing) {
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
    }
    
    // Draw horizontal grid lines
    const horizontalSpacing = height / 5;
    for (let y = 0; y < height; y += horizontalSpacing) {
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
    }
    
    ctx.stroke();
    
    // Draw the baseline
    ctx.beginPath();
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
    ctx.moveTo(0, height / 2);
    ctx.lineTo(width, height / 2);
    ctx.stroke();
    
    ctx.restore();
  };
  
  // Handle parameter changes
  const handleParameterChange = (e) => {
    const { name, value } = e.target;
    
    setParameters(prev => ({
      ...prev,
      [name]: parseFloat(value)
    }));
  };
  
  // Handle auto-save
  const handleAutoSave = () => {
    // In a real implementation, this would save the session data to the server
    console.log('Auto-saving session data:', {
      timestamp: new Date().toISOString(),
      parameters,
      sessionData
    });
  };
  
  // Handle manual save
  const handleManualSave = () => {
    handleAutoSave();
    lastSaveRef.current = Date.now();
    setLastSaved('Just now');
  };
  
  // Handle snapshot capture
  const handleCaptureSnapshot = () => {
    if (onCaptureSnapshot) {
      // Create a composite image of both canvases
      const snapshotCanvas = document.createElement('canvas');
      snapshotCanvas.width = ecgCanvasRef.current.width;
      snapshotCanvas.height = ecgCanvasRef.current.height * 2 + 10; // Add some spacing
      
      const snapshotCtx = snapshotCanvas.getContext('2d');
      
      // Draw the ECG canvas
      snapshotCtx.drawImage(ecgCanvasRef.current, 0, 0);
      
      // Draw the pleth canvas below it
      snapshotCtx.drawImage(plethCanvasRef.current, 0, ecgCanvasRef.current.height + 10);
      
      // Convert to data URL
      const dataUrl = snapshotCanvas.toDataURL('image/png');
      
      // Call the callback with the data URL
      onCaptureSnapshot(dataUrl);
    }
  };
  
  return (
    <div className="bg-white p-4 rounded-lg shadow">
      <div className="flex justify-between items-center mb-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Real-time Waveforms</h3>
          <p className="text-sm text-gray-500">ECG and Plethysmogram</p>
        </div>
        <div className="flex items-center space-x-2">
          <div className="text-xs text-gray-500 flex items-center">
            <span className="inline-block h-2 w-2 rounded-full bg-green-500 mr-1"></span>
            Auto-saved {lastSaved}
          </div>
          <button
            type="button"
            onClick={handleManualSave}
            className="inline-flex items-center p-1 border border-transparent rounded-full shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            title="Save now"
          >
            <FaSave className="h-4 w-4" />
          </button>
          <button
            type="button"
            onClick={handleCaptureSnapshot}
            className="inline-flex items-center p-1 border border-transparent rounded-full shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            title="Capture snapshot"
          >
            <FaDownload className="h-4 w-4" />
          </button>
          <button
            type="button"
            onClick={() => setShowSettings(!showSettings)}
            className="inline-flex items-center p-1 border border-transparent rounded-full shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            title="Settings"
          >
            <FaCog className="h-4 w-4" />
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 gap-4">
        <div>
          <div className="flex items-center mb-1">
            <div className="h-3 w-3 rounded-full bg-red-500 mr-2"></div>
            <span className="text-sm font-medium text-gray-700">ECG</span>
          </div>
          <div className="bg-gray-50 rounded border border-gray-200">
            <canvas 
              ref={ecgCanvasRef} 
              width={600} 
              height={150}
              className="w-full h-auto"
            ></canvas>
          </div>
        </div>
        
        <div>
          <div className="flex items-center mb-1">
            <div className="h-3 w-3 rounded-full bg-green-500 mr-2"></div>
            <span className="text-sm font-medium text-gray-700">Plethysmogram</span>
          </div>
          <div className="bg-gray-50 rounded border border-gray-200">
            <canvas 
              ref={plethCanvasRef} 
              width={600} 
              height={150}
              className="w-full h-auto"
            ></canvas>
          </div>
        </div>
      </div>
      
      {showSettings && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h4 className="text-sm font-medium text-gray-900 mb-3">EECP Parameters</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="cuffPressure" className="block text-xs font-medium text-gray-700 mb-1">
                Cuff Pressure (mmHg)
              </label>
              <div className="flex items-center">
                <input
                  type="range"
                  id="cuffPressure"
                  name="cuffPressure"
                  min="180"
                  max="300"
                  step="5"
                  value={parameters.cuffPressure}
                  onChange={handleParameterChange}
                  className="flex-grow h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <span className="ml-2 text-sm font-medium text-gray-900 w-12 text-right">
                  {parameters.cuffPressure}
                </span>
              </div>
            </div>
            
            <div>
              <label htmlFor="inflationRatio" className="block text-xs font-medium text-gray-700 mb-1">
                Inflation Ratio
                <span className="ml-1 text-gray-500 cursor-help" title="Ratio of inflation to deflation time">
                  <FaInfoCircle className="inline h-3 w-3" />
                </span>
              </label>
              <div className="flex items-center">
                <input
                  type="range"
                  id="inflationRatio"
                  name="inflationRatio"
                  min="0.5"
                  max="1.5"
                  step="0.1"
                  value={parameters.inflationRatio}
                  onChange={handleParameterChange}
                  className="flex-grow h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <span className="ml-2 text-sm font-medium text-gray-900 w-12 text-right">
                  {parameters.inflationRatio.toFixed(1)}
                </span>
              </div>
            </div>
            
            <div>
              <label htmlFor="deflationTiming" className="block text-xs font-medium text-gray-700 mb-1">
                Deflation Timing (ms)
                <span className="ml-1 text-gray-500 cursor-help" title="Timing offset for deflation relative to the R wave">
                  <FaInfoCircle className="inline h-3 w-3" />
                </span>
              </label>
              <div className="flex items-center">
                <input
                  type="range"
                  id="deflationTiming"
                  name="deflationTiming"
                  min="-50"
                  max="50"
                  step="5"
                  value={parameters.deflationTiming}
                  onChange={handleParameterChange}
                  className="flex-grow h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <span className="ml-2 text-sm font-medium text-gray-900 w-12 text-right">
                  {parameters.deflationTiming}
                </span>
              </div>
            </div>
            
            <div>
              <label htmlFor="triggerDelay" className="block text-xs font-medium text-gray-700 mb-1">
                Trigger Delay (ms)
                <span className="ml-1 text-gray-500 cursor-help" title="Delay between R wave detection and cuff inflation">
                  <FaInfoCircle className="inline h-3 w-3" />
                </span>
              </label>
              <div className="flex items-center">
                <input
                  type="range"
                  id="triggerDelay"
                  name="triggerDelay"
                  min="0"
                  max="100"
                  step="5"
                  value={parameters.triggerDelay}
                  onChange={handleParameterChange}
                  className="flex-grow h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <span className="ml-2 text-sm font-medium text-gray-900 w-12 text-right">
                  {parameters.triggerDelay}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EECPWaveformDisplay;
