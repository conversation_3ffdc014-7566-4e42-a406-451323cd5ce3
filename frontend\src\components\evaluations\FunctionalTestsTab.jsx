import React from 'react';
import { TrendingUp, Info } from 'lucide-react';

const FunctionalTestsTab = ({ functionalTests, onChange }) => {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-6">Functional Tests Comparison</h3>
      
      <div className="space-y-6">
        <div className="grid grid-cols-3 gap-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Classification</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-1">NYHA Class</label>
                <select 
                  value={functionalTests.nyhaClass}
                  onChange={(e) => onChange('nyhaClass', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">Select Class</option>
                  <option value="1">Class I</option>
                  <option value="2">Class II</option>
                  <option value="3">Class III</option>
                  <option value="4">Class IV</option>
                </select>
                <div className="text-xs text-gray-500 mt-1">
                  Baseline: Class 3
                </div>
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">CCS Class</label>
                <select 
                  value={functionalTests.ccsClass}
                  onChange={(e) => onChange('ccsClass', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">Select Class</option>
                  <option value="1">Class I</option>
                  <option value="2">Class II</option>
                  <option value="3">Class III</option>
                  <option value="4">Class IV</option>
                </select>
                <div className="text-xs text-gray-500 mt-1">
                  Baseline: Class 3
                </div>
              </div>
            </div>
            
            <div className="mt-4 p-2 bg-green-50 rounded border border-green-200">
              <div className="flex items-center">
                <Info size={16} className="text-green-600 mr-1" />
                <span className="text-sm text-green-800">
                  Improved from Class 3 to Class 1 (CCS)
                </span>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Left Ventricular Function</h4>
            <div>
              <label className="block text-xs text-gray-500 mb-1">LVEF %</label>
              <input
                type="text"
                value={functionalTests.lvef}
                onChange={(e) => onChange('lvef', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Enter LVEF %"
              />
              <div className="text-xs text-gray-500 mt-1">
                Baseline: 38%
              </div>
            </div>
            
            <div className="mt-4 p-2 bg-green-50 rounded border border-green-200">
              <div className="flex items-center">
                <TrendingUp size={16} className="text-green-600 mr-1" />
                <span className="text-sm text-green-800">
                  +6% absolute improvement in LVEF
                </span>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-3">6-Minute Walk Distance</h4>
            <div>
              <label className="block text-xs text-gray-500 mb-1">Distance</label>
              <input
                type="text"
                value={functionalTests.walkDistance}
                onChange={(e) => onChange('walkDistance', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Enter distance in meters"
              />
              <div className="text-xs text-gray-500 mt-1">
                meters
              </div>
            </div>
            
            <div className="mt-4 p-2 bg-green-50 rounded border border-green-200">
              <div className="flex items-center">
                <TrendingUp size={16} className="text-green-600 mr-1" />
                <span className="text-sm text-green-800">
                  +105m (33% improvement)
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-700 mb-2">Functional Assessment Notes</h4>
          <textarea
            value={functionalTests.notes}
            onChange={(e) => onChange('notes', e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md h-24"
            placeholder="Enter notes about functional assessment..."
          ></textarea>
        </div>
      </div>
    </div>
  );
};

export default FunctionalTestsTab; 