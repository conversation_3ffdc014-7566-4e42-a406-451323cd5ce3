{"version": 3, "file": "authService.js", "sourceRoot": "", "sources": ["../../app/auth/authService.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,WAAW,EAAqC,MAAM,cAAc,CAAC;AAE9E,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAE3D;;GAEG;AACH,MAAM,OAAO,WAAW;IACtB,wCAAwC;IAChC,aAAa,CAAiB;IAEtC;QACE,2CAA2C;QAC3C,2CAA2C;QAC3C,mDAAmD;QACnD,qDAAqD;QACrD,wBAAwB;QACxB,MAAM;QAEN,IAAI,CAAC,aAAa,GAAG,IAAI,cAAc,EAAE,CAAC;IAC5C,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,KAAK,CAAC,WAAyB;QAC1C,IAAI,CAAC;YACH,kDAAkD;YAClD,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAC/B,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;YAErE,sCAAsC;YACtC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAC9C,WAAW,CAAC,KAAK,EACjB,WAAW,CAAC,QAAQ,CACrB,CAAC;YACF,+BAA+B;YAC/B,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;gBACrB,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;YAC5D,CAAC;YAED,MAAM,aAAa,GAAkB;gBACnC,KAAK,EAAE,SAAS,CAAC,KAAK;gBACtB,IAAI,EAAE;oBACJ,KAAK,EAAE,WAAW,CAAC,KAAK;oBACxB,SAAS,EAAE,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;oBACnD,QAAQ,EAAE,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,MAAM;oBAC9C,IAAI,EAAE,QAAQ;iBACf;aACF,CAAC;YACF,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,8DAA8D;YAC9D,IAAI,KAAK,YAAY,CAAC,CAAC,QAAQ,EAAE,CAAC;gBAChC,MAAM,gBAAgB,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrG,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,gBAAgB,CAAC,CAAC;gBACxE,MAAM,IAAI,KAAK,CAAC,sBAAsB,gBAAgB,EAAE,CAAC,CAAC;YAC5D,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;gBACxC,MAAM,IAAI,KAAK,CAAC,iBAAiB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAAA,CAAC"}