# Code Quality (Python)

## 1.1 General Conventions
- Use snake_case for variable names
- Use PascalCase for class names
- Use UPPER_CASE_SNAKE_CASE for constants (MAX_RETRY_COUNT)
- Keep Lines under 79-100 characters
- Boolean Variables: Should sound like yes/no questions (e.g., is_user_logged_in)
- Ensure security of API Keys in ENV files

## 1.2 File Structure.

```
# 1. Module Docstring (For the whole file)
"""
user.py
Module for managing user-related operations.
"""

# 2. Imports (Standard, Third-party, Local, in this order)
import os
from typing import Optional
from myapp.database import UserRepository

# 3. Constants
MAX_LOGIN_ATTEMPTS = 5

# 4. Classes
class User:
    """
    Represents a user in the system.
    """

    def __init__(self, username: str, email: str) -> None:
        self.username = username
        self.email = email

    # 5. Instance Methods
    def reset_password(self) -> None:
        """Sends a password reset link to the user's email."""
        print(f"Password reset link sent to {self.email}")

# 6. Top-level functions
def fetch_user_by_id(user_id: str) -> Optional[User]:
    """
    Fetches a user by their unique ID.

    Args:
        user_id (str): The unique identifier for the user.

    Returns:
        Optional[User]: The user object if found, otherwise None.

    Raises:
        ValueError: If user_id is None.
    """
    if user_id is None:
        raise ValueError("User ID cannot be None")
    # Imagine userRepository is a database accessor
    return UserRepository.find_by_id(user_id)
```

## 1.3 Docstrings
- triple double-quotes (""") for module, class, and method/function docstrings.
- Every public class, method, and function should have a docstring.
- Include sections for Args, Returns, and Raises as needed.

## 1.4 Folder Structure

# Code Quality (React)

## 2.1 General Conventions

- Use functional components and React Hooks for state and lifecycle management. (eg useState, useEffect)
- Keep lines under 100 characters when possible.
- One file, one component (except for very small, closely related helpers) (Refrain from pages having nested component code.)
- Component Names: PascalCase `(e.g, UserProfileCard).`
- Props/State/Variables: camelCase `(e.g., userList, setUserList).`
- Small, focused components: Each should do one thing well.
- Ensure security of API Keys in ENV files

## 2.2 File Structure
```
/src
  /components
    /appointments
    /auth
    /dashboard
      dashboard_pages...
    /evaluations
      evaluation_pages...
    /functions
    /layouts
    ... 
  /assets
  /utils
    fetchData.js
  /pages
    Dashboard.jsx
  App.jsx
  index.js
```

## 2.3 Comment Structure for Functions

For functions use this format.

/**
 * Fetches appointment data from the API.
 *
 * @param {string} userId - The ID of the user whose appointments to fetch.
 * @returns {Promise<Array>} A promise resolving to an array of appointments.
 * @throws {Error} If the fetch request fails.
 */
async function fetchAppointments(userId) {
  // function code
}

For Full Components please use this
/**
 * Renders a list of upcoming appointments for the user.
 *
 * @param {Object} props
 * @param {Array} props.appointments - The array of appointment objects.
 */
function AppointmentList({ appointments }) {
  // component code
}

## 2.4 API and Fetching
- Use fetch for HTTP requests.