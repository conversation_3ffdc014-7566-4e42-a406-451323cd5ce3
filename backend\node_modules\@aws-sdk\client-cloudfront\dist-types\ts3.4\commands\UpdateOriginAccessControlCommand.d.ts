import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CloudFrontClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../CloudFrontClient";
import {
  UpdateOriginAccessControlRequest,
  UpdateOriginAccessControlResult,
} from "../models/models_2";
export { __MetadataBearer };
export { $Command };
export interface UpdateOriginAccessControlCommandInput
  extends UpdateOriginAccessControlRequest {}
export interface UpdateOriginAccessControlCommandOutput
  extends UpdateOriginAccessControlResult,
    __MetadataBearer {}
declare const UpdateOriginAccessControlCommand_base: {
  new (
    input: UpdateOriginAccessControlCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateOriginAccessControlCommandInput,
    Update<PERSON>riginAccessControlCommandOutput,
    CloudFrontClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateOriginAccessControlCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateOriginAccessControlCommandInput,
    UpdateOriginAccessControlCommandOutput,
    CloudFrontClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateOriginAccessControlCommand extends UpdateOriginAccessControlCommand_base {
  protected static __types: {
    api: {
      input: UpdateOriginAccessControlRequest;
      output: UpdateOriginAccessControlResult;
    };
    sdk: {
      input: UpdateOriginAccessControlCommandInput;
      output: UpdateOriginAccessControlCommandOutput;
    };
  };
}
