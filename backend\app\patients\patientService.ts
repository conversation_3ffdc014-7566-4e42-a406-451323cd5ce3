/**
 * @file patientService.ts
 * @description Service layer for patient-related operations using FHIR standards.
 * Handles conversion between internal schemas and FHIR Patient resources.
 */

import { MedplumClient } from '@medplum/core';
import { Patient, Extension } from '@medplum/fhirtypes';
import {
    PatientCreate,
    PatientUpdate,
    PatientRead,
    validatePatientCreate
} from './patientSchemas.js';

// Constants
const INTAKE_REASON_EXTENSION_URL =
    'http://syncore.com/fhir/StructureDefinition/intake-reason';
const MAX_SEARCH_RESULTS = 100;
const DEFAULT_SEARCH_LIMIT = 20;

// Type aliases for better readability
type PatientWithExtensions = Patient;

/**
 * Converts internal patient schema to FHIR Patient resource format.
 * 
 * @param {PatientCreate} patient - Internal patient data structure
 * @returns {PatientWithExtensions} FHIR-compliant Patient resource
 */
function toFHIRPatient(patient: PatientCreate): PatientWithExtensions {
    const fhirPatient: PatientWithExtensions = {
        resourceType: 'Patient',
        active: true,
        name: [{
            use: 'official',
            family: patient.family,
            given: patient.given
        }]
    };

    if (patient.gender) {
        fhirPatient.gender = patient.gender;
    }

    if (patient.birthDate) {
        fhirPatient.birthDate = patient.birthDate;
    }

    // Store intake reason in FHIR extension field
    if (patient.intakeReason) {
        fhirPatient.extension = fhirPatient.extension || [];
        fhirPatient.extension.push({
            url: INTAKE_REASON_EXTENSION_URL,
            valueString: patient.intakeReason
        });
    }

    return fhirPatient;
}

/**
 * Converts FHIR Patient resource to internal patient schema format.
 * 
 * @param {PatientWithExtensions} fhirPatient - FHIR Patient resource
 * @returns {PatientRead} Internal patient data structure
 */
function fromFHIRPatient(fhirPatient: PatientWithExtensions): PatientRead {
    const patient: PatientRead = {
        id: fhirPatient.id!,
        resourceType: 'Patient',
        family: '',
        given: []
    };

    // Extract name from FHIR format
    if (fhirPatient.name && fhirPatient.name.length > 0) {
        const officialName = fhirPatient.name.find((n: any) =>
            n.use === 'official'
        ) || fhirPatient.name[0];

        if (officialName.family) {
            patient.family = officialName.family;
        }
        if (officialName.given) {
            patient.given = officialName.given;
        }
    }

    if (fhirPatient.gender) {
        patient.gender = fhirPatient.gender;
    }

    if (fhirPatient.birthDate) {
        patient.birthDate = fhirPatient.birthDate;
    }

    // Extract intake reason from extensions
    if (fhirPatient.extension && fhirPatient.extension.length > 0) {
        const intakeExtension = fhirPatient.extension.find((ext: any) =>
            ext.url === INTAKE_REASON_EXTENSION_URL
        );
        if (intakeExtension && intakeExtension.valueString) {
            patient.intakeReason = intakeExtension.valueString;
        }
    }

    // Include FHIR metadata
    if (fhirPatient.meta) {
        patient.meta = fhirPatient.meta;
    }

    return patient;
}

/**
 * Creates a new patient in Medplum FHIR server.
 * 
 * @param {MedplumClient} client - Authenticated Medplum client instance
 * @param {PatientCreate} patientIn - Patient data to create
 * @returns {Promise<PatientRead>} Promise resolving to created patient data
 * @throws {Error} If validation fails or Medplum operation fails
 */
export async function createPatient(
    client: MedplumClient,
    patientIn: PatientCreate
): Promise<PatientRead> {
    try {
        // Validate input
        const validationErrors = validatePatientCreate(patientIn);
        if (validationErrors.length > 0) {
            throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
        }

        console.log('🏥 Creating patient:', {
            family: patientIn.family,
            given: patientIn.given.join(' '),
            gender: patientIn.gender,
            birthDate: patientIn.birthDate,
            intakeReason: patientIn.intakeReason
        });

        // Convert to FHIR format
        const fhirPatient = toFHIRPatient(patientIn);

        // Create in Medplum
        const createdPatient = await client.createResource(fhirPatient) as
            PatientWithExtensions;

        console.log(' Patient created successfully:', createdPatient.id);

        // Convert back to our format
        return fromFHIRPatient(createdPatient);

    } catch (error: any) {
        console.error(' Error in createPatient:', error);

        if (error.message.includes('Validation failed')) {
            throw error;
        }

        // Handle Medplum-specific errors
        if (error.response?.status === 400) {
            const diagnostics = error.response.data?.issue?.[0]?.diagnostics ||
                error.message;
            throw new Error(`Invalid patient data: ${diagnostics}`);
        } else if (error.response?.status === 401) {
            throw new Error('Authentication failed. Please check Medplum credentials.');
        } else if (error.response?.status === 403) {
            throw new Error('Permission denied. Insufficient access rights.');
        } else {
            throw new Error(`Failed to create patient: ${error.message}`);
        }
    }
}

/**
 * Retrieves a patient by ID from Medplum FHIR server.
 * 
 * @param {MedplumClient} client - Authenticated Medplum client instance
 * @param {string} patientId - Unique patient identifier
 * @returns {Promise<PatientRead>} Promise resolving to patient data
 * @throws {Error} If patient not found or Medplum operation fails
 */
export async function getPatient(
    client: MedplumClient,
    patientId: string
): Promise<PatientRead> {
    try {
        if (!patientId || patientId.trim().length === 0) {
            throw new Error('Patient ID is required');
        }

        console.log(' Retrieving patient:', patientId);

        // Get from Medplum
        const fhirPatient = await client.readResource('Patient', patientId) as
            PatientWithExtensions;

        if (!fhirPatient) {
            throw new Error(`Patient not found: ${patientId}`);
        }

        console.log(' Patient retrieved successfully:', patientId);

        // Convert to our format
        return fromFHIRPatient(fhirPatient);

    } catch (error: any) {
        console.error(' Error in getPatient:', error);

        if (error.response?.status === 404) {
            throw new Error(`Patient not found: ${patientId}`);
        } else if (error.response?.status === 401) {
            throw new Error('Authentication failed. Please check Medplum credentials.');
        } else if (error.response?.status === 403) {
            throw new Error('Permission denied. Insufficient access rights.');
        } else {
            throw new Error(`Failed to retrieve patient: ${error.message}`);
        }
    }
}

/**
 * Updates an existing patient in Medplum FHIR server.
 * 
 * @param {MedplumClient} client - Authenticated Medplum client instance
 * @param {string} patientId - Unique patient identifier
 * @param {PatientUpdate} patientIn - Patient data to update
 * @returns {Promise<PatientRead>} Promise resolving to updated patient data
 * @throws {Error} If patient not found or Medplum operation fails
 */
export async function updatePatient(
    client: MedplumClient,
    patientId: string,
    patientIn: PatientUpdate
): Promise<PatientRead> {
    try {
        if (!patientId || patientId.trim().length === 0) {
            throw new Error('Patient ID is required');
        }

        console.log('📝 Updating patient:', patientId, patientIn);

        // First, get the existing patient
        const existingPatient = await client.readResource('Patient', patientId) as
            PatientWithExtensions;
        if (!existingPatient) {
            throw new Error(`Patient not found: ${patientId}`);
        }

        // Update only the provided fields
        const updatedPatient: PatientWithExtensions = { ...existingPatient };

        if (patientIn.gender !== undefined) {
            updatedPatient.gender = patientIn.gender;
        }

        if (patientIn.birthDate !== undefined) {
            updatedPatient.birthDate = patientIn.birthDate;
        }

        if (patientIn.intakeReason !== undefined) {
            // Update intake reason in extensions
            if (!updatedPatient.extension) {
                updatedPatient.extension = [];
            }

            // Find existing intake reason extension
            const intakeExtensionIndex = updatedPatient.extension.findIndex(
                (ext: any) => ext.url === INTAKE_REASON_EXTENSION_URL
            );

            const newExtension = {
                url: INTAKE_REASON_EXTENSION_URL,
                valueString: patientIn.intakeReason
            };

            if (intakeExtensionIndex >= 0) {
                // Update existing extension
                updatedPatient.extension[intakeExtensionIndex] = newExtension;
            } else {
                // Add new extension
                updatedPatient.extension.push(newExtension);
            }
        }

        // Update in Medplum
        const result = await client.updateResource(updatedPatient) as
            PatientWithExtensions;

        console.log(' Patient updated successfully:', patientId);

        // Convert back to our format
        return fromFHIRPatient(result);

    } catch (error: any) {
        console.error(' Error in updatePatient:', error);

        if (error.response?.status === 404) {
            throw new Error(`Patient not found: ${patientId}`);
        } else if (error.response?.status === 400) {
            const diagnostics = error.response.data?.issue?.[0]?.diagnostics ||
                error.message;
            throw new Error(`Invalid update data: ${diagnostics}`);
        } else if (error.response?.status === 401) {
            throw new Error('Authentication failed. Please check Medplum credentials.');
        } else if (error.response?.status === 403) {
            throw new Error('Permission denied. Insufficient access rights.');
        } else {
            throw new Error(`Failed to update patient: ${error.message}`);
        }
    }
}

/**
 * Deletes a patient from Medplum FHIR server.
 * 
 * @param {MedplumClient} client - Authenticated Medplum client instance
 * @param {string} patientId - Unique patient identifier
 * @returns {Promise<void>} Promise resolving when deletion is complete
 * @throws {Error} If patient not found or Medplum operation fails
 */
export async function deletePatient(
    client: MedplumClient,
    patientId: string
): Promise<void> {
    try {
        if (!patientId || patientId.trim().length === 0) {
            throw new Error('Patient ID is required');
        }

        console.log(' Deleting patient:', patientId);

        // Check if patient exists first
        const existingPatient = await client.readResource('Patient', patientId);
        if (!existingPatient) {
            throw new Error(`Patient not found: ${patientId}`);
        }

        // Delete from Medplum
        await client.deleteResource('Patient', patientId);

        console.log(' Patient deleted successfully:', patientId);

    } catch (error: any) {
        console.error(' Error in deletePatient:', error);

        if (error.response?.status === 404) {
            throw new Error(`Patient not found: ${patientId}`);
        } else if (error.response?.status === 401) {
            throw new Error('Authentication failed. Please check Medplum credentials.');
        } else if (error.response?.status === 403) {
            throw new Error('Permission denied. Insufficient access rights.');
        } else {
            throw new Error(`Failed to delete patient: ${error.message}`);
        }
    }
}

/**
 * Searches for patients with pagination and filtering options.
 * 
 * @param {MedplumClient} client - Authenticated Medplum client instance
 * @param {Object} params - Search parameters
 * @param {string} [params.search] - Text search across name fields
 * @param {number} [params.limit] - Maximum results to return
 * @param {number} [params.offset] - Number of results to skip
 * @param {string} [params.gender] - Filter by gender
 * @param {boolean} [params.active] - Filter by active status
 * @returns {Promise<Object>} Promise resolving to search results with pagination
 * @throws {Error} If Medplum operation fails
 */
export async function searchPatients(
    client: MedplumClient,
    params: {
        search?: string;
        limit?: number;
        offset?: number;
        gender?: string;
        active?: boolean;
    } = {}
): Promise<{
    patients: PatientRead[];
    total: number;
    hasMore: boolean;
}> {
    try {
        const {
            search,
            limit = DEFAULT_SEARCH_LIMIT,
            offset = 0,
            gender,
            active
        } = params;

        console.log(' Searching patients:', params);

        // Build search parameters for FHIR search
        const searchParams: Record<string, string> = {
            _count: Math.min(limit, MAX_SEARCH_RESULTS).toString(),
            _offset: offset.toString(),
        };

        if (search) {
            searchParams.name = search;
        }

        if (gender) {
            searchParams.gender = gender;
        }

        if (active !== undefined) {
            searchParams.active = active.toString();
        }

        // Search in Medplum
        const searchResult = await client.search('Patient', searchParams);

        // Convert results to our format
        const patients: PatientRead[] = [];
        if (searchResult.entry) {
            for (const entry of searchResult.entry) {
                if (entry.resource && entry.resource.resourceType === 'Patient') {
                    patients.push(fromFHIRPatient(entry.resource as
                        PatientWithExtensions));
                }
            }
        }

        const total = searchResult.total || patients.length;
        const hasMore = total > (offset + patients.length);

        console.log(` Found ${patients.length} patients (${total} total)`);

        return {
            patients,
            total,
            hasMore
        };

    } catch (error: any) {
        console.error(' Error in searchPatients:', error);

        if (error.response?.status === 401) {
            throw new Error('Authentication failed. Please check Medplum credentials.');
        } else if (error.response?.status === 403) {
            throw new Error('Permission denied. Insufficient access rights.');
        } else {
            throw new Error(`Failed to search patients: ${error.message}`);
        }
    }
}