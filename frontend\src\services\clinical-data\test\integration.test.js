const { ClinicalDataService } = require('../index');
const { OpenAIService } = require('../collection/openAIService.service');
const { ClinicalResponseParser } = require('../extraction/responseParser');

jest.mock('../collection/openAIService.service');
jest.mock('../extraction/responseParser');

describe('Clinical Data Integration', () => {
    let clinicalService;
    let mockOpenAIService;
    let mockResponseParser;

    // Create simple mock data
    const mockAIResponse = {
        choices: [{
            message: {
                content: JSON.stringify({
                    chiefComplaint: "chest pain",
                    currentSymptoms: [{
                        symptom: "chest pain",
                        severity: "6"
                    }]
                })
            }
        }]
    };

    const mockParsedData = {
        chiefComplaint: "chest pain",
        currentSymptoms: [{
            symptom: "chest pain",
            severity: "6"
        }],
        extractionType: 'symptoms',
        context: {
            sessionType: 'general_consultation'
        }
    };

    beforeEach(() => {
        // Reset mocks
        jest.clearAllMocks();

        // Setup mock implementations
        mockOpenAIService = {
            extractClinicalData: jest.fn().mockResolvedValue(mockAIResponse)
        };

        mockResponseParser = {
            parseResponse: jest.fn().mockResolvedValue({
                success: true,
                data: mockParsedData
            })
        };

        // Mock the constructor implementations
        OpenAIService.mockImplementation(() => mockOpenAIService);
        ClinicalResponseParser.mockImplementation(() => mockResponseParser);

        // Create service instance
        clinicalService = new ClinicalDataService();
    });

    test('processes clinical data with extraction type', async () => {
        const transcript = "Patient complains of chest pain";
        const extractionType = 'symptoms';

        const result = await clinicalService.processClinicalData(transcript, extractionType);

        expect(mockOpenAIService.extractClinicalData).toHaveBeenCalledWith(transcript, extractionType);
        expect(result.data).toHaveProperty('chiefComplaint');
        expect(result.data).toHaveProperty('currentSymptoms');
        expect(result.data.currentSymptoms[0].symptom).toBe('chest pain');
    });

    test('handles processing errors', async () => {
        const transcript = "Patient complains of chest pain";
        const extractionType = 'symptoms';

        mockOpenAIService.extractClinicalData.mockRejectedValue(
            new Error('Processing failed')
        );

        await expect(clinicalService.processClinicalData(transcript, extractionType))
            .rejects.toThrow('Processing failed');
    });
});

