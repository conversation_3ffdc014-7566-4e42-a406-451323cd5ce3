{"resourceType": "Bundle", "type": "collection", "entry": [{"fullUrl": "http://unstats.un.org/unsd/methods/m49/m49.htm", "resource": {"resourceType": "CodeSystem", "status": "active", "url": "http://unstats.un.org/unsd/methods/m49/m49.htm", "content": "complete", "concept": [{"code": "100", "display": "Bulgaria", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "104", "display": "Myanmar", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "108", "display": "Burundi", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "112", "display": "Belarus", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "116", "display": "Cambodia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "120", "display": "Cameroon", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "124", "display": "Canada", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "132", "display": "Cabo Verde", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "136", "display": "Cayman Islands", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "140", "display": "Central African Republic", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "142", "display": "Asia", "property": [{"code": "class", "valueCode": "region"}]}, {"code": "143", "display": "Central Asia", "property": [{"code": "class", "valueCode": "sub-region"}]}, {"code": "144", "display": "Sri Lanka", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "145", "display": "Western Asia", "property": [{"code": "class", "valueCode": "sub-region"}]}, {"code": "148", "display": "Chad", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "150", "display": "Europe", "property": [{"code": "class", "valueCode": "region"}]}, {"code": "151", "display": "Eastern Europe", "property": [{"code": "class", "valueCode": "sub-region"}]}, {"code": "152", "display": "Chile", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "154", "display": "Northern Europe", "property": [{"code": "class", "valueCode": "sub-region"}]}, {"code": "155", "display": "Western Europe", "property": [{"code": "class", "valueCode": "sub-region"}]}, {"code": "156", "display": "China", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "162", "display": "Christmas Island", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "166", "display": "Cocos (Keeling) Islands", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "170", "display": "Colombia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "174", "display": "Comoros", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "175", "display": "Mayotte", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "178", "display": "Congo", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "180", "display": "Democratic Republic of the Congo", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "184", "display": "Cook Islands", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "188", "display": "Costa Rica", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "191", "display": "Croatia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "192", "display": "Cuba", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "196", "display": "Cyprus", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "202", "display": "Sub-Saharan Africa", "property": [{"code": "class", "valueCode": "sub-region"}]}, {"code": "203", "display": "Czechia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "204", "display": "Benin", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "208", "display": "Denmark", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "212", "display": "Dominica", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "214", "display": "Dominican Republic", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "218", "display": "Ecuador", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "222", "display": "El Salvador", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "226", "display": "Equatorial Guinea", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "231", "display": "Ethiopia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "232", "display": "Eritrea", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "233", "display": "Estonia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "234", "display": "Faroe Islands", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "238", "display": "Falkland Islands (Malvinas)", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "239", "display": "South Georgia and the South Sandwich Islands", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "242", "display": "Fiji", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "246", "display": "Finland", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "248", "display": "Åland Islands", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "250", "display": "France", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "254", "display": "French Guiana", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "258", "display": "French Polynesia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "260", "display": "French Southern Territories", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "262", "display": "Djibouti", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "266", "display": "Gabon", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "268", "display": "Georgia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "270", "display": "Gambia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "275", "display": "State of Palestine", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "276", "display": "Germany", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "288", "display": "Ghana", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "292", "display": "Gibraltar", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "296", "display": "Kiribati", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "300", "display": "Greece", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "304", "display": "Greenland", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "308", "display": "Grenada", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "312", "display": "Guadeloupe", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "316", "display": "Guam", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "320", "display": "Guatemala", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "324", "display": "Guinea", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "328", "display": "Guyana", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "332", "display": "Haiti", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "334", "display": "Heard Island and McDonald Islands", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "336", "display": "Holy See", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "340", "display": "Honduras", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "344", "display": "China, Hong Kong Special Administrative Region", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "348", "display": "Hungary", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "352", "display": "Iceland", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "356", "display": "India", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "360", "display": "Indonesia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "364", "display": "Iran (Islamic Republic of)", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "368", "display": "Iraq", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "372", "display": "Ireland", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "376", "display": "Israel", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "380", "display": "Italy", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "384", "display": "Côte d’Ivoire", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "388", "display": "Jamaica", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "392", "display": "Japan", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "398", "display": "Kazakhstan", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "400", "display": "Jordan", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "404", "display": "Kenya", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "408", "display": "Democratic People's Republic of Korea", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "410", "display": "Republic of Korea", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "414", "display": "Kuwait", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "417", "display": "Kyrgyzstan", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "418", "display": "Lao People's Democratic Republic", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "419", "display": "Latin America and the Caribbean", "property": [{"code": "class", "valueCode": "sub-region"}]}, {"code": "422", "display": "Lebanon", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "426", "display": "Lesotho", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "428", "display": "Latvia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "430", "display": "Liberia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "434", "display": "Libya", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "438", "display": "Liechtenstein", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "440", "display": "Lithuania", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "442", "display": "Luxembourg", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "446", "display": "China, Macao Special Administrative Region", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "450", "display": "Madagascar", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "454", "display": "Malawi", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "458", "display": "Malaysia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "462", "display": "Maldives", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "466", "display": "Mali", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "470", "display": "Malta", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "474", "display": "Martinique", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "478", "display": "Mauritania", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "480", "display": "Mauritius", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "484", "display": "Mexico", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "492", "display": "Monaco", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "496", "display": "Mongolia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "498", "display": "Republic of Moldova", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "499", "display": "Montenegro", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "500", "display": "Montserrat", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "504", "display": "Morocco", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "508", "display": "Mozambique", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "512", "display": "Oman", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "516", "display": "Namibia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "520", "display": "Nauru", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "524", "display": "Nepal", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "528", "display": "Netherlands (Kingdom of the)", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "531", "display": "Curaçao", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "533", "display": "Aruba", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "534", "display": "<PERSON><PERSON> Ma<PERSON>n (Dutch part)", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "535", "display": "Bonaire, Sint Eustatius and Saba", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "540", "display": "New Caledonia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "548", "display": "Vanuatu", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "554", "display": "New Zealand", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "558", "display": "Nicaragua", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "562", "display": "Niger", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "566", "display": "Nigeria", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "570", "display": "Niue", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "574", "display": "Norfolk Island", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "578", "display": "Norway", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "580", "display": "Northern Mariana Islands", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "581", "display": "United States Minor Outlying Islands", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "583", "display": "Micronesia (Federated States of)", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "584", "display": "Marshall Islands", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "585", "display": "<PERSON><PERSON>", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "586", "display": "Pakistan", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "591", "display": "Panama", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "598", "display": "Papua New Guinea", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "600", "display": "Paraguay", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "604", "display": "Peru", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "608", "display": "Philippines", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "612", "display": "Pitcairn", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "616", "display": "Poland", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "620", "display": "Portugal", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "624", "display": "Guinea-Bissau", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "626", "display": "Timor-Leste", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "630", "display": "Puerto Rico", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "634", "display": "Qatar", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "638", "display": "Réunion", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "642", "display": "Romania", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "643", "display": "Russian Federation", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "646", "display": "Rwanda", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "652", "display": "<PERSON>", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "654", "display": "Saint Helena", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "659", "display": "Saint Kitts and Nevis", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "660", "display": "<PERSON><PERSON><PERSON>", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "662", "display": "Saint Lucia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "663", "display": "<PERSON> (French Part)", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "666", "display": "Saint Pierre and Miquelon", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "670", "display": "Saint Vincent and the Grenadines", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "674", "display": "San Marino", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "678", "display": "Sao Tome and Principe", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "682", "display": "Saudi Arabia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "686", "display": "Senegal", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "688", "display": "Serbia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "690", "display": "Seychelles", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "694", "display": "Sierra Leone", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "702", "display": "Singapore", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "703", "display": "Slovakia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "704", "display": "Viet Nam", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "705", "display": "Slovenia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "706", "display": "Somalia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "710", "display": "South Africa", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "716", "display": "Zimbabwe", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "724", "display": "Spain", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "728", "display": "South Sudan", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "729", "display": "Sudan", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "732", "display": "Western Sahara", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "740", "display": "Suriname", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "744", "display": "Svalbard and Jan Mayen Islands", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "748", "display": "<PERSON><PERSON><PERSON><PERSON>", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "752", "display": "Sweden", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "756", "display": "Switzerland", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "760", "display": "Syrian Arab Republic", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "762", "display": "Tajikistan", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "764", "display": "Thailand", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "768", "display": "Togo", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "772", "display": "Tokelau", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "776", "display": "Tonga", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "780", "display": "Trinidad and Tobago", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "784", "display": "United Arab Emirates", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "788", "display": "Tunisia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "792", "display": "Türkiye", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "795", "display": "Turkmenistan", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "796", "display": "Turks and Caicos Islands", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "798", "display": "Tuvalu", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "800", "display": "Uganda", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "804", "display": "Ukraine", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "807", "display": "North Macedonia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "818", "display": "Egypt", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "826", "display": "United Kingdom of Great Britain and Northern Ireland", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "831", "display": "Guernsey", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "832", "display": "Jersey", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "833", "display": "Isle of Man", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "834", "display": "United Republic of Tanzania", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "840", "display": "United States of America", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "850", "display": "United States Virgin Islands", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "854", "display": "Burkina Faso", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "858", "display": "Uruguay", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "860", "display": "Uzbekistan", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "862", "display": "Venezuela (Bolivarian Republic of)", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "876", "display": "Wallis and Futuna Islands", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "882", "display": "Samoa", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "887", "display": "Yemen", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "894", "display": "Zambia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "001", "display": "World", "property": [{"code": "class", "valueCode": "world"}]}, {"code": "002", "display": "Africa", "property": [{"code": "class", "valueCode": "region"}]}, {"code": "015", "display": "Northern Africa", "property": [{"code": "class", "valueCode": "sub-region"}]}, {"code": "012", "display": "Algeria", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "014", "display": "Eastern Africa", "property": [{"code": "class", "valueCode": "intermediate-region"}]}, {"code": "086", "display": "British Indian Ocean Territory", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "017", "display": "Middle Africa", "property": [{"code": "class", "valueCode": "intermediate-region"}]}, {"code": "024", "display": "Angola", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "018", "display": "Southern Africa", "property": [{"code": "class", "valueCode": "intermediate-region"}]}, {"code": "072", "display": "Botswana", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "011", "display": "Western Africa", "property": [{"code": "class", "valueCode": "intermediate-region"}]}, {"code": "019", "display": "Americas", "property": [{"code": "class", "valueCode": "region"}]}, {"code": "029", "display": "Caribbean", "property": [{"code": "class", "valueCode": "intermediate-region"}]}, {"code": "028", "display": "Antigua and Barbuda", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "044", "display": "Bahamas", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "052", "display": "Barbados", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "092", "display": "British Virgin Islands", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "013", "display": "Central America", "property": [{"code": "class", "valueCode": "intermediate-region"}]}, {"code": "084", "display": "Belize", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "005", "display": "South America", "property": [{"code": "class", "valueCode": "intermediate-region"}]}, {"code": "032", "display": "Argentina", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "068", "display": "Bolivia (Plurinational State of)", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "074", "display": "Bouvet Island", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "076", "display": "Brazil", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "021", "display": "Northern America", "property": [{"code": "class", "valueCode": "sub-region"}]}, {"code": "060", "display": "Bermuda", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "010", "display": "Antarctica", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "030", "display": "Eastern Asia", "property": [{"code": "class", "valueCode": "sub-region"}]}, {"code": "035", "display": "South-eastern Asia", "property": [{"code": "class", "valueCode": "sub-region"}]}, {"code": "096", "display": "Brunei Darussalam", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "034", "display": "Southern Asia", "property": [{"code": "class", "valueCode": "sub-region"}]}, {"code": "004", "display": "Afghanistan", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "050", "display": "Bangladesh", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "064", "display": "Bhutan", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "051", "display": "Armenia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "031", "display": "Azerbaijan", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "048", "display": "Bahrain", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "039", "display": "Southern Europe", "property": [{"code": "class", "valueCode": "sub-region"}]}, {"code": "008", "display": "Albania", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "020", "display": "Andorra", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "070", "display": "Bosnia and Herzegovina", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "040", "display": "Austria", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "056", "display": "Belgium", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "009", "display": "Oceania", "property": [{"code": "class", "valueCode": "region"}]}, {"code": "053", "display": "Australia and New Zealand", "property": [{"code": "class", "valueCode": "sub-region"}]}, {"code": "036", "display": "Australia", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "054", "display": "Melanesia", "property": [{"code": "class", "valueCode": "sub-region"}]}, {"code": "090", "display": "Solomon Islands", "property": [{"code": "class", "valueCode": "country"}]}, {"code": "057", "display": "Micronesia", "property": [{"code": "class", "valueCode": "sub-region"}]}, {"code": "061", "display": "Polynesia", "property": [{"code": "class", "valueCode": "sub-region"}]}, {"code": "016", "display": "American Samoa", "property": [{"code": "class", "valueCode": "country"}]}], "property": [{"code": "class", "type": "code", "description": "Size category of region described by the code"}]}}, {"fullUrl": "urn:iso:std:iso:3166", "resource": {"resourceType": "CodeSystem", "status": "active", "url": "urn:iso:std:iso:3166", "content": "complete", "concept": [{"code": "DZ", "display": "Algeria", "property": [{"code": "synonym", "valueCode": "DZA"}, {"code": "numeric", "valueCode": "012"}]}, {"code": "DZA", "display": "Algeria", "property": [{"code": "synonym", "valueCode": "DZ"}, {"code": "numeric", "valueCode": "012"}]}, {"code": "EG", "display": "Egypt", "property": [{"code": "synonym", "valueCode": "EGY"}, {"code": "numeric", "valueCode": "818"}]}, {"code": "EGY", "display": "Egypt", "property": [{"code": "synonym", "valueCode": "EG"}, {"code": "numeric", "valueCode": "818"}]}, {"code": "LY", "display": "Libya", "property": [{"code": "synonym", "valueCode": "LBY"}, {"code": "numeric", "valueCode": "434"}]}, {"code": "LBY", "display": "Libya", "property": [{"code": "synonym", "valueCode": "LY"}, {"code": "numeric", "valueCode": "434"}]}, {"code": "MA", "display": "Morocco", "property": [{"code": "synonym", "valueCode": "MAR"}, {"code": "numeric", "valueCode": "504"}]}, {"code": "MAR", "display": "Morocco", "property": [{"code": "synonym", "valueCode": "MA"}, {"code": "numeric", "valueCode": "504"}]}, {"code": "SD", "display": "Sudan", "property": [{"code": "synonym", "valueCode": "SDN"}, {"code": "numeric", "valueCode": "729"}]}, {"code": "SDN", "display": "Sudan", "property": [{"code": "synonym", "valueCode": "SD"}, {"code": "numeric", "valueCode": "729"}]}, {"code": "TN", "display": "Tunisia", "property": [{"code": "synonym", "valueCode": "TUN"}, {"code": "numeric", "valueCode": "788"}]}, {"code": "TUN", "display": "Tunisia", "property": [{"code": "synonym", "valueCode": "TN"}, {"code": "numeric", "valueCode": "788"}]}, {"code": "EH", "display": "Western Sahara", "property": [{"code": "synonym", "valueCode": "ESH"}, {"code": "numeric", "valueCode": "732"}]}, {"code": "ESH", "display": "Western Sahara", "property": [{"code": "synonym", "valueCode": "EH"}, {"code": "numeric", "valueCode": "732"}]}, {"code": "IO", "display": "British Indian Ocean Territory", "property": [{"code": "synonym", "valueCode": "IOT"}, {"code": "numeric", "valueCode": "086"}]}, {"code": "IOT", "display": "British Indian Ocean Territory", "property": [{"code": "synonym", "valueCode": "IO"}, {"code": "numeric", "valueCode": "086"}]}, {"code": "BI", "display": "Burundi", "property": [{"code": "synonym", "valueCode": "BDI"}, {"code": "numeric", "valueCode": "108"}]}, {"code": "BDI", "display": "Burundi", "property": [{"code": "synonym", "valueCode": "BI"}, {"code": "numeric", "valueCode": "108"}]}, {"code": "KM", "display": "Comoros", "property": [{"code": "synonym", "valueCode": "COM"}, {"code": "numeric", "valueCode": "174"}]}, {"code": "COM", "display": "Comoros", "property": [{"code": "synonym", "valueCode": "KM"}, {"code": "numeric", "valueCode": "174"}]}, {"code": "DJ", "display": "Djibouti", "property": [{"code": "synonym", "valueCode": "DJI"}, {"code": "numeric", "valueCode": "262"}]}, {"code": "DJI", "display": "Djibouti", "property": [{"code": "synonym", "valueCode": "DJ"}, {"code": "numeric", "valueCode": "262"}]}, {"code": "ER", "display": "Eritrea", "property": [{"code": "synonym", "valueCode": "ERI"}, {"code": "numeric", "valueCode": "232"}]}, {"code": "ERI", "display": "Eritrea", "property": [{"code": "synonym", "valueCode": "ER"}, {"code": "numeric", "valueCode": "232"}]}, {"code": "ET", "display": "Ethiopia", "property": [{"code": "synonym", "valueCode": "ETH"}, {"code": "numeric", "valueCode": "231"}]}, {"code": "ETH", "display": "Ethiopia", "property": [{"code": "synonym", "valueCode": "ET"}, {"code": "numeric", "valueCode": "231"}]}, {"code": "TF", "display": "French Southern Territories", "property": [{"code": "synonym", "valueCode": "ATF"}, {"code": "numeric", "valueCode": "260"}]}, {"code": "ATF", "display": "French Southern Territories", "property": [{"code": "synonym", "valueCode": "TF"}, {"code": "numeric", "valueCode": "260"}]}, {"code": "KE", "display": "Kenya", "property": [{"code": "synonym", "valueCode": "KEN"}, {"code": "numeric", "valueCode": "404"}]}, {"code": "KEN", "display": "Kenya", "property": [{"code": "synonym", "valueCode": "KE"}, {"code": "numeric", "valueCode": "404"}]}, {"code": "MG", "display": "Madagascar", "property": [{"code": "synonym", "valueCode": "MDG"}, {"code": "numeric", "valueCode": "450"}]}, {"code": "MDG", "display": "Madagascar", "property": [{"code": "synonym", "valueCode": "MG"}, {"code": "numeric", "valueCode": "450"}]}, {"code": "MW", "display": "Malawi", "property": [{"code": "synonym", "valueCode": "MWI"}, {"code": "numeric", "valueCode": "454"}]}, {"code": "MWI", "display": "Malawi", "property": [{"code": "synonym", "valueCode": "MW"}, {"code": "numeric", "valueCode": "454"}]}, {"code": "MU", "display": "Mauritius", "property": [{"code": "synonym", "valueCode": "MUS"}, {"code": "numeric", "valueCode": "480"}]}, {"code": "MUS", "display": "Mauritius", "property": [{"code": "synonym", "valueCode": "MU"}, {"code": "numeric", "valueCode": "480"}]}, {"code": "YT", "display": "Mayotte", "property": [{"code": "synonym", "valueCode": "MYT"}, {"code": "numeric", "valueCode": "175"}]}, {"code": "MYT", "display": "Mayotte", "property": [{"code": "synonym", "valueCode": "YT"}, {"code": "numeric", "valueCode": "175"}]}, {"code": "MZ", "display": "Mozambique", "property": [{"code": "synonym", "valueCode": "MOZ"}, {"code": "numeric", "valueCode": "508"}]}, {"code": "MOZ", "display": "Mozambique", "property": [{"code": "synonym", "valueCode": "MZ"}, {"code": "numeric", "valueCode": "508"}]}, {"code": "RE", "display": "Réunion", "property": [{"code": "synonym", "valueCode": "REU"}, {"code": "numeric", "valueCode": "638"}]}, {"code": "REU", "display": "Réunion", "property": [{"code": "synonym", "valueCode": "RE"}, {"code": "numeric", "valueCode": "638"}]}, {"code": "RW", "display": "Rwanda", "property": [{"code": "synonym", "valueCode": "RWA"}, {"code": "numeric", "valueCode": "646"}]}, {"code": "RWA", "display": "Rwanda", "property": [{"code": "synonym", "valueCode": "RW"}, {"code": "numeric", "valueCode": "646"}]}, {"code": "SC", "display": "Seychelles", "property": [{"code": "synonym", "valueCode": "SYC"}, {"code": "numeric", "valueCode": "690"}]}, {"code": "SYC", "display": "Seychelles", "property": [{"code": "synonym", "valueCode": "SC"}, {"code": "numeric", "valueCode": "690"}]}, {"code": "SO", "display": "Somalia", "property": [{"code": "synonym", "valueCode": "SOM"}, {"code": "numeric", "valueCode": "706"}]}, {"code": "SOM", "display": "Somalia", "property": [{"code": "synonym", "valueCode": "SO"}, {"code": "numeric", "valueCode": "706"}]}, {"code": "SS", "display": "South Sudan", "property": [{"code": "synonym", "valueCode": "SSD"}, {"code": "numeric", "valueCode": "728"}]}, {"code": "SSD", "display": "South Sudan", "property": [{"code": "synonym", "valueCode": "SS"}, {"code": "numeric", "valueCode": "728"}]}, {"code": "UG", "display": "Uganda", "property": [{"code": "synonym", "valueCode": "UGA"}, {"code": "numeric", "valueCode": "800"}]}, {"code": "UGA", "display": "Uganda", "property": [{"code": "synonym", "valueCode": "UG"}, {"code": "numeric", "valueCode": "800"}]}, {"code": "TZ", "display": "United Republic of Tanzania", "property": [{"code": "synonym", "valueCode": "TZA"}, {"code": "numeric", "valueCode": "834"}]}, {"code": "TZA", "display": "United Republic of Tanzania", "property": [{"code": "synonym", "valueCode": "TZ"}, {"code": "numeric", "valueCode": "834"}]}, {"code": "ZM", "display": "Zambia", "property": [{"code": "synonym", "valueCode": "ZMB"}, {"code": "numeric", "valueCode": "894"}]}, {"code": "ZMB", "display": "Zambia", "property": [{"code": "synonym", "valueCode": "ZM"}, {"code": "numeric", "valueCode": "894"}]}, {"code": "ZW", "display": "Zimbabwe", "property": [{"code": "synonym", "valueCode": "ZWE"}, {"code": "numeric", "valueCode": "716"}]}, {"code": "ZWE", "display": "Zimbabwe", "property": [{"code": "synonym", "valueCode": "ZW"}, {"code": "numeric", "valueCode": "716"}]}, {"code": "AO", "display": "Angola", "property": [{"code": "synonym", "valueCode": "AGO"}, {"code": "numeric", "valueCode": "024"}]}, {"code": "AGO", "display": "Angola", "property": [{"code": "synonym", "valueCode": "AO"}, {"code": "numeric", "valueCode": "024"}]}, {"code": "CM", "display": "Cameroon", "property": [{"code": "synonym", "valueCode": "CMR"}, {"code": "numeric", "valueCode": "120"}]}, {"code": "CMR", "display": "Cameroon", "property": [{"code": "synonym", "valueCode": "CM"}, {"code": "numeric", "valueCode": "120"}]}, {"code": "CF", "display": "Central African Republic", "property": [{"code": "synonym", "valueCode": "CAF"}, {"code": "numeric", "valueCode": "140"}]}, {"code": "CAF", "display": "Central African Republic", "property": [{"code": "synonym", "valueCode": "CF"}, {"code": "numeric", "valueCode": "140"}]}, {"code": "TD", "display": "Chad", "property": [{"code": "synonym", "valueCode": "TCD"}, {"code": "numeric", "valueCode": "148"}]}, {"code": "TCD", "display": "Chad", "property": [{"code": "synonym", "valueCode": "TD"}, {"code": "numeric", "valueCode": "148"}]}, {"code": "CG", "display": "Congo", "property": [{"code": "synonym", "valueCode": "COG"}, {"code": "numeric", "valueCode": "178"}]}, {"code": "COG", "display": "Congo", "property": [{"code": "synonym", "valueCode": "CG"}, {"code": "numeric", "valueCode": "178"}]}, {"code": "CD", "display": "Democratic Republic of the Congo", "property": [{"code": "synonym", "valueCode": "COD"}, {"code": "numeric", "valueCode": "180"}]}, {"code": "COD", "display": "Democratic Republic of the Congo", "property": [{"code": "synonym", "valueCode": "CD"}, {"code": "numeric", "valueCode": "180"}]}, {"code": "GQ", "display": "Equatorial Guinea", "property": [{"code": "synonym", "valueCode": "GNQ"}, {"code": "numeric", "valueCode": "226"}]}, {"code": "GNQ", "display": "Equatorial Guinea", "property": [{"code": "synonym", "valueCode": "GQ"}, {"code": "numeric", "valueCode": "226"}]}, {"code": "GA", "display": "Gabon", "property": [{"code": "synonym", "valueCode": "GAB"}, {"code": "numeric", "valueCode": "266"}]}, {"code": "GAB", "display": "Gabon", "property": [{"code": "synonym", "valueCode": "GA"}, {"code": "numeric", "valueCode": "266"}]}, {"code": "ST", "display": "Sao Tome and Principe", "property": [{"code": "synonym", "valueCode": "STP"}, {"code": "numeric", "valueCode": "678"}]}, {"code": "STP", "display": "Sao Tome and Principe", "property": [{"code": "synonym", "valueCode": "ST"}, {"code": "numeric", "valueCode": "678"}]}, {"code": "BW", "display": "Botswana", "property": [{"code": "synonym", "valueCode": "BWA"}, {"code": "numeric", "valueCode": "072"}]}, {"code": "BWA", "display": "Botswana", "property": [{"code": "synonym", "valueCode": "BW"}, {"code": "numeric", "valueCode": "072"}]}, {"code": "SZ", "display": "<PERSON><PERSON><PERSON><PERSON>", "property": [{"code": "synonym", "valueCode": "SWZ"}, {"code": "numeric", "valueCode": "748"}]}, {"code": "SWZ", "display": "<PERSON><PERSON><PERSON><PERSON>", "property": [{"code": "synonym", "valueCode": "SZ"}, {"code": "numeric", "valueCode": "748"}]}, {"code": "LS", "display": "Lesotho", "property": [{"code": "synonym", "valueCode": "LSO"}, {"code": "numeric", "valueCode": "426"}]}, {"code": "LSO", "display": "Lesotho", "property": [{"code": "synonym", "valueCode": "LS"}, {"code": "numeric", "valueCode": "426"}]}, {"code": "NA", "display": "Namibia", "property": [{"code": "synonym", "valueCode": "NAM"}, {"code": "numeric", "valueCode": "516"}]}, {"code": "NAM", "display": "Namibia", "property": [{"code": "synonym", "valueCode": "NA"}, {"code": "numeric", "valueCode": "516"}]}, {"code": "ZA", "display": "South Africa", "property": [{"code": "synonym", "valueCode": "ZAF"}, {"code": "numeric", "valueCode": "710"}]}, {"code": "ZAF", "display": "South Africa", "property": [{"code": "synonym", "valueCode": "ZA"}, {"code": "numeric", "valueCode": "710"}]}, {"code": "BJ", "display": "Benin", "property": [{"code": "synonym", "valueCode": "BEN"}, {"code": "numeric", "valueCode": "204"}]}, {"code": "BEN", "display": "Benin", "property": [{"code": "synonym", "valueCode": "BJ"}, {"code": "numeric", "valueCode": "204"}]}, {"code": "BF", "display": "Burkina Faso", "property": [{"code": "synonym", "valueCode": "BFA"}, {"code": "numeric", "valueCode": "854"}]}, {"code": "BFA", "display": "Burkina Faso", "property": [{"code": "synonym", "valueCode": "BF"}, {"code": "numeric", "valueCode": "854"}]}, {"code": "CV", "display": "Cabo Verde", "property": [{"code": "synonym", "valueCode": "CPV"}, {"code": "numeric", "valueCode": "132"}]}, {"code": "CPV", "display": "Cabo Verde", "property": [{"code": "synonym", "valueCode": "CV"}, {"code": "numeric", "valueCode": "132"}]}, {"code": "CI", "display": "Côte d’Ivoire", "property": [{"code": "synonym", "valueCode": "CIV"}, {"code": "numeric", "valueCode": "384"}]}, {"code": "CIV", "display": "Côte d’Ivoire", "property": [{"code": "synonym", "valueCode": "CI"}, {"code": "numeric", "valueCode": "384"}]}, {"code": "GM", "display": "Gambia", "property": [{"code": "synonym", "valueCode": "GMB"}, {"code": "numeric", "valueCode": "270"}]}, {"code": "GMB", "display": "Gambia", "property": [{"code": "synonym", "valueCode": "GM"}, {"code": "numeric", "valueCode": "270"}]}, {"code": "GH", "display": "Ghana", "property": [{"code": "synonym", "valueCode": "GHA"}, {"code": "numeric", "valueCode": "288"}]}, {"code": "GHA", "display": "Ghana", "property": [{"code": "synonym", "valueCode": "GH"}, {"code": "numeric", "valueCode": "288"}]}, {"code": "GN", "display": "Guinea", "property": [{"code": "synonym", "valueCode": "GIN"}, {"code": "numeric", "valueCode": "324"}]}, {"code": "GIN", "display": "Guinea", "property": [{"code": "synonym", "valueCode": "GN"}, {"code": "numeric", "valueCode": "324"}]}, {"code": "GW", "display": "Guinea-Bissau", "property": [{"code": "synonym", "valueCode": "GNB"}, {"code": "numeric", "valueCode": "624"}]}, {"code": "GNB", "display": "Guinea-Bissau", "property": [{"code": "synonym", "valueCode": "GW"}, {"code": "numeric", "valueCode": "624"}]}, {"code": "LR", "display": "Liberia", "property": [{"code": "synonym", "valueCode": "LBR"}, {"code": "numeric", "valueCode": "430"}]}, {"code": "LBR", "display": "Liberia", "property": [{"code": "synonym", "valueCode": "LR"}, {"code": "numeric", "valueCode": "430"}]}, {"code": "ML", "display": "Mali", "property": [{"code": "synonym", "valueCode": "MLI"}, {"code": "numeric", "valueCode": "466"}]}, {"code": "MLI", "display": "Mali", "property": [{"code": "synonym", "valueCode": "ML"}, {"code": "numeric", "valueCode": "466"}]}, {"code": "MR", "display": "Mauritania", "property": [{"code": "synonym", "valueCode": "MRT"}, {"code": "numeric", "valueCode": "478"}]}, {"code": "MRT", "display": "Mauritania", "property": [{"code": "synonym", "valueCode": "MR"}, {"code": "numeric", "valueCode": "478"}]}, {"code": "NE", "display": "Niger", "property": [{"code": "synonym", "valueCode": "NER"}, {"code": "numeric", "valueCode": "562"}]}, {"code": "NER", "display": "Niger", "property": [{"code": "synonym", "valueCode": "NE"}, {"code": "numeric", "valueCode": "562"}]}, {"code": "NG", "display": "Nigeria", "property": [{"code": "synonym", "valueCode": "NGA"}, {"code": "numeric", "valueCode": "566"}]}, {"code": "NGA", "display": "Nigeria", "property": [{"code": "synonym", "valueCode": "NG"}, {"code": "numeric", "valueCode": "566"}]}, {"code": "SH", "display": "Saint Helena", "property": [{"code": "synonym", "valueCode": "SHN"}, {"code": "numeric", "valueCode": "654"}]}, {"code": "SHN", "display": "Saint Helena", "property": [{"code": "synonym", "valueCode": "SH"}, {"code": "numeric", "valueCode": "654"}]}, {"code": "SN", "display": "Senegal", "property": [{"code": "synonym", "valueCode": "SEN"}, {"code": "numeric", "valueCode": "686"}]}, {"code": "SEN", "display": "Senegal", "property": [{"code": "synonym", "valueCode": "SN"}, {"code": "numeric", "valueCode": "686"}]}, {"code": "SL", "display": "Sierra Leone", "property": [{"code": "synonym", "valueCode": "SLE"}, {"code": "numeric", "valueCode": "694"}]}, {"code": "SLE", "display": "Sierra Leone", "property": [{"code": "synonym", "valueCode": "SL"}, {"code": "numeric", "valueCode": "694"}]}, {"code": "TG", "display": "Togo", "property": [{"code": "synonym", "valueCode": "TGO"}, {"code": "numeric", "valueCode": "768"}]}, {"code": "TGO", "display": "Togo", "property": [{"code": "synonym", "valueCode": "TG"}, {"code": "numeric", "valueCode": "768"}]}, {"code": "AI", "display": "<PERSON><PERSON><PERSON>", "property": [{"code": "synonym", "valueCode": "AIA"}, {"code": "numeric", "valueCode": "660"}]}, {"code": "AIA", "display": "<PERSON><PERSON><PERSON>", "property": [{"code": "synonym", "valueCode": "AI"}, {"code": "numeric", "valueCode": "660"}]}, {"code": "AG", "display": "Antigua and Barbuda", "property": [{"code": "synonym", "valueCode": "ATG"}, {"code": "numeric", "valueCode": "028"}]}, {"code": "ATG", "display": "Antigua and Barbuda", "property": [{"code": "synonym", "valueCode": "AG"}, {"code": "numeric", "valueCode": "028"}]}, {"code": "AW", "display": "Aruba", "property": [{"code": "synonym", "valueCode": "ABW"}, {"code": "numeric", "valueCode": "533"}]}, {"code": "ABW", "display": "Aruba", "property": [{"code": "synonym", "valueCode": "AW"}, {"code": "numeric", "valueCode": "533"}]}, {"code": "BS", "display": "Bahamas", "property": [{"code": "synonym", "valueCode": "BHS"}, {"code": "numeric", "valueCode": "044"}]}, {"code": "BHS", "display": "Bahamas", "property": [{"code": "synonym", "valueCode": "BS"}, {"code": "numeric", "valueCode": "044"}]}, {"code": "BB", "display": "Barbados", "property": [{"code": "synonym", "valueCode": "BRB"}, {"code": "numeric", "valueCode": "052"}]}, {"code": "BRB", "display": "Barbados", "property": [{"code": "synonym", "valueCode": "BB"}, {"code": "numeric", "valueCode": "052"}]}, {"code": "BQ", "display": "Bonaire, Sint Eustatius and Saba", "property": [{"code": "synonym", "valueCode": "BES"}, {"code": "numeric", "valueCode": "535"}]}, {"code": "BES", "display": "Bonaire, Sint Eustatius and Saba", "property": [{"code": "synonym", "valueCode": "BQ"}, {"code": "numeric", "valueCode": "535"}]}, {"code": "VG", "display": "British Virgin Islands", "property": [{"code": "synonym", "valueCode": "VGB"}, {"code": "numeric", "valueCode": "092"}]}, {"code": "VGB", "display": "British Virgin Islands", "property": [{"code": "synonym", "valueCode": "VG"}, {"code": "numeric", "valueCode": "092"}]}, {"code": "KY", "display": "Cayman Islands", "property": [{"code": "synonym", "valueCode": "CYM"}, {"code": "numeric", "valueCode": "136"}]}, {"code": "CYM", "display": "Cayman Islands", "property": [{"code": "synonym", "valueCode": "KY"}, {"code": "numeric", "valueCode": "136"}]}, {"code": "CU", "display": "Cuba", "property": [{"code": "synonym", "valueCode": "CUB"}, {"code": "numeric", "valueCode": "192"}]}, {"code": "CUB", "display": "Cuba", "property": [{"code": "synonym", "valueCode": "CU"}, {"code": "numeric", "valueCode": "192"}]}, {"code": "CW", "display": "Curaçao", "property": [{"code": "synonym", "valueCode": "CUW"}, {"code": "numeric", "valueCode": "531"}]}, {"code": "CUW", "display": "Curaçao", "property": [{"code": "synonym", "valueCode": "CW"}, {"code": "numeric", "valueCode": "531"}]}, {"code": "DM", "display": "Dominica", "property": [{"code": "synonym", "valueCode": "DMA"}, {"code": "numeric", "valueCode": "212"}]}, {"code": "DMA", "display": "Dominica", "property": [{"code": "synonym", "valueCode": "DM"}, {"code": "numeric", "valueCode": "212"}]}, {"code": "DO", "display": "Dominican Republic", "property": [{"code": "synonym", "valueCode": "DOM"}, {"code": "numeric", "valueCode": "214"}]}, {"code": "DOM", "display": "Dominican Republic", "property": [{"code": "synonym", "valueCode": "DO"}, {"code": "numeric", "valueCode": "214"}]}, {"code": "GD", "display": "Grenada", "property": [{"code": "synonym", "valueCode": "GRD"}, {"code": "numeric", "valueCode": "308"}]}, {"code": "GRD", "display": "Grenada", "property": [{"code": "synonym", "valueCode": "GD"}, {"code": "numeric", "valueCode": "308"}]}, {"code": "GP", "display": "Guadeloupe", "property": [{"code": "synonym", "valueCode": "GLP"}, {"code": "numeric", "valueCode": "312"}]}, {"code": "GLP", "display": "Guadeloupe", "property": [{"code": "synonym", "valueCode": "GP"}, {"code": "numeric", "valueCode": "312"}]}, {"code": "HT", "display": "Haiti", "property": [{"code": "synonym", "valueCode": "HTI"}, {"code": "numeric", "valueCode": "332"}]}, {"code": "HTI", "display": "Haiti", "property": [{"code": "synonym", "valueCode": "HT"}, {"code": "numeric", "valueCode": "332"}]}, {"code": "JM", "display": "Jamaica", "property": [{"code": "synonym", "valueCode": "JAM"}, {"code": "numeric", "valueCode": "388"}]}, {"code": "JAM", "display": "Jamaica", "property": [{"code": "synonym", "valueCode": "JM"}, {"code": "numeric", "valueCode": "388"}]}, {"code": "MQ", "display": "Martinique", "property": [{"code": "synonym", "valueCode": "MTQ"}, {"code": "numeric", "valueCode": "474"}]}, {"code": "MTQ", "display": "Martinique", "property": [{"code": "synonym", "valueCode": "MQ"}, {"code": "numeric", "valueCode": "474"}]}, {"code": "MS", "display": "Montserrat", "property": [{"code": "synonym", "valueCode": "MSR"}, {"code": "numeric", "valueCode": "500"}]}, {"code": "MSR", "display": "Montserrat", "property": [{"code": "synonym", "valueCode": "MS"}, {"code": "numeric", "valueCode": "500"}]}, {"code": "PR", "display": "Puerto Rico", "property": [{"code": "synonym", "valueCode": "PRI"}, {"code": "numeric", "valueCode": "630"}]}, {"code": "PRI", "display": "Puerto Rico", "property": [{"code": "synonym", "valueCode": "PR"}, {"code": "numeric", "valueCode": "630"}]}, {"code": "BL", "display": "<PERSON>", "property": [{"code": "synonym", "valueCode": "BLM"}, {"code": "numeric", "valueCode": "652"}]}, {"code": "BLM", "display": "<PERSON>", "property": [{"code": "synonym", "valueCode": "BL"}, {"code": "numeric", "valueCode": "652"}]}, {"code": "KN", "display": "Saint Kitts and Nevis", "property": [{"code": "synonym", "valueCode": "KNA"}, {"code": "numeric", "valueCode": "659"}]}, {"code": "KNA", "display": "Saint Kitts and Nevis", "property": [{"code": "synonym", "valueCode": "KN"}, {"code": "numeric", "valueCode": "659"}]}, {"code": "LC", "display": "Saint Lucia", "property": [{"code": "synonym", "valueCode": "LCA"}, {"code": "numeric", "valueCode": "662"}]}, {"code": "LCA", "display": "Saint Lucia", "property": [{"code": "synonym", "valueCode": "LC"}, {"code": "numeric", "valueCode": "662"}]}, {"code": "MF", "display": "<PERSON> (French Part)", "property": [{"code": "synonym", "valueCode": "MAF"}, {"code": "numeric", "valueCode": "663"}]}, {"code": "MAF", "display": "<PERSON> (French Part)", "property": [{"code": "synonym", "valueCode": "MF"}, {"code": "numeric", "valueCode": "663"}]}, {"code": "VC", "display": "Saint Vincent and the Grenadines", "property": [{"code": "synonym", "valueCode": "VCT"}, {"code": "numeric", "valueCode": "670"}]}, {"code": "VCT", "display": "Saint Vincent and the Grenadines", "property": [{"code": "synonym", "valueCode": "VC"}, {"code": "numeric", "valueCode": "670"}]}, {"code": "SX", "display": "<PERSON><PERSON> Ma<PERSON>n (Dutch part)", "property": [{"code": "synonym", "valueCode": "SXM"}, {"code": "numeric", "valueCode": "534"}]}, {"code": "SXM", "display": "<PERSON><PERSON> Ma<PERSON>n (Dutch part)", "property": [{"code": "synonym", "valueCode": "SX"}, {"code": "numeric", "valueCode": "534"}]}, {"code": "TT", "display": "Trinidad and Tobago", "property": [{"code": "synonym", "valueCode": "TTO"}, {"code": "numeric", "valueCode": "780"}]}, {"code": "TTO", "display": "Trinidad and Tobago", "property": [{"code": "synonym", "valueCode": "TT"}, {"code": "numeric", "valueCode": "780"}]}, {"code": "TC", "display": "Turks and Caicos Islands", "property": [{"code": "synonym", "valueCode": "TCA"}, {"code": "numeric", "valueCode": "796"}]}, {"code": "TCA", "display": "Turks and Caicos Islands", "property": [{"code": "synonym", "valueCode": "TC"}, {"code": "numeric", "valueCode": "796"}]}, {"code": "VI", "display": "United States Virgin Islands", "property": [{"code": "synonym", "valueCode": "VIR"}, {"code": "numeric", "valueCode": "850"}]}, {"code": "VIR", "display": "United States Virgin Islands", "property": [{"code": "synonym", "valueCode": "VI"}, {"code": "numeric", "valueCode": "850"}]}, {"code": "BZ", "display": "Belize", "property": [{"code": "synonym", "valueCode": "BLZ"}, {"code": "numeric", "valueCode": "084"}]}, {"code": "BLZ", "display": "Belize", "property": [{"code": "synonym", "valueCode": "BZ"}, {"code": "numeric", "valueCode": "084"}]}, {"code": "CR", "display": "Costa Rica", "property": [{"code": "synonym", "valueCode": "CRI"}, {"code": "numeric", "valueCode": "188"}]}, {"code": "CRI", "display": "Costa Rica", "property": [{"code": "synonym", "valueCode": "CR"}, {"code": "numeric", "valueCode": "188"}]}, {"code": "SV", "display": "El Salvador", "property": [{"code": "synonym", "valueCode": "SLV"}, {"code": "numeric", "valueCode": "222"}]}, {"code": "SLV", "display": "El Salvador", "property": [{"code": "synonym", "valueCode": "SV"}, {"code": "numeric", "valueCode": "222"}]}, {"code": "GT", "display": "Guatemala", "property": [{"code": "synonym", "valueCode": "GTM"}, {"code": "numeric", "valueCode": "320"}]}, {"code": "GTM", "display": "Guatemala", "property": [{"code": "synonym", "valueCode": "GT"}, {"code": "numeric", "valueCode": "320"}]}, {"code": "HN", "display": "Honduras", "property": [{"code": "synonym", "valueCode": "HND"}, {"code": "numeric", "valueCode": "340"}]}, {"code": "HND", "display": "Honduras", "property": [{"code": "synonym", "valueCode": "HN"}, {"code": "numeric", "valueCode": "340"}]}, {"code": "MX", "display": "Mexico", "property": [{"code": "synonym", "valueCode": "MEX"}, {"code": "numeric", "valueCode": "484"}]}, {"code": "MEX", "display": "Mexico", "property": [{"code": "synonym", "valueCode": "MX"}, {"code": "numeric", "valueCode": "484"}]}, {"code": "NI", "display": "Nicaragua", "property": [{"code": "synonym", "valueCode": "NIC"}, {"code": "numeric", "valueCode": "558"}]}, {"code": "NIC", "display": "Nicaragua", "property": [{"code": "synonym", "valueCode": "NI"}, {"code": "numeric", "valueCode": "558"}]}, {"code": "PA", "display": "Panama", "property": [{"code": "synonym", "valueCode": "PAN"}, {"code": "numeric", "valueCode": "591"}]}, {"code": "PAN", "display": "Panama", "property": [{"code": "synonym", "valueCode": "PA"}, {"code": "numeric", "valueCode": "591"}]}, {"code": "AR", "display": "Argentina", "property": [{"code": "synonym", "valueCode": "ARG"}, {"code": "numeric", "valueCode": "032"}]}, {"code": "ARG", "display": "Argentina", "property": [{"code": "synonym", "valueCode": "AR"}, {"code": "numeric", "valueCode": "032"}]}, {"code": "BO", "display": "Bolivia (Plurinational State of)", "property": [{"code": "synonym", "valueCode": "BOL"}, {"code": "numeric", "valueCode": "068"}]}, {"code": "BOL", "display": "Bolivia (Plurinational State of)", "property": [{"code": "synonym", "valueCode": "BO"}, {"code": "numeric", "valueCode": "068"}]}, {"code": "BV", "display": "Bouvet Island", "property": [{"code": "synonym", "valueCode": "BVT"}, {"code": "numeric", "valueCode": "074"}]}, {"code": "BVT", "display": "Bouvet Island", "property": [{"code": "synonym", "valueCode": "BV"}, {"code": "numeric", "valueCode": "074"}]}, {"code": "BR", "display": "Brazil", "property": [{"code": "synonym", "valueCode": "BRA"}, {"code": "numeric", "valueCode": "076"}]}, {"code": "BRA", "display": "Brazil", "property": [{"code": "synonym", "valueCode": "BR"}, {"code": "numeric", "valueCode": "076"}]}, {"code": "CL", "display": "Chile", "property": [{"code": "synonym", "valueCode": "CHL"}, {"code": "numeric", "valueCode": "152"}]}, {"code": "CHL", "display": "Chile", "property": [{"code": "synonym", "valueCode": "CL"}, {"code": "numeric", "valueCode": "152"}]}, {"code": "CO", "display": "Colombia", "property": [{"code": "synonym", "valueCode": "COL"}, {"code": "numeric", "valueCode": "170"}]}, {"code": "COL", "display": "Colombia", "property": [{"code": "synonym", "valueCode": "CO"}, {"code": "numeric", "valueCode": "170"}]}, {"code": "EC", "display": "Ecuador", "property": [{"code": "synonym", "valueCode": "ECU"}, {"code": "numeric", "valueCode": "218"}]}, {"code": "ECU", "display": "Ecuador", "property": [{"code": "synonym", "valueCode": "EC"}, {"code": "numeric", "valueCode": "218"}]}, {"code": "FK", "display": "Falkland Islands (Malvinas)", "property": [{"code": "synonym", "valueCode": "FLK"}, {"code": "numeric", "valueCode": "238"}]}, {"code": "FLK", "display": "Falkland Islands (Malvinas)", "property": [{"code": "synonym", "valueCode": "FK"}, {"code": "numeric", "valueCode": "238"}]}, {"code": "GF", "display": "French Guiana", "property": [{"code": "synonym", "valueCode": "GUF"}, {"code": "numeric", "valueCode": "254"}]}, {"code": "GUF", "display": "French Guiana", "property": [{"code": "synonym", "valueCode": "GF"}, {"code": "numeric", "valueCode": "254"}]}, {"code": "GY", "display": "Guyana", "property": [{"code": "synonym", "valueCode": "GUY"}, {"code": "numeric", "valueCode": "328"}]}, {"code": "GUY", "display": "Guyana", "property": [{"code": "synonym", "valueCode": "GY"}, {"code": "numeric", "valueCode": "328"}]}, {"code": "PY", "display": "Paraguay", "property": [{"code": "synonym", "valueCode": "PRY"}, {"code": "numeric", "valueCode": "600"}]}, {"code": "PRY", "display": "Paraguay", "property": [{"code": "synonym", "valueCode": "PY"}, {"code": "numeric", "valueCode": "600"}]}, {"code": "PE", "display": "Peru", "property": [{"code": "synonym", "valueCode": "PER"}, {"code": "numeric", "valueCode": "604"}]}, {"code": "PER", "display": "Peru", "property": [{"code": "synonym", "valueCode": "PE"}, {"code": "numeric", "valueCode": "604"}]}, {"code": "GS", "display": "South Georgia and the South Sandwich Islands", "property": [{"code": "synonym", "valueCode": "SGS"}, {"code": "numeric", "valueCode": "239"}]}, {"code": "SGS", "display": "South Georgia and the South Sandwich Islands", "property": [{"code": "synonym", "valueCode": "GS"}, {"code": "numeric", "valueCode": "239"}]}, {"code": "SR", "display": "Suriname", "property": [{"code": "synonym", "valueCode": "SUR"}, {"code": "numeric", "valueCode": "740"}]}, {"code": "SUR", "display": "Suriname", "property": [{"code": "synonym", "valueCode": "SR"}, {"code": "numeric", "valueCode": "740"}]}, {"code": "UY", "display": "Uruguay", "property": [{"code": "synonym", "valueCode": "URY"}, {"code": "numeric", "valueCode": "858"}]}, {"code": "URY", "display": "Uruguay", "property": [{"code": "synonym", "valueCode": "UY"}, {"code": "numeric", "valueCode": "858"}]}, {"code": "VE", "display": "Venezuela (Bolivarian Republic of)", "property": [{"code": "synonym", "valueCode": "VEN"}, {"code": "numeric", "valueCode": "862"}]}, {"code": "VEN", "display": "Venezuela (Bolivarian Republic of)", "property": [{"code": "synonym", "valueCode": "VE"}, {"code": "numeric", "valueCode": "862"}]}, {"code": "BM", "display": "Bermuda", "property": [{"code": "synonym", "valueCode": "BMU"}, {"code": "numeric", "valueCode": "060"}]}, {"code": "BMU", "display": "Bermuda", "property": [{"code": "synonym", "valueCode": "BM"}, {"code": "numeric", "valueCode": "060"}]}, {"code": "CA", "display": "Canada", "property": [{"code": "synonym", "valueCode": "CAN"}, {"code": "numeric", "valueCode": "124"}]}, {"code": "CAN", "display": "Canada", "property": [{"code": "synonym", "valueCode": "CA"}, {"code": "numeric", "valueCode": "124"}]}, {"code": "GL", "display": "Greenland", "property": [{"code": "synonym", "valueCode": "GRL"}, {"code": "numeric", "valueCode": "304"}]}, {"code": "GRL", "display": "Greenland", "property": [{"code": "synonym", "valueCode": "GL"}, {"code": "numeric", "valueCode": "304"}]}, {"code": "PM", "display": "Saint Pierre and Miquelon", "property": [{"code": "synonym", "valueCode": "SPM"}, {"code": "numeric", "valueCode": "666"}]}, {"code": "SPM", "display": "Saint Pierre and Miquelon", "property": [{"code": "synonym", "valueCode": "PM"}, {"code": "numeric", "valueCode": "666"}]}, {"code": "US", "display": "United States of America", "property": [{"code": "synonym", "valueCode": "USA"}, {"code": "numeric", "valueCode": "840"}]}, {"code": "USA", "display": "United States of America", "property": [{"code": "synonym", "valueCode": "US"}, {"code": "numeric", "valueCode": "840"}]}, {"code": "AQ", "display": "Antarctica", "property": [{"code": "synonym", "valueCode": "ATA"}, {"code": "numeric", "valueCode": "010"}]}, {"code": "ATA", "display": "Antarctica", "property": [{"code": "synonym", "valueCode": "AQ"}, {"code": "numeric", "valueCode": "010"}]}, {"code": "KZ", "display": "Kazakhstan", "property": [{"code": "synonym", "valueCode": "KAZ"}, {"code": "numeric", "valueCode": "398"}]}, {"code": "KAZ", "display": "Kazakhstan", "property": [{"code": "synonym", "valueCode": "KZ"}, {"code": "numeric", "valueCode": "398"}]}, {"code": "KG", "display": "Kyrgyzstan", "property": [{"code": "synonym", "valueCode": "KGZ"}, {"code": "numeric", "valueCode": "417"}]}, {"code": "KGZ", "display": "Kyrgyzstan", "property": [{"code": "synonym", "valueCode": "KG"}, {"code": "numeric", "valueCode": "417"}]}, {"code": "TJ", "display": "Tajikistan", "property": [{"code": "synonym", "valueCode": "TJK"}, {"code": "numeric", "valueCode": "762"}]}, {"code": "TJK", "display": "Tajikistan", "property": [{"code": "synonym", "valueCode": "TJ"}, {"code": "numeric", "valueCode": "762"}]}, {"code": "TM", "display": "Turkmenistan", "property": [{"code": "synonym", "valueCode": "TKM"}, {"code": "numeric", "valueCode": "795"}]}, {"code": "TKM", "display": "Turkmenistan", "property": [{"code": "synonym", "valueCode": "TM"}, {"code": "numeric", "valueCode": "795"}]}, {"code": "UZ", "display": "Uzbekistan", "property": [{"code": "synonym", "valueCode": "UZB"}, {"code": "numeric", "valueCode": "860"}]}, {"code": "UZB", "display": "Uzbekistan", "property": [{"code": "synonym", "valueCode": "UZ"}, {"code": "numeric", "valueCode": "860"}]}, {"code": "CN", "display": "China", "property": [{"code": "synonym", "valueCode": "CHN"}, {"code": "numeric", "valueCode": "156"}]}, {"code": "CHN", "display": "China", "property": [{"code": "synonym", "valueCode": "CN"}, {"code": "numeric", "valueCode": "156"}]}, {"code": "HK", "display": "China, Hong Kong Special Administrative Region", "property": [{"code": "synonym", "valueCode": "HKG"}, {"code": "numeric", "valueCode": "344"}]}, {"code": "HKG", "display": "China, Hong Kong Special Administrative Region", "property": [{"code": "synonym", "valueCode": "HK"}, {"code": "numeric", "valueCode": "344"}]}, {"code": "MO", "display": "China, Macao Special Administrative Region", "property": [{"code": "synonym", "valueCode": "MAC"}, {"code": "numeric", "valueCode": "446"}]}, {"code": "MAC", "display": "China, Macao Special Administrative Region", "property": [{"code": "synonym", "valueCode": "MO"}, {"code": "numeric", "valueCode": "446"}]}, {"code": "KP", "display": "Democratic People's Republic of Korea", "property": [{"code": "synonym", "valueCode": "PRK"}, {"code": "numeric", "valueCode": "408"}]}, {"code": "PRK", "display": "Democratic People's Republic of Korea", "property": [{"code": "synonym", "valueCode": "KP"}, {"code": "numeric", "valueCode": "408"}]}, {"code": "JP", "display": "Japan", "property": [{"code": "synonym", "valueCode": "JPN"}, {"code": "numeric", "valueCode": "392"}]}, {"code": "JPN", "display": "Japan", "property": [{"code": "synonym", "valueCode": "JP"}, {"code": "numeric", "valueCode": "392"}]}, {"code": "MN", "display": "Mongolia", "property": [{"code": "synonym", "valueCode": "MNG"}, {"code": "numeric", "valueCode": "496"}]}, {"code": "MNG", "display": "Mongolia", "property": [{"code": "synonym", "valueCode": "MN"}, {"code": "numeric", "valueCode": "496"}]}, {"code": "KR", "display": "Republic of Korea", "property": [{"code": "synonym", "valueCode": "KOR"}, {"code": "numeric", "valueCode": "410"}]}, {"code": "KOR", "display": "Republic of Korea", "property": [{"code": "synonym", "valueCode": "KR"}, {"code": "numeric", "valueCode": "410"}]}, {"code": "BN", "display": "Brunei Darussalam", "property": [{"code": "synonym", "valueCode": "BRN"}, {"code": "numeric", "valueCode": "096"}]}, {"code": "BRN", "display": "Brunei Darussalam", "property": [{"code": "synonym", "valueCode": "BN"}, {"code": "numeric", "valueCode": "096"}]}, {"code": "KH", "display": "Cambodia", "property": [{"code": "synonym", "valueCode": "KHM"}, {"code": "numeric", "valueCode": "116"}]}, {"code": "KHM", "display": "Cambodia", "property": [{"code": "synonym", "valueCode": "KH"}, {"code": "numeric", "valueCode": "116"}]}, {"code": "ID", "display": "Indonesia", "property": [{"code": "synonym", "valueCode": "IDN"}, {"code": "numeric", "valueCode": "360"}]}, {"code": "IDN", "display": "Indonesia", "property": [{"code": "synonym", "valueCode": "ID"}, {"code": "numeric", "valueCode": "360"}]}, {"code": "LA", "display": "Lao People's Democratic Republic", "property": [{"code": "synonym", "valueCode": "LAO"}, {"code": "numeric", "valueCode": "418"}]}, {"code": "LAO", "display": "Lao People's Democratic Republic", "property": [{"code": "synonym", "valueCode": "LA"}, {"code": "numeric", "valueCode": "418"}]}, {"code": "MY", "display": "Malaysia", "property": [{"code": "synonym", "valueCode": "MYS"}, {"code": "numeric", "valueCode": "458"}]}, {"code": "MYS", "display": "Malaysia", "property": [{"code": "synonym", "valueCode": "MY"}, {"code": "numeric", "valueCode": "458"}]}, {"code": "MM", "display": "Myanmar", "property": [{"code": "synonym", "valueCode": "MMR"}, {"code": "numeric", "valueCode": "104"}]}, {"code": "MMR", "display": "Myanmar", "property": [{"code": "synonym", "valueCode": "MM"}, {"code": "numeric", "valueCode": "104"}]}, {"code": "PH", "display": "Philippines", "property": [{"code": "synonym", "valueCode": "PHL"}, {"code": "numeric", "valueCode": "608"}]}, {"code": "PHL", "display": "Philippines", "property": [{"code": "synonym", "valueCode": "PH"}, {"code": "numeric", "valueCode": "608"}]}, {"code": "SG", "display": "Singapore", "property": [{"code": "synonym", "valueCode": "SGP"}, {"code": "numeric", "valueCode": "702"}]}, {"code": "SGP", "display": "Singapore", "property": [{"code": "synonym", "valueCode": "SG"}, {"code": "numeric", "valueCode": "702"}]}, {"code": "TH", "display": "Thailand", "property": [{"code": "synonym", "valueCode": "THA"}, {"code": "numeric", "valueCode": "764"}]}, {"code": "THA", "display": "Thailand", "property": [{"code": "synonym", "valueCode": "TH"}, {"code": "numeric", "valueCode": "764"}]}, {"code": "TL", "display": "Timor-Leste", "property": [{"code": "synonym", "valueCode": "TLS"}, {"code": "numeric", "valueCode": "626"}]}, {"code": "TLS", "display": "Timor-Leste", "property": [{"code": "synonym", "valueCode": "TL"}, {"code": "numeric", "valueCode": "626"}]}, {"code": "VN", "display": "Viet Nam", "property": [{"code": "synonym", "valueCode": "VNM"}, {"code": "numeric", "valueCode": "704"}]}, {"code": "VNM", "display": "Viet Nam", "property": [{"code": "synonym", "valueCode": "VN"}, {"code": "numeric", "valueCode": "704"}]}, {"code": "AF", "display": "Afghanistan", "property": [{"code": "synonym", "valueCode": "AFG"}, {"code": "numeric", "valueCode": "004"}]}, {"code": "AFG", "display": "Afghanistan", "property": [{"code": "synonym", "valueCode": "AF"}, {"code": "numeric", "valueCode": "004"}]}, {"code": "BD", "display": "Bangladesh", "property": [{"code": "synonym", "valueCode": "BGD"}, {"code": "numeric", "valueCode": "050"}]}, {"code": "BGD", "display": "Bangladesh", "property": [{"code": "synonym", "valueCode": "BD"}, {"code": "numeric", "valueCode": "050"}]}, {"code": "BT", "display": "Bhutan", "property": [{"code": "synonym", "valueCode": "BTN"}, {"code": "numeric", "valueCode": "064"}]}, {"code": "BTN", "display": "Bhutan", "property": [{"code": "synonym", "valueCode": "BT"}, {"code": "numeric", "valueCode": "064"}]}, {"code": "IN", "display": "India", "property": [{"code": "synonym", "valueCode": "IND"}, {"code": "numeric", "valueCode": "356"}]}, {"code": "IND", "display": "India", "property": [{"code": "synonym", "valueCode": "IN"}, {"code": "numeric", "valueCode": "356"}]}, {"code": "IR", "display": "Iran (Islamic Republic of)", "property": [{"code": "synonym", "valueCode": "IRN"}, {"code": "numeric", "valueCode": "364"}]}, {"code": "IRN", "display": "Iran (Islamic Republic of)", "property": [{"code": "synonym", "valueCode": "IR"}, {"code": "numeric", "valueCode": "364"}]}, {"code": "MV", "display": "Maldives", "property": [{"code": "synonym", "valueCode": "MDV"}, {"code": "numeric", "valueCode": "462"}]}, {"code": "MDV", "display": "Maldives", "property": [{"code": "synonym", "valueCode": "MV"}, {"code": "numeric", "valueCode": "462"}]}, {"code": "NP", "display": "Nepal", "property": [{"code": "synonym", "valueCode": "NPL"}, {"code": "numeric", "valueCode": "524"}]}, {"code": "NPL", "display": "Nepal", "property": [{"code": "synonym", "valueCode": "NP"}, {"code": "numeric", "valueCode": "524"}]}, {"code": "PK", "display": "Pakistan", "property": [{"code": "synonym", "valueCode": "PAK"}, {"code": "numeric", "valueCode": "586"}]}, {"code": "PAK", "display": "Pakistan", "property": [{"code": "synonym", "valueCode": "PK"}, {"code": "numeric", "valueCode": "586"}]}, {"code": "LK", "display": "Sri Lanka", "property": [{"code": "synonym", "valueCode": "LKA"}, {"code": "numeric", "valueCode": "144"}]}, {"code": "LKA", "display": "Sri Lanka", "property": [{"code": "synonym", "valueCode": "LK"}, {"code": "numeric", "valueCode": "144"}]}, {"code": "AM", "display": "Armenia", "property": [{"code": "synonym", "valueCode": "ARM"}, {"code": "numeric", "valueCode": "051"}]}, {"code": "ARM", "display": "Armenia", "property": [{"code": "synonym", "valueCode": "AM"}, {"code": "numeric", "valueCode": "051"}]}, {"code": "AZ", "display": "Azerbaijan", "property": [{"code": "synonym", "valueCode": "AZE"}, {"code": "numeric", "valueCode": "031"}]}, {"code": "AZE", "display": "Azerbaijan", "property": [{"code": "synonym", "valueCode": "AZ"}, {"code": "numeric", "valueCode": "031"}]}, {"code": "BH", "display": "Bahrain", "property": [{"code": "synonym", "valueCode": "BHR"}, {"code": "numeric", "valueCode": "048"}]}, {"code": "BHR", "display": "Bahrain", "property": [{"code": "synonym", "valueCode": "BH"}, {"code": "numeric", "valueCode": "048"}]}, {"code": "CY", "display": "Cyprus", "property": [{"code": "synonym", "valueCode": "CYP"}, {"code": "numeric", "valueCode": "196"}]}, {"code": "CYP", "display": "Cyprus", "property": [{"code": "synonym", "valueCode": "CY"}, {"code": "numeric", "valueCode": "196"}]}, {"code": "GE", "display": "Georgia", "property": [{"code": "synonym", "valueCode": "GEO"}, {"code": "numeric", "valueCode": "268"}]}, {"code": "GEO", "display": "Georgia", "property": [{"code": "synonym", "valueCode": "GE"}, {"code": "numeric", "valueCode": "268"}]}, {"code": "IQ", "display": "Iraq", "property": [{"code": "synonym", "valueCode": "IRQ"}, {"code": "numeric", "valueCode": "368"}]}, {"code": "IRQ", "display": "Iraq", "property": [{"code": "synonym", "valueCode": "IQ"}, {"code": "numeric", "valueCode": "368"}]}, {"code": "IL", "display": "Israel", "property": [{"code": "synonym", "valueCode": "ISR"}, {"code": "numeric", "valueCode": "376"}]}, {"code": "ISR", "display": "Israel", "property": [{"code": "synonym", "valueCode": "IL"}, {"code": "numeric", "valueCode": "376"}]}, {"code": "JO", "display": "Jordan", "property": [{"code": "synonym", "valueCode": "JOR"}, {"code": "numeric", "valueCode": "400"}]}, {"code": "JOR", "display": "Jordan", "property": [{"code": "synonym", "valueCode": "JO"}, {"code": "numeric", "valueCode": "400"}]}, {"code": "KW", "display": "Kuwait", "property": [{"code": "synonym", "valueCode": "KWT"}, {"code": "numeric", "valueCode": "414"}]}, {"code": "KWT", "display": "Kuwait", "property": [{"code": "synonym", "valueCode": "KW"}, {"code": "numeric", "valueCode": "414"}]}, {"code": "LB", "display": "Lebanon", "property": [{"code": "synonym", "valueCode": "LBN"}, {"code": "numeric", "valueCode": "422"}]}, {"code": "LBN", "display": "Lebanon", "property": [{"code": "synonym", "valueCode": "LB"}, {"code": "numeric", "valueCode": "422"}]}, {"code": "OM", "display": "Oman", "property": [{"code": "synonym", "valueCode": "OMN"}, {"code": "numeric", "valueCode": "512"}]}, {"code": "OMN", "display": "Oman", "property": [{"code": "synonym", "valueCode": "OM"}, {"code": "numeric", "valueCode": "512"}]}, {"code": "QA", "display": "Qatar", "property": [{"code": "synonym", "valueCode": "QAT"}, {"code": "numeric", "valueCode": "634"}]}, {"code": "QAT", "display": "Qatar", "property": [{"code": "synonym", "valueCode": "QA"}, {"code": "numeric", "valueCode": "634"}]}, {"code": "SA", "display": "Saudi Arabia", "property": [{"code": "synonym", "valueCode": "SAU"}, {"code": "numeric", "valueCode": "682"}]}, {"code": "SAU", "display": "Saudi Arabia", "property": [{"code": "synonym", "valueCode": "SA"}, {"code": "numeric", "valueCode": "682"}]}, {"code": "PS", "display": "State of Palestine", "property": [{"code": "synonym", "valueCode": "PSE"}, {"code": "numeric", "valueCode": "275"}]}, {"code": "PSE", "display": "State of Palestine", "property": [{"code": "synonym", "valueCode": "PS"}, {"code": "numeric", "valueCode": "275"}]}, {"code": "SY", "display": "Syrian Arab Republic", "property": [{"code": "synonym", "valueCode": "SYR"}, {"code": "numeric", "valueCode": "760"}]}, {"code": "SYR", "display": "Syrian Arab Republic", "property": [{"code": "synonym", "valueCode": "SY"}, {"code": "numeric", "valueCode": "760"}]}, {"code": "TR", "display": "Türkiye", "property": [{"code": "synonym", "valueCode": "TUR"}, {"code": "numeric", "valueCode": "792"}]}, {"code": "TUR", "display": "Türkiye", "property": [{"code": "synonym", "valueCode": "TR"}, {"code": "numeric", "valueCode": "792"}]}, {"code": "AE", "display": "United Arab Emirates", "property": [{"code": "synonym", "valueCode": "ARE"}, {"code": "numeric", "valueCode": "784"}]}, {"code": "ARE", "display": "United Arab Emirates", "property": [{"code": "synonym", "valueCode": "AE"}, {"code": "numeric", "valueCode": "784"}]}, {"code": "YE", "display": "Yemen", "property": [{"code": "synonym", "valueCode": "YEM"}, {"code": "numeric", "valueCode": "887"}]}, {"code": "YEM", "display": "Yemen", "property": [{"code": "synonym", "valueCode": "YE"}, {"code": "numeric", "valueCode": "887"}]}, {"code": "BY", "display": "Belarus", "property": [{"code": "synonym", "valueCode": "BLR"}, {"code": "numeric", "valueCode": "112"}]}, {"code": "BLR", "display": "Belarus", "property": [{"code": "synonym", "valueCode": "BY"}, {"code": "numeric", "valueCode": "112"}]}, {"code": "BG", "display": "Bulgaria", "property": [{"code": "synonym", "valueCode": "BGR"}, {"code": "numeric", "valueCode": "100"}]}, {"code": "BGR", "display": "Bulgaria", "property": [{"code": "synonym", "valueCode": "BG"}, {"code": "numeric", "valueCode": "100"}]}, {"code": "CZ", "display": "Czechia", "property": [{"code": "synonym", "valueCode": "CZE"}, {"code": "numeric", "valueCode": "203"}]}, {"code": "CZE", "display": "Czechia", "property": [{"code": "synonym", "valueCode": "CZ"}, {"code": "numeric", "valueCode": "203"}]}, {"code": "HU", "display": "Hungary", "property": [{"code": "synonym", "valueCode": "HUN"}, {"code": "numeric", "valueCode": "348"}]}, {"code": "HUN", "display": "Hungary", "property": [{"code": "synonym", "valueCode": "HU"}, {"code": "numeric", "valueCode": "348"}]}, {"code": "PL", "display": "Poland", "property": [{"code": "synonym", "valueCode": "POL"}, {"code": "numeric", "valueCode": "616"}]}, {"code": "POL", "display": "Poland", "property": [{"code": "synonym", "valueCode": "PL"}, {"code": "numeric", "valueCode": "616"}]}, {"code": "MD", "display": "Republic of Moldova", "property": [{"code": "synonym", "valueCode": "MDA"}, {"code": "numeric", "valueCode": "498"}]}, {"code": "MDA", "display": "Republic of Moldova", "property": [{"code": "synonym", "valueCode": "MD"}, {"code": "numeric", "valueCode": "498"}]}, {"code": "RO", "display": "Romania", "property": [{"code": "synonym", "valueCode": "ROU"}, {"code": "numeric", "valueCode": "642"}]}, {"code": "ROU", "display": "Romania", "property": [{"code": "synonym", "valueCode": "RO"}, {"code": "numeric", "valueCode": "642"}]}, {"code": "RU", "display": "Russian Federation", "property": [{"code": "synonym", "valueCode": "RUS"}, {"code": "numeric", "valueCode": "643"}]}, {"code": "RUS", "display": "Russian Federation", "property": [{"code": "synonym", "valueCode": "RU"}, {"code": "numeric", "valueCode": "643"}]}, {"code": "SK", "display": "Slovakia", "property": [{"code": "synonym", "valueCode": "SVK"}, {"code": "numeric", "valueCode": "703"}]}, {"code": "SVK", "display": "Slovakia", "property": [{"code": "synonym", "valueCode": "SK"}, {"code": "numeric", "valueCode": "703"}]}, {"code": "UA", "display": "Ukraine", "property": [{"code": "synonym", "valueCode": "UKR"}, {"code": "numeric", "valueCode": "804"}]}, {"code": "UKR", "display": "Ukraine", "property": [{"code": "synonym", "valueCode": "UA"}, {"code": "numeric", "valueCode": "804"}]}, {"code": "AX", "display": "Åland Islands", "property": [{"code": "synonym", "valueCode": "ALA"}, {"code": "numeric", "valueCode": "248"}]}, {"code": "ALA", "display": "Åland Islands", "property": [{"code": "synonym", "valueCode": "AX"}, {"code": "numeric", "valueCode": "248"}]}, {"code": "DK", "display": "Denmark", "property": [{"code": "synonym", "valueCode": "DNK"}, {"code": "numeric", "valueCode": "208"}]}, {"code": "DNK", "display": "Denmark", "property": [{"code": "synonym", "valueCode": "DK"}, {"code": "numeric", "valueCode": "208"}]}, {"code": "EE", "display": "Estonia", "property": [{"code": "synonym", "valueCode": "EST"}, {"code": "numeric", "valueCode": "233"}]}, {"code": "EST", "display": "Estonia", "property": [{"code": "synonym", "valueCode": "EE"}, {"code": "numeric", "valueCode": "233"}]}, {"code": "FO", "display": "Faroe Islands", "property": [{"code": "synonym", "valueCode": "FRO"}, {"code": "numeric", "valueCode": "234"}]}, {"code": "FRO", "display": "Faroe Islands", "property": [{"code": "synonym", "valueCode": "FO"}, {"code": "numeric", "valueCode": "234"}]}, {"code": "FI", "display": "Finland", "property": [{"code": "synonym", "valueCode": "FIN"}, {"code": "numeric", "valueCode": "246"}]}, {"code": "FIN", "display": "Finland", "property": [{"code": "synonym", "valueCode": "FI"}, {"code": "numeric", "valueCode": "246"}]}, {"code": "GG", "display": "Guernsey", "property": [{"code": "synonym", "valueCode": "GGY"}, {"code": "numeric", "valueCode": "831"}]}, {"code": "GGY", "display": "Guernsey", "property": [{"code": "synonym", "valueCode": "GG"}, {"code": "numeric", "valueCode": "831"}]}, {"code": "IS", "display": "Iceland", "property": [{"code": "synonym", "valueCode": "ISL"}, {"code": "numeric", "valueCode": "352"}]}, {"code": "ISL", "display": "Iceland", "property": [{"code": "synonym", "valueCode": "IS"}, {"code": "numeric", "valueCode": "352"}]}, {"code": "IE", "display": "Ireland", "property": [{"code": "synonym", "valueCode": "IRL"}, {"code": "numeric", "valueCode": "372"}]}, {"code": "IRL", "display": "Ireland", "property": [{"code": "synonym", "valueCode": "IE"}, {"code": "numeric", "valueCode": "372"}]}, {"code": "IM", "display": "Isle of Man", "property": [{"code": "synonym", "valueCode": "IMN"}, {"code": "numeric", "valueCode": "833"}]}, {"code": "IMN", "display": "Isle of Man", "property": [{"code": "synonym", "valueCode": "IM"}, {"code": "numeric", "valueCode": "833"}]}, {"code": "JE", "display": "Jersey", "property": [{"code": "synonym", "valueCode": "JEY"}, {"code": "numeric", "valueCode": "832"}]}, {"code": "JEY", "display": "Jersey", "property": [{"code": "synonym", "valueCode": "JE"}, {"code": "numeric", "valueCode": "832"}]}, {"code": "LV", "display": "Latvia", "property": [{"code": "synonym", "valueCode": "LVA"}, {"code": "numeric", "valueCode": "428"}]}, {"code": "LVA", "display": "Latvia", "property": [{"code": "synonym", "valueCode": "LV"}, {"code": "numeric", "valueCode": "428"}]}, {"code": "LT", "display": "Lithuania", "property": [{"code": "synonym", "valueCode": "LTU"}, {"code": "numeric", "valueCode": "440"}]}, {"code": "LTU", "display": "Lithuania", "property": [{"code": "synonym", "valueCode": "LT"}, {"code": "numeric", "valueCode": "440"}]}, {"code": "NO", "display": "Norway", "property": [{"code": "synonym", "valueCode": "NOR"}, {"code": "numeric", "valueCode": "578"}]}, {"code": "NOR", "display": "Norway", "property": [{"code": "synonym", "valueCode": "NO"}, {"code": "numeric", "valueCode": "578"}]}, {"code": "SJ", "display": "Svalbard and Jan Mayen Islands", "property": [{"code": "synonym", "valueCode": "SJM"}, {"code": "numeric", "valueCode": "744"}]}, {"code": "SJM", "display": "Svalbard and Jan Mayen Islands", "property": [{"code": "synonym", "valueCode": "SJ"}, {"code": "numeric", "valueCode": "744"}]}, {"code": "SE", "display": "Sweden", "property": [{"code": "synonym", "valueCode": "SWE"}, {"code": "numeric", "valueCode": "752"}]}, {"code": "SWE", "display": "Sweden", "property": [{"code": "synonym", "valueCode": "SE"}, {"code": "numeric", "valueCode": "752"}]}, {"code": "GB", "display": "United Kingdom of Great Britain and Northern Ireland", "property": [{"code": "synonym", "valueCode": "GBR"}, {"code": "numeric", "valueCode": "826"}]}, {"code": "GBR", "display": "United Kingdom of Great Britain and Northern Ireland", "property": [{"code": "synonym", "valueCode": "GB"}, {"code": "numeric", "valueCode": "826"}]}, {"code": "AL", "display": "Albania", "property": [{"code": "synonym", "valueCode": "ALB"}, {"code": "numeric", "valueCode": "008"}]}, {"code": "ALB", "display": "Albania", "property": [{"code": "synonym", "valueCode": "AL"}, {"code": "numeric", "valueCode": "008"}]}, {"code": "AD", "display": "Andorra", "property": [{"code": "synonym", "valueCode": "AND"}, {"code": "numeric", "valueCode": "020"}]}, {"code": "AND", "display": "Andorra", "property": [{"code": "synonym", "valueCode": "AD"}, {"code": "numeric", "valueCode": "020"}]}, {"code": "BA", "display": "Bosnia and Herzegovina", "property": [{"code": "synonym", "valueCode": "BIH"}, {"code": "numeric", "valueCode": "070"}]}, {"code": "BIH", "display": "Bosnia and Herzegovina", "property": [{"code": "synonym", "valueCode": "BA"}, {"code": "numeric", "valueCode": "070"}]}, {"code": "HR", "display": "Croatia", "property": [{"code": "synonym", "valueCode": "HRV"}, {"code": "numeric", "valueCode": "191"}]}, {"code": "HRV", "display": "Croatia", "property": [{"code": "synonym", "valueCode": "HR"}, {"code": "numeric", "valueCode": "191"}]}, {"code": "GI", "display": "Gibraltar", "property": [{"code": "synonym", "valueCode": "GIB"}, {"code": "numeric", "valueCode": "292"}]}, {"code": "GIB", "display": "Gibraltar", "property": [{"code": "synonym", "valueCode": "GI"}, {"code": "numeric", "valueCode": "292"}]}, {"code": "GR", "display": "Greece", "property": [{"code": "synonym", "valueCode": "GRC"}, {"code": "numeric", "valueCode": "300"}]}, {"code": "GRC", "display": "Greece", "property": [{"code": "synonym", "valueCode": "GR"}, {"code": "numeric", "valueCode": "300"}]}, {"code": "VA", "display": "Holy See", "property": [{"code": "synonym", "valueCode": "VAT"}, {"code": "numeric", "valueCode": "336"}]}, {"code": "VAT", "display": "Holy See", "property": [{"code": "synonym", "valueCode": "VA"}, {"code": "numeric", "valueCode": "336"}]}, {"code": "IT", "display": "Italy", "property": [{"code": "synonym", "valueCode": "ITA"}, {"code": "numeric", "valueCode": "380"}]}, {"code": "ITA", "display": "Italy", "property": [{"code": "synonym", "valueCode": "IT"}, {"code": "numeric", "valueCode": "380"}]}, {"code": "MT", "display": "Malta", "property": [{"code": "synonym", "valueCode": "MLT"}, {"code": "numeric", "valueCode": "470"}]}, {"code": "MLT", "display": "Malta", "property": [{"code": "synonym", "valueCode": "MT"}, {"code": "numeric", "valueCode": "470"}]}, {"code": "ME", "display": "Montenegro", "property": [{"code": "synonym", "valueCode": "MNE"}, {"code": "numeric", "valueCode": "499"}]}, {"code": "MNE", "display": "Montenegro", "property": [{"code": "synonym", "valueCode": "ME"}, {"code": "numeric", "valueCode": "499"}]}, {"code": "MK", "display": "North Macedonia", "property": [{"code": "synonym", "valueCode": "MKD"}, {"code": "numeric", "valueCode": "807"}]}, {"code": "MKD", "display": "North Macedonia", "property": [{"code": "synonym", "valueCode": "MK"}, {"code": "numeric", "valueCode": "807"}]}, {"code": "PT", "display": "Portugal", "property": [{"code": "synonym", "valueCode": "PRT"}, {"code": "numeric", "valueCode": "620"}]}, {"code": "PRT", "display": "Portugal", "property": [{"code": "synonym", "valueCode": "PT"}, {"code": "numeric", "valueCode": "620"}]}, {"code": "SM", "display": "San Marino", "property": [{"code": "synonym", "valueCode": "SMR"}, {"code": "numeric", "valueCode": "674"}]}, {"code": "SMR", "display": "San Marino", "property": [{"code": "synonym", "valueCode": "SM"}, {"code": "numeric", "valueCode": "674"}]}, {"code": "RS", "display": "Serbia", "property": [{"code": "synonym", "valueCode": "SRB"}, {"code": "numeric", "valueCode": "688"}]}, {"code": "SRB", "display": "Serbia", "property": [{"code": "synonym", "valueCode": "RS"}, {"code": "numeric", "valueCode": "688"}]}, {"code": "SI", "display": "Slovenia", "property": [{"code": "synonym", "valueCode": "SVN"}, {"code": "numeric", "valueCode": "705"}]}, {"code": "SVN", "display": "Slovenia", "property": [{"code": "synonym", "valueCode": "SI"}, {"code": "numeric", "valueCode": "705"}]}, {"code": "ES", "display": "Spain", "property": [{"code": "synonym", "valueCode": "ESP"}, {"code": "numeric", "valueCode": "724"}]}, {"code": "ESP", "display": "Spain", "property": [{"code": "synonym", "valueCode": "ES"}, {"code": "numeric", "valueCode": "724"}]}, {"code": "AT", "display": "Austria", "property": [{"code": "synonym", "valueCode": "AUT"}, {"code": "numeric", "valueCode": "040"}]}, {"code": "AUT", "display": "Austria", "property": [{"code": "synonym", "valueCode": "AT"}, {"code": "numeric", "valueCode": "040"}]}, {"code": "BE", "display": "Belgium", "property": [{"code": "synonym", "valueCode": "BEL"}, {"code": "numeric", "valueCode": "056"}]}, {"code": "BEL", "display": "Belgium", "property": [{"code": "synonym", "valueCode": "BE"}, {"code": "numeric", "valueCode": "056"}]}, {"code": "FR", "display": "France", "property": [{"code": "synonym", "valueCode": "FRA"}, {"code": "numeric", "valueCode": "250"}]}, {"code": "FRA", "display": "France", "property": [{"code": "synonym", "valueCode": "FR"}, {"code": "numeric", "valueCode": "250"}]}, {"code": "DE", "display": "Germany", "property": [{"code": "synonym", "valueCode": "DEU"}, {"code": "numeric", "valueCode": "276"}]}, {"code": "DEU", "display": "Germany", "property": [{"code": "synonym", "valueCode": "DE"}, {"code": "numeric", "valueCode": "276"}]}, {"code": "LI", "display": "Liechtenstein", "property": [{"code": "synonym", "valueCode": "LIE"}, {"code": "numeric", "valueCode": "438"}]}, {"code": "LIE", "display": "Liechtenstein", "property": [{"code": "synonym", "valueCode": "LI"}, {"code": "numeric", "valueCode": "438"}]}, {"code": "LU", "display": "Luxembourg", "property": [{"code": "synonym", "valueCode": "LUX"}, {"code": "numeric", "valueCode": "442"}]}, {"code": "LUX", "display": "Luxembourg", "property": [{"code": "synonym", "valueCode": "LU"}, {"code": "numeric", "valueCode": "442"}]}, {"code": "MC", "display": "Monaco", "property": [{"code": "synonym", "valueCode": "MCO"}, {"code": "numeric", "valueCode": "492"}]}, {"code": "MCO", "display": "Monaco", "property": [{"code": "synonym", "valueCode": "MC"}, {"code": "numeric", "valueCode": "492"}]}, {"code": "NL", "display": "Netherlands (Kingdom of the)", "property": [{"code": "synonym", "valueCode": "NLD"}, {"code": "numeric", "valueCode": "528"}]}, {"code": "NLD", "display": "Netherlands (Kingdom of the)", "property": [{"code": "synonym", "valueCode": "NL"}, {"code": "numeric", "valueCode": "528"}]}, {"code": "CH", "display": "Switzerland", "property": [{"code": "synonym", "valueCode": "CHE"}, {"code": "numeric", "valueCode": "756"}]}, {"code": "CHE", "display": "Switzerland", "property": [{"code": "synonym", "valueCode": "CH"}, {"code": "numeric", "valueCode": "756"}]}, {"code": "AU", "display": "Australia", "property": [{"code": "synonym", "valueCode": "AUS"}, {"code": "numeric", "valueCode": "036"}]}, {"code": "AUS", "display": "Australia", "property": [{"code": "synonym", "valueCode": "AU"}, {"code": "numeric", "valueCode": "036"}]}, {"code": "CX", "display": "Christmas Island", "property": [{"code": "synonym", "valueCode": "CXR"}, {"code": "numeric", "valueCode": "162"}]}, {"code": "CXR", "display": "Christmas Island", "property": [{"code": "synonym", "valueCode": "CX"}, {"code": "numeric", "valueCode": "162"}]}, {"code": "CC", "display": "Cocos (Keeling) Islands", "property": [{"code": "synonym", "valueCode": "CCK"}, {"code": "numeric", "valueCode": "166"}]}, {"code": "CCK", "display": "Cocos (Keeling) Islands", "property": [{"code": "synonym", "valueCode": "CC"}, {"code": "numeric", "valueCode": "166"}]}, {"code": "HM", "display": "Heard Island and McDonald Islands", "property": [{"code": "synonym", "valueCode": "HMD"}, {"code": "numeric", "valueCode": "334"}]}, {"code": "HMD", "display": "Heard Island and McDonald Islands", "property": [{"code": "synonym", "valueCode": "HM"}, {"code": "numeric", "valueCode": "334"}]}, {"code": "NZ", "display": "New Zealand", "property": [{"code": "synonym", "valueCode": "NZL"}, {"code": "numeric", "valueCode": "554"}]}, {"code": "NZL", "display": "New Zealand", "property": [{"code": "synonym", "valueCode": "NZ"}, {"code": "numeric", "valueCode": "554"}]}, {"code": "NF", "display": "Norfolk Island", "property": [{"code": "synonym", "valueCode": "NFK"}, {"code": "numeric", "valueCode": "574"}]}, {"code": "NFK", "display": "Norfolk Island", "property": [{"code": "synonym", "valueCode": "NF"}, {"code": "numeric", "valueCode": "574"}]}, {"code": "FJ", "display": "Fiji", "property": [{"code": "synonym", "valueCode": "FJI"}, {"code": "numeric", "valueCode": "242"}]}, {"code": "FJI", "display": "Fiji", "property": [{"code": "synonym", "valueCode": "FJ"}, {"code": "numeric", "valueCode": "242"}]}, {"code": "NC", "display": "New Caledonia", "property": [{"code": "synonym", "valueCode": "NCL"}, {"code": "numeric", "valueCode": "540"}]}, {"code": "NCL", "display": "New Caledonia", "property": [{"code": "synonym", "valueCode": "NC"}, {"code": "numeric", "valueCode": "540"}]}, {"code": "PG", "display": "Papua New Guinea", "property": [{"code": "synonym", "valueCode": "PNG"}, {"code": "numeric", "valueCode": "598"}]}, {"code": "PNG", "display": "Papua New Guinea", "property": [{"code": "synonym", "valueCode": "PG"}, {"code": "numeric", "valueCode": "598"}]}, {"code": "SB", "display": "Solomon Islands", "property": [{"code": "synonym", "valueCode": "SLB"}, {"code": "numeric", "valueCode": "090"}]}, {"code": "SLB", "display": "Solomon Islands", "property": [{"code": "synonym", "valueCode": "SB"}, {"code": "numeric", "valueCode": "090"}]}, {"code": "VU", "display": "Vanuatu", "property": [{"code": "synonym", "valueCode": "VUT"}, {"code": "numeric", "valueCode": "548"}]}, {"code": "VUT", "display": "Vanuatu", "property": [{"code": "synonym", "valueCode": "VU"}, {"code": "numeric", "valueCode": "548"}]}, {"code": "GU", "display": "Guam", "property": [{"code": "synonym", "valueCode": "GUM"}, {"code": "numeric", "valueCode": "316"}]}, {"code": "GUM", "display": "Guam", "property": [{"code": "synonym", "valueCode": "GU"}, {"code": "numeric", "valueCode": "316"}]}, {"code": "KI", "display": "Kiribati", "property": [{"code": "synonym", "valueCode": "KIR"}, {"code": "numeric", "valueCode": "296"}]}, {"code": "KIR", "display": "Kiribati", "property": [{"code": "synonym", "valueCode": "KI"}, {"code": "numeric", "valueCode": "296"}]}, {"code": "MH", "display": "Marshall Islands", "property": [{"code": "synonym", "valueCode": "MHL"}, {"code": "numeric", "valueCode": "584"}]}, {"code": "MHL", "display": "Marshall Islands", "property": [{"code": "synonym", "valueCode": "MH"}, {"code": "numeric", "valueCode": "584"}]}, {"code": "FM", "display": "Micronesia (Federated States of)", "property": [{"code": "synonym", "valueCode": "FSM"}, {"code": "numeric", "valueCode": "583"}]}, {"code": "FSM", "display": "Micronesia (Federated States of)", "property": [{"code": "synonym", "valueCode": "FM"}, {"code": "numeric", "valueCode": "583"}]}, {"code": "NR", "display": "Nauru", "property": [{"code": "synonym", "valueCode": "NRU"}, {"code": "numeric", "valueCode": "520"}]}, {"code": "NRU", "display": "Nauru", "property": [{"code": "synonym", "valueCode": "NR"}, {"code": "numeric", "valueCode": "520"}]}, {"code": "MP", "display": "Northern Mariana Islands", "property": [{"code": "synonym", "valueCode": "MNP"}, {"code": "numeric", "valueCode": "580"}]}, {"code": "MNP", "display": "Northern Mariana Islands", "property": [{"code": "synonym", "valueCode": "MP"}, {"code": "numeric", "valueCode": "580"}]}, {"code": "PW", "display": "<PERSON><PERSON>", "property": [{"code": "synonym", "valueCode": "PLW"}, {"code": "numeric", "valueCode": "585"}]}, {"code": "PLW", "display": "<PERSON><PERSON>", "property": [{"code": "synonym", "valueCode": "PW"}, {"code": "numeric", "valueCode": "585"}]}, {"code": "UM", "display": "United States Minor Outlying Islands", "property": [{"code": "synonym", "valueCode": "UMI"}, {"code": "numeric", "valueCode": "581"}]}, {"code": "UMI", "display": "United States Minor Outlying Islands", "property": [{"code": "synonym", "valueCode": "UM"}, {"code": "numeric", "valueCode": "581"}]}, {"code": "AS", "display": "American Samoa", "property": [{"code": "synonym", "valueCode": "ASM"}, {"code": "numeric", "valueCode": "016"}]}, {"code": "ASM", "display": "American Samoa", "property": [{"code": "synonym", "valueCode": "AS"}, {"code": "numeric", "valueCode": "016"}]}, {"code": "CK", "display": "Cook Islands", "property": [{"code": "synonym", "valueCode": "COK"}, {"code": "numeric", "valueCode": "184"}]}, {"code": "COK", "display": "Cook Islands", "property": [{"code": "synonym", "valueCode": "CK"}, {"code": "numeric", "valueCode": "184"}]}, {"code": "PF", "display": "French Polynesia", "property": [{"code": "synonym", "valueCode": "PYF"}, {"code": "numeric", "valueCode": "258"}]}, {"code": "PYF", "display": "French Polynesia", "property": [{"code": "synonym", "valueCode": "PF"}, {"code": "numeric", "valueCode": "258"}]}, {"code": "NU", "display": "Niue", "property": [{"code": "synonym", "valueCode": "NIU"}, {"code": "numeric", "valueCode": "570"}]}, {"code": "NIU", "display": "Niue", "property": [{"code": "synonym", "valueCode": "NU"}, {"code": "numeric", "valueCode": "570"}]}, {"code": "PN", "display": "Pitcairn", "property": [{"code": "synonym", "valueCode": "PCN"}, {"code": "numeric", "valueCode": "612"}]}, {"code": "PCN", "display": "Pitcairn", "property": [{"code": "synonym", "valueCode": "PN"}, {"code": "numeric", "valueCode": "612"}]}, {"code": "WS", "display": "Samoa", "property": [{"code": "synonym", "valueCode": "WSM"}, {"code": "numeric", "valueCode": "882"}]}, {"code": "WSM", "display": "Samoa", "property": [{"code": "synonym", "valueCode": "WS"}, {"code": "numeric", "valueCode": "882"}]}, {"code": "TK", "display": "Tokelau", "property": [{"code": "synonym", "valueCode": "TKL"}, {"code": "numeric", "valueCode": "772"}]}, {"code": "TKL", "display": "Tokelau", "property": [{"code": "synonym", "valueCode": "TK"}, {"code": "numeric", "valueCode": "772"}]}, {"code": "TO", "display": "Tonga", "property": [{"code": "synonym", "valueCode": "TON"}, {"code": "numeric", "valueCode": "776"}]}, {"code": "TON", "display": "Tonga", "property": [{"code": "synonym", "valueCode": "TO"}, {"code": "numeric", "valueCode": "776"}]}, {"code": "TV", "display": "Tuvalu", "property": [{"code": "synonym", "valueCode": "TUV"}, {"code": "numeric", "valueCode": "798"}]}, {"code": "TUV", "display": "Tuvalu", "property": [{"code": "synonym", "valueCode": "TV"}, {"code": "numeric", "valueCode": "798"}]}, {"code": "WF", "display": "Wallis and Futuna Islands", "property": [{"code": "synonym", "valueCode": "WLF"}, {"code": "numeric", "valueCode": "876"}]}, {"code": "WLF", "display": "Wallis and Futuna Islands", "property": [{"code": "synonym", "valueCode": "WF"}, {"code": "numeric", "valueCode": "876"}]}], "property": [{"code": "synonym", "type": "code", "uri": "http://hl7.org/fhir/concept-properties#synonym", "description": "Equivalent code"}, {"code": "numeric", "type": "code", "uri": "http://hl7.org/fhir/concept-properties#synonym", "description": "Equivalent numeric code"}]}}, {"fullUrl": "urn:iso:std:iso:4217", "resource": {"resourceType": "CodeSystem", "status": "active", "url": "urn:iso:std:iso:4217", "content": "complete", "concept": [{"code": "AFN", "display": "Afghani", "property": [{"code": "numeric", "valueCode": "971"}]}, {"code": "EUR", "display": "Euro", "property": [{"code": "numeric", "valueCode": "978"}]}, {"code": "ALL", "display": "Lek", "property": [{"code": "numeric", "valueCode": "008"}]}, {"code": "DZD", "display": "Algerian Dinar", "property": [{"code": "numeric", "valueCode": "012"}]}, {"code": "USD", "display": "US Dollar", "property": [{"code": "numeric", "valueCode": "840"}]}, {"code": "AOA", "display": "Kwan<PERSON>", "property": [{"code": "numeric", "valueCode": "973"}]}, {"code": "XCD", "display": "East Caribbean Dollar", "property": [{"code": "numeric", "valueCode": "951"}]}, {"code": "ARS", "display": "Argentine Peso", "property": [{"code": "numeric", "valueCode": "032"}]}, {"code": "AMD", "display": "Armenian Dram", "property": [{"code": "numeric", "valueCode": "051"}]}, {"code": "AWG", "display": "Aruban Florin", "property": [{"code": "numeric", "valueCode": "533"}]}, {"code": "AUD", "display": "Australian Dollar", "property": [{"code": "numeric", "valueCode": "036"}]}, {"code": "AZN", "display": "Azerbaijan Manat", "property": [{"code": "numeric", "valueCode": "944"}]}, {"code": "BSD", "display": "Bahamian Dollar", "property": [{"code": "numeric", "valueCode": "044"}]}, {"code": "BHD", "display": "<PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "048"}]}, {"code": "BDT", "display": "<PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "050"}]}, {"code": "BBD", "display": "Barbados Dollar", "property": [{"code": "numeric", "valueCode": "052"}]}, {"code": "BYN", "display": "Belarusian Ruble", "property": [{"code": "numeric", "valueCode": "933"}]}, {"code": "BZD", "display": "Belize Dollar", "property": [{"code": "numeric", "valueCode": "084"}]}, {"code": "XOF", "display": "CFA Franc BCEAO", "property": [{"code": "numeric", "valueCode": "952"}]}, {"code": "BMD", "display": "Bermudian Dollar", "property": [{"code": "numeric", "valueCode": "060"}]}, {"code": "INR", "display": "Indian Rupee", "property": [{"code": "numeric", "valueCode": "356"}]}, {"code": "BTN", "display": "Ngultrum", "property": [{"code": "numeric", "valueCode": "064"}]}, {"code": "BOB", "display": "Boliviano", "property": [{"code": "numeric", "valueCode": "068"}]}, {"code": "BOV", "display": "Mvdol", "property": [{"code": "numeric", "valueCode": "984"}]}, {"code": "BAM", "display": "Convertible Mark", "property": [{"code": "numeric", "valueCode": "977"}]}, {"code": "BWP", "display": "<PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "072"}]}, {"code": "NOK", "display": "Norwegian Krone", "property": [{"code": "numeric", "valueCode": "578"}]}, {"code": "BRL", "display": "Brazilian Real", "property": [{"code": "numeric", "valueCode": "986"}]}, {"code": "BND", "display": "Brunei Dollar", "property": [{"code": "numeric", "valueCode": "096"}]}, {"code": "BGN", "display": "Bulgarian Lev", "property": [{"code": "numeric", "valueCode": "975"}]}, {"code": "BIF", "display": "Burundi Franc", "property": [{"code": "numeric", "valueCode": "108"}]}, {"code": "CVE", "display": "Cabo Verde Escudo", "property": [{"code": "numeric", "valueCode": "132"}]}, {"code": "KHR", "display": "Riel", "property": [{"code": "numeric", "valueCode": "116"}]}, {"code": "XAF", "display": "CFA Franc BEAC", "property": [{"code": "numeric", "valueCode": "950"}]}, {"code": "CAD", "display": "Canadian Dollar", "property": [{"code": "numeric", "valueCode": "124"}]}, {"code": "KYD", "display": "Cayman Islands Dollar", "property": [{"code": "numeric", "valueCode": "136"}]}, {"code": "CLP", "display": "Chilean Peso", "property": [{"code": "numeric", "valueCode": "152"}]}, {"code": "CLF", "display": "Unidad de Fomento", "property": [{"code": "numeric", "valueCode": "990"}]}, {"code": "CNY", "display": "<PERSON>", "property": [{"code": "numeric", "valueCode": "156"}]}, {"code": "COP", "display": "Colombian Peso", "property": [{"code": "numeric", "valueCode": "170"}]}, {"code": "COU", "display": "Unidad de Valor Real", "property": [{"code": "numeric", "valueCode": "970"}]}, {"code": "KMF", "display": "<PERSON><PERSON> ", "property": [{"code": "numeric", "valueCode": "174"}]}, {"code": "CDF", "display": "Congolese Franc", "property": [{"code": "numeric", "valueCode": "976"}]}, {"code": "NZD", "display": "New Zealand Dollar", "property": [{"code": "numeric", "valueCode": "554"}]}, {"code": "CRC", "display": "Costa Rican Colon", "property": [{"code": "numeric", "valueCode": "188"}]}, {"code": "CUP", "display": "Cuban Peso", "property": [{"code": "numeric", "valueCode": "192"}]}, {"code": "CUC", "display": "Peso Convertible", "property": [{"code": "numeric", "valueCode": "931"}]}, {"code": "ANG", "display": "Netherlands Antillean Guilder", "property": [{"code": "numeric", "valueCode": "532"}]}, {"code": "CZK", "display": "Czech Koruna", "property": [{"code": "numeric", "valueCode": "203"}]}, {"code": "DKK", "display": "Danish Krone", "property": [{"code": "numeric", "valueCode": "208"}]}, {"code": "DJF", "display": "Djibouti Franc", "property": [{"code": "numeric", "valueCode": "262"}]}, {"code": "DOP", "display": "Dominican Peso", "property": [{"code": "numeric", "valueCode": "214"}]}, {"code": "EGP", "display": "Egyptian Pound", "property": [{"code": "numeric", "valueCode": "818"}]}, {"code": "SVC", "display": "El Salvador Colon", "property": [{"code": "numeric", "valueCode": "222"}]}, {"code": "ERN", "display": "Nakfa", "property": [{"code": "numeric", "valueCode": "232"}]}, {"code": "SZL", "display": "<PERSON><PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "748"}]}, {"code": "ETB", "display": "Ethiopian Birr", "property": [{"code": "numeric", "valueCode": "230"}]}, {"code": "FKP", "display": "Falkland Islands Pound", "property": [{"code": "numeric", "valueCode": "238"}]}, {"code": "FJD", "display": "Fiji Dollar", "property": [{"code": "numeric", "valueCode": "242"}]}, {"code": "XPF", "display": "CFP Franc", "property": [{"code": "numeric", "valueCode": "953"}]}, {"code": "GMD", "display": "<PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "270"}]}, {"code": "GEL", "display": "<PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "981"}]}, {"code": "GHS", "display": "Ghana Cedi", "property": [{"code": "numeric", "valueCode": "936"}]}, {"code": "GIP", "display": "Gibraltar Pound", "property": [{"code": "numeric", "valueCode": "292"}]}, {"code": "GTQ", "display": "Quetzal", "property": [{"code": "numeric", "valueCode": "320"}]}, {"code": "GBP", "display": "Pound Sterling", "property": [{"code": "numeric", "valueCode": "826"}]}, {"code": "GNF", "display": "Guinean Franc", "property": [{"code": "numeric", "valueCode": "324"}]}, {"code": "GYD", "display": "Guyana Dollar", "property": [{"code": "numeric", "valueCode": "328"}]}, {"code": "HTG", "display": "<PERSON><PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "332"}]}, {"code": "HNL", "display": "<PERSON><PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "340"}]}, {"code": "HKD", "display": "Hong Kong Dollar", "property": [{"code": "numeric", "valueCode": "344"}]}, {"code": "HUF", "display": "Forint", "property": [{"code": "numeric", "valueCode": "348"}]}, {"code": "ISK", "display": "Iceland Krona", "property": [{"code": "numeric", "valueCode": "352"}]}, {"code": "IDR", "display": "<PERSON><PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "360"}]}, {"code": "XDR", "display": "SDR (Special Drawing Right)", "property": [{"code": "numeric", "valueCode": "960"}]}, {"code": "IRR", "display": "Iranian Rial", "property": [{"code": "numeric", "valueCode": "364"}]}, {"code": "IQD", "display": "Iraqi <PERSON>", "property": [{"code": "numeric", "valueCode": "368"}]}, {"code": "ILS", "display": "New Israeli Sheqel", "property": [{"code": "numeric", "valueCode": "376"}]}, {"code": "JMD", "display": "Jamaican Dollar", "property": [{"code": "numeric", "valueCode": "388"}]}, {"code": "JPY", "display": "Yen", "property": [{"code": "numeric", "valueCode": "392"}]}, {"code": "JOD", "display": "<PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "400"}]}, {"code": "KZT", "display": "Tenge", "property": [{"code": "numeric", "valueCode": "398"}]}, {"code": "KES", "display": "Kenyan Shilling", "property": [{"code": "numeric", "valueCode": "404"}]}, {"code": "KPW", "display": "North Korean Won", "property": [{"code": "numeric", "valueCode": "408"}]}, {"code": "KRW", "display": "Won", "property": [{"code": "numeric", "valueCode": "410"}]}, {"code": "KWD", "display": "<PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "414"}]}, {"code": "KGS", "display": "Som", "property": [{"code": "numeric", "valueCode": "417"}]}, {"code": "LAK", "display": "<PERSON>", "property": [{"code": "numeric", "valueCode": "418"}]}, {"code": "LBP", "display": "Lebanese Pound", "property": [{"code": "numeric", "valueCode": "422"}]}, {"code": "LSL", "display": "Loti", "property": [{"code": "numeric", "valueCode": "426"}]}, {"code": "ZAR", "display": "Rand", "property": [{"code": "numeric", "valueCode": "710"}]}, {"code": "LRD", "display": "Liberian Dollar", "property": [{"code": "numeric", "valueCode": "430"}]}, {"code": "LYD", "display": "Libyan Dinar", "property": [{"code": "numeric", "valueCode": "434"}]}, {"code": "CHF", "display": "Swiss Franc", "property": [{"code": "numeric", "valueCode": "756"}]}, {"code": "MOP", "display": "Pataca", "property": [{"code": "numeric", "valueCode": "446"}]}, {"code": "MKD", "display": "<PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "807"}]}, {"code": "MGA", "display": "Malagasy Ariary", "property": [{"code": "numeric", "valueCode": "969"}]}, {"code": "MWK", "display": "Malawi Kwacha", "property": [{"code": "numeric", "valueCode": "454"}]}, {"code": "MYR", "display": "Malaysian Ringgit", "property": [{"code": "numeric", "valueCode": "458"}]}, {"code": "MVR", "display": "<PERSON><PERSON><PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "462"}]}, {"code": "MRU", "display": "Ouguiya", "property": [{"code": "numeric", "valueCode": "929"}]}, {"code": "MUR", "display": "Mauritius Rupee", "property": [{"code": "numeric", "valueCode": "480"}]}, {"code": "XUA", "display": "ADB Unit of Account", "property": [{"code": "numeric", "valueCode": "965"}]}, {"code": "MXN", "display": "Mexican Peso", "property": [{"code": "numeric", "valueCode": "484"}]}, {"code": "MXV", "display": "Mexican Unidad de Inversion (UDI)", "property": [{"code": "numeric", "valueCode": "979"}]}, {"code": "MDL", "display": "Moldovan Leu", "property": [{"code": "numeric", "valueCode": "498"}]}, {"code": "MNT", "display": "<PERSON><PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "496"}]}, {"code": "MAD", "display": "Moroccan <PERSON><PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "504"}]}, {"code": "MZN", "display": "Mozambique Metical", "property": [{"code": "numeric", "valueCode": "943"}]}, {"code": "MMK", "display": "Kyat", "property": [{"code": "numeric", "valueCode": "104"}]}, {"code": "NAD", "display": "Namibia Dollar", "property": [{"code": "numeric", "valueCode": "516"}]}, {"code": "NPR", "display": "Nepalese Rupee", "property": [{"code": "numeric", "valueCode": "524"}]}, {"code": "NIO", "display": "Cordoba Oro", "property": [{"code": "numeric", "valueCode": "558"}]}, {"code": "NGN", "display": "<PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "566"}]}, {"code": "OMR", "display": "<PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "512"}]}, {"code": "PKR", "display": "Pakistan Rupee", "property": [{"code": "numeric", "valueCode": "586"}]}, {"code": "PAB", "display": "Balboa", "property": [{"code": "numeric", "valueCode": "590"}]}, {"code": "PGK", "display": "<PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "598"}]}, {"code": "PYG", "display": "Guarani", "property": [{"code": "numeric", "valueCode": "600"}]}, {"code": "PEN", "display": "Sol", "property": [{"code": "numeric", "valueCode": "604"}]}, {"code": "PHP", "display": "Philippine Peso", "property": [{"code": "numeric", "valueCode": "608"}]}, {"code": "PLN", "display": "<PERSON><PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "985"}]}, {"code": "QAR", "display": "<PERSON><PERSON> R<PERSON>", "property": [{"code": "numeric", "valueCode": "634"}]}, {"code": "RON", "display": "Romanian Leu", "property": [{"code": "numeric", "valueCode": "946"}]}, {"code": "RUB", "display": "Russian Ruble", "property": [{"code": "numeric", "valueCode": "643"}]}, {"code": "RWF", "display": "Rwanda Franc", "property": [{"code": "numeric", "valueCode": "646"}]}, {"code": "SHP", "display": "<PERSON>", "property": [{"code": "numeric", "valueCode": "654"}]}, {"code": "WST", "display": "<PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "882"}]}, {"code": "STN", "display": "Dobra", "property": [{"code": "numeric", "valueCode": "930"}]}, {"code": "SAR", "display": "Saudi Riyal", "property": [{"code": "numeric", "valueCode": "682"}]}, {"code": "RSD", "display": "Serbian Dinar", "property": [{"code": "numeric", "valueCode": "941"}]}, {"code": "SCR", "display": "Seychelles Rupee", "property": [{"code": "numeric", "valueCode": "690"}]}, {"code": "SLE", "display": "Leone", "property": [{"code": "numeric", "valueCode": "925"}]}, {"code": "SGD", "display": "Singapore Dollar", "property": [{"code": "numeric", "valueCode": "702"}]}, {"code": "XSU", "display": "<PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "994"}]}, {"code": "SBD", "display": "Solomon Islands Dollar", "property": [{"code": "numeric", "valueCode": "090"}]}, {"code": "SOS", "display": "Somali Shilling", "property": [{"code": "numeric", "valueCode": "706"}]}, {"code": "SSP", "display": "South Sudanese Pound", "property": [{"code": "numeric", "valueCode": "728"}]}, {"code": "LKR", "display": "Sri Lanka Rupee", "property": [{"code": "numeric", "valueCode": "144"}]}, {"code": "SDG", "display": "Sudanese Pound", "property": [{"code": "numeric", "valueCode": "938"}]}, {"code": "SRD", "display": "Surinam Dollar", "property": [{"code": "numeric", "valueCode": "968"}]}, {"code": "SEK", "display": "Swedish Krona", "property": [{"code": "numeric", "valueCode": "752"}]}, {"code": "CHE", "display": "WIR Euro", "property": [{"code": "numeric", "valueCode": "947"}]}, {"code": "CHW", "display": "WIR Franc", "property": [{"code": "numeric", "valueCode": "948"}]}, {"code": "SYP", "display": "Syrian Pound", "property": [{"code": "numeric", "valueCode": "760"}]}, {"code": "TWD", "display": "New Taiwan Dollar", "property": [{"code": "numeric", "valueCode": "901"}]}, {"code": "TJS", "display": "So<PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "972"}]}, {"code": "TZS", "display": "Tanzanian <PERSON>", "property": [{"code": "numeric", "valueCode": "834"}]}, {"code": "THB", "display": "Baht", "property": [{"code": "numeric", "valueCode": "764"}]}, {"code": "TOP", "display": "Pa’anga", "property": [{"code": "numeric", "valueCode": "776"}]}, {"code": "TTD", "display": "Trinidad and Tobago Dollar", "property": [{"code": "numeric", "valueCode": "780"}]}, {"code": "TND", "display": "Tunisian Dinar", "property": [{"code": "numeric", "valueCode": "788"}]}, {"code": "TRY", "display": "Turkish Lira", "property": [{"code": "numeric", "valueCode": "949"}]}, {"code": "TMT", "display": "Turkmenistan New Manat", "property": [{"code": "numeric", "valueCode": "934"}]}, {"code": "UGX", "display": "Uganda Shilling", "property": [{"code": "numeric", "valueCode": "800"}]}, {"code": "UAH", "display": "Hryvnia", "property": [{"code": "numeric", "valueCode": "980"}]}, {"code": "AED", "display": "UAE Dirham", "property": [{"code": "numeric", "valueCode": "784"}]}, {"code": "USN", "display": "US Dollar (Next day)", "property": [{"code": "numeric", "valueCode": "997"}]}, {"code": "UYU", "display": "Peso Uruguayo", "property": [{"code": "numeric", "valueCode": "858"}]}, {"code": "UYI", "display": "Uruguay Peso en Unidades Indexadas (UI)", "property": [{"code": "numeric", "valueCode": "940"}]}, {"code": "UYW", "display": "Unidad Previsional", "property": [{"code": "numeric", "valueCode": "927"}]}, {"code": "UZS", "display": "Uzbekistan Sum", "property": [{"code": "numeric", "valueCode": "860"}]}, {"code": "VUV", "display": "Vatu", "property": [{"code": "numeric", "valueCode": "548"}]}, {"code": "VES", "display": "<PERSON><PERSON><PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "928"}]}, {"code": "VED", "display": "<PERSON><PERSON><PERSON><PERSON>", "property": [{"code": "numeric", "valueCode": "926"}]}, {"code": "VND", "display": "<PERSON>", "property": [{"code": "numeric", "valueCode": "704"}]}, {"code": "YER", "display": "Yemeni R<PERSON>", "property": [{"code": "numeric", "valueCode": "886"}]}, {"code": "ZMW", "display": "Zambian <PERSON>", "property": [{"code": "numeric", "valueCode": "967"}]}, {"code": "ZWG", "display": "Zimbabwe Gold", "property": [{"code": "numeric", "valueCode": "924"}]}, {"code": "XBA", "display": "Bond Markets Unit European Composite Unit (EURCO)", "property": [{"code": "numeric", "valueCode": "955"}]}, {"code": "XBB", "display": "Bond Markets Unit European Monetary Unit (E.M.U.-6)", "property": [{"code": "numeric", "valueCode": "956"}]}, {"code": "XBC", "display": "Bond Markets Unit European Unit of Account 9 (E.U.A.-9)", "property": [{"code": "numeric", "valueCode": "957"}]}, {"code": "XBD", "display": "Bond Markets Unit European Unit of Account 17 (E.U.A.-17)", "property": [{"code": "numeric", "valueCode": "958"}]}, {"code": "XTS", "display": "Codes specifically reserved for testing purposes", "property": [{"code": "numeric", "valueCode": "963"}]}, {"code": "XXX", "display": "The codes assigned for transactions where no currency is involved", "property": [{"code": "numeric", "valueCode": "999"}]}, {"code": "XAU", "display": "Gold", "property": [{"code": "numeric", "valueCode": "959"}]}, {"code": "XPD", "display": "Palladium", "property": [{"code": "numeric", "valueCode": "964"}]}, {"code": "XPT", "display": "Platinum", "property": [{"code": "numeric", "valueCode": "962"}]}, {"code": "XAG", "display": "Silver", "property": [{"code": "numeric", "valueCode": "961"}]}], "property": [{"code": "numeric", "type": "code", "uri": "http://hl7.org/fhir/concept-properties#synonym", "description": "Equivalent numeric code"}]}}]}