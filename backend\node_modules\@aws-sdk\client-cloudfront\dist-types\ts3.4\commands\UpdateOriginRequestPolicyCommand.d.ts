import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CloudFrontClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../CloudFrontClient";
import {
  UpdateOriginRequestPolicyRequest,
  UpdateOriginRequestPolicyResult,
} from "../models/models_2";
export { __MetadataBearer };
export { $Command };
export interface UpdateOriginRequestPolicyCommandInput
  extends UpdateOriginRequestPolicyRequest {}
export interface UpdateOriginRequestPolicyCommandOutput
  extends UpdateOriginRequestPolicyResult,
    __MetadataBearer {}
declare const UpdateOriginRequestPolicyCommand_base: {
  new (
    input: UpdateOriginRequestPolicyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateOriginRequestPolicyCommandInput,
    Update<PERSON>riginRequestPolicyCommandOutput,
    CloudFrontClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateOriginRequestPolicyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateOriginRequestPolicyCommandInput,
    UpdateOriginRequestPolicyCommandOutput,
    CloudFrontClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateOriginRequestPolicyCommand extends UpdateOriginRequestPolicyCommand_base {
  protected static __types: {
    api: {
      input: UpdateOriginRequestPolicyRequest;
      output: UpdateOriginRequestPolicyResult;
    };
    sdk: {
      input: UpdateOriginRequestPolicyCommandInput;
      output: UpdateOriginRequestPolicyCommandOutput;
    };
  };
}
