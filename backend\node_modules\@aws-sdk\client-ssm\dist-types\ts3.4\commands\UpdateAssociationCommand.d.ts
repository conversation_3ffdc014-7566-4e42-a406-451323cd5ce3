import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  UpdateAssociationRequest,
  UpdateAssociationResult,
} from "../models/models_2";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SSMClientResolvedConfig,
} from "../SSMClient";
export { __MetadataBearer };
export { $Command };
export interface UpdateAssociationCommandInput
  extends UpdateAssociationRequest {}
export interface UpdateAssociationCommandOutput
  extends UpdateAssociationResult,
    __MetadataBearer {}
declare const UpdateAssociationCommand_base: {
  new (
    input: UpdateAssociationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateAssociationCommandInput,
    UpdateAssociationCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateAssociationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateAssociationCommandInput,
    UpdateAssociationCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateAssociationCommand extends UpdateAssociationCommand_base {
  protected static __types: {
    api: {
      input: UpdateAssociationRequest;
      output: UpdateAssociationResult;
    };
    sdk: {
      input: UpdateAssociationCommandInput;
      output: UpdateAssociationCommandOutput;
    };
  };
}
