/**
 * @file medplumHelper.ts
 * @description Helper functions for Medplum operations
 */
import { MedplumClient } from '@medplum/core';
/**
 * Get or create a MedplumClient instance using client credentials
 * This uses system-level authentication, not user authentication
 */
export declare function getMedplumClient(): Promise<MedplumClient>;
/**
 * Reset client instance (useful for testing or when credentials change)
 */
export declare function resetMedplumClient(): void;
/**
 * Check if client is authenticated and ready
 */
export declare function isMedplumClientReady(): boolean;
/**
 * Get client configuration info for debugging
 */
export declare function getMedplumConfig(): {
    baseUrl: string;
    hasClientId: boolean;
    hasClientSecret: boolean;
    isAuthenticated: boolean;
    environment: string;
};
/**
 * Test Medplum connection
 */
export declare function testMedplumConnection(): Promise<boolean>;
//# sourceMappingURL=medplumHelper.d.ts.map