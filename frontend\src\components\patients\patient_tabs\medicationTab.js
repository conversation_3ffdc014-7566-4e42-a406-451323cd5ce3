import React, { useState } from 'react';
import { 
  PillBottle, Plus, Edit3, Save, X, Trash2, Check, Calendar, User, 
  FileText, AlertCircle, Clock, Stethoscope
} from 'lucide-react';

const MedicationTab = ({ patientData }) => {
    console.log(patientData, "THIS IS THE PATIENT INFO");
  // Sample patient data

  // Initial medications, note this will require changes to the database schema for this page to be functional with the database.
  const [medications, setMedications] = useState([
    {
      id: 'med1',
      name: 'Aspirin',
      dose: '81',
      unit: 'mg',
      frequency: 'Once Daily',
      route: 'Oral',
      indication: 'Antiplatelet therapy',
      prescriber: 'Dr. <PERSON>',
      startDate: '2023-12-15',
      instructions: 'Take with food to reduce stomach irritation',
      status: 'Active'
    },
    {
      id: 'med2',
      name: 'Atorvastatin',
      dose: '40',
      unit: 'mg',
      frequency: 'Once Daily',
      route: 'Oral',
      indication: 'Cholesterol management',
      prescriber: 'Dr. <PERSON>',
      startDate: '2023-12-15',
      instructions: 'Take in the evening',
      status: 'Active'
    }
  ]);

  const [editingMedication, setEditingMedication] = useState(null);
  const [confirmingDelete, setConfirmingDelete] = useState(null);

  // Add new medication
  const addNewMedication = () => {
    const newMed = {
      id: `med_${Date.now()}`,
      name: '',
      dose: '',
      unit: 'mg',
      frequency: '',
      route: 'Oral',
      indication: '',
      prescriber: patientData.treatingPhysician,
      startDate: new Date().toISOString().split('T')[0],
      instructions: '',
      status: 'Active'
    };
    
    setMedications(prev => [newMed, ...prev]);
    setEditingMedication(newMed.id);
  };

  // Start delete confirmation
  const startDeleteConfirmation = (medicationId) => {
    console.log('Starting delete confirmation for:', medicationId);
    setConfirmingDelete(medicationId);
  };

  // Confirm delete
  const confirmDelete = () => {
    if (confirmingDelete) {
      console.log('Confirming delete for:', confirmingDelete);
      
      // Clear editing state if we're deleting the medication being edited
      if (editingMedication === confirmingDelete) {
        setEditingMedication(null);
      }
      
      // Remove the medication from the list
      setMedications(currentMedications => {
        const newList = currentMedications.filter(med => med.id !== confirmingDelete);
        console.log('Medications after delete:', newList);
        return newList;
      });
      
      // Clear confirmation state
      setConfirmingDelete(null);
    }
  };

  // Cancel delete
  const cancelDelete = () => {
    console.log('Cancelling delete');
    setConfirmingDelete(null);
  };

  // Update medication field
  const updateMedication = (id, field, value) => {
    setMedications(prev => 
      prev.map(med => 
        med.id === id ? { ...med, [field]: value } : med
      )
    );
  };

  // Simple editable field component
  const EditableField = ({ medication, field, label, type = "text", options = null, placeholder = "" }) => {
    const [isEditing, setIsEditing] = useState(false);
    const [tempValue, setTempValue] = useState(medication[field] || '');
    
    const startEdit = () => {
      setTempValue(medication[field] || '');
      setIsEditing(true);
    };
    
    const saveEdit = () => {
      updateMedication(medication.id, field, tempValue);
      setIsEditing(false);
    };
    
    const cancelEdit = () => {
      setTempValue(medication[field] || '');
      setIsEditing(false);
    };

    const handleKeyPress = (e) => {
      if (e.key === 'Enter') saveEdit();
      if (e.key === 'Escape') cancelEdit();
    };

    if (isEditing) {
      return (
        <div className="space-y-1">
          <label className="text-sm font-medium text-gray-700">{label}</label>
          <div className="flex space-x-2">
            {options ? (
              <select
                value={tempValue}
                onChange={(e) => setTempValue(e.target.value)}
                onKeyDown={handleKeyPress}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                autoFocus
              >
                <option value="">Select...</option>
                {options.map(opt => (
                  <option key={opt} value={opt}>{opt}</option>
                ))}
              </select>
            ) : (
              <input
                type={type}
                value={tempValue}
                onChange={(e) => setTempValue(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder={placeholder}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                autoFocus
              />
            )}
            <button
              onClick={saveEdit}
              className="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              <Check className="w-4 h-4" />
            </button>
            <button
              onClick={cancelEdit}
              className="px-3 py-2 bg-gray-400 text-white rounded-md hover:bg-gray-500"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-1">
        <label className="text-sm font-medium text-gray-700">{label}</label>
        <div 
          onClick={startEdit}
          className="px-3 py-2 border border-gray-200 rounded-md cursor-pointer hover:bg-gray-50 hover:border-gray-300 transition-colors min-h-[40px] flex items-center"
        >
          {medication[field] ? (
            <span className="text-gray-900">{medication[field]}</span>
          ) : (
            <span className="text-gray-400 italic">Click to add {label.toLowerCase()}</span>
          )}
        </div>
      </div>
    );
  };

  // Medication card component
  const MedicationCard = ({ medication, index }) => {
    const isEditing = editingMedication === medication.id;
    
    return (
      <div className={`bg-white rounded-lg border-2 p-6 transition-all ${
        isEditing ? 'border-blue-500 shadow-lg' : 'border-gray-200 hover:border-gray-300'
      }`}>
        {/* Header */}
        <div className="flex items-start justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center text-white font-bold text-lg">
              {medication.name ? medication.name.charAt(0).toUpperCase() : index + 1}
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {medication.name || 'New Medication'}
              </h3>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  medication.status === 'Active' ? 'bg-green-100 text-green-800' :
                  medication.status === 'On Hold' ? 'bg-yellow-100 text-yellow-800' :
                  medication.status === 'Discontinued' ? 'bg-red-100 text-red-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {medication.status}
                </span>
                {medication.dose && medication.unit && (
                  <span>{medication.dose}{medication.unit}</span>
                )}
                {medication.frequency && (
                  <span>{medication.frequency}</span>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={() => setEditingMedication(isEditing ? null : medication.id)}
              className={`px-4 py-2 rounded-md font-medium transition-colors ${
                isEditing 
                  ? 'bg-blue-600 text-white hover:bg-blue-700' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {isEditing ? 'Done Editing' : 'Edit'}
            </button>
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('Delete button clicked for medication:', medication.id, medication.name);
                startDeleteConfirmation(medication.id);
              }}
              className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 font-medium transition-colors"
            >
              Delete
            </button>
          </div>
        </div>

        {/* Editable Fields */}
        {isEditing && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <EditableField
                medication={medication}
                field="name"
                label="Medication Name"
                placeholder="Enter medication name"
              />
              <EditableField
                medication={medication}
                field="indication"
                label="What is this medication for?"
                placeholder="e.g., High blood pressure, Diabetes"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <EditableField
                medication={medication}
                field="dose"
                label="Dose Amount"
                placeholder="e.g., 81, 40"
              />
              <EditableField
                medication={medication}
                field="unit"
                label="Unit"
                options={['mg', 'g', 'mcg', 'mL', 'units', 'tablets', 'capsules']}
              />
              <EditableField
                medication={medication}
                field="frequency"
                label="How Often"
                options={['Once Daily', 'Twice Daily', 'Three Times Daily', 'Every 8 Hours', 'Every 12 Hours', 'As Needed', 'Weekly']}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <EditableField
                medication={medication}
                field="route"
                label="How to Take"
                options={['Oral', 'Sublingual', 'Topical', 'Injection', 'Inhaled']}
              />
              <EditableField
                medication={medication}
                field="status"
                label="Status"
                options={['Active', 'On Hold', 'Discontinued']}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <EditableField
                medication={medication}
                field="prescriber"
                label="Prescribed By"
                placeholder="Dr. Name"
              />
              <EditableField
                medication={medication}
                field="startDate"
                label="Start Date"
                type="date"
              />
            </div>

            <EditableField
              medication={medication}
              field="instructions"
              label="Special Instructions"
              placeholder="e.g., Take with food, Take at bedtime"
            />
          </div>
        )}

        {/* Read-only view */}
        {!isEditing && (
          <div className="space-y-4">
            {medication.indication && (
              <div>
                <span className="text-sm font-medium text-gray-700">For: </span>
                <span className="text-gray-900">{medication.indication}</span>
              </div>
            )}
            
            {medication.instructions && (
              <div>
                <span className="text-sm font-medium text-gray-700">Instructions: </span>
                <span className="text-gray-900">{medication.instructions}</span>
              </div>
            )}
            
            <div className="flex flex-wrap gap-4 text-sm text-gray-600">
              {medication.route && (
                <span><strong>Route:</strong> {medication.route}</span>
              )}
              {medication.prescriber && (
                <span><strong>Prescribed by:</strong> {medication.prescriber}</span>
              )}
              {medication.startDate && (
                <span><strong>Started:</strong> {new Date(medication.startDate).toLocaleDateString()}</span>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Medications</h1>
              <p className="text-gray-600 mt-1">
                Managing medications for {patientData.name} (ID: {patientData.id})
              </p>
            </div>
            
            <button 
              onClick={addNewMedication}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium flex items-center space-x-2 transition-colors"
            >
              <Plus className="w-5 h-5" />
              <span>Add New Medication</span>
            </button>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-start space-x-3">
            <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
            <div className="text-sm text-blue-800">
              <p className="font-medium">How to use:</p>
              <p>• Click "Edit" on any medication to modify it</p>
              <p>• Click "Add New Medication" to create a new entry</p>
              <p>• Click any empty field to add information</p>
              <p>• Press Enter to save, Esc to cancel</p>
            </div>
          </div>
        </div>

        {/* Medications List */}
        <div className="space-y-6">
          {medications.length === 0 ? (
            <div className="text-center py-12 bg-white rounded-lg border-2 border-dashed border-gray-300">
              <PillBottle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-700 mb-2">No medications added yet</h3>
              <p className="text-gray-500 mb-6">Click the button below to add the first medication</p>
              <button 
                onClick={addNewMedication}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
              >
                Add First Medication
              </button>
            </div>
          ) : (
            medications.map((medication, index) => (
              <MedicationCard 
                key={medication.id} 
                medication={medication} 
                index={index}
              />
            ))
          )}
        </div>

        {/* Summary */}
        {medications.length > 0 && (
          <div className="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Summary</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{medications.length}</div>
                <div className="text-sm text-gray-600">Total Medications</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {medications.filter(m => m.status === 'Active').length}
                </div>
                <div className="text-sm text-gray-600">Active</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {medications.filter(m => m.status === 'On Hold').length}
                </div>
                <div className="text-sm text-gray-600">On Hold</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {medications.filter(m => m.status === 'Discontinued').length}
                </div>
                <div className="text-sm text-gray-600">Discontinued</div>
              </div>
            </div>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        {confirmingDelete && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                  <AlertCircle className="w-6 h-6 text-red-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Delete Medication</h3>
                </div>
              </div>
              
              <p className="text-gray-600 mb-6">
                Are you sure you want to delete "{medications.find(m => m.id === confirmingDelete)?.name || 'this medication'}"?
                <br />
                <span className="font-medium text-red-600">This action cannot be undone.</span>
              </p>
              
              <div className="flex space-x-3">
                <button
                  onClick={confirmDelete}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 font-medium"
                >
                  Yes, Delete
                </button>
                <button
                  onClick={cancelDelete}
                  className="flex-1 px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 font-medium"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MedicationTab;