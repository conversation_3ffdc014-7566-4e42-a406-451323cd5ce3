{"name": "@medplum/core", "version": "4.2.0", "description": "Medplum TS/JS Library", "keywords": ["medplum", "fhir", "healthcare", "interoperability", "json", "serialization", "hl7", "standards", "clinical", "dstu2", "stu3", "r4", "normative"], "homepage": "https://www.medplum.com/", "bugs": {"url": "https://github.com/medplum/medplum/issues"}, "repository": {"type": "git", "url": "git+https://github.com/medplum/medplum.git", "directory": "packages/core"}, "license": "Apache-2.0", "author": "Medplum <<EMAIL>>", "sideEffects": false, "type": "module", "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.mjs"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.cjs"}}, "main": "dist/cjs/index.cjs", "module": "dist/esm/index.mjs", "types": "dist/esm/index.d.ts", "files": ["dist/cjs", "dist/esm"], "scripts": {"api-documenter": "api-documenter markdown --input-folder ./dist/api/ --output-folder ./dist/docs/", "api-extractor": "api-extractor run --local && cp dist/types.d.ts dist/cjs/index.d.ts && cp dist/types.d.ts dist/esm/index.d.ts", "build": "npm run clean && tsc && node esbuild.mjs && npm run api-extractor && npm run api-documenter", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint .", "test": "jest"}, "devDependencies": {"@medplum/definitions": "4.2.0", "@medplum/fhirtypes": "4.2.0", "jest-websocket-mock": "2.5.0"}, "peerDependencies": {"pdfmake": "^0.2.5"}, "peerDependenciesMeta": {"pdfmake": {"optional": true}}, "engines": {"node": ">=20.0.0"}}