import React, { useState, useEffect, useRef } from 'react';
import { Clock, Play, Square, AlertCircle, User, Activity, Heart, CheckCircle2, Calendar, Timer } from 'lucide-react';

const NewSessionPage = () => {
  const [currentStage, setCurrentStage] = useState('pre-session');
  const [sessionStartTime, setSessionStartTime] = useState(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [patientId, setPatientId] = useState(null);
  const [patientInfo, setPatientInfo] = useState(null);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);



  const [sessionData, setSessionData] = useState({
    sessionNumber: 1,
    date: new Date().toISOString().split('T')[0],
    startTime: '',
    endTime: '',
    duration: 0,
    status: 'in-progress',
    measurements: {
      preEECP: {
        timestamp: '',
        o2Liters: null,
        weight: '',
        weightUnit: 'lbs',
        bloodPressure: { systolic: '', diastolic: '' },
        pulseRate: '',
        spo2: ''
      },
      during15Min: {
        timestamp: '',
        dsPeak: '',
        dsArea: '',
        pulseRate: '',
        appliedPressure: ''
      },
      during45Min: {
        timestamp: '',
        dsPeak: '',
        dsArea: '',
        pulseRate: '',
        appliedPressure: ''
      },
      postEECP: {
        timestamp: '',
        bloodPressure: { systolic: '', diastolic: '' },
        pulseRate: '',
        spo2: '',
        skinCondition: 'Normal - No irritation',
        electrodeInducedArtefact: false
      }
    },
    personnel: {
      therapist: '',
      physician: ''
    },
    notes: '',
    adverseEvents: [],
    interruptions: []
  });

  const timerRef = useRef(null);
  const [showPrompt, setShowPrompt] = useState(false);
  const [promptStage, setPromptStage] = useState('');

  useEffect(() => {
    if (currentStage === 'active' && sessionStartTime) {
      timerRef.current = setInterval(() => {
        const now = new Date();
        const elapsed = Math.floor((now - sessionStartTime) / 1000);
        setElapsedTime(elapsed);

        if (elapsed === 900 && promptStage !== '15min') {
          setPromptStage('15min');
          setShowPrompt(true);
        } else if (elapsed === 2700 && promptStage !== '45min') {
          setPromptStage('45min');
          setShowPrompt(true);
        }
      }, 1000);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [currentStage, sessionStartTime, promptStage]);

  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleInputChange = (section, field, value) => {
    setSessionData(prev => ({
      ...prev,
      measurements: {
        ...prev.measurements,
        [section]: {
          ...prev.measurements[section],
          [field]: value
        }
      }
    }));
  };

  const handleNestedInputChange = (section, parentField, childField, value) => {
    setSessionData(prev => ({
      ...prev,
      measurements: {
        ...prev.measurements,
        [section]: {
          ...prev.measurements[section],
          [parentField]: {
            ...prev.measurements[section][parentField],
            [childField]: value
          }
        }
      }
    }));
  };

  const handlePersonnelChange = (field, value) => {
    setSessionData(prev => ({
      ...prev,
      personnel: {
        ...prev.personnel,
        [field]: value
      }
    }));
  };

  const startSession = () => {
    const now = new Date();
    setSessionStartTime(now);
    setSessionData(prev => ({
      ...prev,
      startTime: now.toTimeString().split(' ')[0],
      measurements: {
        ...prev.measurements,
        preEECP: {
          ...prev.measurements.preEECP,
          timestamp: now.toISOString()
        }
      }
    }));
    setCurrentStage('active');
  };

  const stopSession = () => {
    const now = new Date();
    const duration = Math.floor((now - sessionStartTime) / 60000);
    
    setSessionData(prev => ({
      ...prev,
      endTime: now.toTimeString().split(' ')[0],
      duration: duration,
      status: 'completed'
    }));
    setCurrentStage('post-session');
    
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
  };

  const handlePromptData = () => {
    const now = new Date();
    const section = promptStage === '15min' ? 'during15Min' : 'during45Min';
    
    setSessionData(prev => ({
      ...prev,
      measurements: {
        ...prev.measurements,
        [section]: {
          ...prev.measurements[section],
          timestamp: now.toISOString()
        }
      }
    }));
    
    setShowPrompt(false);
    setCurrentStage(`during-${promptStage === '15min' ? '15' : '45'}`);
  };

  const completeSession = async () => {
    const now = new Date();
    setSessionData(prev => ({
      ...prev,
      measurements: {
        ...prev.measurements,
        postEECP: {
          ...prev.measurements.postEECP,
          timestamp: now.toISOString()
        }
      }
    }));

    console.log(sessionData, "THIS IS SESSION DATA HERE")

    // Send the session data to the server

    // Assuming no sessions exist, create an empty array
    if (!Array.isArray(patientInfo.sessions)) {
      patientInfo.sessions = []; // create an empty array if it didn’t exist
    }
    patientInfo.sessions.push(sessionData);

    const response = await fetch(`http://localhost:4000/patients/${patientId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(patientInfo)
    });

    setCurrentStage('complete');
  };

  // Retrieving the patient from the database. 
  const retrievePatient = async (patientId) => {
    fetch(`http://localhost:4000/patients/${patientId}`).then(res => {
      if (!res.ok) {
        throw new Error('Failed to fetch patient');
      }
      return res.json();
    }).then(data => {
      console.log(data);
      setPatientInfo(data);
      setError(null);
      setSuccess('Patient retrieved successfully');
    }).catch(
      (error) => {
        setError(error.message);
        setSuccess(null);
      }
    );
  }

  const handleIdSubmit = (e) => {
    e.preventDefault();
    retrievePatient(patientId);
  }
  
  const renderPreSessionForm = () => (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-3xl font-light text-gray-900 mb-3">Pre-session setup</h1>
        <p className="text-gray-500">Enter patient measurements to begin EECP treatment</p>
      </div>

      <div className="flex flex-col gap-4">    
        <form onSubmit={handleIdSubmit} className="flex flex-col gap-4">
        <label htmlFor="patientId" className="font-medium">
          Patient ID
        </label>
        <input
          id="patientId"
          type="text"
          value={patientId}
          onChange={(e) => setPatientId(e.target.value)}
          placeholder="e.g. 123 or SYN-964441"
          className="border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <button
          type="submit"
          className="bg-black text-white px-4 py-2 rounded-md hover:bg-gray-800"
        >
          Validate Patient
        </button>
      </form>

      <div className="flex flex-col gap-4 mb-6"> 
      {error && (
        <div className="mt-4 bg-red-50 border-l-4 border-red-400 p-3 text-red-700">
          {error}
        </div>
      )}
        {success && (
          <div className="mt-4 bg-green-50 border-l-4 border-green-400 p-3 text-green-700">
            {success}
          </div>
      )}
      </div>
      </div>
      

      
      {/* Vital Signs */}
      <div className="mb-8">
        <h2 className="text-lg font-medium text-gray-900 mb-6">Vital signs</h2>
        <div className="space-y-6">
          {/* Weight Row */}
          <div className="flex space-x-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">Weight</label>
              <input
                type="text"
                inputMode="decimal"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                value={sessionData.measurements.preEECP.weight}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value === '' || /^\d*\.?\d*$/.test(value)) {
                    handleInputChange('preEECP', 'weight', value);
                  }
                }}
                placeholder="185.5"
              />
            </div>
            <div className="w-24">
              <label className="block text-sm font-medium text-gray-700 mb-2">Unit</label>
              <select
                className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent bg-white"
                value={sessionData.measurements.preEECP.weightUnit}
                onChange={(e) => handleInputChange('preEECP', 'weightUnit', e.target.value)}
              >
                <option value="lbs">lbs</option>
                <option value="kg">kg</option>
              </select>
            </div>
          </div>
          
          {/* Blood Pressure Row */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Blood pressure</label>
            <div className="flex space-x-4">
              <div className="flex-1">
                <input
                  type="text"
                  inputMode="numeric"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                  value={sessionData.measurements.preEECP.bloodPressure.systolic}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (value === '' || /^\d+$/.test(value)) {
                      handleNestedInputChange('preEECP', 'bloodPressure', 'systolic', value);
                    }
                  }}
                  placeholder="120"
                />
                <div className="text-xs text-gray-500 mt-1">Systolic</div>
              </div>
              <div className="flex items-center text-gray-400 pb-6">/</div>
              <div className="flex-1">
                <input
                  type="text"
                  inputMode="numeric"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                  value={sessionData.measurements.preEECP.bloodPressure.diastolic}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (value === '' || /^\d+$/.test(value)) {
                      handleNestedInputChange('preEECP', 'bloodPressure', 'diastolic', value);
                    }
                  }}
                  placeholder="80"
                />
                <div className="text-xs text-gray-500 mt-1">Diastolic</div>
              </div>
            </div>
          </div>
          
          {/* Pulse and SpO2 Row */}
          <div className="flex space-x-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">Pulse rate</label>
              <input
                type="text"
                inputMode="numeric"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                value={sessionData.measurements.preEECP.pulseRate}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value === '' || /^\d+$/.test(value)) {
                    handleInputChange('preEECP', 'pulseRate', value);
                  }
                }}
                placeholder="70"
              />
              <div className="text-xs text-gray-500 mt-1">BPM</div>
            </div>
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">SpO2</label>
              <input
                type="text"
                inputMode="numeric"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                value={sessionData.measurements.preEECP.spo2}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value === '' || /^\d+$/.test(value)) {
                    handleInputChange('preEECP', 'spo2', value);
                  }
                }}
                placeholder="98"
              />
              <div className="text-xs text-gray-500 mt-1">%</div>
            </div>
          </div>
          
          {/* O2 (Optional) */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">O2 liters <span className="text-gray-400">(optional)</span></label>
            <input
              type="text"
              inputMode="decimal"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
              value={sessionData.measurements.preEECP.o2Liters || ''}
              onChange={(e) => {
                const value = e.target.value;
                if (value === '' || /^\d*\.?\d*$/.test(value)) {
                  handleInputChange('preEECP', 'o2Liters', value ? parseFloat(value) : null);
                }
              }}
              placeholder="2.0"
            />
            <div className="text-xs text-gray-500 mt-1">L/min</div>
          </div>
        </div>
      </div>
      
      {/* Personnel */}
      <div className="mb-12">
        <h2 className="text-lg font-medium text-gray-900 mb-6">Personnel</h2>
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Therapist</label>
            <input
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
              value={sessionData.personnel.therapist}
              onChange={(e) => handlePersonnelChange('therapist', e.target.value)}
              placeholder="Emma Thompson, RN"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Physician</label>
            <input
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
              value={sessionData.personnel.physician}
              onChange={(e) => handlePersonnelChange('physician', e.target.value)}
              placeholder="Dr. Michael Chen"
            />
          </div>
        </div>
      </div>
      
      {/* Start Button */}
      <button
        onClick={startSession}
        className="w-full bg-black hover:bg-gray-800 text-white font-medium py-4 px-6 rounded-lg transition-colors"
      >
        Start session
      </button>
    </div>
  );

  const renderActiveSession = () => (
    <div className="max-w-lg mx-auto text-center">
      <div className="mb-8">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <div className="w-8 h-8 bg-green-500 rounded-full animate-pulse"></div>
        </div>
        <h1 className="text-2xl font-light text-gray-900 mb-2">Session active</h1>
        <p className="text-gray-500">EECP treatment in progress</p>
      </div>
      
      <div className="bg-gray-50 rounded-2xl p-8 mb-8">
        <div className="text-5xl font-light text-gray-900 mb-2 font-mono">
          {formatTime(elapsedTime)}
        </div>
        <div className="text-gray-500">Duration</div>
      </div>
      
      <div className="grid grid-cols-2 gap-4 mb-8 text-sm">
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="font-medium text-gray-900">15:00</div>
          <div className="text-gray-500">First checkpoint</div>
        </div>
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="font-medium text-gray-900">45:00</div>
          <div className="text-gray-500">Second checkpoint</div>
        </div>
      </div>
      
      <button
        onClick={stopSession}
        className="w-full bg-red-500 hover:bg-red-600 text-white font-medium py-4 px-6 rounded-lg transition-colors"
      >
        Stop session
      </button>
    </div>
  );

  const renderDuringForm = (stage) => {
    const section = stage === '15' ? 'during15Min' : 'during45Min';
    const timeLabel = stage === '15' ? '15-minute' : '45-minute';
    
    return (
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-3xl font-light text-gray-900 mb-3">{timeLabel} checkpoint</h1>
          <p className="text-gray-500">Record measurements during treatment</p>
        </div>
        
        <div className="space-y-6 mb-12">
          <div className="flex space-x-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">DS Peak</label>
              <input
                type="text"
                inputMode="decimal"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                value={sessionData.measurements[section].dsPeak}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value === '' || /^\d*\.?\d*$/.test(value)) {
                    handleInputChange(section, 'dsPeak', value);
                  }
                }}
                placeholder="1.50"
              />
            </div>
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">DS Area</label>
              <input
                type="text"
                inputMode="decimal"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                value={sessionData.measurements[section].dsArea}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value === '' || /^\d*\.?\d*$/.test(value)) {
                    handleInputChange(section, 'dsArea', value);
                  }
                }}
                placeholder="0.85"
              />
            </div>
          </div>
          
          <div className="flex space-x-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">Pulse rate</label>
              <input
                type="text"
                inputMode="numeric"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                value={sessionData.measurements[section].pulseRate}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value === '' || /^\d+$/.test(value)) {
                    handleInputChange(section, 'pulseRate', value);
                  }
                }}
                placeholder="65"
              />
              <div className="text-xs text-gray-500 mt-1">BPM</div>
            </div>
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">Applied pressure</label>
              <input
                type="text"
                inputMode="numeric"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                value={sessionData.measurements[section].appliedPressure}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value === '' || /^\d+$/.test(value)) {
                    handleInputChange(section, 'appliedPressure', value);
                  }
                }}
                placeholder="245"
              />
              <div className="text-xs text-gray-500 mt-1">mmHg</div>
            </div>
          </div>
        </div>
        
        <button
          onClick={() => setCurrentStage('active')}
          className="w-full bg-black hover:bg-gray-800 text-white font-medium py-4 px-6 rounded-lg transition-colors"
        >
          Continue session
        </button>
      </div>
    );
  };

  const renderPostSessionForm = () => (
    <div className="max-w-2xl mx-auto">
      <div className="text-center mb-12">
        <h1 className="text-3xl font-light text-gray-900 mb-3">Post-session assessment</h1>
        <p className="text-gray-500">Complete final measurements and notes</p>
      </div>
      
      <div className="space-y-8 mb-12">
        {/* Blood Pressure */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Blood pressure</label>
          <div className="flex space-x-4">
            <div className="flex-1">
              <input
                type="text"
                inputMode="numeric"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                value={sessionData.measurements.postEECP.bloodPressure.systolic}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value === '' || /^\d+$/.test(value)) {
                    handleNestedInputChange('postEECP', 'bloodPressure', 'systolic', value);
                  }
                }}
                placeholder="115"
              />
              <div className="text-xs text-gray-500 mt-1">Systolic</div>
            </div>
            <div className="flex items-center text-gray-400 pb-6">/</div>
            <div className="flex-1">
              <input
                type="text"
                inputMode="numeric"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                value={sessionData.measurements.postEECP.bloodPressure.diastolic}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value === '' || /^\d+$/.test(value)) {
                    handleNestedInputChange('postEECP', 'bloodPressure', 'diastolic', value);
                  }
                }}
                placeholder="72"
              />
              <div className="text-xs text-gray-500 mt-1">Diastolic</div>
            </div>
          </div>
        </div>
        
        {/* Pulse and SpO2 */}
        <div className="flex space-x-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">Pulse rate</label>
            <input
              type="text"
              inputMode="numeric"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
              value={sessionData.measurements.postEECP.pulseRate}
              onChange={(e) => {
                const value = e.target.value;
                if (value === '' || /^\d+$/.test(value)) {
                  handleInputChange('postEECP', 'pulseRate', value);
                }
              }}
              placeholder="60"
            />
            <div className="text-xs text-gray-500 mt-1">BPM</div>
          </div>
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">SpO2</label>
            <input
              type="text"
              inputMode="numeric"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
              value={sessionData.measurements.postEECP.spo2}
              onChange={(e) => {
                const value = e.target.value;
                if (value === '' || /^\d+$/.test(value)) {
                  handleInputChange('postEECP', 'spo2', value);
                }
              }}
              placeholder="99"
            />
            <div className="text-xs text-gray-500 mt-1">%</div>
          </div>
        </div>
        
        {/* Skin Condition */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Skin condition</label>
          <select
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent bg-white"
            value={sessionData.measurements.postEECP.skinCondition}
            onChange={(e) => handleInputChange('postEECP', 'skinCondition', e.target.value)}
          >
            <option value="Normal - No irritation">Normal - No irritation</option>
            <option value="Mild irritation">Mild irritation</option>
            <option value="Moderate irritation">Moderate irritation</option>
            <option value="Severe irritation">Severe irritation</option>
          </select>
        </div>
        
        {/* Electrode Artifact */}
        <div className="flex items-center space-x-3">
          <input
            type="checkbox"
            id="electrodeArtefact"
            checked={sessionData.measurements.postEECP.electrodeInducedArtefact}
            onChange={(e) => handleInputChange('postEECP', 'electrodeInducedArtefact', e.target.checked)}
            className="w-4 h-4 text-black focus:ring-black border-gray-300 rounded"
          />
          <label htmlFor="electrodeArtefact" className="text-sm font-medium text-gray-700">
            Electrode induced artifact present
          </label>
        </div>
        
        {/* Notes */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Session notes</label>
          <textarea
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent resize-none"
            rows="4"
            value={sessionData.notes}
            onChange={(e) => setSessionData(prev => ({ ...prev, notes: e.target.value }))}
            placeholder="Enter any observations, patient feedback, or important notes..."
          />
        </div>
      </div>
      
      <button
        onClick={completeSession}
        className="w-full bg-black hover:bg-gray-800 text-white font-medium py-4 px-6 rounded-lg transition-colors"
      >
        Complete session
      </button>
    </div>
  );

  const renderComplete = () => (
    <div className="max-w-2xl mx-auto">
      <div className="text-center mb-12">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <CheckCircle2 className="w-8 h-8 text-green-600" />
        </div>
        <h1 className="text-3xl font-light text-gray-900 mb-3">Session complete</h1>
        <p className="text-gray-500">All data has been recorded successfully</p>
      </div>
      
      <div className="bg-gray-50 rounded-2xl p-8 mb-8">
        <div className="grid grid-cols-2 gap-8 text-center">
          <div>
            <div className="text-2xl font-light text-gray-900">{sessionData.duration} min</div>
            <div className="text-sm text-gray-500">Duration</div>
          </div>
          <div>
            <div className="text-2xl font-light text-gray-900">#{sessionData.sessionNumber}</div>
            <div className="text-sm text-gray-500">Session</div>
          </div>
        </div>
        <div className="border-t border-gray-200 mt-6 pt-6 text-sm text-gray-600">
          <div className="flex justify-between mb-2">
            <span>Therapist</span>
            <span>{sessionData.personnel.therapist}</span>
          </div>
          <div className="flex justify-between">
            <span>Physician</span>
            <span>{sessionData.personnel.physician}</span>
          </div>
        </div>
      </div>
      
      <div className="bg-gray-50 rounded-lg p-6 mb-8">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-medium text-gray-900">JSON output</h3>
          <button
            onClick={() => navigator.clipboard.writeText(JSON.stringify(sessionData, null, 2))}
            className="text-sm text-gray-500 hover:text-gray-700"
          >
            Copy
          </button>
        </div>
        <pre className="text-xs text-gray-600 overflow-auto max-h-64 font-mono">
          {JSON.stringify(sessionData, null, 2)}
        </pre>
      </div>
      
      <button
        onClick={() => {
          setCurrentStage('pre-session');
          setElapsedTime(0);
          setSessionStartTime(null);
          setShowPrompt(false);
          setPromptStage('');
          setSessionData({
            ...sessionData,
            sessionNumber: sessionData.sessionNumber + 1,
            notes: '',
            measurements: {
              preEECP: {
                timestamp: '', o2Liters: null, weight: '', weightUnit: 'lbs',
                bloodPressure: { systolic: '', diastolic: '' }, pulseRate: '', spo2: ''
              },
              during15Min: { timestamp: '', dsPeak: '', dsArea: '', pulseRate: '', appliedPressure: '' },
              during45Min: { timestamp: '', dsPeak: '', dsArea: '', pulseRate: '', appliedPressure: '' },
              postEECP: {
                timestamp: '', bloodPressure: { systolic: '', diastolic: '' },
                pulseRate: '', spo2: '', skinCondition: 'Normal - No irritation', electrodeInducedArtefact: false
              }
            }
          });
        }}
        className="w-full bg-black hover:bg-gray-800 text-white font-medium py-4 px-6 rounded-lg transition-colors"
      >
        Start new session
      </button>
    </div>
  );

  return (
    <div className="min-h-screen bg-white">
      {/* <div className="border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between max-w-4xl mx-auto">
          <div className="flex items-center space-x-4">
            <h1 className="text-lg font-medium text-gray-900">EECP Session</h1>
            <div className="text-sm text-gray-500">#{sessionData.sessionNumber}</div>
          </div>
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <span>{new Date(sessionData.date).toLocaleDateString()}</span>
            <div className={`px-2 py-1 rounded-full text-xs font-medium ${
              currentStage === 'active' ? 'bg-green-100 text-green-800' :
              currentStage === 'complete' ? 'bg-blue-100 text-blue-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {currentStage === 'pre-session' ? 'Setup' :
               currentStage === 'active' ? 'Active' :
               currentStage.startsWith('during') ? `${currentStage.split('-')[1]}min` :
               currentStage === 'post-session' ? 'Assessment' : 'Complete'}
            </div>
          </div>
        </div>
      </div> */}

      {/* Main Content */}
      <div className="px-6 py-12">
        {/* Time Prompt Modal */}
        {showPrompt && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-8 max-w-sm w-full mx-4 text-center">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="w-6 h-6 text-orange-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Time checkpoint</h3>
              <p className="text-gray-500 mb-6">
                Collect {promptStage === '15min' ? '15-minute' : '45-minute'} measurements now
              </p>
              <button
                onClick={handlePromptData}
                className="w-full bg-black hover:bg-gray-800 text-white font-medium py-3 px-6 rounded-lg transition-colors"
              >
                Continue
              </button>
            </div>
          </div>
        )}

        {/* Stage Content */}
        {currentStage === 'pre-session' && renderPreSessionForm()}
        {currentStage === 'active' && renderActiveSession()}
        {currentStage === 'during-15' && renderDuringForm('15')}
        {currentStage === 'during-45' && renderDuringForm('45')}
        {currentStage === 'post-session' && renderPostSessionForm()}
        {currentStage === 'complete' && renderComplete()}
      </div>
    </div>
  );
};

export default NewSessionPage;
