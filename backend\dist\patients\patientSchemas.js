"use strict";
/**
 * @file patientSchemas.ts
 * @description This file contains the JSON schemas for patient-related data.
 *
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.validatePatientCreate = validatePatientCreate;
/**
 * Validation helper: Check if required fields are present
 */
function validatePatientCreate(patient) {
    const errors = [];
    if (!patient.family || patient.family.trim().length === 0) {
        errors.push('family name is required');
    }
    if (!patient.given || !Array.isArray(patient.given) || patient.given.length === 0) {
        errors.push('given name(s) are required');
    }
    else if (patient.given.some(name => !name || name.trim().length === 0)) {
        errors.push('given names cannot be empty');
    }
    if (patient.birthDate && !isValidDate(patient.birthDate)) {
        errors.push('birthDate must be in YYYY-MM-DD format');
    }
    if (patient.gender && !['male', 'female', 'other', 'unknown'].includes(patient.gender)) {
        errors.push('gender must be one of: male, female, other, unknown');
    }
    return errors;
}
/**
 * Helper function to validate date format
 */
function isValidDate(dateString) {
    const regex = /^\d{4}-\d{2}-\d{2}$/;
    if (!regex.test(dateString))
        return false;
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime()) && date.toISOString().startsWith(dateString);
}
//# sourceMappingURL=patientSchemas.js.map