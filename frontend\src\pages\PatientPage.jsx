// import React, { useState, useEffect } from 'react';
// import { useParams } from 'react-router-dom';
// import PatientHeader from '../components/patients/PatientHeader';
// import MedicalHistoryTab from '../components/patients/MedicalHistoryTab';
// import { MOCK_PATIENTS } from '../data/mockData';

// const PatientPage = () => {
//   const { id } = useParams();
//   const [patient, setPatient] = useState(null);
//   const [activeTab, setActiveTab] = useState('medical-history');
//   const [medicalHistoryRef, setMedicalHistoryRef] = useState(null);

//   useEffect(() => {
//     // In a real app, this would be an API call
//     const foundPatient = MOCK_PATIENTS.find(p => p.id === id);
//     setPatient(foundPatient);
//   }, [id]);

//   const handlePrint = () => {
//     if (activeTab === 'medical-history' && medicalHistoryRef?.generatePDF) {
//       medicalHistoryRef.generatePDF();
//     }
//   };

//   if (!patient) return null;

//   return (
//     <div className="container mx-auto px-4 py-6">
//       <PatientHeader patient={patient} onPrintClick={handlePrint} />
      
//       <div className="mb-6 border-b border-gray-200">
//         <nav className="-mb-px flex space-x-8">
//           <button
//             onClick={() => setActiveTab('overview')}
//             className={`pb-4 px-1 border-b-2 font-medium text-sm ${
//               activeTab === 'overview'
//                 ? 'border-blue-500 text-blue-600'
//                 : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
//             }`}
//           >
//             Overview
//           </button>
//           <button
//             onClick={() => setActiveTab('medical-history')}
//             className={`pb-4 px-1 border-b-2 font-medium text-sm ${
//               activeTab === 'medical-history'
//                 ? 'border-blue-500 text-blue-600'
//                 : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
//             }`}
//           >
//             Medical History
//           </button>
//           <button
//             onClick={() => setActiveTab('sessions')}
//             className={`pb-4 px-1 border-b-2 font-medium text-sm ${
//               activeTab === 'sessions'
//                 ? 'border-blue-500 text-blue-600'
//                 : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
//             }`}
//           >
//             Sessions & Parameters
//           </button>
//           <button
//             onClick={() => setActiveTab('vitals')}
//             className={`pb-4 px-1 border-b-2 font-medium text-sm ${
//               activeTab === 'vitals'
//                 ? 'border-blue-500 text-blue-600'
//                 : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
//             }`}
//           >
//             Vitals & Trends
//           </button>
//           <button
//             onClick={() => setActiveTab('outcomes')}
//             className={`pb-4 px-1 border-b-2 font-medium text-sm ${
//               activeTab === 'outcomes'
//                 ? 'border-blue-500 text-blue-600'
//                 : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
//             }`}
//           >
//             Clinical Outcomes
//           </button>
//         </nav>
//       </div>

//       <div className="py-4">
//         {activeTab === 'medical-history' && (
//           <MedicalHistoryTab 
//             patient={patient} 
//             ref={ref => setMedicalHistoryRef(ref)}
//           />
//         )}
//         {/* Other tabs will be implemented similarly */}
//       </div>
//     </div>
//   );
// };

// export default PatientPage; 