import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { FaUserInjured, FaMicrophone, FaMicrophoneSlash, FaSave, FaHeartbeat, FaPlay, FaPause, FaExclamationTriangle, FaSpinner, FaCamera } from 'react-icons/fa';
import EECPWaveformDisplay from './EECPWaveformDisplay';

// Mock patient data for the demo
const MOCK_PATIENTS = [
  { 
    id: 'p1', 
    name: '<PERSON>', 
    age: 67, 
    gender: 'Male',
    diagnosis: 'Coronary Artery Disease',
    lvef: 28,
    bnp: 450,
    status: 'Active',
    lastVisit: '2025-04-10',
    nextAppointment: '2025-04-16T10:00:00'
  },
  { 
    id: 'p2', 
    name: '<PERSON>', 
    age: 72, 
    gender: 'Female',
    diagnosis: 'Congestive Heart Failure',
    lvef: 32,
    bnp: 380,
    status: 'Active',
    lastVisit: '2025-04-12',
    nextAppointment: '2025-04-16T11:30:00'
  },
  { 
    id: 'p3', 
    name: '<PERSON>', 
    age: 58, 
    gender: 'Male',
    diagnosis: 'Angina',
    lvef: 45,
    bnp: 120,
    status: 'Active',
    lastVisit: '2025-04-08',
    nextAppointment: '2025-04-16T14:00:00'
  }
];

// Mock session data for the demo
const MOCK_SESSIONS = [
  {
    id: 'sess1',
    patientId: 'p1',
    sessionNumber: 3,
    totalSessions: 35,
    date: '2025-04-16T10:00:00',
    status: 'Scheduled',
    room: 'Room 101'
  },
  {
    id: 'sess2',
    patientId: 'p2',
    sessionNumber: 12,
    totalSessions: 35,
    date: '2025-04-16T11:30:00',
    status: 'Scheduled',
    room: 'Room 102'
  },
  {
    id: 'sess3',
    patientId: 'p3',
    sessionNumber: 7,
    totalSessions: 35,
    date: '2025-04-16T14:00:00',
    status: 'Scheduled',
    room: 'Room 101'
  }
];

const SessionExecutionPanel = () => {
  const { sessionId } = useParams();
  const navigate = useNavigate();
  const { medplum } = useAuth();
  
  const [loading, setLoading] = useState(true);
  const [session, setSession] = useState(null);
  const [patient, setPatient] = useState(null);
  const [sessionStatus, setSessionStatus] = useState('ready'); // ready, running, paused, completed
  const [showAIScribeModal, setShowAIScribeModal] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [soapNote, setSoapNote] = useState('');
  const [structuredNote, setStructuredNote] = useState({
    subjective: '',
    objective: '',
    assessment: '',
    plan: ''
  });
  // Audio state for processing
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const timerRef = useRef(null);
  const [vitals, setVitals] = useState({
    preHeartRate: '',
    preBloodPressure: '',
    preOxygenSaturation: '',
    postHeartRate: '',
    postBloodPressure: '',
    postOxygenSaturation: '',
  });
  
  const [symptoms, setSymptoms] = useState({
    chestPain: false,
    shortnessOfBreath: false,
    fatigue: false,
    dizziness: false,
    edema: false,
    palpitations: false,
    other: ''
  });

  useEffect(() => {
    // In a real implementation, we would fetch data from Medplum
    // For the demo, we'll use mock data
    const fetchData = async () => {
      try {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Find the session in our mock data
        const foundSession = MOCK_SESSIONS.find(s => s.id === sessionId);
        
        if (foundSession) {
          setSession(foundSession);
          
          // Find the patient associated with this session
          const foundPatient = MOCK_PATIENTS.find(p => p.id === foundSession.patientId);
          if (foundPatient) {
            setPatient(foundPatient);
          }
        }
      } catch (error) {
        console.error('Error fetching session data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [sessionId, medplum]);

  const handleVitalChange = (e) => {
    const { name, value } = e.target;
    setVitals({
      ...vitals,
      [name]: value
    });
  };

  const handleSymptomChange = (e) => {
    const { name, checked, value, type } = e.target;
    
    if (type === 'checkbox') {
      setSymptoms({
        ...symptoms,
        [name]: checked
      });
    } else {
      setSymptoms({
        ...symptoms,
        [name]: value
      });
    }
  };

  const startSession = () => {
    setSessionStatus('running');
  };

  const pauseSession = () => {
    setSessionStatus('paused');
  };

  // Start recording for AI Scribe
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      audioChunksRef.current = [];
      
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };
      
      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        await processAudio(audioBlob);
      };
      
      // Start recording
      mediaRecorder.start(1000);
      setIsRecording(true);
      
      // Start timer
      setRecordingTime(0);
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
      
    } catch (error) {
      console.error('Error starting recording:', error);
      alert('Could not access microphone. Please check permissions.');
    }
  };
  
  // Stop recording
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
      setIsRecording(false);
      
      // Stop timer
      clearInterval(timerRef.current);
    }
  };
  
  // Simulation function for demo purposes
  const simulateWhisperResponse = async () => {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Generate dynamic response based on the actual session and patient data
    // Use the real vitals data that has been entered
    const actualBP = vitals.preBloodPressure || '120/80';
    const actualHR = vitals.preHeartRate || '72';
    const actualO2 = vitals.preOxygenSaturation || '98';
    
    // Generate random values for dynamic content
    const painScaleStart = 5 + Math.floor(Math.random() * 5); // 5-9
    const painScaleEnd = Math.max(1, painScaleStart - (2 + Math.floor(Math.random() * 3))); // Reduction by 2-4 points
    const cuffPressure = 220 + Math.floor(Math.random() * 60); // 220-280 mmHg
    const sessionDuration = 55 + Math.floor(Math.random() * 10); // 55-65 minutes
    
    // Generate random symptoms improvement
    const symptomsList = [];
    if (session && session.sessionNumber > 5) {
      symptomsList.push("improved exercise tolerance");
    }
    if (session && session.sessionNumber > 10) {
      symptomsList.push("reduced frequency of angina episodes");
    }
    if (session && session.sessionNumber > 15) {
      symptomsList.push("improved quality of life");
    }
    
    // Generate random side effects (rare)
    const sideEffects = [];
    if (Math.random() > 0.9) {
      sideEffects.push("mild skin irritation at cuff sites");
    }
    if (Math.random() > 0.95) {
      sideEffects.push("slight leg discomfort during inflation");
    }
    
    // Generate dynamic transcription text
    let transcriptionText = `Patient ${patient ? patient.name : 'John Doe'} is here for EECP session number ${session ? session.sessionNumber : 1} of ${session ? session.totalSessions : 35}. `;
    
    // Add subjective information
    transcriptionText += `Patient reports ${symptomsList.length > 0 ? symptomsList.join(" and ") + " since starting therapy. " : ""}`;
    transcriptionText += `Chest pain has decreased from a ${painScaleStart} to a ${painScaleEnd} on the pain scale. `;
    
    if (Math.random() > 0.5) {
      transcriptionText += "No shortness of breath during normal activities. ";
    } else {
      transcriptionText += "Mild shortness of breath only with significant exertion. ";
    }
    
    // Add objective information
    transcriptionText += `Vitals are stable with blood pressure at ${actualBP} and heart rate at ${actualHR} bpm. `;
    transcriptionText += `Oxygen saturation is ${actualO2}%. `;
    transcriptionText += `EECP session was well tolerated ${sideEffects.length > 0 ? "with " + sideEffects.join(" and ") : "with no complications"}. `;
    transcriptionText += `Patient completed full ${sessionDuration}-minute treatment. `;
    transcriptionText += `Cuff pressure was set to ${cuffPressure} mmHg. `;
    transcriptionText += `ECG showed normal sinus rhythm throughout. `;
    
    // Add plan
    if (session && session.sessionNumber < session.totalSessions - 5) {
      transcriptionText += `Plan is to continue with daily sessions as scheduled. `;
    } else {
      transcriptionText += `Plan is to complete the remaining ${session ? session.totalSessions - session.sessionNumber : 30} sessions and then reassess. `;
    }
    
    transcriptionText += `Recommended patient continue with prescribed medications and low-sodium diet.`;
    
    // Set the transcription text
    setSoapNote(transcriptionText);
    
    // Create structured SOAP note from the transcription
    const structuredSOAP = {
      subjective: `Patient reports ${symptomsList.length > 0 ? symptomsList.join(" and ") + ". " : ""}Chest pain has decreased from a ${painScaleStart} to a ${painScaleEnd} on the pain scale. ${Math.random() > 0.5 ? "No shortness of breath during normal activities." : "Mild shortness of breath only with significant exertion."}`,
      
      objective: `Vitals: BP ${actualBP}, HR ${actualHR} bpm, O2 sat ${actualO2}%. EECP session was well tolerated ${sideEffects.length > 0 ? "with " + sideEffects.join(" and ") : "with no complications"}. Patient completed full ${sessionDuration}-minute treatment. Cuff pressure was set to ${cuffPressure} mmHg. ECG showed normal sinus rhythm throughout.`,
      
      assessment: `Patient showing ${session && session.sessionNumber > 10 ? "significant" : "gradual"} improvement in symptoms with EECP therapy. ${painScaleEnd < 3 ? "Excellent" : "Good"} response to treatment with reduced angina and ${symptomsList.length > 0 ? symptomsList.join(" and ") : "stable cardiovascular status"}.`,
      
      plan: `${session && session.sessionNumber < session.totalSessions - 5 ? "Continue with daily sessions as scheduled." : `Complete the remaining ${session ? session.totalSessions - session.sessionNumber : 30} sessions and then reassess.`} Recommended patient continue with prescribed medications and low-sodium diet. ${Math.random() > 0.7 ? "Consider follow-up echocardiogram after completion of therapy." : ""}`
    };
    
    // Set the structured note
    setStructuredNote(structuredSOAP);
    
    // Also update the vitals fields if they're empty
    if (!vitals.postHeartRate) {
      const postHR = Math.floor(parseFloat(actualHR) * (1 + (Math.random() * 0.1 - 0.05))); // +/- 5%
      setVitals(prev => ({
        ...prev,
        postHeartRate: postHR.toString()
      }));
    }
    
    if (!vitals.postBloodPressure) {
      // Parse the BP like "120/80"
      const bpParts = actualBP.split('/');
      if (bpParts.length === 2) {
        const systolic = parseInt(bpParts[0], 10);
        const diastolic = parseInt(bpParts[1], 10);
        
        if (!isNaN(systolic) && !isNaN(diastolic)) {
          // Adjust slightly for post-session
          const postSystolic = Math.floor(systolic * (1 + (Math.random() * 0.1 - 0.05))); // +/- 5%
          const postDiastolic = Math.floor(diastolic * (1 + (Math.random() * 0.1 - 0.05))); // +/- 5%
          
          setVitals(prev => ({
            ...prev,
            postBloodPressure: `${postSystolic}/${postDiastolic}`
          }));
        }
      }
    }
    
    // Log the updated state
    console.log('Updated SOAP note:', transcriptionText);
    console.log('Updated structured note:', structuredSOAP);
    console.log('Updated vitals:', vitals);
  };
  
  // Process audio with Whisper and update fields
  const processAudio = async (audioBlob) => {
    setIsProcessing(true);
    
    try {
      // Create a FormData object to send the audio file
      const formData = new FormData();
      formData.append('file', audioBlob, 'recording.wav');
      formData.append('model', 'whisper-1');
      
      // REAL IMPLEMENTATION CODE
      // Using the actual OpenAI Whisper API
      // Use the API key directly from the .env file
      const apiKey = process.env.REACT_APP_OPENAI_API_KEY;      
      try {
        console.log('Starting API call to Whisper...');
        const response = await fetch('https://api.openai.com/v1/audio/transcriptions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${apiKey}`
          },
          body: formData
        });
        
        if (!response.ok) {
          console.error('Whisper API error:', response.status);
          const errorText = await response.text();
          console.error('Error details:', errorText);
          throw new Error(`API error: ${response.status} ${errorText}`);
        }
        
        const data = await response.json();
        console.log('Whisper API response:', data);
        const transcriptionText = data.text;
        
        // Then use an LLM to structure the transcription into a SOAP note
        console.log('Starting API call to GPT-4...');
        const soapResponse = await fetch('https://api.openai.com/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            model: "gpt-4",
            messages: [
              {
                role: "system",
                content: `You are a medical scribe assistant. Convert the following transcription from an EECP therapy session into a structured SOAP note.
                          Include relevant patient data and session information.
                          Format your response as a JSON object with the following structure:
                          {
                            "subjective": "Patient's reported symptoms and feelings",
                            "objective": "Measurable clinical data including vitals and observations",
                            "assessment": "Clinical assessment of patient's condition and response to therapy",
                            "plan": "Treatment plan and recommendations"
                          }
                          
                          IMPORTANT: Your entire response must be valid JSON that can be parsed with JSON.parse().`
              },
              {
                role: "user",
                content: transcriptionText
              }
            ],
            temperature: 0.3
          })
        });
        
        if (!soapResponse.ok) {
          console.error('GPT-4 API error:', soapResponse.status);
          const errorText = await soapResponse.text();
          console.error('Error details:', errorText);
          throw new Error(`API error in chat completion: ${soapResponse.status} ${errorText}`);
        }
        
        const soapData = await soapResponse.json();
        console.log('GPT-4 API response:', soapData);
        
        // Check if the response has the expected structure
        if (!soapData || !soapData.choices || !soapData.choices[0] || !soapData.choices[0].message || !soapData.choices[0].message.content) {
          console.error('Unexpected response format:', soapData);
          throw new Error('Unexpected response format from OpenAI API');
        }
        
        try {
          const contentStr = soapData.choices[0].message.content;
          console.log('Content to parse:', contentStr);
          const structuredSOAP = JSON.parse(contentStr);
          console.log('Parsed SOAP:', structuredSOAP);
          
          // Ensure the structuredSOAP has all required fields
          const defaultSOAP = {
            subjective: '',
            objective: '',
            assessment: '',
            plan: ''
          };
          
          const completeSOAP = {
            ...defaultSOAP,
            ...structuredSOAP
          };
          
          setSoapNote(transcriptionText);
          setStructuredNote(completeSOAP);
        } catch (parseError) {
          console.error('Error parsing JSON from OpenAI response:', parseError);
          throw new Error('Failed to parse structured data from OpenAI response');
        }
      } catch (error) {
        console.error('Error calling OpenAI API:', error);
        
        // Fall back to simulation if there's a real error
        alert(`Error processing audio with OpenAI API: ${error.message}. Falling back to simulation.`);
        simulateWhisperResponse();
      }
      
    } catch (error) {
      console.error('Error processing audio:', error);
      alert('Error processing audio. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };
  
  // Toggle recording
  const toggleRecording = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };
  
  // Format seconds to MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60).toString().padStart(2, '0');
    const secs = (seconds % 60).toString().padStart(2, '0');
    return `${mins}:${secs}`;
  };

  const toggleAIScribe = () => {
    setShowAIScribeModal(!showAIScribeModal);
    
    // If opening the modal, start recording
    if (!showAIScribeModal && !isRecording && !soapNote) {
      startRecording();
    }
  };

  const saveSession = async () => {
    try {
      setLoading(true);
      
      // In a real implementation, we would save the session data to Medplum
      // For the demo, we'll just simulate a successful save
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Navigate back to the dashboard
      navigate('/nurse-dashboard');
    } catch (error) {
      console.error('Error saving session:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!session || !patient) {
    return (
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <FaExclamationTriangle className="h-5 w-5 text-yellow-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Session not found. Please select a valid session.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <header className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">EECP Session Execution</h1>
        <p className="mt-1 text-sm text-gray-500">
          Session #{session.sessionNumber} of {session.totalSessions} for {patient.name}
        </p>
      </header>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Patient Information */}
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
            <div>
              <h2 className="text-lg font-medium text-gray-900">Patient Information</h2>
              <p className="mt-1 text-sm text-gray-500">Personal and medical details</p>
            </div>
            <div className="flex-shrink-0 h-12 w-12 flex items-center justify-center rounded-full bg-blue-100 text-primary">
              <FaUserInjured className="h-6 w-6" />
            </div>
          </div>
          <div className="border-t border-gray-200 px-4 py-5 sm:p-0">
            <dl className="sm:divide-y sm:divide-gray-200">
              <div className="py-3 sm:py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Full name</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{patient.name}</dd>
              </div>
              <div className="py-3 sm:py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Age / Gender</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{patient.age} years / {patient.gender}</dd>
              </div>
              <div className="py-3 sm:py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Diagnosis</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{patient.diagnosis}</dd>
              </div>
              <div className="py-3 sm:py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">LVEF</dt>
                <dd className={`mt-1 text-sm sm:mt-0 sm:col-span-2 ${patient.lvef < 30 ? 'text-red-600 font-bold' : 'text-gray-900'}`}>
                  {patient.lvef}%
                </dd>
              </div>
              <div className="py-3 sm:py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">BNP</dt>
                <dd className={`mt-1 text-sm sm:mt-0 sm:col-span-2 ${patient.bnp > 400 ? 'text-red-600 font-bold' : 'text-gray-900'}`}>
                  {patient.bnp} pg/mL
                </dd>
              </div>
            </dl>
          </div>
        </div>

        {/* Pre-Session Vitals and Symptoms */}
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <h2 className="text-lg font-medium text-gray-900">Pre-Session Assessment</h2>
            <p className="mt-1 text-sm text-gray-500">Record vitals and symptoms before starting</p>
          </div>
          <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
            <div className="space-y-6">
              <div>
                <h3 className="text-md font-medium text-gray-900 mb-2">Vitals</h3>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div>
                    <label htmlFor="preHeartRate" className="block text-sm font-medium text-gray-700">Heart Rate (bpm)</label>
                    <input
                      type="number"
                      id="preHeartRate"
                      name="preHeartRate"
                      value={vitals.preHeartRate}
                      onChange={handleVitalChange}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                    />
                  </div>
                  <div>
                    <label htmlFor="preBloodPressure" className="block text-sm font-medium text-gray-700">Blood Pressure (mmHg)</label>
                    <input
                      type="text"
                      id="preBloodPressure"
                      name="preBloodPressure"
                      placeholder="e.g. 120/80"
                      value={vitals.preBloodPressure}
                      onChange={handleVitalChange}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                    />
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-md font-medium text-gray-900 mb-2">Symptoms</h3>
                <div className="grid grid-cols-1 gap-2 sm:grid-cols-2">
                  <div className="flex items-center">
                    <input
                      id="chestPain"
                      name="chestPain"
                      type="checkbox"
                      checked={symptoms.chestPain}
                      onChange={handleSymptomChange}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                    />
                    <label htmlFor="chestPain" className="ml-2 block text-sm text-gray-900">
                      Chest Pain
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      id="shortnessOfBreath"
                      name="shortnessOfBreath"
                      type="checkbox"
                      checked={symptoms.shortnessOfBreath}
                      onChange={handleSymptomChange}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                    />
                    <label htmlFor="shortnessOfBreath" className="ml-2 block text-sm text-gray-900">
                      Shortness of Breath
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Post-Session Vitals */}
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <h2 className="text-lg font-medium text-gray-900">Post-Session Assessment</h2>
            <p className="mt-1 text-sm text-gray-500">Record vitals after completing the session</p>
          </div>
          <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
            <div className="space-y-6">
              <div>
                <h3 className="text-md font-medium text-gray-900 mb-2">Vitals</h3>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div>
                    <label htmlFor="postHeartRate" className="block text-sm font-medium text-gray-700">Heart Rate (bpm)</label>
                    <input
                      type="number"
                      id="postHeartRate"
                      name="postHeartRate"
                      value={vitals.postHeartRate}
                      onChange={handleVitalChange}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                    />
                  </div>
                  <div>
                    <label htmlFor="postBloodPressure" className="block text-sm font-medium text-gray-700">Blood Pressure (mmHg)</label>
                    <input
                      type="text"
                      id="postBloodPressure"
                      name="postBloodPressure"
                      placeholder="e.g. 120/80"
                      value={vitals.postBloodPressure}
                      onChange={handleVitalChange}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                    />
                  </div>
                </div>
              </div>
              
              <div className="flex flex-col space-y-4">
                <button
                  type="button"
                  onClick={toggleAIScribe}
                  className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  <FaMicrophone className="mr-2" /> Launch AI-Scribe
                </button>
                
                <button
                  type="button"
                  onClick={saveSession}
                  className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  <FaSave className="mr-2" /> Save Session
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Session Controls */}
      <div className="mt-6 bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h2 className="text-lg font-medium text-gray-900">Session Controls</h2>
            <p className="mt-1 text-sm text-gray-500">
              Control the EECP therapy session
            </p>
          </div>
          <div className="flex space-x-2">
            {sessionStatus === 'ready' && (
              <button
                type="button"
                onClick={startSession}
                className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                <FaPlay className="mr-1" /> Start Session
              </button>
            )}
            {sessionStatus === 'running' && (
              <button
                type="button"
                onClick={pauseSession}
                className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
              >
                <FaPause className="mr-1" /> Pause Session
              </button>
            )}
            {sessionStatus === 'paused' && (
              <button
                type="button"
                onClick={startSession}
                className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                <FaPlay className="mr-1" /> Resume Session
              </button>
            )}
          </div>
        </div>
      </div>
      
      {/* Waveform Display */}
      <div className="mt-6">
        <EECPWaveformDisplay 
          isRunning={sessionStatus === 'running'} 
          sessionData={{
            patientId: patient.id,
            patientName: patient.name,
            sessionNumber: session.sessionNumber,
            totalSessions: session.totalSessions,
            cuffPressure: 220
          }}
          onParameterChange={(params) => {
            console.log('Parameter changed:', params);
            // In a real implementation, we would update the session parameters
          }}
          onCaptureSnapshot={(dataUrl) => {
            console.log('Snapshot captured');
            // In a real implementation, we would save the snapshot to the session record
          }}
        />
      </div>

      {/* AI Scribe Modal */}
      {showAIScribeModal && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 flex items-center">
                      <FaMicrophone className="mr-2 text-primary" />
                      AI-Scribe: Session Notes
                    </h3>
                    
                    {isRecording || isProcessing ? (
                      <div className="mt-4 flex flex-col items-center justify-center py-8">
                        {isRecording ? (
                          <>
                            <div className="flex items-center mb-4">
                              <div className="animate-pulse h-3 w-3 bg-red-600 rounded-full mr-2"></div>
                              <p className="text-gray-700">Recording... {formatTime(recordingTime)}</p>
                            </div>
                            <button
                              type="button"
                              onClick={stopRecording}
                              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                            >
                              <FaMicrophoneSlash className="mr-2" /> Stop Recording
                            </button>
                          </>
                        ) : (
                          <div className="flex items-center">
                            <FaSpinner className="animate-spin h-5 w-5 text-primary mr-3" />
                            <p className="text-gray-700">Processing audio...</p>
                          </div>
                        )}
                      </div>
                    ) : soapNote ? (
                      <div className="mt-4">
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="text-sm font-medium text-gray-700 mb-2">Transcription</h4>
                          <p className="text-sm text-gray-600">{soapNote}</p>
                        </div>
                        
                        <div className="mt-4 space-y-4">
                          <div>
                            <h4 className="text-sm font-medium text-gray-700 mb-1">Subjective</h4>
                            <div className="bg-blue-50 p-3 rounded">
                              <p className="text-sm text-gray-800">{structuredNote.subjective}</p>
                            </div>
                          </div>
                          
                          <div>
                            <h4 className="text-sm font-medium text-gray-700 mb-1">Objective</h4>
                            <div className="bg-green-50 p-3 rounded">
                              <p className="text-sm text-gray-800">{structuredNote.objective}</p>
                            </div>
                          </div>
                          
                          <div>
                            <h4 className="text-sm font-medium text-gray-700 mb-1">Assessment</h4>
                            <div className="bg-yellow-50 p-3 rounded">
                              <p className="text-sm text-gray-800">{structuredNote.assessment}</p>
                            </div>
                          </div>
                          
                          <div>
                            <h4 className="text-sm font-medium text-gray-700 mb-1">Plan</h4>
                            <div className="bg-purple-50 p-3 rounded">
                              <p className="text-sm text-gray-800">{structuredNote.plan}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="mt-4 flex flex-col items-center justify-center py-8">
                        <button
                          type="button"
                          onClick={startRecording}
                          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                        >
                          <FaMicrophone className="mr-2" /> Start Recording
                        </button>
                        <p className="mt-2 text-sm text-gray-500">
                          Click to record your session notes
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={toggleAIScribe}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SessionExecutionPanel;
