/**
 * @file medplumHelper.ts
 * @description Simplified helper functions for Medplum FHIR client operations.
 * Provides singleton client management and connection testing.
 */

import { MedplumClient } from '@medplum/core';

// Constants
const DEFAULT_MEDPLUM_BASE_URL = 'https://api.medplum.com/';
const TEST_SEARCH_COUNT = '1';

// Singleton instance for client management
let medplumClientInstance: MedplumClient | null = null;

/**
 * Gets or creates a singleton MedplumClient instance with system authentication.
 * Uses client credentials flow for system-level access to FHIR resources.
 * 
 * @returns {Promise<MedplumClient>} Promise resolving to authenticated client
 * @throws {Error} If environment variables missing or authentication fails
 */
export async function getMedplumClient(): Promise<MedplumClient> {
    if (!medplumClientInstance) {
        const clientId = process.env.CLIENT_ID;
        const clientSecret = process.env.CLIENT_SECRET;
        const baseUrl = process.env.MEDPLUM_BASE_URL || DEFAULT_MEDPLUM_BASE_URL;

        // Validate required environment variables
        if (!clientId || !clientSecret) {
            throw new Error(
                'Missing required environment variables: ' +
                'CLIENT_ID and CLIENT_SECRET must be set'
            );
        }

        console.log('🔧 Initializing Medplum client...');

        try {
            // Create new client instance
            medplumClientInstance = new MedplumClient({
                baseUrl,
                clientId,
                clientSecret,
            });

            // Authenticate using client credentials (system authentication)
            await medplumClientInstance.startClientLogin(
                clientId,
                clientSecret,
            );

            console.log(' Medplum client authenticated successfully');

        } catch (error: any) {
            console.error(' Failed to authenticate Medplum client:', error);
            medplumClientInstance = null;

            // Provide specific error messages based on error type
            if (error.message.includes('401') ||
                error.message.includes('unauthorized')) {
                throw new Error(
                    'Invalid Medplum credentials. ' +
                    'Please check CLIENT_ID and CLIENT_SECRET in your .env file'
                );
            } else if (error.message.includes('network') ||
                error.message.includes('fetch')) {
                throw new Error(
                    'Network error connecting to Medplum. ' +
                    'Please check your internet connection and MEDPLUM_BASE_URL'
                );
            } else {
                throw new Error(`Failed to authenticate with Medplum: ${error.message}`);
            }
        }
    }

    return medplumClientInstance;
}

/**
 * Tests the Medplum connection by performing a minimal search operation.
 * Used for health checks and connection validation.
 * 
 * @returns {Promise<boolean>} Promise resolving to true if connection successful
 */
export async function testMedplumConnection(): Promise<boolean> {
    try {
        const client = await getMedplumClient();

        // Perform minimal search to verify connection
        await client.searchResources('Patient', { _count: TEST_SEARCH_COUNT });

        console.log(' Medplum connection test successful');
        return true;

    } catch (error: any) {
        console.error(' Medplum connection test failed:', error.message);
        return false;
    }
}