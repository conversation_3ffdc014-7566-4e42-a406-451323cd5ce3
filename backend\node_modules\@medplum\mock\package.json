{"name": "@medplum/mock", "version": "4.1.12", "description": "Medplum Mock Client", "keywords": ["medplum", "fhir", "healthcare", "interoperability", "json", "serialization", "hl7", "standards", "clinical", "dstu2", "stu3", "r4", "normative"], "homepage": "https://www.medplum.com/", "bugs": {"url": "https://github.com/medplum/medplum/issues"}, "repository": {"type": "git", "url": "git+https://github.com/medplum/medplum.git", "directory": "packages/mock"}, "license": "Apache-2.0", "author": "Medplum <<EMAIL>>", "sideEffects": false, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.mjs"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.cjs"}}, "main": "dist/cjs/index.cjs", "module": "dist/esm/index.mjs", "types": "dist/cjs/index.d.ts", "files": ["dist/cjs", "dist/esm"], "scripts": {"api-extractor": "api-extractor run --local && cp dist/types.d.ts dist/cjs/index.d.ts && cp dist/types.d.ts dist/esm/index.d.ts", "build": "npm run clean && tsc && node esbuild.mjs && npm run api-extractor", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint .", "test": "jest"}, "dependencies": {"@medplum/core": "4.1.12", "@medplum/definitions": "4.1.12", "@medplum/fhir-router": "4.1.12", "@medplum/fhirtypes": "4.1.12", "dataloader": "2.2.3", "jest-websocket-mock": "2.5.0", "rfc6902": "5.1.2"}, "devDependencies": {"@types/pdfmake": "0.2.11"}, "engines": {"node": ">=20.0.0"}}