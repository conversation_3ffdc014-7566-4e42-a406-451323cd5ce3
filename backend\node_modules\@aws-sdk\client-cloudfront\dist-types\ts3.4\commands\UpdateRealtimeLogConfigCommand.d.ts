import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CloudFrontClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../CloudFrontClient";
import {
  UpdateRealtimeLogConfigRequest,
  UpdateRealtimeLogConfigResult,
} from "../models/models_2";
export { __MetadataBearer };
export { $Command };
export interface UpdateRealtimeLogConfigCommandInput
  extends UpdateRealtimeLogConfigRequest {}
export interface UpdateRealtimeLogConfigCommandOutput
  extends UpdateRealtimeLogConfigResult,
    __MetadataBearer {}
declare const UpdateRealtimeLogConfigCommand_base: {
  new (
    input: UpdateRealtimeLogConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateRealtimeLogConfigCommandInput,
    UpdateRealtimeLogConfigCommandOutput,
    CloudFrontClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [UpdateRealtimeLogConfigCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateRealtimeLogConfigCommandInput,
    UpdateRealtimeLogConfigCommandOutput,
    CloudFrontClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateRealtimeLogConfigCommand extends UpdateRealtimeLogConfigCommand_base {
  protected static __types: {
    api: {
      input: UpdateRealtimeLogConfigRequest;
      output: UpdateRealtimeLogConfigResult;
    };
    sdk: {
      input: UpdateRealtimeLogConfigCommandInput;
      output: UpdateRealtimeLogConfigCommandOutput;
    };
  };
}
