import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CloudFrontClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../CloudFrontClient";
import {
  UpdateStreamingDistributionRequest,
  UpdateStreamingDistributionResult,
} from "../models/models_2";
export { __MetadataBearer };
export { $Command };
export interface UpdateStreamingDistributionCommandInput
  extends UpdateStreamingDistributionRequest {}
export interface UpdateStreamingDistributionCommandOutput
  extends UpdateStreamingDistributionResult,
    __MetadataBearer {}
declare const UpdateStreamingDistributionCommand_base: {
  new (
    input: UpdateStreamingDistributionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateStreamingDistributionCommandInput,
    UpdateStreamingDistributionCommandOutput,
    CloudFrontClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UpdateStreamingDistributionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UpdateStreamingDistributionCommandInput,
    UpdateStreamingDistributionCommandOutput,
    CloudFrontClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UpdateStreamingDistributionCommand extends UpdateStreamingDistributionCommand_base {
  protected static __types: {
    api: {
      input: UpdateStreamingDistributionRequest;
      output: UpdateStreamingDistributionResult;
    };
    sdk: {
      input: UpdateStreamingDistributionCommandInput;
      output: UpdateStreamingDistributionCommandOutput;
    };
  };
}
