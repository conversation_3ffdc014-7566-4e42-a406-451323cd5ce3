import React, { useState } from 'react';
import { localStorageService } from '../../services/localStorageService';
import { PlusCircle, Trash2, Edit2, Check, X } from 'lucide-react';

const PatientNotes = ({ patient, onNotesUpdate }) => {
  const [showNotes, setShowNotes] = useState(false);
  const [newNote, setNewNote] = useState('');
  const [editingNoteId, setEditingNoteId] = useState(null);
  const [editedNoteText, setEditedNoteText] = useState('');

  const handleAddNote = (e) => {
    e.stopPropagation();
    if (newNote.trim()) {
      // Add note using the dedicated service method
      const addedNote = localStorageService.addNoteToPatient(patient.id, {
        text: newNote,
        author: 'Dr. <PERSON>'
      });

      if (addedNote) {
        // Update the patient object with the new note
        const updatedPatient = {
          ...patient,
          notes: [...(patient.notes || []), addedNote]
        };

        // Notify parent component
        if (onNotesUpdate) {
          onNotesUpdate(updatedPatient);
        }

        setNewNote('');
        setShowNotes(false);
      }
    }
  };

  const handleDeleteNote = (noteId) => {
    const updatedPatient = {
      ...patient,
      notes: patient.notes.filter(note => note.id !== noteId)
    };

    // Update the patient in localStorage
    localStorageService.updatePatient(patient.id, {
      notes: updatedPatient.notes
    });

    // Notify parent component
    if (onNotesUpdate) {
      onNotesUpdate(updatedPatient);
    }
  };

  const handleEditNote = (note) => {
    setEditingNoteId(note.id);
    setEditedNoteText(note.text);
  };

  const handleSaveEdit = (noteId) => {
    const updatedNotes = patient.notes.map(note => 
      note.id === noteId 
        ? { ...note, text: editedNoteText, date: new Date().toISOString() }
        : note
    );

    // Update the patient in localStorage
    localStorageService.updatePatient(patient.id, {
      notes: updatedNotes
    });

    // Update the patient object
    const updatedPatient = {
      ...patient,
      notes: updatedNotes
    };

    // Notify parent component
    if (onNotesUpdate) {
      onNotesUpdate(updatedPatient);
    }

    setEditingNoteId(null);
    setEditedNoteText('');
  };

  const handleCancelEdit = () => {
    setEditingNoteId(null);
    setEditedNoteText('');
  };

  return (
    <div className="mt-4 pt-4 border-t border-gray-100">
      <div className="flex justify-between items-center mb-2">
        <h4 className="text-sm font-medium text-gray-700">Notes</h4>
        <div className="flex space-x-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowNotes(!showNotes);
            }}
            className="text-xs text-blue-600 hover:text-blue-800 flex items-center"
          >
            <PlusCircle className="h-4 w-4 mr-1" />
            {showNotes ? 'Cancel' : 'Add Note'}
          </button>
        </div>
      </div>

      {/* Add Note Form */}
      {showNotes && (
        <div className="mb-3">
          <textarea
            value={newNote}
            onChange={(e) => setNewNote(e.target.value)}
            placeholder="Add a note about this patient..."
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            rows="2"
            onClick={(e) => e.stopPropagation()}
          />
          <div className="flex justify-end mt-2">
            <button
              onClick={handleAddNote}
              className="px-3 py-1 bg-blue-600 text-white text-xs font-medium rounded hover:bg-blue-700 transition-colors"
            >
              Save Note
            </button>
          </div>
        </div>
      )}

      {/* Existing Notes */}
      <div className="space-y-2">
        {patient.notes?.length > 0 ? (
          patient.notes.map((note) => (
            <div key={note.id} className="bg-gray-50 rounded-lg p-3">
              {editingNoteId === note.id ? (
                <div className="space-y-2">
                  <textarea
                    value={editedNoteText}
                    onChange={(e) => setEditedNoteText(e.target.value)}
                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    rows="2"
                  />
                  <div className="flex justify-end space-x-2">
                    <button
                      onClick={() => handleSaveEdit(note.id)}
                      className="p-1 text-green-600 hover:text-green-700"
                    >
                      <Check className="h-4 w-4" />
                    </button>
                    <button
                      onClick={handleCancelEdit}
                      className="p-1 text-red-600 hover:text-red-700"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ) : (
                <div>
                  <div className="flex justify-between items-start">
                    <p className="text-sm text-gray-700">{note.text}</p>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEditNote(note)}
                        className="p-1 text-blue-600 hover:text-blue-700"
                      >
                        <Edit2 className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteNote(note.id)}
                        className="p-1 text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {new Date(note.date).toLocaleDateString()} by {note.author}
                  </div>
                </div>
              )}
            </div>
          ))
        ) : (
          <div className="text-center py-4 text-gray-500 text-sm">
            No notes available
          </div>
        )}
      </div>
    </div>
  );
};

export default PatientNotes; 