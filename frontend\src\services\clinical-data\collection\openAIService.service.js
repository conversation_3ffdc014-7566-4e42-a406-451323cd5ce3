/**
 * OpenAI API Integration Service for clinical data extraction
 * 
 * Main service for sending clinical transcripts to GPT-4o and handling responses
 * 
 * Why a separate service?
 * 
 * WORKFLOW INTEGRATION:
 *   Real-time processing during patient visits
 *   Batch processing for non-urgent clinical documentation
 *   Integration with existing EHR systems and clinical workflows
 *   Supports different clinical scenarios (intake, EECP sessions, follow-ups)
 * 
 * DATA MANAGEMENT:
 *   Caching for frequently requested data
 *   Error handling and retries
 *   Rate limiting and quota management
 *   Secure API key management
 * 
 *  MEDICAL ACCURACY & RELIABILITY:
 *    Clinical extractions need higher accuracy than general AI tasks
 *    Failed extractions could impact patient safety
 *    Consistent API parameters for reliable medical data processing
 *    Quality validation of AI responses before clinical use
 */


const { ClinicalPromptBuilder } = require('../prompt/promptTemplates');
const { ClinicalResponseParser } = require('../extraction/responseParser');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Create parser instance
const clinicalParser = new ClinicalResponseParser({
    enableLogging: process.env.NODE_ENV === 'development',
    strictValidation: true,
    sanitizeData: true
});

class OpenAIService {
    /**
     * Initialize OpenAI service with clinical configuration
     * @param {Object} config - Clinical configuration
     * @param {string} config.apiKey - OpenAI API key
     * @param {string} config.model - OpenAI model name
     * @param {boolean} config.enableLogging - Enable logging of API requests and responses
     */
    constructor(config = {}) {
        this.apiKey = config.apiKey || process.env.REACT_APP_OPENAI_API_KEY;
        this.model = config.defaultModel || 'gpt-4';
        this.enableLogging = config.enableLogging || false;
        this.baseURL = config.baseURL || 'https://api.openai.com/v1/chat/completions';

        if (!this.apiKey) {
            throw new Error('API key is required');
        }
        if (!this.apiKey.startsWith('sk-')) {
            throw new Error('Invalid API key format');
        }

        // Simple request logging
        this.requestLog = [];
        this.maxLogEntries = 100;

        this.retryConfig = {
            maxRetries: 3,
            baseDelay: 1000,
            maxDelay: 10000,
        };

        if (this.enableLogging) {
            console.log('OpenAI service initialized with:', {
                model: this.model,
                hasAPIKey: !!this.apiKey,
                timestamp: new Date().toISOString(),
            });
        }
    }

    /**
     * Extract clinical data from a transcript using GPT-4o
     * Main method for clinical data extraction using patient conversation
     */
    async extractClinicalData(transcript, extractionType, context = {}, priority = 'standard') {
        const startTime = Date.now();
        try {
            // Basic validation
            if (!transcript || typeof transcript !== 'string') {
                throw new Error('Transcript is required and must be a string');
            }
            if (!extractionType || typeof extractionType !== 'string') {
                throw new Error('Extraction type is required and must be a string');
            }

            const validExtractionTypes = ['demographics', "medical_history", "symptoms", "vitals"];
            if (!validExtractionTypes.includes(extractionType)) {
                throw new Error(`Invalid extraction type: ${extractionType}. Must be one of: ${validExtractionTypes.join(', ')}`);
            }

            // Build prompt
            const promptObject = ClinicalPromptBuilder.buildExtractionPrompt(extractionType, {
                transcript: transcript,
                sessionType: context.sessionType || 'general_consultation',
                sessionNumber: context.sessionNumber || 1,
                existingData: context.existingData || {}
            });

            // Configure API settings
            const apiConfig = this._getAPIConfig(extractionType, priority);

            // Clinical context for logging
            const clinicalContext = {
                extractionType,
                patientId: context.patientId || 'unknown',
                userId: context.userId || 'unknown',
                sessionType: context.sessionType || 'general_consultation',
                priority,
                timestamp: new Date().toISOString(),
            };

            // Make API call
            const response = await this.makeAPICall(promptObject, apiConfig, clinicalContext, this.retryConfig.maxRetries);

            // Parse response
            let parsedResult = await clinicalParser.parseResponse(response, extractionType, clinicalContext);

            // Check for uncertain fields
            const uncertainFields = this._identifyUncertainFields(parsedResult.data, extractionType);

            // Handle uncertainty if needed
            if (uncertainFields.length > 0) {
                parsedResult.data.hasUncertainty = true;
                parsedResult.data.uncertainFields = uncertainFields;

                // Check if any critical fields are uncertain
                const criticalUncertainFields = uncertainFields.filter(field =>
                    this._isCriticalField(field, extractionType)
                );

                if (criticalUncertainFields.length > 0) {
                    parsedResult.data.requiresReview = true;
                }
            }

            // Log successful extraction
            if (this.enableLogging) {
                this._logExtraction({
                    extractionType,
                    context: clinicalContext,
                    processingTime: Date.now() - startTime,
                    success: true,
                    responseLength: response.length,
                    timestamp: new Date().toISOString()
                });
            }

            // Return structured clinical data
            return {
                ...parsedResult.data,
                extractionType,
                context: clinicalContext,
                processingTime: Date.now() - startTime,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            if (this.enableLogging) {
                this._logExtraction({
                    extractionType,
                    context: context,
                    processingTime: Date.now() - startTime,
                    success: false,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
            throw error;
        }
    }

    /**
     * Make API call to OpenAI with retry logic and error handling
     */
    async makeAPICall(prompt, config, context, maxRetries = 3) {
        let lastError = null;

        for (let attempt = 0; attempt < maxRetries; attempt++) {
            try {
                // For testing purposes, if callOpenAI is mocked
                if (this.callOpenAI && typeof this.callOpenAI === 'function') {
                    const mockResponse = await this.callOpenAI();
                    return mockResponse;
                }

                const requestPayload = {
                    model: config.model || this.model,
                    messages: [
                        {
                            role: 'system',
                            content: prompt.systemPrompt
                        },
                        {
                            role: 'user',
                            content: prompt.userPrompt
                        }
                    ],
                    temperature: config.temperature || 0.2,
                    max_tokens: config.maxTokens || 4000,
                    top_p: config.topP || 1,
                    frequency_penalty: config.frequencyPenalty || 0,
                    presence_penalty: config.presencePenalty || 0,
                };

                const headers = {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json',
                    'User-Agent': 'Syncore-Clinical-EHR/1.0'
                };

                if (this.enableLogging) {
                    console.log(`Making OpenAI API call (attempt ${attempt + 1} of ${maxRetries})`, {
                        model: requestPayload.model,
                        context: context.extractionType,
                        patientId: context.patientId,
                        timestamp: new Date().toISOString()
                    });
                }

                // Make API call
                const response = await fetch(this.baseURL, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(requestPayload)
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(`API request failed with status ${response.status}: ${errorData.error?.message || 'Unknown error'}`);
                }

                const responseData = await response.json();

                if (this.enableLogging) {
                    this._logAPICall({
                        success: true,
                        attempt: attempt + 1,
                        context,
                        responseLength: responseData.choices?.[0]?.message?.content?.length || 0,
                        tokensUsed: responseData.usage?.total_tokens || 0,
                        timestamp: new Date().toISOString()
                    });
                }

                return responseData;
            } catch (error) {
                lastError = error;
                if (this.enableLogging) {
                    console.error(`API call failed: ${error.message}`, {
                        error: error.message,
                        context,
                        timestamp: new Date().toISOString()
                    });
                }

                if (attempt === maxRetries - 1) {
                    break;
                }

                // exponential backoff
                await new Promise(resolve => setTimeout(resolve, this.retryConfig.baseDelay * Math.pow(2, attempt)));
            }
        }

        // All attempts failed
        throw new OpenAIAPIError('All API calls failed', {
            error: lastError?.message || 'Unknown error',
            context,
            attempts: maxRetries
        });
    }

    /**
     * COMPREHENSIVE CLINICAL DATA EXTRACTION - EXTRACTS MULTIPLE DATA TYPES
     * Skeletal implementation for future expansion
     */
    async extractComprehensiveClinicalData(transcript, options = {}) {
        const {
            extractionTypes = ['demographics', 'medical_history', 'symptoms', 'vitals'],
            sessionType = 'intake',
            patientId = 'unknown'
        } = options;

        // Validate extraction types
        const validTypes = ['demographics', 'medical_history', 'symptoms', 'vitals'];
        const invalidTypes = extractionTypes.filter(type => !validTypes.includes(type));
        if (invalidTypes.length > 0) {
            throw new Error(`Invalid extraction types: ${invalidTypes.join(', ')}. Must be one of: ${validTypes.join(', ')}`);
        }

        // Extract each type of data
        const results = {};
        for (const type of extractionTypes) {
            try {
                const result = await this.extractClinicalData(transcript, type, {
                    sessionType,
                    patientId,
                    priority: 'standard'
                });
                results[type] = result;
            } catch (error) {
                console.error(`Failed to extract ${type} data:`, error);
                results[type] = {
                    success: false,
                    error: error.message,
                    extractionType: type,
                    timestamp: new Date().toISOString()
                };
            }
        }

        return {
            success: true,
            results,
            extractionTypes,
            context: {
                sessionType,
                patientId,
                timestamp: new Date().toISOString()
            }
        };
    }

    /**
     * Handle uncertain extraction with focused prompts
     * Skeletal implementation for future expansion
     */
    async handleUncertainExtraction(initialResult, transcript) {
        return initialResult;
    }

    /**
     * Validate clinical response for safety and accuracy
     * Skeletal implementation for future expansion
     */
    async validateClinicalResponse(response, extractionType) {
        console.log("Validating clinical response");
        return { isValid: true, response };
    }

    /**
     * Track API usage for billing and quota management
     * Skeletal implementation for future expansion
     */
    trackAPIUsage(usage) {
        if (!this.enableLogging) return;
        console.log("Tracking API usage ", usage);
    }

    // Helper methods 
    _getAPIConfig(extractionType, priority) {
        const baseConfig = {
            model: this.model,
            temperature: 0.2,
            maxTokens: 4000,
        };

        const configByExtractionType = {
            'demographics': {
                ...baseConfig,
                maxTokens: 4000,
                temperature: 0.1,
            },
            'medical_history': {
                ...baseConfig,
                maxTokens: 4000,
                temperature: 0.2,
            },
            'symptoms': {
                ...baseConfig,
                maxTokens: 4000,
            },
            'vitals': {
                ...baseConfig,
                maxTokens: 2000,
            }
        };

        return configByExtractionType[extractionType] || baseConfig;
    }

    _logExtraction(extractionDetails) {
        if (!this.enableLogging) return;

        // Add timestamp if not present
        if (!extractionDetails.timestamp) {
            extractionDetails.timestamp = new Date().toISOString();
        }

        // Log to console
        console.log('Clinical extraction:', {
            type: extractionDetails.extractionType,
            success: extractionDetails.success,
            processingTime: extractionDetails.processingTime,
            patientId: extractionDetails.context?.patientId || 'unknown',
            timestamp: extractionDetails.timestamp
        });

        // Store in request log with limit to prevent memory leaks
        this.requestLog.push(extractionDetails);
        if (this.requestLog.length > this.maxLogEntries) {
            this.requestLog.shift(); // Remove oldest entry
        }
    }

    _logAPICall(logData) {
        if (!this.enableLogging) return;
        console.log('OpenAI API call logged:', logData);
    }

    // Helper methods 
    _checkRateLimits() {
        return { isRateLimited: false };
    }

    _isRetryableError(error) {
        return true;
    }

    _calculateRetryDelay(attempt) {
        return this.retryConfig.baseDelay * Math.pow(2, attempt);
    }

    _identifyUncertainFields(data, extractionType) {
        const uncertainFields = [];

        // Iterate through data fields to find uncertain values
        for (const [key, value] of Object.entries(data)) {
            if (typeof value === 'string') {
                if (value.includes('uncertain') ||
                    value.includes('unclear') ||
                    value.includes('unknown') ||
                    value === 'Unknown' ||
                    value === '?') {
                    uncertainFields.push(key);
                }
            }
        }

        return uncertainFields;
    }

    _isCriticalField(field, extractionType) {
        const criticalFieldsByType = {
            'medical_history': ['allergies', 'medications', 'diagnoses'],
            'vitals': ['bloodPressure', 'heartRate', 'oxygenSaturation'],
            'symptoms': ['currentSymptoms']
        };

        return criticalFieldsByType[extractionType]?.includes(field) || false;
    }
}

/**
 * Custom error class for OpenAI API related errors
 */
class OpenAIAPIError extends Error {
    constructor(message, details = {}) {
        super(message);
        this.name = 'OpenAIAPIError';
        this.details = details;
        this.timestamp = new Date().toISOString();
    }
}

module.exports = {
    OpenAIService,
    OpenAIAPIError
};