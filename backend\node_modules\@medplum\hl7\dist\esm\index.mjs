var a=class extends EventTarget{addEventListener(t,e,s){super.addEventListener(t,e,s)}removeEventListener(t,e,s){super.removeEventListener(t,e,s)}};import{connect as y}from"node:net";import{Hl7Message as x}from"@medplum/core";import m from"iconv-lite";var H=11,p=13,v=28;var h=class extends Event{constructor(t,e){super("message"),this.connection=t,this.message=e}},i=class extends Event{constructor(t){super("error"),this.error=t}},l=class extends Event{constructor(){super("close")}};var d=class extends a{constructor(e,s="utf-8",r=!1){super();this.chunks=[];this.messageQueue=[];this.socket=e,this.encoding=s,this.enhancedMode=r,e.on("data",n=>{try{if(this.appendData(n),n.at(-2)===28&&n.at(-1)===13){let o=Buffer.concat(this.chunks),g=o.subarray(1,o.length-2),E=m.decode(g,this.encoding),k=x.parse(E);this.dispatchEvent(new h(this,k)),this.resetBuffer()}}catch(o){this.dispatchEvent(new i(o))}}),e.on("error",n=>{this.resetBuffer(),this.dispatchEvent(new i(n))}),e.on("end",()=>{this.close()}),this.addEventListener("message",n=>{r&&this.send(n.message.buildAck({ackCode:"CA"}));let o=this.messageQueue.shift();if(!o){this.dispatchEvent(new i(new Error(`Received a message when no pending messages were in the queue. Message: ${n.message}`)));return}o.resolve?.(n.message)})}sendImpl(e,s){this.messageQueue.push(s);let r=e.toString(),n=m.encode(r,this.encoding),o=Buffer.alloc(n.length+3);o.writeInt8(11,0),n.copy(o,1),o.writeInt8(28,n.length+1),o.writeInt8(13,n.length+2),this.socket.write(o)}send(e){this.sendImpl(e,{message:e})}async sendAndWait(e){return new Promise((s,r)=>{let n={message:e,resolve:s,reject:r};this.sendImpl(e,n)})}close(){this.socket.end(),this.socket.destroy(),this.dispatchEvent(new l)}appendData(e){this.chunks.push(e)}resetBuffer(){this.chunks=[]}};var u=class extends a{constructor(t){super(),this.options=t,this.host=this.options.host,this.port=this.options.port,this.encoding=this.options.encoding,this.keepAlive=this.options.keepAlive??!1,this.connectTimeout=this.options.connectTimeout??3e4}connect(){return this.connection?Promise.resolve(this.connection):(this.socket&&(this.socket.removeAllListeners(),this.socket.destroy(),this.socket=void 0),new Promise((t,e)=>{this.socket=y({host:this.host,port:this.port,keepAlive:this.keepAlive}),this.connectTimeout>0&&(this.socket.setTimeout(this.connectTimeout),this.socket.on("timeout",()=>{let s=new Error(`Connection timeout after ${this.connectTimeout}ms`);this.socket&&(this.socket.destroy(),this.socket=void 0),e(s)})),this.socket.on("connect",()=>{if(!this.socket)return;let s;this.connection=s=new d(this.socket,this.encoding),this.socket.setTimeout(0),s.addEventListener("close",()=>{this.socket=void 0,this.dispatchEvent(new l)}),s.addEventListener("error",r=>{this.dispatchEvent(new i(r.error))}),t(this.connection)}),this.socket.on("error",s=>{this.socket&&(this.socket.destroy(),this.socket=void 0),e(s)})}))}async send(t){return(await this.connect()).send(t)}async sendAndWait(t){return(await this.connect()).sendAndWait(t)}close(){this.socket&&(this.socket.removeAllListeners(),this.socket.destroy(),this.socket=void 0),this.connection&&(this.connection.close(),delete this.connection)}};import M from"node:net";var f=class{constructor(t){this.handler=t}start(t,e,s=!1){let r=M.createServer(n=>{let o=new d(n,e,s);this.handler(o)});r.listen(t),this.server=r}async stop(){return new Promise((t,e)=>{if(!this.server){e(new Error("Stop was called but there is no server running"));return}this.server.close(s=>{if(s){e(s);return}t()}),this.server=void 0})}};export{p as CR,v as FS,a as Hl7Base,u as Hl7Client,l as Hl7CloseEvent,d as Hl7Connection,i as Hl7ErrorEvent,h as Hl7MessageEvent,f as Hl7Server,H as VT};
//# sourceMappingURL=index.mjs.map
