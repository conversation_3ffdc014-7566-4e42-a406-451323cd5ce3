aiohappyeyeballs==2.4.3
aiohttp==3.10.9
aiosignal==1.3.1
altair==5.4.1
annotated-types==0.7.0
anyio==4.6.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asttokens==2.4.1
async-lru==2.0.4
attrs==24.2.0
babel==2.16.0
bcrypt==4.3.0
beautifulsoup4==4.12.3
bleach==6.2.0
bleak==0.22.3
bleak-winrt==1.2.0
blinker==1.8.2
build==1.2.2.post1
CacheControl==0.14.0
cachetools==5.5.0
cairocffi==1.7.1
CairoSVG==2.7.1
certifi==2024.8.30
cffi==1.17.1
charset-normalizer==3.3.2
cleo==2.1.0
click==8.1.7
colorama==0.4.6
comm==0.2.2
crashtest==0.4.1
cssselect==1.3.0
cssselect2==0.8.0
dataclasses-json==0.5.14
debugpy==1.8.9
decorator==5.1.1
defusedxml==0.7.1
Deprecated==1.2.14
dirtyjson==1.0.8
discord.py==2.4.0
distlib==0.3.9
distro==1.9.0
Django==1.8.3
django-appconf==1.1.0
django-axes==1.4.0
django-compressor==1.5
django-reversion==1.8.7
djangorestframework==3.2.2
dnspython==2.7.0
docutils==0.21.2
dulwich==0.21.7
exa_py==1.7.1
executing==2.1.0
fastjsonschema==2.20.0
ffs==0.0.8.2
filelock==3.16.1
filetype==1.2.0
fqdn==1.5.1
frozendict==2.4.6
frozenlist==1.4.1
fsspec==2024.9.0
gitdb==4.0.11
GitPython==3.1.43
google-ai-generativelanguage==0.6.10
google-api-core==2.20.0
google-api-python-client==2.147.0
google-auth==2.35.0
google-auth-httplib2==0.2.0
google-generativeai==0.8.2
googleapis-common-protos==1.65.0
greenlet==3.1.1
grpcio==1.66.2
grpcio-status==1.66.2
gTTS==2.5.3
h11==0.14.0
html5lib==1.1
httpcore==1.0.6
httplib2==0.22.0
httpx==0.27.2
huggingface-hub==0.25.1
idna==3.10
importlib_metadata==8.5.0
installer==0.7.0
ipykernel==6.29.5
ipython==8.28.0
isoduration==20.11.0
jaraco.classes==3.4.0
jedi==0.19.1
Jinja2==3.1.4
jiter==0.5.0
joblib==1.4.2
json5==0.9.28
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2023.12.1
jupyter-events==0.10.0
jupyter-lsp==2.2.5
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyter_server==2.14.2
jupyter_server_terminals==0.5.3
jupyterlab==4.2.6
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
keyring==24.3.1
Kivy==2.3.1
kivy-deps.angle==0.4.0
kivy-deps.glew==0.3.1
Kivy-Garden==0.1.5
kivy_deps.sdl2==0.8.0
langchain==0.0.137
langchain-core==0.3.15
langchain-openai==0.2.5
langchain-text-splitters==0.3.0
langsmith==0.1.131
letter==0.5
llama-cloud==0.1.2
llama-index==0.11.16
llama-index-agent-openai==0.3.4
llama-index-cli==0.3.1
llama-index-core==0.11.16
llama-index-embeddings-openai==0.2.5
llama-index-indices-managed-llama-cloud==0.4.0
llama-index-legacy==0.9.48.post3
llama-index-llms-openai==0.2.11
llama-index-multi-modal-llms-openai==0.2.2
llama-index-program-openai==0.2.0
llama-index-question-gen-openai==0.2.0
llama-index-readers-file==0.2.2
llama-index-readers-llama-parse==0.3.0
llama-parse==0.5.7
loguru==0.7.2
lxml==5.3.0
markdown-it-py==3.0.0
MarkupSafe==2.1.5
marshmallow==3.22.0
matplotlib-inline==0.1.7
mdurl==0.1.2
mistune==3.0.2
more-itertools==10.5.0
mpmath==1.3.0
msgpack==1.1.0
multidict==6.1.0
multitasking==0.0.11
mygnuhealth==2.2.1
mypy-extensions==1.0.0
narwhals==1.9.1
nbclient==0.10.0
nbconvert==7.16.4
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.3
nltk==3.9.1
notebook==7.2.2
notebook_shim==0.2.4
numpy==1.26.4
ollama==0.4.5
opal==0.5.6
openai==1.55.0
openapi-schema-pydantic==1.2.4
orjson==3.10.7
overrides==7.7.0
packaging==24.1
pandas==2.2.3
pandocfilters==1.5.1
parso==0.8.4
peewee==3.17.8
pexpect==4.9.0
pillow==10.4.0
pinecone-client==2.2.1
pkginfo==1.11.2
platformdirs==4.3.6
poetry==1.8.4
poetry-core==1.9.1
poetry-plugin-export==1.8.0
praw==7.7.1
prawcore==2.4.0
prometheus_client==0.21.0
prompt_toolkit==3.0.48
proto-plus==1.24.0
protobuf==5.28.2
psutil==6.1.0
ptyprocess==0.7.0
pure_eval==0.2.3
pyarrow==17.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
pydantic==2.9.2
pydantic_core==2.23.4
pydeck==0.9.1
pygal==3.0.5
Pygments==2.18.0
pyparsing==3.1.4
pypdf==4.3.1
pypiwin32==223
pyproject_hooks==1.2.0
python-dateutil==2.4.2
python-dotenv==1.0.1
python-json-logger==2.0.7
pytz==2024.2
pywin32==308
pywin32-ctypes==0.2.3
pywinpty==2.0.14
PyYAML==6.0.2
pyzmq==26.2.0
RapidFuzz==3.10.1
referencing==0.35.1
regex==2024.9.11
requests==2.32.3
requests-toolbelt==1.0.0
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==13.9.2
rpds-py==0.20.0
rsa==4.9
safetensors==0.4.5
scikit-learn==1.5.2
scipy==1.14.1
Send2Trash==1.8.3
sentence-transformers==3.1.1
shellingham==1.5.4
six==1.16.0
smmap==5.0.1
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==1.4.54
stack-data==0.6.3
streamlit==1.38.0
striprtf==0.0.26
sympy==1.13.3
ta==0.11.0
tenacity==8.5.0
terminado==0.18.1
threadpoolctl==3.5.0
tiktoken==0.8.0
tinycss==0.4
tinycss2==1.4.0
tinydb==4.8.2
tokenizers==0.20.0
toml==0.10.2
tomlkit==0.13.2
torch==2.4.1
tornado==6.4.1
tqdm==4.66.5
traitlets==5.14.3
transformers==4.45.1
trove-classifiers==2024.10.21.16
types-python-dateutil==2.9.0.20241003
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2024.2
update-checker==0.18.0
uri-template==1.3.0
uritemplate==4.1.1
urllib3==2.2.3
uv==0.4.22
virtualenv==20.27.1
watchdog==4.0.2
wcwidth==0.2.13
webcolors==24.11.1
webencodings==0.5.1
websocket-client==1.8.0
win32-setctime==1.1.0
wrapt==1.16.0
yarl==1.13.1
yfinance==0.2.51
zipp==3.20.2
