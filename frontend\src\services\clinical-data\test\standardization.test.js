const { MEDICAL_STANDARDIZATION } = require('../validation/clinicalSchemas');

describe('Medical Standardization', () => {
    test('standardizes common symptoms', () => {
        expect(MEDICAL_STANDARDIZATION.SYMPTOM_MAPPINGS['sob']).toBe('Dyspnea');
        expect(MEDICAL_STANDARDIZATION.SYMPTOM_MAPPINGS['chest pain']).toBe('Chest Pain');
    });

    test('standardizes medications', () => {
        expect(MEDICAL_STANDARDIZATION.MEDICATION_MAPPINGS['lopressor']).toBe('Metoprolol');
    });

    test('maps conditions to ICD codes', () => {
        const condition = 'Hypertension';
        expect(MEDICAL_STANDARDIZATION.ICD10_CODES[condition]).toBeDefined();
    });
});
