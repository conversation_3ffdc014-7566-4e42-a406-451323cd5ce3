/**
 * @file Implements the authentication service.
 */
import { z } from 'zod';
import { LoginSchema } from './authSchema';
import { MedplumService } from '../clients/medplum_client';
/**
 * AuthService class provides methods for user authentication.
 */
export class AuthService {
    // private medplumClient: MedplumClient;
    medplumClient;
    constructor() {
        // this.medplumClient = new MedplumClient({
        //   clientId: process.env.CLIENT_ID || '',
        //   clientSecret: process.env.CLIENT_SECRET || '',
        //   storage: new ClientStorage(new MemoryStorage()),
        //   fetch: fetch as any
        // });
        this.medplumClient = new MedplumService();
    }
    /**
     * Handles user login.
     * @param credentials - An object containing the user's email and password.
     * @returns A Promise that resolves to a LoginResponse on success, or rejects with an error.
     */
    async login(credentials) {
        try {
            // 1. Validate input credentials using Zod schema.
            LoginSchema.parse(credentials);
            console.log('AuthService: Attempting login for:', credentials.email);
            // 2. Call MedplumService login method
            const loginResp = await this.medplumClient.login(credentials.email, credentials.password);
            // Ensure we have a valid token
            if (!loginResp.login) {
                throw new Error('Login failed: No access token received');
            }
            const loginResponse = {
                token: loginResp.login,
                user: {
                    email: credentials.email,
                    firstName: loginResp.profile?.name?.[0]?.given?.[0],
                    lastName: loginResp.profile?.name?.[0]?.family,
                    role: 'doctor',
                },
            };
            return loginResponse;
        }
        catch (error) {
            // Handle Zod validation errors or errors from MedplumService.
            if (error instanceof z.ZodError) {
                const validationErrors = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join('; ');
                console.error('AuthService: Login validation error:', validationErrors);
                throw new Error(`Validation failed: ${validationErrors}`);
            }
            else {
                console.error('failed:', error.message);
                throw new Error(`Login failed: ${error.message}`);
            }
        }
    }
}
;
//# sourceMappingURL=authService.js.map