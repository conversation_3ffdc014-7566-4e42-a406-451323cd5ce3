/**
 * API Configuration for AI Services
 * OPENAI API CONFIGURATION AND SETTINGS
 * CENTRA<PERSON>IZES API CONFIGURATION FOR MAINTAIN<PERSON><PERSON>ITY AND SECURITY
 * 
 * WHY SEPERATE API CONFIGURATION IS NEEDED:
 * 
 * 1. SECURITY AND <PERSON>NV MANAGEMENT:
 *    - API keys are sensitive and should not be hardcoded
 *    - Environment variables provide flexibility and security
 * 
 * 2. RATE LIMITS AND QUOTA MANAGEMENT:
 *    - OPENAI API has rate limits and quotas
 *    - Configuration allows easy adjustment of limits
 *    - Medical data extraction can be expensive - needs cost controls
 *    - Prevents accidental API overuse
 * 
 * 3. CLINICAL RELIABILITY:
 *    - Standardizes API usage across the application
 *    - Timeout and retry logic can be managed in one place
 */

/**
 *  Validate OpenAI API Key format and availability
 * 
 * @param {string} key - OpenAI API key
 * @returns {boolean} true if key is valid, false otherwise
 * @throws {Error} if key is invalid
 */
export const validateOpenAIKey = (key) => {
    //get key from environment variables
    const apiKey = process.env.REACT_APP_OPENAI_API_KEY || key;
    //check if key exists in environment variables
    if (!apiKey) {
        throw new Error('OpenAI API key is not set in environment variables');
    }
    //validate API key format
    if (typeof apiKey !== 'string' || apiKey.trim() === '') {
        throw new Error('Invalid OpenAI API key');
    }
    if (!apiKey.startsWith('sk-')) {
        throw new Error('Invalid OpenAI API key format. Key should start with "sk-".');
    }

    if (apiKey.length < 20) {
        throw new Error('OpenAI API key appears to be too short. Please check your key.');
    }
    // return boolean or throw error
    return true;
};

/**
 * Get OpenAI API configuration based on extraction type
 * @param {string} extractionType - Type of data to extract
 * @param {string} priority - Priority level for extraction
 * @returns {Object} API configuration
 */
export const getOpenAIConfig = (extractionType, priority = "standard") => {
    //validate extraction type
    const validExtractionTypes = ['demographics', 'medical_history', 'symptoms', 'vitals'];
    if (!validExtractionTypes.includes(extractionType)) {
        console.warn(`Invalid extraction type: ${extractionType}. Defaulting to 'demographics'.`);
        extractionType = 'demographics';
    }

    //validate priority
    const validPriorities = ['urgent', 'standard', 'batch'];
    if (!validPriorities.includes(priority)) {
        console.warn(`Invalid priority: ${priority}. Defaulting to 'standard'.`);
        priority = 'standard';
    }

    //return config based on extraction type and priority
    const extractionConfig = {
        demographics: {
            model: 'gpt-4o',
            max_tokens: 1000,
            temperature: 0.1,
            timeout: 30000
        },
        medical_history: {
            model: 'gpt-4o',
            max_tokens: 2000,
            temperature: 0.2,
            timeout: 45000
        },
        symptoms: {
            model: 'gpt-4o',
            max_tokens: 1500,
            temperature: 0.15,
            timeout: 40000
        },
        vitals: {
            model: 'gpt-4o',
            max_tokens: 800,
            temperature: 0.1,
            timeout: 25000
        }
    };

    //default to standard if invalid extraction type or priority
    const priorityAdjustments = {
        urgent: {
            timeout_mulitplier: 0.7,
            model_upgrade: true
        },
        standard: {
            timeout_mulitplier: 1.0,
            model_upgrade: false
        },
        batch: {
            timeout_mulitplier: 1.5,
            model_downgrade: true
        }
    };

    //get base config
    let config = { ...extractionConfig[extractionType] };
    const priorityConfig = priorityAdjustments[priority];

    //Apply adjustments
    config.timeout = Math.round(config.timeout * priorityConfig.timeout_mulitplier);

    //Model adjustment
    if (priority === 'urgent' && priorityConfig.model_upgrade) {
        if (config.model === 'gpt-4o-mini') {
            config.model = 'gpt-4o';
        }
    } else if (priority === 'batch' && priorityConfig.model_downgrade) {
        if (config.model === 'gpt-4o') {
            config.model = 'gpt-4o-mini';
            config.max_tokens = Math.min(config.max_tokens, 1000);
        }
    }

    //additional configs
    config.extractionType = extractionType;
    config.priority = priority;
    config.timestamp = new Date().toISOString();

    return config;
};

/**
 * Get rate limits configuration for OpenAI API calls
 * @param {string} userRole - Role of the user
 * @returns {Object} rate limits configuration
 */
export const getRateLimitsConfig = (userRole) => {
    //validate user role
    const validRoles = ['doctor', 'nurses', 'admin', 'system'];
    if (!validRoles.includes(userRole)) {
        console.warn(`Invalid user role: ${userRole}. Defaulting to 'admin'.`);
        userRole = 'admin';
    }

    //return config based on user role
    const roleConfigs = {
        doctor: {
            maxTokensPerMinute: 50000,
            maxRequestsPerMinute: 100,
            retryDelay: 1000,
            description: 'High priority and detailed medical analysis'
        },
        nurses: {
            maxTokensPerMinute: 30000,
            maxRequestsPerMinute: 60,
            retryDelay: 1500,
            description: 'Basic medical analysis and patient monitoring'
        },
        admin: {
            maxTokensPerMinute: 40000,
            maxRequestsPerMinute: 80,
            retryDelay: 1000,
            description: 'System admin and reporting'
        },
        system: {
            maxTokensPerMinute: 20000,
            maxRequestsPerMinute: 40,
            retryDelay: 2000,
            description: 'Automated system processes'
        }
    };

    const rateLimitsConfig = roleConfigs[userRole];

    if (!rateLimitsConfig) {
        throw new Error(`Rate limit configuration not found for role: ${userRole}`);
    }

    //return config with additional properties
    return {
        ...rateLimitsConfig,
        userRole,
        configuredAt: new Date().toISOString(),
        maxRetries: 3,
        backoffStrategy: 1.5,
    };
};

// API endpoints
export const API_ENDPOINTS = {
    CHAT_COMPLETIONS: 'https://api.openai.com/v1/chat/completions',
    MODELS: 'https://api.openai.com/v1/models',
    USAGE: 'https://api.openai.com/v1/usage',
};

// Model configurations
export const MODEL_CONFIG = {
    // GPT-4o - Primary model for complex medical tasks
    'gpt-4o': {
        name: 'gpt-4o',
        max_tokens: 4096,
        context_window: 128000,
        cost_per_input_token: 0.000005,   // $0.005 per 1K tokens
        cost_per_output_token: 0.000015,  // $0.015 per 1K tokens
        capabilities: ['complex_reasoning', 'medical_terminology', 'multi_step_analysis'],
        recommended_for: ['medical_history', 'symptoms', 'complex_diagnoses']
    },

    // GPT-4o-mini - Cost-effective model for simpler tasks
    'gpt-4o-mini': {
        name: 'gpt-4o-mini',
        max_tokens: 16384,
        context_window: 128000,
        cost_per_input_token: 0.00000015,  // $0.00015 per 1K tokens
        cost_per_output_token: 0.0000006,   // $0.0006 per 1K tokens
        capabilities: ['basic_extraction', 'structured_data', 'simple_reasoning'],
        recommended_for: ['demographics', 'vitals', 'basic_information']
    }
};

/**
 * Get model configuration by name
 * @param {string} name - Name of the model
 * @returns {Object} model configuration
 * @throws {Error} if model not found
 */
export const getModelConfig = (name) => {
    const config = MODEL_CONFIG[name];
    if (!config) {
        throw new Error(`Model configuration not found for name: ${name}`);
    }
    return config;
};

/**
 * Calculate cost of an API call        
 * @param {string} model - Name of the model
 * @param {number} inputTokens - Number of input tokens
 * @param {number} outputTokens - Number of output tokens
 * @returns {Object} cost details
 */
export const calculateCost = (model, inputTokens, outputTokens) => {
    const modelConfig = getModelConfig(model);

    const inputCost = inputTokens * modelConfig.cost_per_input_token;
    const outputCost = outputTokens * modelConfig.cost_per_output_token;
    const totalCost = inputCost + outputCost;

    return {
        model: model,
        inputTokens,
        outputTokens,
        inputCost: Math.round(inputCost * 100000) / 100000,
        outputCost: Math.round(outputCost * 100000) / 100000,
        totalCost: Math.round(totalCost * 100000) / 100000,
        currency: 'USD',
    };
};

/**
 * Get default API configuration for quick setup
 * @returns {Object} default API configuration
 */
export const getDefaultConfig = () => {
    return getOpenAIConfig('medical_history', 'standard');
};

/**
 * Get default rate limits configuration for quick setup
 * @returns {Object} default rate limits configuration
 */
export const getDefaultRateLimitsConfig = () => {
    return getRateLimitsConfig('admin');
};

// Skeletal implementations for future expansion

/**
 * Check if current usage is within rate limits
 * @param {string} userRole - Role of the user
 * @param {Object} usage - Current usage statistics
 * @returns {boolean} true if within limits, false otherwise
 */
export const checkRateLimits = (userRole, usage) => {
    console.log("Checking rate limits");
    return true;
};

/**
 * Track API usage for billing and quota management
 * @param {Object} usageData - Usage data to track
 */
export const trackAPIUsage = (usageData) => {
    // Skeletal implementation
    console.log("Tracking API usage", usageData);
};

/**
 * Get recommended model for specific clinical task
 * @param {string} task - Clinical task
 * @param {Object} options - Additional options
 * @returns {string} recommended model name
 */
export const getRecommendedModel = (task, options = {}) => {
    // Skeletal implementation
    console.log("Getting recommended model");
    return 'gpt-4o';
};
