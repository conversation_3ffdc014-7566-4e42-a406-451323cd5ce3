const { OpenAIService, OpenAIAPIError } = require('../collection/openAIService.service');

describe('OpenAI Collection Service', () => {
    let openAIService;

    beforeEach(() => {
        openAIService = new OpenAIService({
            apiKey: 'sk-test-key'
        });

        // Mock the API call
        openAIService.callOpenAI = jest.fn();
    });

    test('extracts clinical data successfully', async () => {
        const mockResponse = {
            choices: [{
                message: {
                    content: JSON.stringify({
                        chiefComplaint: "chest pain",
                        currentSymptoms: [{
                            symptom: "chest pain",
                            severity: "6"
                        }]
                    })
                }
            }]
        };

        openAIService.callOpenAI.mockResolvedValue(mockResponse);

        const result = await openAIService.extractClinicalData("test", "symptoms");
        expect(result).toHaveProperty('chiefComplaint');
        expect(result.currentSymptoms[0].symptom).toBe('chest pain');
    });

    test('handles API errors', async () => {
        const errorMessage = 'API Error';
        openAIService.callOpenAI.mockRejectedValue(new Error(errorMessage));

        await expect(openAIService.extractClinicalData("test", "symptoms"))
            .rejects.toThrow(OpenAIAPIError);
    });

    test('validates extraction type', async () => {
        await expect(openAIService.extractClinicalData("test", "invalid_type"))
            .rejects.toThrow('Invalid extraction type');
    });
});
