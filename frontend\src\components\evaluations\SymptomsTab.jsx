import React from 'react';
import { TrendingUp, AlertCircle, Check } from 'lucide-react';

const SymptomsTab = ({ symptoms, onChange }) => {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-6">Symptoms Assessment</h3>
      
      <div className="space-y-6">
        {/* Angina Symptoms */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Angina Symptoms</h4>
          <div className="space-y-4">
            <div>
              <label className="block text-xs text-gray-500 mb-1">Frequency</label>
              <select 
                value={symptoms.anginaFrequency}
                onChange={(e) => onChange('anginaFrequency', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Select Frequency</option>
                <option value="0">None</option>
                <option value="1">Rarely (1-2/week)</option>
                <option value="2">Sometimes (3-4/week)</option>
                <option value="3">Often (5-6/week)</option>
                <option value="4">Daily</option>
                <option value="5">Multiple times daily</option>
              </select>
            </div>
            
            <div>
              <label className="block text-xs text-gray-500 mb-1">Severity</label>
              <select 
                value={symptoms.anginaSeverity}
                onChange={(e) => onChange('anginaSeverity', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Select Severity</option>
                <option value="0">None</option>
                <option value="1">Mild</option>
                <option value="2">Moderate</option>
                <option value="3">Severe</option>
                <option value="4">Very Severe</option>
              </select>
            </div>
            
            <div className="p-2 bg-green-50 rounded border border-green-200">
              <div className="flex items-center">
                <Check size={16} className="text-green-600 mr-1" />
                <span className="text-sm text-green-800">
                  No angina episodes reported in the last 4 weeks
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Other Cardiac Symptoms */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Other Cardiac Symptoms</h4>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-1">Shortness of Breath</label>
                <select 
                  value={symptoms.shortnessOfBreath}
                  onChange={(e) => onChange('shortnessOfBreath', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">Select Severity</option>
                  <option value="0">None</option>
                  <option value="1">Mild</option>
                  <option value="2">Moderate</option>
                  <option value="3">Severe</option>
                </select>
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">Fatigue</label>
                <select 
                  value={symptoms.fatigue}
                  onChange={(e) => onChange('fatigue', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">Select Severity</option>
                  <option value="0">None</option>
                  <option value="1">Mild</option>
                  <option value="2">Moderate</option>
                  <option value="3">Severe</option>
                </select>
              </div>
            </div>
            
            <div className="p-2 bg-green-50 rounded border border-green-200">
              <div className="flex items-center">
                <TrendingUp size={16} className="text-green-600 mr-1" />
                <span className="text-sm text-green-800">
                  Significant improvement in exercise tolerance
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Medication Side Effects */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Medication Side Effects</h4>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-1">Headache</label>
                <select 
                  value={symptoms.headache}
                  onChange={(e) => onChange('headache', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">Select Severity</option>
                  <option value="0">None</option>
                  <option value="1">Mild</option>
                  <option value="2">Moderate</option>
                  <option value="3">Severe</option>
                </select>
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">Dizziness</label>
                <select 
                  value={symptoms.dizziness}
                  onChange={(e) => onChange('dizziness', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">Select Severity</option>
                  <option value="0">None</option>
                  <option value="1">Mild</option>
                  <option value="2">Moderate</option>
                  <option value="3">Severe</option>
                </select>
              </div>
            </div>
            
            <div className="p-2 bg-yellow-50 rounded border border-yellow-200">
              <div className="flex items-center">
                <AlertCircle size={16} className="text-yellow-600 mr-1" />
                <span className="text-sm text-yellow-800">
                  Mild headache reported, possibly related to medication
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Symptoms Summary */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-blue-700 mb-2">Symptoms Summary</h4>
          <div className="flex items-center justify-between">
            <div className="text-2xl font-bold text-blue-800">Significant Improvement</div>
            <div className="flex items-center text-green-600 text-sm">
              <span className="mr-1">-90%</span>
              <TrendingUp size={14} />
            </div>
          </div>
          <div className="text-xs text-gray-500 mt-1">Reduction in symptom frequency and severity</div>
        </div>

        {/* Assessment Notes */}
        <div className="bg-blue-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-700 mb-2">Symptoms Assessment Notes</h4>
          <textarea
            value={symptoms.notes}
            onChange={(e) => onChange('notes', e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-md h-24"
            placeholder="Enter notes about symptoms assessment..."
          ></textarea>
        </div>
      </div>
    </div>
  );
};

export default SymptomsTab; 