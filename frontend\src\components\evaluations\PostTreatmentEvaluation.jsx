import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { usePDF } from 'react-to-pdf';
import { useAuth } from '../../context/AuthContext';
import { localStorageService } from '../../services/localStorageService';
import { 
  ArrowLeft, 
  Calendar, 
  BarChart2, 
  Download, 
  FileText, 
  Heart, 
  Info, 
  Printer, 
  Star, 
  TrendingUp, 
  User, 
  Clock, 
  AlertCircle,
  Check 
} from 'lucide-react';
import VitalsTab from './VitalsTab';
import FunctionalTestsTab from './FunctionalTestsTab';
import QualityOfLifeTab from './QualityOfLifeTab';
import SymptomsTab from './SymptomsTab';
import BiomarkersTab from './BiomarkersTab';

const PostTreatmentEvaluation = () => {
  const { search } = useLocation();
  const navigate = useNavigate();
  const { medplum, currentUser } = useAuth();
  const patientId = new URLSearchParams(search).get('patientId');
  const [activeTab, setActiveTab] = useState('vitals');
  const [patient, setPatient] = useState(null);
  const [loading, setLoading] = useState(true);
  const [evaluation, setEvaluation] = useState({
    vitals: {
      height: '',
      weight: '',
      bmi: '',
      systolic: '',
      diastolic: '',
      heartRate: '',
      spo2: '',
      notes: ''
    },
    functionalTests: {
      nyhaClass: '',
      ccsClass: '',
      lvef: '',
      walkDistance: '',
      notes: ''
    },
    qualityOfLife: {
      energyLevel: '',
      sleepQuality: '',
      mood: '',
      anxiety: '',
      socialActivity: '',
      workStatus: '',
      notes: ''
    },
    symptoms: {
      anginaFrequency: '',
      anginaSeverity: '',
      shortnessOfBreath: '',
      fatigue: '',
      headache: '',
      dizziness: '',
      notes: ''
    },
    biomarkers: {
      bnp: '',
      troponin: '',
      crp: '',
      esr: '',
      totalCholesterol: '',
      ldl: '',
      hdl: '',
      triglycerides: '',
      notes: ''
    },
    progress: {
      vitals: 'completed',
      functionalTests: 'completed',
      qualityOfLife: 'completed',
      symptoms: 'in_progress',
      biomarkers: 'pending'
    }
  });

  const { toPDF, targetRef } = usePDF({
    filename: `Post-Treatment_Evaluation_${patient?.name?.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`,
    page: { format: 'a4', margin: 20 }
  });

  useEffect(() => {
    const fetchPatientData = async () => {
      try {
        setLoading(true);
        const foundPatient = localStorageService.getPatientById(patientId);
        
        if (foundPatient) {
          setPatient(foundPatient);
          // Pre-fill evaluation data from patient's current data
          setEvaluation(prev => ({
            ...prev,
            vitals: {
              ...prev.vitals,
              systolic: foundPatient.outcomeMeasures?.current?.bloodPressure?.systolic || '',
              diastolic: foundPatient.outcomeMeasures?.current?.bloodPressure?.diastolic || '',
            },
            functionalTests: {
              ...prev.functionalTests,
              ccsClass: foundPatient.outcomeMeasures?.current?.anginaClass || '',
              lvef: foundPatient.outcomeMeasures?.current?.lvef || '',
              walkDistance: foundPatient.outcomeMeasures?.current?.walkDistance || ''
            }
          }));
        } else {
          console.error('Patient not found');
          navigate('/patients');
        }
        setLoading(false);
      } catch (error) {
        console.error('Error fetching patient data:', error);
        setLoading(false);
      }
    };
    
    fetchPatientData();
  }, [patientId, navigate]);

  const handleInputChange = (section, field, value) => {
    setEvaluation(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const calculateProgress = () => {
    const progressValues = {
      completed: 1,
      in_progress: 0.7,
      pending: 0
    };

    const sections = Object.values(evaluation.progress);
    const total = sections.reduce((acc, status) => acc + progressValues[status], 0);
    return Math.round((total / sections.length) * 100);
  };

  const handlePrint = () => {
    window.print();
  };

  const handleExportPDF = () => {
    toPDF();
  };

  const handleCompleteEvaluation = async () => {
    try {
      // Create evaluation data
      const evaluationData = {
        id: `eval_${Date.now()}`,
        date: new Date().toISOString(),
        author: currentUser?.name || 'System',
        summary: 'Post-treatment evaluation completed',
        patientId: patientId,
        data: evaluation
      };

      // Save evaluation using the service
      localStorageService.addEvaluation(patientId, evaluationData);

      // Add a note about the evaluation
      localStorageService.addNoteToPatient(patientId, {
        author: currentUser?.name || 'System',
        content: 'Post-treatment evaluation completed'
      });

      // Update patient's current outcome measures
      const updatedOutcomeMeasures = {
        ...patient.outcomeMeasures,
        current: {
          date: new Date().toISOString().split('T')[0],
          bloodPressure: {
            systolic: evaluation.vitals.systolic,
            diastolic: evaluation.vitals.diastolic
          },
          anginaClass: evaluation.functionalTests.ccsClass,
          lvef: evaluation.functionalTests.lvef,
          walkDistance: evaluation.functionalTests.walkDistance,
          qualityOfLife: evaluation.qualityOfLife.energyLevel
        }
      };

      // Update the patient
      localStorageService.updatePatient(patientId, {
        outcomeMeasures: updatedOutcomeMeasures
      });

      // Notify parent window if it exists
      if (window.opener && window.opener.handleNewEvaluation) {
        window.opener.handleNewEvaluation(evaluationData);
      }

      alert('Evaluation completed successfully!');
      navigate(`/patients/${patientId}`);
    } catch (error) {
      console.error('Error completing evaluation:', error);
      alert('Failed to complete evaluation. Please try again.');
    }
  };

  if (loading || !patient) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 py-4 px-6 no-print">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <button 
              onClick={() => navigate(-1)}
              className="mr-4 text-gray-500 hover:text-gray-700"
            >
              <ArrowLeft size={20} />
            </button>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">Post-Treatment Evaluation</h1>
              <div className="flex items-center mt-1">
                <div className="flex items-center text-sm text-gray-500 mr-4">
                  <User size={16} className="mr-1" />
                  <span>{patient.name}</span>
                </div>
                <div className="flex items-center text-sm text-gray-500 mr-4">
                  <Clock size={16} className="mr-1" />
                  <span>35/35 Sessions Completed</span>
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar size={16} className="mr-1" />
                  <span>Evaluation Date: {new Date().toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex">
            <button 
              onClick={handlePrint}
              className="flex items-center bg-white border border-gray-300 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 mr-3"
            >
              <Printer size={16} className="mr-1" />
              Print
            </button>
            <button 
              onClick={handleExportPDF}
              className="flex items-center bg-white border border-gray-300 rounded-lg px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 mr-3"
            >
              <Download size={16} className="mr-1" />
              Export PDF
            </button>
            <button 
              onClick={handleCompleteEvaluation}
              className="flex items-center bg-blue-600 hover:bg-blue-700 text-white rounded-lg px-4 py-2 text-sm font-medium"
            >
              <FileText size={16} className="mr-1" />
              Complete Evaluation
            </button>
          </div>
        </div>
      </header>

      {/* Main content */}
      <div className="flex flex-1 overflow-hidden">
        <main className="flex-1 overflow-auto" ref={targetRef}>
          {/* Print Header */}
          <div className="hidden print-only bg-white p-6 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-2xl font-semibold text-gray-900">Post-Treatment Evaluation</h1>
                <div className="mt-2 text-sm text-gray-500">
                  <div>Patient: {patient.name}</div>
                  <div>Evaluation Date: {new Date().toLocaleDateString()}</div>
                </div>
              </div>
              <div className="text-sm text-gray-500">
                <div>Generated on: {new Date().toLocaleString()}</div>
              </div>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="bg-white border-b border-gray-200">
            <nav className="flex px-6">
              <button 
                onClick={() => setActiveTab('vitals')}
                className={`px-4 py-3 font-medium text-sm border-b-2 ${
                  activeTab === 'vitals' 
                    ? 'border-blue-600 text-blue-600' 
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Vitals
              </button>
              <button 
                onClick={() => setActiveTab('functional-tests')}
                className={`ml-8 px-4 py-3 font-medium text-sm border-b-2 ${
                  activeTab === 'functional-tests' 
                    ? 'border-blue-600 text-blue-600' 
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Functional Tests
              </button>
              <button 
                onClick={() => setActiveTab('qol')}
                className={`ml-8 px-4 py-3 font-medium text-sm border-b-2 ${
                  activeTab === 'qol' 
                    ? 'border-blue-600 text-blue-600' 
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Quality of Life
              </button>
              <button 
                onClick={() => setActiveTab('symptoms')}
                className={`ml-8 px-4 py-3 font-medium text-sm border-b-2 ${
                  activeTab === 'symptoms' 
                    ? 'border-blue-600 text-blue-600' 
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Symptoms
              </button>
              <button 
                onClick={() => setActiveTab('biomarkers')}
                className={`ml-8 px-4 py-3 font-medium text-sm border-b-2 ${
                  activeTab === 'biomarkers' 
                    ? 'border-blue-600 text-blue-600' 
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Biomarkers
              </button>
            </nav>
          </div>

          {/* Tab content */}
          <div className="p-6">
            {/* Tab content */}
            {activeTab === 'vitals' && (
              <VitalsTab 
                vitals={evaluation.vitals}
                onChange={(field, value) => handleInputChange('vitals', field, value)}
              />
            )}
            
            {activeTab === 'functional-tests' && (
              <FunctionalTestsTab
                functionalTests={evaluation.functionalTests}
                onChange={(field, value) => handleInputChange('functionalTests', field, value)}
              />
            )}
            
            {activeTab === 'qol' && (
              <QualityOfLifeTab
                qualityOfLife={evaluation.qualityOfLife}
                onChange={(field, value) => handleInputChange('qualityOfLife', field, value)}
              />
            )}
            
            {activeTab === 'symptoms' && (
              <SymptomsTab
                symptoms={evaluation.symptoms}
                onChange={(field, value) => handleInputChange('symptoms', field, value)}
              />
            )}
            
            {activeTab === 'biomarkers' && (
              <BiomarkersTab
                biomarkers={evaluation.biomarkers}
                onChange={(field, value) => handleInputChange('biomarkers', field, value)}
              />
            )}
          </div>
        </main>

        {/* Right sidebar */}
        <aside className="w-80 bg-white border-l border-gray-200 overflow-y-auto no-print">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Evaluation Progress</h2>
            
            <div className="space-y-3">
              {Object.entries(evaluation.progress).map(([key, status]) => (
                <div key={key}>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-600">
                      {key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
                    </span>
                    <span className={`font-medium ${
                      status === 'completed' ? 'text-green-600' :
                      status === 'in_progress' ? 'text-blue-600' :
                      'text-gray-600'
                    }`}>
                      {status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        status === 'completed' ? 'bg-green-500' :
                        status === 'in_progress' ? 'bg-blue-500' :
                        'bg-gray-500'
                      }`}
                      style={{ 
                        width: `${
                          status === 'completed' ? '100' :
                          status === 'in_progress' ? '70' :
                          '0'
                        }%` 
                      }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-6">
              <div className="flex justify-between text-sm font-medium mb-1">
                <span className="text-gray-700">Overall Progress</span>
                <span className="text-blue-600">{calculateProgress()}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div 
                  className="bg-blue-600 h-2.5 rounded-full" 
                  style={{ width: `${calculateProgress()}%` }}
                ></div>
              </div>
            </div>
          </div>
        </aside>
      </div>
    </div>
  );
};

export default PostTreatmentEvaluation; 