/**
 * CLINICAL DATA SCHEMAS - JSON SCHEMA VALIDATION TEMPLATES
 * 
 * WHY CLINICAL DATA SCHEMAS ARE CRITICAL:
 * 
 * 1. DATA VALIDATION & INTEGRITY:
 *    - Ensures extracted data matches expected format before entering EHR
 *    - Prevents incorrect data entry and maintains data quality
 *    - Validates data types (strings, numbers, dates) to prevent errors
 * 
 * 2. EHR INTEGRATION:
 *    - Populates standardized fields in EHR systems
 *    - Improves data consistency and interoperability
 *    - Follows healthcare standards (ICD-10, LOINC, SNOMED CT)
 *    - Supports data exchange between different healthcare providers
 * 
 * 3. CLINICAL DECISION MAKING:
 *    - Validates critical medical data (e.g. blood pressure, heart rate)
 *    - Prevents dangerous data entry errors (e.g. incorrect dosages)
 *    - Ensures medical allergies are properly formatted for alerts
 *    - Validates dates to prevent scheduling conflicts
 * 
 * 4. LEGAL AND COMPLIANCE:
 *    - Ensures data meets regulatory standards (HIPAA, FDA)
 *    - Protects patient privacy and data security
 *    - Facilitates accurate billing and insurance processing
 *    - Maintains audit trail through structured validation logs
 */

const CLINICAL_DATA_SCHEMA = {
    // Direct access by extraction type string
    DEMOGRAPHICS_SCHEMA: {
        type: 'object',
        properties: {
            firstName: {
                type: 'string',
                minLength: 1,
                maxLength: 50,
                description: "Patient's first name"
            },
            middleName: {
                type: 'string',
                maxLength: 50,
                description: "Patient's middle name (optional)"
            },
            lastName: {
                type: 'string',
                minLength: 1,
                maxLength: 50,
                description: "Patient's last name - required"
            },
            dateOfBirth: {
                type: 'string',
                pattern: '^\\d{4}-\\d{2}-\\d{2}$',
                description: 'Date of birth in YYYY-MM-DD format'
            },
            gender: {
                type: 'string',
                enum: ['Male', 'Female', 'Other', 'Prefer not to say', 'Unknown'],
                description: 'Patient gender - standardized values only'
            },
            phoneNumber: {
                type: 'string',
                description: 'Phone number with country code (optional)'
            },
            email: {
                type: 'string',
                format: 'email',
                description: 'Valid email address'
            },
            address: {
                type: 'object',
                properties: {
                    street: {
                        type: 'string',
                        minLength: 1,
                        maxLength: 100,
                        description: 'Street address'
                    },
                    city: {
                        type: 'string',
                        maxLength: 50,
                        description: 'City name'
                    },
                    state: {
                        type: 'string',
                        maxLength: 50,
                        description: 'State/Province'
                    },
                    zipCode: {
                        type: 'string',
                        pattern: '^[A-Z0-9\\s\\-]{3,10}$',
                        description: 'Postal/ZIP code'
                    },
                    country: {
                        type: 'string',
                        default: 'Canada',
                        description: 'Country'
                    }
                },
                required: ['street', 'city', 'state', 'zipCode']
            },
            emergencyContact: {
                type: 'object',
                properties: {
                    name: {
                        type: 'string',
                        minLength: 1,
                        maxLength: 100,
                        description: 'Emergency contact full name'
                    },
                    relationship: {
                        type: 'string',
                        enum: ['Spouse', 'Parent', 'Child', 'Sibling', 'Friend', 'Other'],
                        description: 'Relationship to patient'
                    },
                    phoneNumber: {
                        type: 'string',
                        pattern: '^[\\+]?[1-9]?[0-9]{7,15}$',
                        description: 'Emergency contact phone number'
                    }
                },
                required: ['name', 'relationship', 'phoneNumber']
            },
            insurance: {
                type: 'object',
                properties: {
                    provider: {
                        type: 'string',
                        maxLength: 100,
                        description: 'Insurance company name'
                    },
                    memberId: {
                        type: 'string',
                        maxLength: 50,
                        description: 'Member/Policy ID number'
                    },
                    groupNumber: {
                        type: 'string',
                        maxLength: 50,
                        description: 'Group number'
                    },
                    policyType: {
                        type: 'string',
                        enum: ['HMO', 'PPO', 'POS', 'EPO', 'Medicare', 'Medicaid', 'Private', 'Other', 'Unknown'],
                        description: 'Type of insurance policy'
                    }
                }
            },
            referringPhysician: {
                type: 'object',
                properties: {
                    name: {
                        type: 'string',
                        maxLength: 100,
                        description: 'Referring physician name'
                    },
                    specialty: {
                        type: 'string',
                        maxLength: 100,
                        description: 'Medical specialty'
                    },
                    contactInfo: {
                        type: 'string',
                        maxLength: 200,
                        description: 'Phone/email/address'
                    }
                }
            }
        },
        required: ['firstName', 'lastName', 'dateOfBirth', 'gender'],
        additionalProperties: false
    },

    // MEDICAL HISTORY SCHEMA - Comprehensive medical background
    MEDICAL_HISTORY_SCHEMA: {
        type: 'object',
        properties: {
            diagnoses: {
                type: 'array',
                description: 'List of medical diagnoses',
                items: {
                    type: 'object',
                    properties: {
                        condition: {
                            type: 'string',
                            minLength: 1,
                            maxLength: 200,
                            description: 'Standardized condition name'
                        },
                        icd10Code: {
                            type: 'string',
                            pattern: "^[A-Z][0-9]{2}\\.?[0-9A-Z]*$",
                            description: 'Valid ICD-10 diagnosis code'
                        },
                        status: {
                            type: 'string',
                            enum: ['Active', 'Resolved', 'Chronic', 'Inactive', 'Unknown'],
                            description: 'Current status of condition'
                        },
                        onsetDate: {
                            type: 'string',
                            pattern: '^\\d{4}-\\d{2}-\\d{2}|Unknown|Approximately \\d{4}$',
                            description: 'Date of onset (YYYY-MM-DD) or approximation'
                        },
                        severity: {
                            type: 'string',
                            enum: ['Mild', 'Moderate', 'Severe', 'Unknown'],
                            description: 'Severity level'
                        }
                    },
                    required: ['condition', 'status'],
                    additionalProperties: false
                }
            },
            medications: {
                type: 'array',
                description: 'List of current medications',
                items: {
                    type: 'object',
                    properties: {
                        name: {
                            type: 'string',
                            minLength: 1,
                            maxLength: 100,
                            description: 'Generic medication name preferred'
                        },
                        dosage: {
                            type: 'string',
                            pattern: "^[0-9]+(\\.[0-9]+)?\\s*(mg|g|ml|units?|mcg|µg)$|Unknown",
                            description: 'Dosage with standard units (e.g., 100mg, 5ml)'
                        },
                        frequency: {
                            type: 'string',
                            enum: [
                                'Once daily', 'Twice daily', 'Three times daily', 'Four times daily',
                                'Every 4 hours', 'Every 6 hours', 'Every 8 hours', 'Every 12 hours',
                                'As needed', 'Weekly', 'Monthly', 'Other', 'Unknown'
                            ],
                            description: 'Standardized frequency'
                        },
                        route: {
                            type: 'string',
                            enum: ['Oral', 'IV', 'IM', 'Subcutaneous', 'Topical', 'Nasal', 'Rectal', 'Inhaled', 'Other', 'Unknown'],
                            description: 'Route of administration'
                        },
                        startDate: {
                            type: 'string',
                            pattern: '^\\d{4}-\\d{2}-\\d{2}|Unknown$',
                            description: 'Start date (YYYY-MM-DD)'
                        },
                        prescribingPhysician: {
                            type: 'string',
                            maxLength: 100,
                            description: 'Prescribing physician name'
                        }
                    },
                    required: ['name'],
                    additionalProperties: false
                }
            },
            allergies: {
                type: 'array',
                description: 'List of allergies',
                items: {
                    type: 'object',
                    properties: {
                        allergen: {
                            type: 'string',
                            minLength: 1,
                            maxLength: 100,
                            description: 'Allergen name'
                        },
                        allergenType: {
                            type: 'string',
                            enum: ['Medication', 'Food', 'Environmental', 'Other', 'Unknown'],
                            description: 'Type of allergen'
                        },
                        reaction: {
                            type: 'string',
                            minLength: 1,
                            maxLength: 200,
                            description: 'Allergic reaction description'
                        },
                        severity: {
                            type: 'string',
                            enum: ['Mild', 'Moderate', 'Severe', 'Life-threatening', 'Unknown'],
                            description: 'Severity of reaction'
                        }
                    },
                    required: ['allergen'],
                    additionalProperties: false
                }
            },
            surgeries: {
                type: 'array',
                description: 'List of surgeries',
                items: {
                    type: 'object',
                    properties: {
                        procedure: {
                            type: 'string',
                            minLength: 1,
                            maxLength: 200,
                            description: 'Surgical procedure name'
                        },
                        date: {
                            type: 'string',
                            pattern: '^\\d{4}-\\d{2}-\\d{2}|Unknown$',
                            description: 'Surgery date (YYYY-MM-DD)'
                        },
                        hospital: {
                            type: 'string',
                            maxLength: 100,
                            description: 'Hospital/facility name'
                        },
                        surgeon: {
                            type: 'string',
                            maxLength: 100,
                            description: 'Surgeon name'
                        },
                        complications: {
                            type: 'string',
                            maxLength: 500,
                            description: 'Any complications'
                        }
                    },
                    required: ['procedure'],
                    additionalProperties: false
                }
            },
            familyHistory: {
                type: 'array',
                description: 'List of family medical history',
                items: {
                    type: 'object',
                    properties: {
                        relation: {
                            type: 'string',
                            enum: ['Father', 'Mother', 'Sibling', 'Child', 'Grandparent', 'Other'],
                            description: 'Family relationship'
                        },
                        condition: {
                            type: 'string',
                            minLength: 1,
                            maxLength: 100,
                            description: 'Medical condition'
                        },
                        ageOfOnset: {
                            type: 'number',
                            minimum: 0,
                            maximum: 120,
                            description: 'Age when condition developed'
                        }
                    },
                    required: ['relation', 'condition'],
                    additionalProperties: false
                }
            }
        },
        required: ['diagnoses', 'medications', 'allergies', 'surgeries', 'familyHistory'],
        additionalProperties: false
    },

    // SYMPTOMS SCHEMA - Current clinical presentation
    SYMPTOMS_SCHEMA: {
        type: 'object',
        properties: {
            chiefComplaint: {
                type: 'string',
                minLength: 1,
                maxLength: 500,
                description: 'Primary reason for visit in patient\'s words'
            },
            currentSymptoms: {
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        symptom: {
                            type: 'string',
                            minLength: 1,
                            maxLength: 100,
                            description: 'Standardized symptom name'
                        },
                        onset: {
                            type: 'string',
                            maxLength: 100,
                            description: 'When symptom started'
                        },
                        duration: {
                            type: 'string',
                            maxLength: 100,
                            description: 'How long symptom has lasted'
                        },
                        severity: {
                            type: 'string',
                            enum: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'Unknown'],
                            description: 'Pain/severity scale 1-10 (1=mild, 10=severe) or Unknown'
                        },
                        location: {
                            type: 'string',
                            maxLength: 100,
                            description: 'Where symptom occurs'
                        },
                        quality: {
                            type: 'string',
                            maxLength: 100,
                            description: 'Description of symptom (sharp, dull, etc.)'
                        },
                        aggravatingFactors: {
                            type: 'array',
                            items: { type: 'string', maxLength: 100 },
                            description: 'What makes symptom worse'
                        },
                        alleviatingFactors: {
                            type: 'array',
                            items: { type: 'string', maxLength: 100 },
                            description: 'What makes symptom better'
                        }
                    },
                    required: ['symptom'],
                    additionalProperties: false
                }
            },
            functionalStatus: {
                type: 'object',
                properties: {
                    nyhaClass: {
                        type: 'string',
                        enum: ['I', 'II', 'III', 'IV', 'Unknown'],
                        description: 'New York Heart Association Classification'
                    },
                    ccsClass: {
                        type: 'string',
                        enum: ['I', 'II', 'III', 'IV', 'Unknown'],
                        description: 'Canadian Cardiovascular Society Angina Classification'
                    },
                    exerciseTolerance: {
                        type: 'string',
                        maxLength: 200,
                        description: 'Exercise capacity description'
                    },
                    qualityOfLife: {
                        type: 'string',
                        enum: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'Unknown'],
                        description: 'Quality of life scale 1-10 (1=poor, 10=excellent) or Unknown'
                    }
                },
                additionalProperties: false
            }
        },
        required: ['chiefComplaint'],
        additionalProperties: false
    },

    // VITALS SCHEMA - Objective clinical measurements
    VITALS_SCHEMA: {
        type: 'object',
        properties: {
            bloodPressure: {
                type: 'object',
                properties: {
                    systolic: {
                        type: 'number',
                        minimum: 60,
                        maximum: 250,
                        description: 'Systolic blood pressure in mmHg'
                    },
                    diastolic: {
                        type: 'number',
                        minimum: 30,
                        maximum: 150,
                        description: 'Diastolic blood pressure in mmHg'
                    },
                    unit: {
                        type: 'string',
                        enum: ['mmHg'],
                        default: 'mmHg'
                    }
                },
                required: ['systolic', 'diastolic'],
                additionalProperties: false
            },
            heartRate: {
                type: 'object',
                properties: {
                    value: {
                        type: 'number',
                        minimum: 30,
                        maximum: 200,
                        description: 'Heart rate in beats per minute'
                    },
                    unit: {
                        type: 'string',
                        enum: ['bpm'],
                        default: 'bpm'
                    }
                },
                required: ['value'],
                additionalProperties: false
            },
            respiratoryRate: {
                type: 'object',
                properties: {
                    value: {
                        type: 'number',
                        minimum: 8,
                        maximum: 40,
                        description: 'Respiratory rate per minute'
                    },
                    unit: {
                        type: 'string',
                        enum: ['breaths/min'],
                        default: 'breaths/min'
                    }
                },
                required: ['value'],
                additionalProperties: false
            },
            temperature: {
                type: 'object',
                properties: {
                    value: {
                        type: 'number',
                        minimum: 35,
                        maximum: 42,
                        description: 'Body temperature in Celsius'
                    },
                    unit: {
                        type: 'string',
                        enum: ['°C'],
                        default: '°C'
                    }
                },
                required: ['value'],
                additionalProperties: false
            },
            oxygenSaturation: {
                type: 'object',
                properties: {
                    value: {
                        type: 'number',
                        minimum: 70,
                        maximum: 100,
                        description: 'Oxygen saturation percentage'
                    },
                    unit: {
                        type: 'string',
                        enum: ['%'],
                        default: '%'
                    }
                },
                required: ['value'],
                additionalProperties: false
            },
            anthropometric: {
                type: 'object',
                properties: {
                    height: {
                        type: 'object',
                        properties: {
                            value: {
                                type: 'number',
                                minimum: 100,
                                maximum: 250,
                                description: 'Height in centimeters'
                            },
                            unit: { type: 'string', enum: ['cm'], default: 'cm' }
                        },
                        required: ['value']
                    },
                    weight: {
                        type: 'object',
                        properties: {
                            value: {
                                type: 'number',
                                minimum: 20,
                                maximum: 300,
                                description: 'Weight in kilograms'
                            },
                            unit: { type: 'string', enum: ['kg'], default: 'kg' }
                        },
                        required: ['value']
                    },
                    bmi: {
                        type: 'number',
                        minimum: 10,
                        maximum: 50,
                        description: 'Body Mass Index (calculated)'
                    }
                },
                additionalProperties: false
            },
            cardiacFunction: {
                type: 'object',
                properties: {
                    lvef: {
                        type: 'number',
                        minimum: 10,
                        maximum: 90,
                        description: 'Left Ventricular Ejection Fraction in %'
                    },
                    bnp: {
                        type: 'number',
                        minimum: 5,
                        maximum: 5000,
                        description: 'B-type Natriuretic Peptide in pg/mL'
                    },
                    cardiacOutput: {
                        type: 'number',
                        minimum: 2,
                        maximum: 20,
                        description: 'Cardiac Output in L/min'
                    }
                },
                additionalProperties: false
            }
        },
        additionalProperties: false
    }
};

// MEDICAL STANDARDIZATION MAPPINGS - Data normalization templates
const MEDICAL_STANDARDIZATION = {

    // Common medical condition mappings
    CONDITION_MAPPINGS: {
        // Cardiovascular conditions
        'cad': 'Coronary Artery Disease',
        'coronary artery disease': 'Coronary Artery Disease',
        'chf': 'Congestive Heart Failure',
        'congestive heart failure': 'Congestive Heart Failure',
        'heart failure': 'Heart Failure',
        'hf': 'Heart Failure',
        'mi': 'Myocardial Infarction',
        'myocardial infarction': 'Myocardial Infarction',
        'heart attack': 'Myocardial Infarction',
        'htn': 'Hypertension',
        'hypertension': 'Hypertension',
        'high blood pressure': 'Hypertension',
        'high bp': 'Hypertension',
        'afib': 'Atrial Fibrillation',
        'atrial fibrillation': 'Atrial Fibrillation',
        'a fib': 'Atrial Fibrillation',

        // Metabolic conditions
        'dm': 'Diabetes Mellitus',
        'diabetes': 'Diabetes Mellitus Type 2',
        't2dm': 'Diabetes Mellitus Type 2',
        'type 2 diabetes': 'Diabetes Mellitus Type 2',
        't1dm': 'Diabetes Mellitus Type 1',
        'type 1 diabetes': 'Diabetes Mellitus Type 1',

        // Respiratory conditions
        'copd': 'Chronic Obstructive Pulmonary Disease',
        'chronic obstructive pulmonary disease': 'Chronic Obstructive Pulmonary Disease',
        'asthma': 'Asthma',

        // Vascular conditions
        'pvd': 'Peripheral Vascular Disease',
        'peripheral artery disease': 'Peripheral Artery Disease',
        'pad': 'Peripheral Artery Disease',
        'dvt': 'Deep Vein Thrombosis',
        'pe': 'Pulmonary Embolism',

        // Neurological conditions
        'stroke': 'Cerebrovascular Accident',
        'cva': 'Cerebrovascular Accident',
        'tia': 'Transient Ischemic Attack'
    },

    // ICD-10 code mappings
    ICD10_CODES: {
        'Coronary Artery Disease': 'I25.9',
        'Myocardial Infarction': 'I21.9',
        'Congestive Heart Failure': 'I50.9',
        'Heart Failure': 'I50.9',
        'Hypertension': 'I10',
        'Angina Pectoris': 'I20.9',
        'Atrial Fibrillation': 'I48.91',
        'Diabetes Mellitus Type 2': 'E11.9',
        'Diabetes Mellitus Type 1': 'E10.9',
        'Hyperlipidemia': 'E78.5',
        'Chronic Obstructive Pulmonary Disease': 'J44.9',
        'Asthma': 'J45.9',
        'Peripheral Artery Disease': 'I73.9',
        'Deep Vein Thrombosis': 'I82.90',
        'Pulmonary Embolism': 'I26.9',
        'Cerebrovascular Accident': 'I63.9',
        'Transient Ischemic Attack': 'G93.1'
    },

    // Medication standardization
    MEDICATION_MAPPINGS: {
        // Beta blockers
        'lopressor': 'Metoprolol',
        'toprol': 'Metoprolol',
        'metoprolol': 'Metoprolol',

        // ACE inhibitors
        'lisinopril': 'Lisinopril',
        'prinivil': 'Lisinopril',
        'zestril': 'Lisinopril',

        // Statins
        'lipitor': 'Atorvastatin',
        'atorvastatin': 'Atorvastatin',
        'crestor': 'Rosuvastatin',
        'rosuvastatin': 'Rosuvastatin',

        // Antiplatelet agents
        'plavix': 'Clopidogrel',
        'clopidogrel': 'Clopidogrel',
        'aspirin': 'Acetylsalicylic Acid',
        'asa': 'Acetylsalicylic Acid',

        // Generic categories
        'ace inhibitor': 'ACE Inhibitor',
        'beta blocker': 'Beta Blocker',
        'calcium channel blocker': 'Calcium Channel Blocker',
        'arb': 'Angiotensin Receptor Blocker',
        'statin': 'HMG-CoA Reductase Inhibitor',
        'diuretic': 'Diuretic',
        'water pill': 'Diuretic'
    },

    // Symptom standardization
    SYMPTOM_MAPPINGS: {
        'chest pain': 'Chest Pain',
        'chest discomfort': 'Chest Pain',
        'angina': 'Angina Pectoris',
        'shortness of breath': 'Dyspnea',
        'sob': 'Dyspnea',
        'dyspnea': 'Dyspnea',
        'difficulty breathing': 'Dyspnea',
        'palpitations': 'Palpitations',
        'heart racing': 'Palpitations',
        'irregular heartbeat': 'Palpitations',
        'dizziness': 'Dizziness',
        'lightheadedness': 'Dizziness',
        'fatigue': 'Fatigue',
        'tiredness': 'Fatigue',
        'weakness': 'Fatigue',
        'swelling': 'Edema',
        'edema': 'Edema',
        'fluid retention': 'Edema'
    },

    // Unit standardization
    UNIT_MAPPINGS: {
        'beats per minute': 'bpm',
        'beats/min': 'bpm',
        'per minute': '/min',
        'millimeters of mercury': 'mmHg',
        'mm hg': 'mmHg',
        'mmhg': 'mmHg',
        'celsius': '°C',
        'centigrade': '°C',
        'fahrenheit': '°F',
        'percent': '%',
        'percentage': '%',
        'milligrams': 'mg',
        'grams': 'g',
        'kilograms': 'kg',
        'centimeters': 'cm',
        'meters': 'm'
    }
};

module.exports = {
    DEMOGRAPHICS_SCHEMA: CLINICAL_DATA_SCHEMA.DEMOGRAPHICS_SCHEMA,
    MEDICAL_HISTORY_SCHEMA: CLINICAL_DATA_SCHEMA.MEDICAL_HISTORY_SCHEMA,
    SYMPTOMS_SCHEMA: CLINICAL_DATA_SCHEMA.SYMPTOMS_SCHEMA,
    VITALS_SCHEMA: CLINICAL_DATA_SCHEMA.VITALS_SCHEMA,
    MEDICAL_STANDARDIZATION
};

