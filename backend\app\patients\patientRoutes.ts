/**
 * @file patientRoutes.ts
 * @description Express routes for patient-related operations.
 * Provides CRUD endpoints for patient management with FHIR integration.
 */

import { Router, Request, Response } from 'express';
import { PatientCreate, PatientUpdate } from './patientSchemas.js';
import * as patientService from './patientService.js';
import { getMedplumClient } from '../helpers/medplumHelper.js';

// Constants
const MAX_PATIENTS_PER_REQUEST = 100;
const DEFAULT_PATIENT_LIMIT = 20;

const router = Router();

/**
 * Creates a new patient in the system.
 * 
 * @route POST /api/patients
 * @param {Request} req - Express request object containing patient data
 * @param {Response} res - Express response object
 * @returns {Promise<Response>} JSON response with created patient data
 */
router.post('/', async (req: Request, res: Response): Promise<any> => {
    try {
        const patientData: PatientCreate = req.body;

        // Validate required fields
        if (!patientData.family ||
            !patientData.given ||
            patientData.given.length === 0) {
            return res.status(400).json({
                error: 'Missing required fields',
                message: 'family and given names are required',
                required: ['family', 'given']
            });
        }

        // Get Medplum client
        const client = await getMedplumClient();

        // Create patient
        const patient = await patientService.createPatient(client, patientData);

        console.log(` Patient created: ${patient.id}`);
        res.status(201).json({
            success: true,
            message: 'Patient created successfully',
            data: patient
        });

    } catch (error: any) {
        console.error(' Error creating patient:', error.message);
        res.status(400).json({
            error: 'Failed to create patient',
            message: error.message,
            details: error.response?.data || null
        });
    }
});

/**
 * Retrieves a patient by their unique ID.
 * 
 * @route GET /api/patients/:id
 * @param {Request} req - Express request object with patient ID in params
 * @param {Response} res - Express response object
 * @returns {Promise<Response>} JSON response with patient data
 */
router.get('/:id', async (req: Request, res: Response): Promise<any> => {
    try {
        const patientId = req.params.id;

        if (!patientId) {
            return res.status(400).json({
                error: 'Patient ID is required'
            });
        }

        // Get Medplum client
        const client = await getMedplumClient();

        // Get patient
        const patient = await patientService.getPatient(client, patientId);

        if (!patient) {
            return res.status(404).json({
                error: 'Patient not found',
                patientId
            });
        }

        console.log(` Patient retrieved: ${patient.id}`);
        res.json({
            success: true,
            data: patient
        });

    } catch (error: any) {
        console.error(' Error retrieving patient:', error.message);

        if (error.message.includes('not found') || error.status === 404) {
            res.status(404).json({
                error: 'Patient not found',
                patientId: req.params.id
            });
        } else {
            res.status(500).json({
                error: 'Failed to retrieve patient',
                message: error.message
            });
        }
    }
});

/**
 * Updates an existing patient's information.
 * 
 * @route PATCH /api/patients/:id
 * @param {Request} req - Express request object with patient ID and update data
 * @param {Response} res - Express response object
 * @returns {Promise<Response>} JSON response with updated patient data
 */
router.patch('/:id', async (req: Request, res: Response): Promise<any> => {
    try {
        const patientId = req.params.id;
        const updateData: PatientUpdate = req.body;

        if (!patientId) {
            return res.status(400).json({
                error: 'Patient ID is required'
            });
        }

        // Get Medplum client
        const client = await getMedplumClient();

        // Update patient
        const updatedPatient = await patientService.updatePatient(
            client,
            patientId,
            updateData
        );

        console.log(` Patient updated: ${updatedPatient.id}`);
        res.json({
            success: true,
            message: 'Patient updated successfully',
            data: updatedPatient
        });

    } catch (error: any) {
        console.error(' Error updating patient:', error.message);

        if (error.message.includes('not found') || error.status === 404) {
            res.status(404).json({
                error: 'Patient not found',
                patientId: req.params.id
            });
        } else {
            res.status(400).json({
                error: 'Failed to update patient',
                message: error.message
            });
        }
    }
});

/**
 * Deletes a patient from the system.
 * 
 * @route DELETE /api/patients/:id
 * @param {Request} req - Express request object with patient ID
 * @param {Response} res - Express response object
 * @returns {Promise<Response>} Empty response with 204 status
 */
router.delete('/:id', async (req: Request, res: Response): Promise<any> => {
    try {
        const patientId = req.params.id;

        if (!patientId) {
            return res.status(400).json({
                error: 'Patient ID is required'
            });
        }

        // Get Medplum client
        const client = await getMedplumClient();

        // Delete patient
        await patientService.deletePatient(client, patientId);

        console.log(`Patient deleted: ${patientId}`);
        res.status(204).send();

    } catch (error: any) {
        console.error(' Error deleting patient:', error.message);

        if (error.message.includes('not found') || error.status === 404) {
            res.status(404).json({
                error: 'Patient not found',
                patientId: req.params.id
            });
        } else {
            res.status(500).json({
                error: 'Failed to delete patient',
                message: error.message
            });
        }
    }
});

/**
 * Retrieves a paginated list of patients with optional search.
 * 
 * @route GET /api/patients
 * @param {Request} req - Express request object with query parameters
 * @param {Response} res - Express response object
 * @returns {Promise<Response>} JSON response with patient list and pagination
 */
router.get('/', async (req: Request, res: Response) => {
    try {
        const limit = Math.min(
            parseInt(req.query.limit as string) || DEFAULT_PATIENT_LIMIT,
            MAX_PATIENTS_PER_REQUEST
        );
        const offset = parseInt(req.query.offset as string) || 0;
        const search = req.query.search as string;

        // Get Medplum client
        const client = await getMedplumClient();

        // Search patients
        const searchResult = await patientService.searchPatients(client, {
            search,
            limit,
            offset
        });

        console.log(` ${searchResult.patients.length} patients retrieved`);

        res.json({
            success: true,
            data: searchResult.patients,
            pagination: {
                total: searchResult.total,
                limit,
                offset,
                hasMore: searchResult.hasMore
            }
        });

    } catch (error: any) {
        console.error(' Error listing patients:', error.message);
        res.status(500).json({
            error: 'Failed to retrieve patients',
            message: error.message
        });
    }
});

/**
 * Health check endpoint for the patients API.
 * 
 * @route GET /api/patients/health
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @returns {Promise<Response>} JSON response with service health status
 */
router.get('/health', async (req: Request, res: Response) => {
    try {
        // Test Medplum connection
        const client = await getMedplumClient();

        // Verify connection to Medplum API
        const testResult = await patientService.searchPatients(client, {
            limit: 1
        });

        res.json({
            status: 'OK',
            service: 'Patients API',
            timestamp: new Date().toISOString(),
            medplum: {
                connected: true,
                baseUrl: process.env.MEDPLUM_BASE_URL ||
                    'https://api.medplum.com/',
                testQueryResults: testResult.patients.length
            },
            endpoints: {
                'POST /': 'Create patient',
                'GET /': 'List patients',
                'GET /:id': 'Get patient by ID',
                'PATCH /:id': 'Update patient',
                'DELETE /:id': 'Delete patient',
            }
        });
    } catch (error: any) {
        res.status(500).json({
            status: 'ERROR',
            service: 'Patients API',
            error: error.message,
            medplum: {
                connected: false,
                error: error.message
            }
        });
    }
});

export default router;