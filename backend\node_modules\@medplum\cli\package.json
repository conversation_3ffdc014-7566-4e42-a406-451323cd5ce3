{"name": "@medplum/cli", "version": "4.2.0", "description": "Medplum Command Line Interface", "keywords": ["medplum", "fhir", "healthcare", "interoperability", "json", "serialization", "hl7", "standards", "clinical", "dstu2", "stu3", "r4", "normative"], "homepage": "https://www.medplum.com/", "bugs": {"url": "https://github.com/medplum/medplum/issues"}, "repository": {"type": "git", "url": "git+https://github.com/medplum/medplum.git", "directory": "packages/cli"}, "license": "Apache-2.0", "author": "Medplum <<EMAIL>>", "bin": {"medplum": "./dist/cjs/index.cjs"}, "sideEffects": false, "exports": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.mjs"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.cjs"}}, "main": "dist/cjs/index.cjs", "module": "dist/esm/index.mjs", "types": "dist/cjs/index.d.ts", "files": ["dist/cjs", "dist/esm"], "scripts": {"api-extractor": "api-extractor run --local && cp dist/types.d.ts dist/cjs/index.d.ts && cp dist/types.d.ts dist/esm/index.d.ts", "build": "npm run clean && tsc && node esbuild.mjs && npm run api-extractor", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint .", "medplum": "ts-node src/index.ts", "test": "jest"}, "dependencies": {"@aws-sdk/client-acm": "3.831.0", "@aws-sdk/client-cloudformation": "3.830.0", "@aws-sdk/client-cloudfront": "3.830.0", "@aws-sdk/client-ecs": "3.834.0", "@aws-sdk/client-s3": "3.832.0", "@aws-sdk/client-ssm": "3.830.0", "@aws-sdk/client-sts": "3.830.0", "@aws-sdk/types": "3.821.0", "@medplum/core": "4.2.0", "@medplum/hl7": "4.2.0", "commander": "12.1.0", "dotenv": "16.5.0", "fast-glob": "3.3.3", "iconv-lite": "0.6.3", "node-fetch": "2.7.0", "semver": "7.7.2", "tar": "7.4.3"}, "devDependencies": {"@medplum/fhirtypes": "4.2.0", "@medplum/mock": "4.2.0", "@types/node-fetch": "2.6.12", "@types/semver": "7.7.0", "aws-sdk-client-mock": "4.1.0"}, "engines": {"node": ">=20.0.0"}}