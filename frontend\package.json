{"name": "syncore-mvp", "version": "0.1.0", "private": true, "dependencies": {"@hcaptcha/react-hcaptcha": "^1.12.0", "@medplum/core": "^4.0.4", "@medplum/react": "^4.0.4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "chart.js": "^4.4.9", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.488.0", "moment": "^2.30.1", "openai": "^4.103.0", "react": "^18.3.1", "react-big-calendar": "^1.18.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-google-recaptcha": "^3.1.0", "react-google-recaptcha-v3": "^1.10.1", "react-icons": "^5.5.0", "react-router-dom": "^7.5.0", "react-scripts": "^5.0.1", "react-to-pdf": "^2.0.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "server": "node src/server/index.js", "server:dev": "nodemon src/server/index.js", "server:init": "node src/server/init/db.init.js", "dev": "concurrently \"npm run start\" \"npm run server:dev\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.14", "concurrently": "^9.1.2", "json-server": "^0.17.4", "nodemon": "^3.1.10", "postcss": "^8.4.23", "tailwindcss": "^3.3.0"}}